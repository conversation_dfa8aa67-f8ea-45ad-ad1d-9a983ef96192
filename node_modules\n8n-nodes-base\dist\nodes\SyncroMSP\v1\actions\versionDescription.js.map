{"version": 3, "sources": ["../../../../../nodes/SyncroMSP/v1/actions/versionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport { NodeConnectionTypes, type INodeTypeDescription } from 'n8n-workflow';\n\nimport * as contact from './contact';\nimport * as customer from './customer';\nimport * as rmm from './rmm';\nimport * as ticket from './ticket';\n\nexport const versionDescription: INodeTypeDescription = {\n\tdisplayName: 'SyncroMSP',\n\tname: 'syncroMsp',\n\t// eslint-disable-next-line n8n-nodes-base/node-class-description-icon-not-svg\n\ticon: 'file:syncromsp.png',\n\tgroup: ['output'],\n\tversion: 1,\n\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\tdescription: 'Gets data from SyncroMSP',\n\tdefaults: {\n\t\tname: 'SyncroMSP',\n\t},\n\tinputs: [NodeConnectionTypes.Main],\n\toutputs: [NodeConnectionTypes.Main],\n\tcredentials: [\n\t\t{\n\t\t\tname: 'syncroMspA<PERSON>',\n\t\t\trequired: true,\n\t\t\ttestedBy: 'syncroMspApiCredentialTest',\n\t\t},\n\t],\n\tproperties: [\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Contact',\n\t\t\t\t\tvalue: 'contact',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Customer',\n\t\t\t\t\tvalue: 'customer',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'RMM',\n\t\t\t\t\tvalue: 'rmm',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Ticket',\n\t\t\t\t\tvalue: 'ticket',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'contact',\n\t\t},\n\t\t...customer.descriptions,\n\t\t...ticket.descriptions,\n\t\t...contact.descriptions,\n\t\t...rmm.descriptions,\n\t],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAA+D;AAE/D,cAAyB;AACzB,eAA0B;AAC1B,UAAqB;AACrB,aAAwB;AAEjB,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,EACb,MAAM;AAAA;AAAA,EAEN,MAAM;AAAA,EACN,OAAO,CAAC,QAAQ;AAAA,EAChB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,aAAa;AAAA,IACZ;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,EACD;AAAA,EACA,YAAY;AAAA,IACX;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA,GAAG,SAAS;AAAA,IACZ,GAAG,OAAO;AAAA,IACV,GAAG,QAAQ;AAAA,IACX,GAAG,IAAI;AAAA,EACR;AACD;", "names": []}