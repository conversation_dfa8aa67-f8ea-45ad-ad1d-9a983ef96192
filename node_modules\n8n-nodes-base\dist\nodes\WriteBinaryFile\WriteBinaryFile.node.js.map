{"version": 3, "sources": ["../../../nodes/WriteBinaryFile/WriteBinaryFile.node.ts"], "sourcesContent": ["import { BINARY_ENCODING, NodeConnectionTypes } from 'n8n-workflow';\nimport type {\n\tIExecuteFunctions,\n\tINodeExecutionData,\n\tINodeType,\n\tINodeTypeDescription,\n} from 'n8n-workflow';\nimport type { Readable } from 'stream';\n\nexport class WriteBinaryFile implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\thidden: true,\n\t\tdisplayName: 'Write Binary File',\n\t\tname: 'writeBinaryFile',\n\t\ticon: 'fa:file-export',\n\t\tgroup: ['output'],\n\t\tversion: 1,\n\t\tdescription: 'Writes a binary file to disk',\n\t\tdefaults: {\n\t\t\tname: 'Write Binary File',\n\t\t\tcolor: '#CC2233',\n\t\t},\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'File Name',\n\t\t\t\tname: 'fileName',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\trequired: true,\n\t\t\t\tplaceholder: '/data/example.jpg',\n\t\t\t\tdescription: 'Path to which the file should be written',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Property Name',\n\t\t\t\tname: 'dataPropertyName',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: 'data',\n\t\t\t\trequired: true,\n\t\t\t\tdescription:\n\t\t\t\t\t'Name of the binary property which contains the data for the file to be written',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Options',\n\t\t\t\tname: 'options',\n\t\t\t\ttype: 'collection',\n\t\t\t\tplaceholder: 'Add option',\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Append',\n\t\t\t\t\t\tname: 'append',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\tdescription: 'Whether to append to an existing file',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\n\t\tconst returnData: INodeExecutionData[] = [];\n\t\tconst length = items.length;\n\t\tlet item: INodeExecutionData;\n\n\t\tfor (let itemIndex = 0; itemIndex < length; itemIndex++) {\n\t\t\ttry {\n\t\t\t\tconst dataPropertyName = this.getNodeParameter('dataPropertyName', itemIndex);\n\n\t\t\t\tconst fileName = this.getNodeParameter('fileName', itemIndex) as string;\n\n\t\t\t\tconst options = this.getNodeParameter('options', 0, {});\n\n\t\t\t\tconst flag: string = options.append ? 'a' : 'w';\n\n\t\t\t\titem = items[itemIndex];\n\n\t\t\t\tconst newItem: INodeExecutionData = {\n\t\t\t\t\tjson: {},\n\t\t\t\t\tpairedItem: {\n\t\t\t\t\t\titem: itemIndex,\n\t\t\t\t\t},\n\t\t\t\t};\n\t\t\t\tObject.assign(newItem.json, item.json);\n\n\t\t\t\tconst binaryData = this.helpers.assertBinaryData(itemIndex, dataPropertyName);\n\n\t\t\t\tlet fileContent: Buffer | Readable;\n\t\t\t\tif (binaryData.id) {\n\t\t\t\t\tfileContent = await this.helpers.getBinaryStream(binaryData.id);\n\t\t\t\t} else {\n\t\t\t\t\tfileContent = Buffer.from(binaryData.data, BINARY_ENCODING);\n\t\t\t\t}\n\n\t\t\t\t// Write the file to disk\n\n\t\t\t\tawait this.helpers.writeContentToFile(fileName, fileContent, flag);\n\n\t\t\t\tif (item.binary !== undefined) {\n\t\t\t\t\t// Create a shallow copy of the binary data so that the old\n\t\t\t\t\t// data references which do not get changed still stay behind\n\t\t\t\t\t// but the incoming data does not get changed.\n\t\t\t\t\tnewItem.binary = {};\n\t\t\t\t\tObject.assign(newItem.binary, item.binary);\n\t\t\t\t}\n\n\t\t\t\t// Add the file name to data\n\n\t\t\t\tnewItem.json.fileName = fileName;\n\n\t\t\t\treturnData.push(newItem);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tjson: {\n\t\t\t\t\t\t\terror: (error as Error).message,\n\t\t\t\t\t\t},\n\t\t\t\t\t\tpairedItem: {\n\t\t\t\t\t\t\titem: itemIndex,\n\t\t\t\t\t\t},\n\t\t\t\t\t});\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAAqD;AAS9C,MAAM,gBAAqC;AAAA,EAA3C;AACN,uBAAoC;AAAA,MACnC,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,QAAQ;AAAA,MAChB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU;AAAA,UACV,aAAa;AAAA,UACb,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU;AAAA,UACV,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,SAAS,CAAC;AAAA,UACV,SAAS;AAAA,YACR;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAEhC,UAAM,aAAmC,CAAC;AAC1C,UAAM,SAAS,MAAM;AACrB,QAAI;AAEJ,aAAS,YAAY,GAAG,YAAY,QAAQ,aAAa;AACxD,UAAI;AACH,cAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,SAAS;AAE5E,cAAM,WAAW,KAAK,iBAAiB,YAAY,SAAS;AAE5D,cAAM,UAAU,KAAK,iBAAiB,WAAW,GAAG,CAAC,CAAC;AAEtD,cAAM,OAAe,QAAQ,SAAS,MAAM;AAE5C,eAAO,MAAM,SAAS;AAEtB,cAAM,UAA8B;AAAA,UACnC,MAAM,CAAC;AAAA,UACP,YAAY;AAAA,YACX,MAAM;AAAA,UACP;AAAA,QACD;AACA,eAAO,OAAO,QAAQ,MAAM,KAAK,IAAI;AAErC,cAAM,aAAa,KAAK,QAAQ,iBAAiB,WAAW,gBAAgB;AAE5E,YAAI;AACJ,YAAI,WAAW,IAAI;AAClB,wBAAc,MAAM,KAAK,QAAQ,gBAAgB,WAAW,EAAE;AAAA,QAC/D,OAAO;AACN,wBAAc,OAAO,KAAK,WAAW,MAAM,mCAAe;AAAA,QAC3D;AAIA,cAAM,KAAK,QAAQ,mBAAmB,UAAU,aAAa,IAAI;AAEjE,YAAI,KAAK,WAAW,QAAW;AAI9B,kBAAQ,SAAS,CAAC;AAClB,iBAAO,OAAO,QAAQ,QAAQ,KAAK,MAAM;AAAA,QAC1C;AAIA,gBAAQ,KAAK,WAAW;AAExB,mBAAW,KAAK,OAAO;AAAA,MACxB,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK;AAAA,YACf,MAAM;AAAA,cACL,OAAQ,MAAgB;AAAA,YACzB;AAAA,YACA,YAAY;AAAA,cACX,MAAM;AAAA,YACP;AAAA,UACD,CAAC;AACD;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AACA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": []}