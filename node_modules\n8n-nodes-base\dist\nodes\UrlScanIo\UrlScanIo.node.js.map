{"version": 3, "sources": ["../../../nodes/UrlScanIo/UrlScanIo.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tIDataObject,\n\tINodeExecutionData,\n\tINodeType,\n\tINodeTypeDescription,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes, NodeOperationError } from 'n8n-workflow';\n\nimport { scanFields, scanOperations } from './descriptions';\nimport { handleListing, normalizeId, urlScanIoApiRequest } from './GenericFunctions';\n\nexport class UrlScanIo implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'urlscan.io',\n\t\tname: 'urlScanIo',\n\t\ticon: 'file:urlScanIo.svg',\n\t\tgroup: ['transform'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription:\n\t\t\t'Provides various utilities for monitoring websites like health checks or screenshots',\n\t\tdefaults: {\n\t\t\tname: 'urlscan.io',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'urlScanIoApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Scan',\n\t\t\t\t\t\tvalue: 'scan',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'scan',\n\t\t\t},\n\t\t\t...scanOperations,\n\t\t\t...scanFields,\n\t\t],\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: IDataObject[] = [];\n\n\t\tconst resource = this.getNodeParameter('resource', 0) as 'scan';\n\t\tconst operation = this.getNodeParameter('operation', 0) as 'perform' | 'get' | 'getAll';\n\n\t\tlet responseData;\n\n\t\tfor (let i = 0; i < items.length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'scan') {\n\t\t\t\t\t// **********************************************************************\n\t\t\t\t\t//                               scan\n\t\t\t\t\t// **********************************************************************\n\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//               scan: get\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst scanId = this.getNodeParameter('scanId', i) as string;\n\t\t\t\t\t\tresponseData = await urlScanIoApiRequest.call(this, 'GET', `/result/${scanId}`);\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             scan: getAll\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\t// https://urlscan.io/docs/search\n\n\t\t\t\t\t\tconst filters = this.getNodeParameter('filters', i) as { query?: string };\n\n\t\t\t\t\t\tconst qs: IDataObject = {};\n\n\t\t\t\t\t\tif (filters?.query) {\n\t\t\t\t\t\t\tqs.q = filters.query;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await handleListing.call(this, '/search', qs);\n\t\t\t\t\t\tresponseData = responseData.map(normalizeId);\n\t\t\t\t\t} else if (operation === 'perform') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             scan: perform\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\t// https://urlscan.io/docs/search\n\n\t\t\t\t\t\tconst { tags: rawTags, ...rest } = this.getNodeParameter('additionalFields', i) as {\n\t\t\t\t\t\t\tcustomAgent?: string;\n\t\t\t\t\t\t\tvisibility?: 'public' | 'private' | 'unlisted';\n\t\t\t\t\t\t\ttags?: string;\n\t\t\t\t\t\t\treferer?: string;\n\t\t\t\t\t\t\toverrideSafety: string;\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\turl: this.getNodeParameter('url', i) as string,\n\t\t\t\t\t\t\t...rest,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (rawTags) {\n\t\t\t\t\t\t\tconst tags = rawTags.split(',').map((tag) => tag.trim());\n\n\t\t\t\t\t\t\tif (tags.length > 10) {\n\t\t\t\t\t\t\t\tthrow new NodeOperationError(this.getNode(), 'Please enter at most 10 tags', {\n\t\t\t\t\t\t\t\t\titemIndex: i,\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tbody.tags = tags;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await urlScanIoApiRequest.call(this, 'POST', '/scan', body);\n\t\t\t\t\t\tresponseData = normalizeId(responseData as IDataObject);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tArray.isArray(responseData)\n\t\t\t\t\t? returnData.push(...(responseData as IDataObject[]))\n\t\t\t\t\t: returnData.push(responseData as IDataObject);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ error: error.message });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\n\t\treturn [this.helpers.returnJsonArray(returnData)];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,0BAAwD;AAExD,0BAA2C;AAC3C,8BAAgE;AAEzD,MAAM,UAA+B;AAAA,EAArC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,WAAW;AAAA,MACnB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aACC;AAAA,MACD,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAA4B,CAAC;AAEnC,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,QAAI;AAEJ,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,UAAI;AACH,YAAI,aAAa,QAAQ;AAKxB,cAAI,cAAc,OAAO;AAKxB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,2BAAe,MAAM,4CAAoB,KAAK,MAAM,OAAO,WAAW,MAAM,EAAE;AAAA,UAC/E,WAAW,cAAc,UAAU;AAOlC,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,kBAAM,KAAkB,CAAC;AAEzB,gBAAI,SAAS,OAAO;AACnB,iBAAG,IAAI,QAAQ;AAAA,YAChB;AAEA,2BAAe,MAAM,sCAAc,KAAK,MAAM,WAAW,EAAE;AAC3D,2BAAe,aAAa,IAAI,mCAAW;AAAA,UAC5C,WAAW,cAAc,WAAW;AAOnC,kBAAM,EAAE,MAAM,SAAS,GAAG,KAAK,IAAI,KAAK,iBAAiB,oBAAoB,CAAC;AAQ9E,kBAAM,OAAoB;AAAA,cACzB,KAAK,KAAK,iBAAiB,OAAO,CAAC;AAAA,cACnC,GAAG;AAAA,YACJ;AAEA,gBAAI,SAAS;AACZ,oBAAM,OAAO,QAAQ,MAAM,GAAG,EAAE,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC;AAEvD,kBAAI,KAAK,SAAS,IAAI;AACrB,sBAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,gCAAgC;AAAA,kBAC5E,WAAW;AAAA,gBACZ,CAAC;AAAA,cACF;AAEA,mBAAK,OAAO;AAAA,YACb;AAEA,2BAAe,MAAM,4CAAoB,KAAK,MAAM,QAAQ,SAAS,IAAI;AACzE,+BAAe,qCAAY,YAA2B;AAAA,UACvD;AAAA,QACD;AAEA,cAAM,QAAQ,YAAY,IACvB,WAAW,KAAK,GAAI,YAA8B,IAClD,WAAW,KAAK,YAA2B;AAAA,MAC/C,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,OAAO,MAAM,QAAQ,CAAC;AACxC;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AAEA,WAAO,CAAC,KAAK,QAAQ,gBAAgB,UAAU,CAAC;AAAA,EACjD;AACD;", "names": []}