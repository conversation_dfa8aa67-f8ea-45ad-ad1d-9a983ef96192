{"version": 3, "sources": ["../../../nodes/Twitter/Twitter.node.ts"], "sourcesContent": ["import type { INodeTypeBaseDescription, IVersionedNodeType } from 'n8n-workflow';\nimport { VersionedNodeType } from 'n8n-workflow';\n\nimport { TwitterV1 } from './V1/TwitterV1.node';\nimport { TwitterV2 } from './V2/TwitterV2.node';\n\nexport class Twitter extends VersionedNodeType {\n\tconstructor() {\n\t\tconst baseDescription: INodeTypeBaseDescription = {\n\t\t\tdisplayName: 'X (Formerly Twitter)',\n\t\t\tname: 'twitter',\n\t\t\ticon: { light: 'file:x.svg', dark: 'file:x.dark.svg' },\n\t\t\tgroup: ['output'],\n\t\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\t\tdescription: 'Consume the X API',\n\t\t\tdefaultVersion: 2,\n\t\t};\n\n\t\tconst nodeVersions: IVersionedNodeType['nodeVersions'] = {\n\t\t\t1: new TwitterV1(baseDescription),\n\t\t\t2: new TwitterV2(baseDescription),\n\t\t};\n\n\t\tsuper(nodeVersions, baseDescription);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAAkC;AAElC,uBAA0B;AAC1B,uBAA0B;AAEnB,MAAM,gBAAgB,sCAAkB;AAAA,EAC9C,cAAc;AACb,UAAM,kBAA4C;AAAA,MACjD,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM,EAAE,OAAO,cAAc,MAAM,kBAAkB;AAAA,MACrD,OAAO,CAAC,QAAQ;AAAA,MAChB,UAAU;AAAA,MACV,aAAa;AAAA,MACb,gBAAgB;AAAA,IACjB;AAEA,UAAM,eAAmD;AAAA,MACxD,GAAG,IAAI,2BAAU,eAAe;AAAA,MAChC,GAAG,IAAI,2BAAU,eAAe;AAAA,IACjC;AAEA,UAAM,cAAc,eAAe;AAAA,EACpC;AACD;", "names": []}