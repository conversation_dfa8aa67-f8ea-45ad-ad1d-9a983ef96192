{"version": 3, "sources": ["../../../utils/sendAndWait/utils.ts"], "sourcesContent": ["import isbot from 'isbot';\nimport {\n\tNodeOperationError,\n\tSEND_AND_WAIT_OPERATION,\n\ttryToParseJsonToFormFields,\n\tupdateDisplayOptions,\n} from 'n8n-workflow';\nimport type {\n\tINodeProperties,\n\tIExecuteFunctions,\n\tIWebhookFunctions,\n\tIDataObject,\n\tFormFieldsParameter,\n} from 'n8n-workflow';\n\nimport { limitWaitTimeProperties } from './descriptions';\nimport {\n\tACTION_RECORDED_PAGE,\n\tBUTTON_STYLE_PRIMARY,\n\tBUTTON_STYLE_SECONDARY,\n\tcreateEmailBodyWithN8nAttribution,\n\tcreateEmailBodyWithoutN8nAttribution,\n} from './email-templates';\nimport type { IEmail } from './interfaces';\nimport { formFieldsProperties } from '../../nodes/Form/Form.node';\nimport { prepareFormData, prepareFormReturnItem, resolveRawData } from '../../nodes/Form/utils';\nimport { escapeHtml } from '../utilities';\n\nexport type SendAndWaitConfig = {\n\ttitle: string;\n\tmessage: string;\n\turl: string;\n\toptions: Array<{ label: string; value: string; style: string }>;\n\tappendAttribution?: boolean;\n};\n\ntype FormResponseTypeOptions = {\n\tmessageButtonLabel?: string;\n\tresponseFormTitle?: string;\n\tresponseFormDescription?: string;\n\tresponseFormButtonLabel?: string;\n};\n\nconst INPUT_FIELD_IDENTIFIER = 'field-0';\n\nconst limitWaitTimeOption: INodeProperties = {\n\tdisplayName: 'Limit Wait Time',\n\tname: 'limitWaitTime',\n\ttype: 'fixedCollection',\n\tdescription:\n\t\t'Whether the workflow will automatically resume execution after the specified limit type',\n\tdefault: { values: { limitType: 'afterTimeInterval', resumeAmount: 45, resumeUnit: 'minutes' } },\n\toptions: [\n\t\t{\n\t\t\tdisplayName: 'Values',\n\t\t\tname: 'values',\n\t\t\tvalues: limitWaitTimeProperties,\n\t\t},\n\t],\n};\n\nconst appendAttributionOption: INodeProperties = {\n\tdisplayName: 'Append n8n Attribution',\n\tname: 'appendAttribution',\n\ttype: 'boolean',\n\tdefault: true,\n\tdescription:\n\t\t'Whether to include the phrase \"This message was sent automatically with n8n\" to the end of the message',\n};\n\n// Operation Properties ----------------------------------------------------------\nexport function getSendAndWaitProperties(\n\ttargetProperties: INodeProperties[],\n\tresource: string = 'message',\n\tadditionalProperties: INodeProperties[] = [],\n\toptions?: {\n\t\tnoButtonStyle?: boolean;\n\t\tdefaultApproveLabel?: string;\n\t\tdefaultDisapproveLabel?: string;\n\t},\n) {\n\tconst buttonStyle: INodeProperties = {\n\t\tdisplayName: 'Button Style',\n\t\tname: 'buttonStyle',\n\t\ttype: 'options',\n\t\tdefault: 'primary',\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Primary',\n\t\t\t\tvalue: 'primary',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Secondary',\n\t\t\t\tvalue: 'secondary',\n\t\t\t},\n\t\t],\n\t};\n\tconst approvalOptionsValues = [\n\t\t{\n\t\t\tdisplayName: 'Type of Approval',\n\t\t\tname: 'approvalType',\n\t\t\ttype: 'options',\n\t\t\tplaceholder: 'Add option',\n\t\t\tdefault: 'single',\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Approve Only',\n\t\t\t\t\tvalue: 'single',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Approve and Disapprove',\n\t\t\t\t\tvalue: 'double',\n\t\t\t\t},\n\t\t\t],\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Approve Button Label',\n\t\t\tname: 'approveLabel',\n\t\t\ttype: 'string',\n\t\t\tdefault: options?.defaultApproveLabel || 'Approve',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tapprovalType: ['single', 'double'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\t...[\n\t\t\toptions?.noButtonStyle\n\t\t\t\t? ({} as INodeProperties)\n\t\t\t\t: {\n\t\t\t\t\t\t...buttonStyle,\n\t\t\t\t\t\tdisplayName: 'Approve Button Style',\n\t\t\t\t\t\tname: 'buttonApprovalStyle',\n\t\t\t\t\t\tdisplayOptions: {\n\t\t\t\t\t\t\tshow: {\n\t\t\t\t\t\t\t\tapprovalType: ['single', 'double'],\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t],\n\t\t{\n\t\t\tdisplayName: 'Disapprove Button Label',\n\t\t\tname: 'disapproveLabel',\n\t\t\ttype: 'string',\n\t\t\tdefault: options?.defaultDisapproveLabel || 'Decline',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tapprovalType: ['double'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\t...[\n\t\t\toptions?.noButtonStyle\n\t\t\t\t? ({} as INodeProperties)\n\t\t\t\t: {\n\t\t\t\t\t\t...buttonStyle,\n\t\t\t\t\t\tdisplayName: 'Disapprove Button Style',\n\t\t\t\t\t\tname: 'buttonDisapprovalStyle',\n\t\t\t\t\t\tdefault: 'secondary',\n\t\t\t\t\t\tdisplayOptions: {\n\t\t\t\t\t\t\tshow: {\n\t\t\t\t\t\t\t\tapprovalType: ['double'],\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t],\n\t].filter((p) => Object.keys(p).length) as INodeProperties[];\n\n\tconst sendAndWait: INodeProperties[] = [\n\t\t...targetProperties,\n\t\t{\n\t\t\tdisplayName: 'Subject',\n\t\t\tname: 'subject',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\trequired: true,\n\t\t\tplaceholder: 'e.g. Approval required',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Message',\n\t\t\tname: 'message',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\trequired: true,\n\t\t\ttypeOptions: {\n\t\t\t\trows: 4,\n\t\t\t},\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Response Type',\n\t\t\tname: 'responseType',\n\t\t\ttype: 'options',\n\t\t\tdefault: 'approval',\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Approval',\n\t\t\t\t\tvalue: 'approval',\n\t\t\t\t\tdescription: 'User can approve/disapprove from within the message',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Free Text',\n\t\t\t\t\tvalue: 'freeText',\n\t\t\t\t\tdescription: 'User can submit a response via a form',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Custom Form',\n\t\t\t\t\tvalue: 'customForm',\n\t\t\t\t\tdescription: 'User can submit a response via a custom form',\n\t\t\t\t},\n\t\t\t],\n\t\t},\n\t\t...updateDisplayOptions(\n\t\t\t{\n\t\t\t\tshow: {\n\t\t\t\t\tresponseType: ['customForm'],\n\t\t\t\t},\n\t\t\t},\n\t\t\tformFieldsProperties,\n\t\t),\n\n\t\t{\n\t\t\tdisplayName: 'Approval Options',\n\t\t\tname: 'approvalOptions',\n\t\t\ttype: 'fixedCollection',\n\t\t\tplaceholder: 'Add option',\n\t\t\tdefault: {},\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Values',\n\t\t\t\t\tname: 'values',\n\t\t\t\t\tvalues: approvalOptionsValues,\n\t\t\t\t},\n\t\t\t],\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresponseType: ['approval'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Options',\n\t\t\tname: 'options',\n\t\t\ttype: 'collection',\n\t\t\tplaceholder: 'Add option',\n\t\t\tdefault: {},\n\t\t\toptions: [limitWaitTimeOption, appendAttributionOption],\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresponseType: ['approval'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Options',\n\t\t\tname: 'options',\n\t\t\ttype: 'collection',\n\t\t\tplaceholder: 'Add option',\n\t\t\tdefault: {},\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Message Button Label',\n\t\t\t\t\tname: 'messageButtonLabel',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: 'Respond',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Response Form Title',\n\t\t\t\t\tname: 'responseFormTitle',\n\t\t\t\t\tdescription: 'Title of the form that the user can access to provide their response',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: '',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Response Form Description',\n\t\t\t\t\tname: 'responseFormDescription',\n\t\t\t\t\tdescription: 'Description of the form that the user can access to provide their response',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: '',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Response Form Button Label',\n\t\t\t\t\tname: 'responseFormButtonLabel',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: 'Submit',\n\t\t\t\t},\n\t\t\t\tlimitWaitTimeOption,\n\t\t\t\tappendAttributionOption,\n\t\t\t],\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresponseType: ['freeText', 'customForm'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\t...additionalProperties,\n\t];\n\n\treturn updateDisplayOptions(\n\t\t{\n\t\t\tshow: {\n\t\t\t\tresource: [resource],\n\t\t\t\toperation: [SEND_AND_WAIT_OPERATION],\n\t\t\t},\n\t\t},\n\t\tsendAndWait,\n\t);\n}\n\n// Webhook Function --------------------------------------------------------------\nconst getFormResponseCustomizations = (context: IWebhookFunctions) => {\n\tconst message = context.getNodeParameter('message', '') as string;\n\tconst options = context.getNodeParameter('options', {}) as FormResponseTypeOptions;\n\n\tlet formTitle = '';\n\tif (options.responseFormTitle) {\n\t\tformTitle = options.responseFormTitle;\n\t}\n\n\tlet formDescription = message;\n\tif (options.responseFormDescription) {\n\t\tformDescription = options.responseFormDescription;\n\t}\n\tformDescription = formDescription.replace(/\\\\n/g, '\\n').replace(/<br>/g, '\\n');\n\n\tlet buttonLabel = 'Submit';\n\tif (options.responseFormButtonLabel) {\n\t\tbuttonLabel = options.responseFormButtonLabel;\n\t}\n\n\treturn {\n\t\tformTitle,\n\t\tformDescription,\n\t\tbuttonLabel,\n\t};\n};\n\nexport async function sendAndWaitWebhook(this: IWebhookFunctions) {\n\tconst method = this.getRequestObject().method;\n\tconst res = this.getResponseObject();\n\tconst req = this.getRequestObject();\n\n\tconst responseType = this.getNodeParameter('responseType', 'approval') as\n\t\t| 'approval'\n\t\t| 'freeText'\n\t\t| 'customForm';\n\n\tif (responseType === 'approval' && isbot(req.headers['user-agent'])) {\n\t\tres.send('');\n\t\treturn { noWebhookResponse: true };\n\t}\n\n\tif (responseType === 'freeText') {\n\t\tif (method === 'GET') {\n\t\t\tconst { formTitle, formDescription, buttonLabel } = getFormResponseCustomizations(this);\n\n\t\t\tconst data = prepareFormData({\n\t\t\t\tformTitle,\n\t\t\t\tformDescription,\n\t\t\t\tformSubmittedHeader: 'Got it, thanks',\n\t\t\t\tformSubmittedText: 'This page can be closed now',\n\t\t\t\tbuttonLabel,\n\t\t\t\tredirectUrl: undefined,\n\t\t\t\tformFields: [\n\t\t\t\t\t{\n\t\t\t\t\t\tfieldLabel: 'Response',\n\t\t\t\t\t\tfieldType: 'textarea',\n\t\t\t\t\t\trequiredField: true,\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\ttestRun: false,\n\t\t\t\tquery: {},\n\t\t\t});\n\n\t\t\tres.render('form-trigger', data);\n\n\t\t\treturn {\n\t\t\t\tnoWebhookResponse: true,\n\t\t\t};\n\t\t}\n\t\tif (method === 'POST') {\n\t\t\tconst data = this.getBodyData().data as IDataObject;\n\n\t\t\treturn {\n\t\t\t\twebhookResponse: ACTION_RECORDED_PAGE,\n\t\t\t\tworkflowData: [[{ json: { data: { text: data[INPUT_FIELD_IDENTIFIER] } } }]],\n\t\t\t};\n\t\t}\n\t}\n\n\tif (responseType === 'customForm') {\n\t\tconst defineForm = this.getNodeParameter('defineForm', 'fields') as 'fields' | 'json';\n\t\tlet fields: FormFieldsParameter = [];\n\n\t\tif (defineForm === 'json') {\n\t\t\ttry {\n\t\t\t\tconst jsonOutput = this.getNodeParameter('jsonOutput', '', {\n\t\t\t\t\trawExpressions: true,\n\t\t\t\t}) as string;\n\n\t\t\t\tfields = tryToParseJsonToFormFields(resolveRawData(this, jsonOutput));\n\t\t\t} catch (error) {\n\t\t\t\tthrow new NodeOperationError(this.getNode(), error.message, {\n\t\t\t\t\tdescription: error.message,\n\t\t\t\t});\n\t\t\t}\n\t\t} else {\n\t\t\tfields = this.getNodeParameter('formFields.values', []) as FormFieldsParameter;\n\t\t}\n\n\t\tif (method === 'GET') {\n\t\t\tconst { formTitle, formDescription, buttonLabel } = getFormResponseCustomizations(this);\n\n\t\t\tconst data = prepareFormData({\n\t\t\t\tformTitle,\n\t\t\t\tformDescription,\n\t\t\t\tformSubmittedHeader: 'Got it, thanks',\n\t\t\t\tformSubmittedText: 'This page can be closed now',\n\t\t\t\tbuttonLabel,\n\t\t\t\tredirectUrl: undefined,\n\t\t\t\tformFields: fields,\n\t\t\t\ttestRun: false,\n\t\t\t\tquery: {},\n\t\t\t});\n\n\t\t\tres.render('form-trigger', data);\n\n\t\t\treturn {\n\t\t\t\tnoWebhookResponse: true,\n\t\t\t};\n\t\t}\n\t\tif (method === 'POST') {\n\t\t\tconst returnItem = await prepareFormReturnItem(this, fields, 'production', true);\n\t\t\tconst json = returnItem.json;\n\n\t\t\tdelete json.submittedAt;\n\t\t\tdelete json.formMode;\n\n\t\t\treturnItem.json = { data: json };\n\n\t\t\treturn {\n\t\t\t\twebhookResponse: ACTION_RECORDED_PAGE,\n\t\t\t\tworkflowData: [[returnItem]],\n\t\t\t};\n\t\t}\n\t}\n\n\tconst query = req.query as { approved: 'false' | 'true' };\n\tconst approved = query.approved === 'true';\n\treturn {\n\t\twebhookResponse: ACTION_RECORDED_PAGE,\n\t\tworkflowData: [[{ json: { data: { approved } } }]],\n\t};\n}\n\n// Send and Wait Config -----------------------------------------------------------\nexport function getSendAndWaitConfig(context: IExecuteFunctions): SendAndWaitConfig {\n\tconst message = escapeHtml((context.getNodeParameter('message', 0, '') as string).trim())\n\t\t.replace(/\\\\n/g, '\\n')\n\t\t.replace(/<br>/g, '\\n');\n\tconst subject = escapeHtml(context.getNodeParameter('subject', 0, '') as string);\n\tconst resumeUrl = context.evaluateExpression('{{ $execution?.resumeUrl }}', 0) as string;\n\tconst nodeId = context.evaluateExpression('{{ $nodeId }}', 0) as string;\n\tconst approvalOptions = context.getNodeParameter('approvalOptions.values', 0, {}) as {\n\t\tapprovalType?: 'single' | 'double';\n\t\tapproveLabel?: string;\n\t\tbuttonApprovalStyle?: string;\n\t\tdisapproveLabel?: string;\n\t\tbuttonDisapprovalStyle?: string;\n\t};\n\n\tconst options = context.getNodeParameter('options', 0, {});\n\n\tconst config: SendAndWaitConfig = {\n\t\ttitle: subject,\n\t\tmessage,\n\t\turl: `${resumeUrl}/${nodeId}`,\n\t\toptions: [],\n\t\tappendAttribution: options?.appendAttribution as boolean,\n\t};\n\n\tconst responseType = context.getNodeParameter('responseType', 0, 'approval') as string;\n\n\tif (responseType === 'freeText' || responseType === 'customForm') {\n\t\tconst label = context.getNodeParameter('options.messageButtonLabel', 0, 'Respond') as string;\n\t\tconfig.options.push({\n\t\t\tlabel,\n\t\t\tvalue: 'true',\n\t\t\tstyle: 'primary',\n\t\t});\n\t} else if (approvalOptions.approvalType === 'double') {\n\t\tconst approveLabel = escapeHtml(approvalOptions.approveLabel || 'Approve');\n\t\tconst buttonApprovalStyle = approvalOptions.buttonApprovalStyle || 'primary';\n\t\tconst disapproveLabel = escapeHtml(approvalOptions.disapproveLabel || 'Disapprove');\n\t\tconst buttonDisapprovalStyle = approvalOptions.buttonDisapprovalStyle || 'secondary';\n\n\t\tconfig.options.push({\n\t\t\tlabel: disapproveLabel,\n\t\t\tvalue: 'false',\n\t\t\tstyle: buttonDisapprovalStyle,\n\t\t});\n\t\tconfig.options.push({\n\t\t\tlabel: approveLabel,\n\t\t\tvalue: 'true',\n\t\t\tstyle: buttonApprovalStyle,\n\t\t});\n\t} else {\n\t\tconst label = escapeHtml(approvalOptions.approveLabel || 'Approve');\n\t\tconst style = approvalOptions.buttonApprovalStyle || 'primary';\n\t\tconfig.options.push({\n\t\t\tlabel,\n\t\t\tvalue: 'true',\n\t\t\tstyle,\n\t\t});\n\t}\n\n\treturn config;\n}\n\nexport function createButton(url: string, label: string, approved: string, style: string) {\n\tlet buttonStyle = BUTTON_STYLE_PRIMARY;\n\tif (style === 'secondary') {\n\t\tbuttonStyle = BUTTON_STYLE_SECONDARY;\n\t}\n\treturn `<a href=\"${url}?approved=${approved}\" target=\"_blank\" style=\"${buttonStyle}\">${label}</a>`;\n}\n\nexport function createEmail(context: IExecuteFunctions) {\n\tconst to = (context.getNodeParameter('sendTo', 0, '') as string).trim();\n\tconst config = getSendAndWaitConfig(context);\n\n\tif (to.indexOf('@') === -1 || (to.match(/@/g) || []).length > 1) {\n\t\tconst description = `The email address '${to}' in the 'To' field isn't valid or contains multiple addresses. Please provide only a single email address.`;\n\t\tthrow new NodeOperationError(context.getNode(), 'Invalid email address', {\n\t\t\tdescription,\n\t\t\titemIndex: 0,\n\t\t});\n\t}\n\n\tconst buttons: string[] = [];\n\tfor (const option of config.options) {\n\t\tbuttons.push(createButton(config.url, option.label, option.value, option.style));\n\t}\n\tlet emailBody: string;\n\tif (config.appendAttribution !== false) {\n\t\tconst instanceId = context.getInstanceId();\n\t\temailBody = createEmailBodyWithN8nAttribution(config.message, buttons.join('\\n'), instanceId);\n\t} else {\n\t\temailBody = createEmailBodyWithoutN8nAttribution(config.message, buttons.join('\\n'));\n\t}\n\n\tconst email: IEmail = {\n\t\tto,\n\t\tsubject: config.title,\n\t\tbody: '',\n\t\thtmlBody: emailBody,\n\t};\n\n\treturn email;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAClB,0BAKO;AASP,0BAAwC;AACxC,6BAMO;AAEP,kBAAqC;AACrC,mBAAuE;AACvE,uBAA2B;AAiB3B,MAAM,yBAAyB;AAE/B,MAAM,sBAAuC;AAAA,EAC5C,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,aACC;AAAA,EACD,SAAS,EAAE,QAAQ,EAAE,WAAW,qBAAqB,cAAc,IAAI,YAAY,UAAU,EAAE;AAAA,EAC/F,SAAS;AAAA,IACR;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,QAAQ;AAAA,IACT;AAAA,EACD;AACD;AAEA,MAAM,0BAA2C;AAAA,EAChD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aACC;AACF;AAGO,SAAS,yBACf,kBACA,WAAmB,WACnB,uBAA0C,CAAC,GAC3C,SAKC;AACD,QAAM,cAA+B;AAAA,IACpC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,EACD;AACA,QAAM,wBAAwB;AAAA,IAC7B;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,SAAS,uBAAuB;AAAA,MACzC,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,cAAc,CAAC,UAAU,QAAQ;AAAA,QAClC;AAAA,MACD;AAAA,IACD;AAAA,IACA,GAAG;AAAA,MACF,SAAS,gBACL,CAAC,IACF;AAAA,QACA,GAAG;AAAA,QACH,aAAa;AAAA,QACb,MAAM;AAAA,QACN,gBAAgB;AAAA,UACf,MAAM;AAAA,YACL,cAAc,CAAC,UAAU,QAAQ;AAAA,UAClC;AAAA,QACD;AAAA,MACD;AAAA,IACH;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,SAAS,0BAA0B;AAAA,MAC5C,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,cAAc,CAAC,QAAQ;AAAA,QACxB;AAAA,MACD;AAAA,IACD;AAAA,IACA,GAAG;AAAA,MACF,SAAS,gBACL,CAAC,IACF;AAAA,QACA,GAAG;AAAA,QACH,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,gBAAgB;AAAA,UACf,MAAM;AAAA,YACL,cAAc,CAAC,QAAQ;AAAA,UACxB;AAAA,QACD;AAAA,MACD;AAAA,IACH;AAAA,EACD,EAAE,OAAO,CAAC,MAAM,OAAO,KAAK,CAAC,EAAE,MAAM;AAErC,QAAM,cAAiC;AAAA,IACtC,GAAG;AAAA,IACH;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,IACd;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,QACZ,MAAM;AAAA,MACP;AAAA,IACD;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,MACD;AAAA,IACD;AAAA,IACA,OAAG;AAAA,MACF;AAAA,QACC,MAAM;AAAA,UACL,cAAc,CAAC,YAAY;AAAA,QAC5B;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,IAEA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS,CAAC;AAAA,MACV,SAAS;AAAA,QACR;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,QAAQ;AAAA,QACT;AAAA,MACD;AAAA,MACA,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,cAAc,CAAC,UAAU;AAAA,QAC1B;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS,CAAC;AAAA,MACV,SAAS,CAAC,qBAAqB,uBAAuB;AAAA,MACtD,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,cAAc,CAAC,UAAU;AAAA,QAC1B;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS,CAAC;AAAA,MACV,SAAS;AAAA,QACR;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACA,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,cAAc,CAAC,YAAY,YAAY;AAAA,QACxC;AAAA,MACD;AAAA,IACD;AAAA,IACA,GAAG;AAAA,EACJ;AAEA,aAAO;AAAA,IACN;AAAA,MACC,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,2CAAuB;AAAA,MACpC;AAAA,IACD;AAAA,IACA;AAAA,EACD;AACD;AAGA,MAAM,gCAAgC,CAAC,YAA+B;AACrE,QAAM,UAAU,QAAQ,iBAAiB,WAAW,EAAE;AACtD,QAAM,UAAU,QAAQ,iBAAiB,WAAW,CAAC,CAAC;AAEtD,MAAI,YAAY;AAChB,MAAI,QAAQ,mBAAmB;AAC9B,gBAAY,QAAQ;AAAA,EACrB;AAEA,MAAI,kBAAkB;AACtB,MAAI,QAAQ,yBAAyB;AACpC,sBAAkB,QAAQ;AAAA,EAC3B;AACA,oBAAkB,gBAAgB,QAAQ,QAAQ,IAAI,EAAE,QAAQ,SAAS,IAAI;AAE7E,MAAI,cAAc;AAClB,MAAI,QAAQ,yBAAyB;AACpC,kBAAc,QAAQ;AAAA,EACvB;AAEA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AAEA,eAAsB,qBAA4C;AACjE,QAAM,SAAS,KAAK,iBAAiB,EAAE;AACvC,QAAM,MAAM,KAAK,kBAAkB;AACnC,QAAM,MAAM,KAAK,iBAAiB;AAElC,QAAM,eAAe,KAAK,iBAAiB,gBAAgB,UAAU;AAKrE,MAAI,iBAAiB,kBAAc,aAAAA,SAAM,IAAI,QAAQ,YAAY,CAAC,GAAG;AACpE,QAAI,KAAK,EAAE;AACX,WAAO,EAAE,mBAAmB,KAAK;AAAA,EAClC;AAEA,MAAI,iBAAiB,YAAY;AAChC,QAAI,WAAW,OAAO;AACrB,YAAM,EAAE,WAAW,iBAAiB,YAAY,IAAI,8BAA8B,IAAI;AAEtF,YAAM,WAAO,8BAAgB;AAAA,QAC5B;AAAA,QACA;AAAA,QACA,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB;AAAA,QACA,aAAa;AAAA,QACb,YAAY;AAAA,UACX;AAAA,YACC,YAAY;AAAA,YACZ,WAAW;AAAA,YACX,eAAe;AAAA,UAChB;AAAA,QACD;AAAA,QACA,SAAS;AAAA,QACT,OAAO,CAAC;AAAA,MACT,CAAC;AAED,UAAI,OAAO,gBAAgB,IAAI;AAE/B,aAAO;AAAA,QACN,mBAAmB;AAAA,MACpB;AAAA,IACD;AACA,QAAI,WAAW,QAAQ;AACtB,YAAM,OAAO,KAAK,YAAY,EAAE;AAEhC,aAAO;AAAA,QACN,iBAAiB;AAAA,QACjB,cAAc,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,KAAK,sBAAsB,EAAE,EAAE,EAAE,CAAC,CAAC;AAAA,MAC5E;AAAA,IACD;AAAA,EACD;AAEA,MAAI,iBAAiB,cAAc;AAClC,UAAM,aAAa,KAAK,iBAAiB,cAAc,QAAQ;AAC/D,QAAI,SAA8B,CAAC;AAEnC,QAAI,eAAe,QAAQ;AAC1B,UAAI;AACH,cAAM,aAAa,KAAK,iBAAiB,cAAc,IAAI;AAAA,UAC1D,gBAAgB;AAAA,QACjB,CAAC;AAED,qBAAS,oDAA2B,6BAAe,MAAM,UAAU,CAAC;AAAA,MACrE,SAAS,OAAO;AACf,cAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,MAAM,SAAS;AAAA,UAC3D,aAAa,MAAM;AAAA,QACpB,CAAC;AAAA,MACF;AAAA,IACD,OAAO;AACN,eAAS,KAAK,iBAAiB,qBAAqB,CAAC,CAAC;AAAA,IACvD;AAEA,QAAI,WAAW,OAAO;AACrB,YAAM,EAAE,WAAW,iBAAiB,YAAY,IAAI,8BAA8B,IAAI;AAEtF,YAAM,WAAO,8BAAgB;AAAA,QAC5B;AAAA,QACA;AAAA,QACA,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB;AAAA,QACA,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,OAAO,CAAC;AAAA,MACT,CAAC;AAED,UAAI,OAAO,gBAAgB,IAAI;AAE/B,aAAO;AAAA,QACN,mBAAmB;AAAA,MACpB;AAAA,IACD;AACA,QAAI,WAAW,QAAQ;AACtB,YAAM,aAAa,UAAM,oCAAsB,MAAM,QAAQ,cAAc,IAAI;AAC/E,YAAM,OAAO,WAAW;AAExB,aAAO,KAAK;AACZ,aAAO,KAAK;AAEZ,iBAAW,OAAO,EAAE,MAAM,KAAK;AAE/B,aAAO;AAAA,QACN,iBAAiB;AAAA,QACjB,cAAc,CAAC,CAAC,UAAU,CAAC;AAAA,MAC5B;AAAA,IACD;AAAA,EACD;AAEA,QAAM,QAAQ,IAAI;AAClB,QAAM,WAAW,MAAM,aAAa;AACpC,SAAO;AAAA,IACN,iBAAiB;AAAA,IACjB,cAAc,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;AAAA,EAClD;AACD;AAGO,SAAS,qBAAqB,SAA+C;AACnF,QAAM,cAAU,6BAAY,QAAQ,iBAAiB,WAAW,GAAG,EAAE,EAAa,KAAK,CAAC,EACtF,QAAQ,QAAQ,IAAI,EACpB,QAAQ,SAAS,IAAI;AACvB,QAAM,cAAU,6BAAW,QAAQ,iBAAiB,WAAW,GAAG,EAAE,CAAW;AAC/E,QAAM,YAAY,QAAQ,mBAAmB,+BAA+B,CAAC;AAC7E,QAAM,SAAS,QAAQ,mBAAmB,iBAAiB,CAAC;AAC5D,QAAM,kBAAkB,QAAQ,iBAAiB,0BAA0B,GAAG,CAAC,CAAC;AAQhF,QAAM,UAAU,QAAQ,iBAAiB,WAAW,GAAG,CAAC,CAAC;AAEzD,QAAM,SAA4B;AAAA,IACjC,OAAO;AAAA,IACP;AAAA,IACA,KAAK,GAAG,SAAS,IAAI,MAAM;AAAA,IAC3B,SAAS,CAAC;AAAA,IACV,mBAAmB,SAAS;AAAA,EAC7B;AAEA,QAAM,eAAe,QAAQ,iBAAiB,gBAAgB,GAAG,UAAU;AAE3E,MAAI,iBAAiB,cAAc,iBAAiB,cAAc;AACjE,UAAM,QAAQ,QAAQ,iBAAiB,8BAA8B,GAAG,SAAS;AACjF,WAAO,QAAQ,KAAK;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,MACP,OAAO;AAAA,IACR,CAAC;AAAA,EACF,WAAW,gBAAgB,iBAAiB,UAAU;AACrD,UAAM,mBAAe,6BAAW,gBAAgB,gBAAgB,SAAS;AACzE,UAAM,sBAAsB,gBAAgB,uBAAuB;AACnE,UAAM,sBAAkB,6BAAW,gBAAgB,mBAAmB,YAAY;AAClF,UAAM,yBAAyB,gBAAgB,0BAA0B;AAEzE,WAAO,QAAQ,KAAK;AAAA,MACnB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,IACR,CAAC;AACD,WAAO,QAAQ,KAAK;AAAA,MACnB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,IACR,CAAC;AAAA,EACF,OAAO;AACN,UAAM,YAAQ,6BAAW,gBAAgB,gBAAgB,SAAS;AAClE,UAAM,QAAQ,gBAAgB,uBAAuB;AACrD,WAAO,QAAQ,KAAK;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,MACP;AAAA,IACD,CAAC;AAAA,EACF;AAEA,SAAO;AACR;AAEO,SAAS,aAAa,KAAa,OAAe,UAAkB,OAAe;AACzF,MAAI,cAAc;AAClB,MAAI,UAAU,aAAa;AAC1B,kBAAc;AAAA,EACf;AACA,SAAO,YAAY,GAAG,aAAa,QAAQ,4BAA4B,WAAW,KAAK,KAAK;AAC7F;AAEO,SAAS,YAAY,SAA4B;AACvD,QAAM,KAAM,QAAQ,iBAAiB,UAAU,GAAG,EAAE,EAAa,KAAK;AACtE,QAAM,SAAS,qBAAqB,OAAO;AAE3C,MAAI,GAAG,QAAQ,GAAG,MAAM,OAAO,GAAG,MAAM,IAAI,KAAK,CAAC,GAAG,SAAS,GAAG;AAChE,UAAM,cAAc,sBAAsB,EAAE;AAC5C,UAAM,IAAI,uCAAmB,QAAQ,QAAQ,GAAG,yBAAyB;AAAA,MACxE;AAAA,MACA,WAAW;AAAA,IACZ,CAAC;AAAA,EACF;AAEA,QAAM,UAAoB,CAAC;AAC3B,aAAW,UAAU,OAAO,SAAS;AACpC,YAAQ,KAAK,aAAa,OAAO,KAAK,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,CAAC;AAAA,EAChF;AACA,MAAI;AACJ,MAAI,OAAO,sBAAsB,OAAO;AACvC,UAAM,aAAa,QAAQ,cAAc;AACzC,oBAAY,0DAAkC,OAAO,SAAS,QAAQ,KAAK,IAAI,GAAG,UAAU;AAAA,EAC7F,OAAO;AACN,oBAAY,6DAAqC,OAAO,SAAS,QAAQ,KAAK,IAAI,CAAC;AAAA,EACpF;AAEA,QAAM,QAAgB;AAAA,IACrB;AAAA,IACA,SAAS,OAAO;AAAA,IAChB,MAAM;AAAA,IACN,UAAU;AAAA,EACX;AAEA,SAAO;AACR;", "names": ["isbot"]}