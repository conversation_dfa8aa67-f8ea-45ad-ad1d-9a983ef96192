{"version": 3, "sources": ["../../../nodes/Webhook/Webhook.node.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-execute-block-wrong-error-thrown */\nimport { createWriteStream } from 'fs';\nimport { stat } from 'fs/promises';\nimport isbot from 'isbot';\nimport type {\n\tIWebhookFunctions,\n\tIDataObject,\n\tINodeExecutionData,\n\tINodeTypeDescription,\n\tIWebhookResponseData,\n\tMultiPartFormData,\n\tINodeProperties,\n} from 'n8n-workflow';\nimport { BINARY_ENCODING, NodeOperationError, Node } from 'n8n-workflow';\nimport { pipeline } from 'stream/promises';\nimport { file as tmpFile } from 'tmp-promise';\nimport { v4 as uuid } from 'uuid';\n\nimport {\n\tauthenticationProperty,\n\tcredentialsProperty,\n\tdefaultWebhookDescription,\n\thttpMethodsProperty,\n\toptionsProperty,\n\tresponseBinaryPropertyNameProperty,\n\tresponseCodeOption,\n\tresponseCodeProperty,\n\tresponseDataProperty,\n\tresponseModeProperty,\n} from './description';\nimport { WebhookAuthorizationError } from './error';\nimport {\n\tcheckResponseModeConfiguration,\n\tconfiguredOutputs,\n\tisIpWhitelisted,\n\tsetupOutputConnection,\n\tvalidateWebhookAuthentication,\n} from './utils';\n\nexport class Webhook extends Node {\n\tauthPropertyName = 'authentication';\n\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Webhook',\n\t\ticon: { light: 'file:webhook.svg', dark: 'file:webhook.dark.svg' },\n\t\tname: 'webhook',\n\t\tgroup: ['trigger'],\n\t\tversion: [1, 1.1, 2],\n\t\tdescription: 'Starts the workflow when a webhook is called',\n\t\teventTriggerDescription: 'Waiting for you to call the Test URL',\n\t\tactivationMessage: 'You can now make calls to your production webhook URL.',\n\t\tdefaults: {\n\t\t\tname: 'Webhook',\n\t\t},\n\t\tsupportsCORS: true,\n\t\ttriggerPanel: {\n\t\t\theader: '',\n\t\t\texecutionsHelp: {\n\t\t\t\tinactive:\n\t\t\t\t\t'Webhooks have two modes: test and production. <br /> <br /> <b>Use test mode while you build your workflow</b>. Click the \\'listen\\' button, then make a request to the test URL. The executions will show up in the editor.<br /> <br /> <b>Use production mode to run your workflow automatically</b>. <a data-key=\"activate\">Activate</a> the workflow, then make requests to the production URL. These executions will show up in the executions list, but not in the editor.',\n\t\t\t\tactive:\n\t\t\t\t\t'Webhooks have two modes: test and production. <br /> <br /> <b>Use test mode while you build your workflow</b>. Click the \\'listen\\' button, then make a request to the test URL. The executions will show up in the editor.<br /> <br /> <b>Use production mode to run your workflow automatically</b>. Since the workflow is activated, you can make requests to the production URL. These executions will show up in the <a data-key=\"executions\">executions list</a>, but not in the editor.',\n\t\t\t},\n\t\t\tactivationHint:\n\t\t\t\t\"Once you've finished building your workflow, run it without having to click this button by using the production webhook URL.\",\n\t\t},\n\n\t\tinputs: [],\n\t\toutputs: `={{(${configuredOutputs})($parameter)}}`,\n\t\tcredentials: credentialsProperty(this.authPropertyName),\n\t\twebhooks: [defaultWebhookDescription],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Allow Multiple HTTP Methods',\n\t\t\t\tname: 'multipleMethods',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tisNodeSetting: true,\n\t\t\t\tdescription: 'Whether to allow the webhook to listen for multiple HTTP methods',\n\t\t\t},\n\t\t\t{\n\t\t\t\t...httpMethodsProperty,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tmultipleMethods: [false],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'HTTP Methods',\n\t\t\t\tname: 'httpMethod',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'DELETE',\n\t\t\t\t\t\tvalue: 'DELETE',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'GET',\n\t\t\t\t\t\tvalue: 'GET',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'HEAD',\n\t\t\t\t\t\tvalue: 'HEAD',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'PATCH',\n\t\t\t\t\t\tvalue: 'PATCH',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'POST',\n\t\t\t\t\t\tvalue: 'POST',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'PUT',\n\t\t\t\t\t\tvalue: 'PUT',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: ['GET', 'POST'],\n\t\t\t\tdescription: 'The HTTP methods to listen to',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tmultipleMethods: [true],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Path',\n\t\t\t\tname: 'path',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: 'webhook',\n\t\t\t\trequired: true,\n\t\t\t\tdescription:\n\t\t\t\t\t\"The path to listen to, dynamic values could be specified by using ':', e.g. 'your-path/:dynamic-value'. If dynamic values are set 'webhookId' would be prepended to path.\",\n\t\t\t},\n\t\t\tauthenticationProperty(this.authPropertyName),\n\t\t\tresponseModeProperty,\n\t\t\t{\n\t\t\t\tdisplayName:\n\t\t\t\t\t'Insert a \\'Respond to Webhook\\' node to control when and how you respond. <a href=\"https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.respondtowebhook/\" target=\"_blank\">More details</a>',\n\t\t\t\tname: 'webhookNotice',\n\t\t\t\ttype: 'notice',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresponseMode: ['responseNode'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\t...responseCodeProperty,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\t'@version': [1, 1.1],\n\t\t\t\t\t},\n\t\t\t\t\thide: {\n\t\t\t\t\t\tresponseMode: ['responseNode'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\tresponseDataProperty,\n\t\t\tresponseBinaryPropertyNameProperty,\n\n\t\t\t{\n\t\t\t\t...optionsProperty,\n\t\t\t\toptions: [...(optionsProperty.options as INodeProperties[]), responseCodeOption].sort(\n\t\t\t\t\t(a, b) => {\n\t\t\t\t\t\tconst nameA = a.displayName.toUpperCase();\n\t\t\t\t\t\tconst nameB = b.displayName.toUpperCase();\n\t\t\t\t\t\tif (nameA < nameB) return -1;\n\t\t\t\t\t\tif (nameA > nameB) return 1;\n\t\t\t\t\t\treturn 0;\n\t\t\t\t\t},\n\t\t\t\t),\n\t\t\t},\n\t\t],\n\t};\n\n\tasync webhook(context: IWebhookFunctions): Promise<IWebhookResponseData> {\n\t\tconst { typeVersion: nodeVersion, type: nodeType } = context.getNode();\n\n\t\tif (nodeVersion >= 2 && nodeType === 'n8n-nodes-base.webhook') {\n\t\t\tcheckResponseModeConfiguration(context);\n\t\t}\n\n\t\tconst options = context.getNodeParameter('options', {}) as {\n\t\t\tbinaryData: boolean;\n\t\t\tignoreBots: boolean;\n\t\t\trawBody: boolean;\n\t\t\tresponseData?: string;\n\t\t\tipWhitelist?: string;\n\t\t};\n\t\tconst req = context.getRequestObject();\n\t\tconst resp = context.getResponseObject();\n\t\tconst requestMethod = context.getRequestObject().method;\n\n\t\tif (!isIpWhitelisted(options.ipWhitelist, req.ips, req.ip)) {\n\t\t\tresp.writeHead(403);\n\t\t\tresp.end('IP is not whitelisted to access the webhook!');\n\t\t\treturn { noWebhookResponse: true };\n\t\t}\n\n\t\tlet validationData: IDataObject | undefined;\n\t\ttry {\n\t\t\tif (options.ignoreBots && isbot(req.headers['user-agent']))\n\t\t\t\tthrow new WebhookAuthorizationError(403);\n\t\t\tvalidationData = await this.validateAuth(context);\n\t\t} catch (error) {\n\t\t\tif (error instanceof WebhookAuthorizationError) {\n\t\t\t\tresp.writeHead(error.responseCode, { 'WWW-Authenticate': 'Basic realm=\"Webhook\"' });\n\t\t\t\tresp.end(error.message);\n\t\t\t\treturn { noWebhookResponse: true };\n\t\t\t}\n\t\t\tthrow error;\n\t\t}\n\n\t\tconst prepareOutput = setupOutputConnection(context, requestMethod, {\n\t\t\tjwtPayload: validationData,\n\t\t});\n\n\t\tif (options.binaryData) {\n\t\t\treturn await this.handleBinaryData(context, prepareOutput);\n\t\t}\n\n\t\tif (req.contentType === 'multipart/form-data') {\n\t\t\treturn await this.handleFormData(context, prepareOutput);\n\t\t}\n\n\t\tif (nodeVersion > 1 && !req.body && !options.rawBody) {\n\t\t\ttry {\n\t\t\t\treturn await this.handleBinaryData(context, prepareOutput);\n\t\t\t} catch (error) {}\n\t\t}\n\n\t\tif (options.rawBody && !req.rawBody) {\n\t\t\tawait req.readRawBody();\n\t\t}\n\n\t\tconst response: INodeExecutionData = {\n\t\t\tjson: {\n\t\t\t\theaders: req.headers,\n\t\t\t\tparams: req.params,\n\t\t\t\tquery: req.query,\n\t\t\t\tbody: req.body,\n\t\t\t},\n\t\t\tbinary: options.rawBody\n\t\t\t\t? {\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tdata: (req.rawBody ?? '').toString(BINARY_ENCODING),\n\t\t\t\t\t\t\tmimeType: req.contentType ?? 'application/json',\n\t\t\t\t\t\t},\n\t\t\t\t\t}\n\t\t\t\t: undefined,\n\t\t};\n\n\t\treturn {\n\t\t\twebhookResponse: options.responseData,\n\t\t\tworkflowData: prepareOutput(response),\n\t\t};\n\t}\n\n\tprivate async validateAuth(context: IWebhookFunctions) {\n\t\treturn await validateWebhookAuthentication(context, this.authPropertyName);\n\t}\n\n\tprivate async handleFormData(\n\t\tcontext: IWebhookFunctions,\n\t\tprepareOutput: (data: INodeExecutionData) => INodeExecutionData[][],\n\t) {\n\t\tconst req = context.getRequestObject() as MultiPartFormData.Request;\n\t\tconst options = context.getNodeParameter('options', {}) as IDataObject;\n\t\tconst { data, files } = req.body;\n\n\t\tconst returnItem: INodeExecutionData = {\n\t\t\tjson: {\n\t\t\t\theaders: req.headers,\n\t\t\t\tparams: req.params,\n\t\t\t\tquery: req.query,\n\t\t\t\tbody: data,\n\t\t\t},\n\t\t};\n\n\t\tif (files && Object.keys(files).length) {\n\t\t\treturnItem.binary = {};\n\t\t}\n\n\t\tlet count = 0;\n\n\t\tfor (const key of Object.keys(files)) {\n\t\t\tconst processFiles: MultiPartFormData.File[] = [];\n\t\t\tlet multiFile = false;\n\t\t\tif (Array.isArray(files[key])) {\n\t\t\t\tprocessFiles.push(...files[key]);\n\t\t\t\tmultiFile = true;\n\t\t\t} else {\n\t\t\t\tprocessFiles.push(files[key]);\n\t\t\t}\n\n\t\t\tlet fileCount = 0;\n\t\t\tfor (const file of processFiles) {\n\t\t\t\tlet binaryPropertyName = key;\n\t\t\t\tif (binaryPropertyName.endsWith('[]')) {\n\t\t\t\t\tbinaryPropertyName = binaryPropertyName.slice(0, -2);\n\t\t\t\t}\n\t\t\t\tif (multiFile) {\n\t\t\t\t\tbinaryPropertyName += fileCount++;\n\t\t\t\t}\n\t\t\t\tif (options.binaryPropertyName) {\n\t\t\t\t\tbinaryPropertyName = `${options.binaryPropertyName}${count}`;\n\t\t\t\t}\n\n\t\t\t\treturnItem.binary![binaryPropertyName] = await context.nodeHelpers.copyBinaryFile(\n\t\t\t\t\tfile.filepath,\n\t\t\t\t\tfile.originalFilename ?? file.newFilename,\n\t\t\t\t\tfile.mimetype,\n\t\t\t\t);\n\n\t\t\t\tcount += 1;\n\t\t\t}\n\t\t}\n\n\t\treturn { workflowData: prepareOutput(returnItem) };\n\t}\n\n\tprivate async handleBinaryData(\n\t\tcontext: IWebhookFunctions,\n\t\tprepareOutput: (data: INodeExecutionData) => INodeExecutionData[][],\n\t): Promise<IWebhookResponseData> {\n\t\tconst req = context.getRequestObject();\n\t\tconst options = context.getNodeParameter('options', {}) as IDataObject;\n\n\t\t// TODO: create empty binaryData placeholder, stream into that path, and then finalize the binaryData\n\t\tconst binaryFile = await tmpFile({ prefix: 'n8n-webhook-' });\n\n\t\ttry {\n\t\t\tawait pipeline(req, createWriteStream(binaryFile.path));\n\n\t\t\tconst returnItem: INodeExecutionData = {\n\t\t\t\tjson: {\n\t\t\t\t\theaders: req.headers,\n\t\t\t\t\tparams: req.params,\n\t\t\t\t\tquery: req.query,\n\t\t\t\t\tbody: {},\n\t\t\t\t},\n\t\t\t};\n\n\t\t\tconst stats = await stat(binaryFile.path);\n\t\t\tif (stats.size) {\n\t\t\t\tconst binaryPropertyName = (options.binaryPropertyName ?? 'data') as string;\n\t\t\t\tconst fileName = req.contentDisposition?.filename ?? uuid();\n\t\t\t\tconst binaryData = await context.nodeHelpers.copyBinaryFile(\n\t\t\t\t\tbinaryFile.path,\n\t\t\t\t\tfileName,\n\t\t\t\t\treq.contentType ?? 'application/octet-stream',\n\t\t\t\t);\n\t\t\t\treturnItem.binary = { [binaryPropertyName]: binaryData };\n\t\t\t}\n\n\t\t\treturn { workflowData: prepareOutput(returnItem) };\n\t\t} catch (error) {\n\t\t\tthrow new NodeOperationError(context.getNode(), error as Error);\n\t\t} finally {\n\t\t\tawait binaryFile.cleanup();\n\t\t}\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAkC;AAClC,sBAAqB;AACrB,mBAAkB;AAUlB,0BAA0D;AAC1D,IAAAA,mBAAyB;AACzB,yBAAgC;AAChC,kBAA2B;AAE3B,yBAWO;AACP,mBAA0C;AAC1C,mBAMO;AAEA,MAAM,gBAAgB,yBAAK;AAAA,EAA3B;AAAA;AACN,4BAAmB;AAEnB,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM,EAAE,OAAO,oBAAoB,MAAM,wBAAwB;AAAA,MACjE,MAAM;AAAA,MACN,OAAO,CAAC,SAAS;AAAA,MACjB,SAAS,CAAC,GAAG,KAAK,CAAC;AAAA,MACnB,aAAa;AAAA,MACb,yBAAyB;AAAA,MACzB,mBAAmB;AAAA,MACnB,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,cAAc;AAAA,QACb,QAAQ;AAAA,QACR,gBAAgB;AAAA,UACf,UACC;AAAA,UACD,QACC;AAAA,QACF;AAAA,QACA,gBACC;AAAA,MACF;AAAA,MAEA,QAAQ,CAAC;AAAA,MACT,SAAS,OAAO,8BAAiB;AAAA,MACjC,iBAAa,wCAAoB,KAAK,gBAAgB;AAAA,MACtD,UAAU,CAAC,4CAAyB;AAAA,MACpC,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,eAAe;AAAA,UACf,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,GAAG;AAAA,UACH,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,iBAAiB,CAAC,KAAK;AAAA,YACxB;AAAA,UACD;AAAA,QACD;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS,CAAC,OAAO,MAAM;AAAA,UACvB,aAAa;AAAA,UACb,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,iBAAiB,CAAC,IAAI;AAAA,YACvB;AAAA,UACD;AAAA,QACD;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,UACb,UAAU;AAAA,UACV,aACC;AAAA,QACF;AAAA,YACA,2CAAuB,KAAK,gBAAgB;AAAA,QAC5C;AAAA,QACA;AAAA,UACC,aACC;AAAA,UACD,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,cAAc,CAAC,cAAc;AAAA,YAC9B;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,GAAG;AAAA,UACH,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,YAAY,CAAC,GAAG,GAAG;AAAA,YACpB;AAAA,YACA,MAAM;AAAA,cACL,cAAc,CAAC,cAAc;AAAA,YAC9B;AAAA,UACD;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,QAEA;AAAA,UACC,GAAG;AAAA,UACH,SAAS,CAAC,GAAI,mCAAgB,SAA+B,qCAAkB,EAAE;AAAA,YAChF,CAAC,GAAG,MAAM;AACT,oBAAM,QAAQ,EAAE,YAAY,YAAY;AACxC,oBAAM,QAAQ,EAAE,YAAY,YAAY;AACxC,kBAAI,QAAQ,MAAO,QAAO;AAC1B,kBAAI,QAAQ,MAAO,QAAO;AAC1B,qBAAO;AAAA,YACR;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,QAAQ,SAA2D;AACxE,UAAM,EAAE,aAAa,aAAa,MAAM,SAAS,IAAI,QAAQ,QAAQ;AAErE,QAAI,eAAe,KAAK,aAAa,0BAA0B;AAC9D,uDAA+B,OAAO;AAAA,IACvC;AAEA,UAAM,UAAU,QAAQ,iBAAiB,WAAW,CAAC,CAAC;AAOtD,UAAM,MAAM,QAAQ,iBAAiB;AACrC,UAAM,OAAO,QAAQ,kBAAkB;AACvC,UAAM,gBAAgB,QAAQ,iBAAiB,EAAE;AAEjD,QAAI,KAAC,8BAAgB,QAAQ,aAAa,IAAI,KAAK,IAAI,EAAE,GAAG;AAC3D,WAAK,UAAU,GAAG;AAClB,WAAK,IAAI,8CAA8C;AACvD,aAAO,EAAE,mBAAmB,KAAK;AAAA,IAClC;AAEA,QAAI;AACJ,QAAI;AACH,UAAI,QAAQ,kBAAc,aAAAC,SAAM,IAAI,QAAQ,YAAY,CAAC;AACxD,cAAM,IAAI,uCAA0B,GAAG;AACxC,uBAAiB,MAAM,KAAK,aAAa,OAAO;AAAA,IACjD,SAAS,OAAO;AACf,UAAI,iBAAiB,wCAA2B;AAC/C,aAAK,UAAU,MAAM,cAAc,EAAE,oBAAoB,wBAAwB,CAAC;AAClF,aAAK,IAAI,MAAM,OAAO;AACtB,eAAO,EAAE,mBAAmB,KAAK;AAAA,MAClC;AACA,YAAM;AAAA,IACP;AAEA,UAAM,oBAAgB,oCAAsB,SAAS,eAAe;AAAA,MACnE,YAAY;AAAA,IACb,CAAC;AAED,QAAI,QAAQ,YAAY;AACvB,aAAO,MAAM,KAAK,iBAAiB,SAAS,aAAa;AAAA,IAC1D;AAEA,QAAI,IAAI,gBAAgB,uBAAuB;AAC9C,aAAO,MAAM,KAAK,eAAe,SAAS,aAAa;AAAA,IACxD;AAEA,QAAI,cAAc,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,SAAS;AACrD,UAAI;AACH,eAAO,MAAM,KAAK,iBAAiB,SAAS,aAAa;AAAA,MAC1D,SAAS,OAAO;AAAA,MAAC;AAAA,IAClB;AAEA,QAAI,QAAQ,WAAW,CAAC,IAAI,SAAS;AACpC,YAAM,IAAI,YAAY;AAAA,IACvB;AAEA,UAAM,WAA+B;AAAA,MACpC,MAAM;AAAA,QACL,SAAS,IAAI;AAAA,QACb,QAAQ,IAAI;AAAA,QACZ,OAAO,IAAI;AAAA,QACX,MAAM,IAAI;AAAA,MACX;AAAA,MACA,QAAQ,QAAQ,UACb;AAAA,QACA,MAAM;AAAA,UACL,OAAO,IAAI,WAAW,IAAI,SAAS,mCAAe;AAAA,UAClD,UAAU,IAAI,eAAe;AAAA,QAC9B;AAAA,MACD,IACC;AAAA,IACJ;AAEA,WAAO;AAAA,MACN,iBAAiB,QAAQ;AAAA,MACzB,cAAc,cAAc,QAAQ;AAAA,IACrC;AAAA,EACD;AAAA,EAEA,MAAc,aAAa,SAA4B;AACtD,WAAO,UAAM,4CAA8B,SAAS,KAAK,gBAAgB;AAAA,EAC1E;AAAA,EAEA,MAAc,eACb,SACA,eACC;AACD,UAAM,MAAM,QAAQ,iBAAiB;AACrC,UAAM,UAAU,QAAQ,iBAAiB,WAAW,CAAC,CAAC;AACtD,UAAM,EAAE,MAAM,MAAM,IAAI,IAAI;AAE5B,UAAM,aAAiC;AAAA,MACtC,MAAM;AAAA,QACL,SAAS,IAAI;AAAA,QACb,QAAQ,IAAI;AAAA,QACZ,OAAO,IAAI;AAAA,QACX,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,SAAS,OAAO,KAAK,KAAK,EAAE,QAAQ;AACvC,iBAAW,SAAS,CAAC;AAAA,IACtB;AAEA,QAAI,QAAQ;AAEZ,eAAW,OAAO,OAAO,KAAK,KAAK,GAAG;AACrC,YAAM,eAAyC,CAAC;AAChD,UAAI,YAAY;AAChB,UAAI,MAAM,QAAQ,MAAM,GAAG,CAAC,GAAG;AAC9B,qBAAa,KAAK,GAAG,MAAM,GAAG,CAAC;AAC/B,oBAAY;AAAA,MACb,OAAO;AACN,qBAAa,KAAK,MAAM,GAAG,CAAC;AAAA,MAC7B;AAEA,UAAI,YAAY;AAChB,iBAAW,QAAQ,cAAc;AAChC,YAAI,qBAAqB;AACzB,YAAI,mBAAmB,SAAS,IAAI,GAAG;AACtC,+BAAqB,mBAAmB,MAAM,GAAG,EAAE;AAAA,QACpD;AACA,YAAI,WAAW;AACd,gCAAsB;AAAA,QACvB;AACA,YAAI,QAAQ,oBAAoB;AAC/B,+BAAqB,GAAG,QAAQ,kBAAkB,GAAG,KAAK;AAAA,QAC3D;AAEA,mBAAW,OAAQ,kBAAkB,IAAI,MAAM,QAAQ,YAAY;AAAA,UAClE,KAAK;AAAA,UACL,KAAK,oBAAoB,KAAK;AAAA,UAC9B,KAAK;AAAA,QACN;AAEA,iBAAS;AAAA,MACV;AAAA,IACD;AAEA,WAAO,EAAE,cAAc,cAAc,UAAU,EAAE;AAAA,EAClD;AAAA,EAEA,MAAc,iBACb,SACA,eACgC;AAChC,UAAM,MAAM,QAAQ,iBAAiB;AACrC,UAAM,UAAU,QAAQ,iBAAiB,WAAW,CAAC,CAAC;AAGtD,UAAM,aAAa,UAAM,mBAAAC,MAAQ,EAAE,QAAQ,eAAe,CAAC;AAE3D,QAAI;AACH,gBAAM,2BAAS,SAAK,6BAAkB,WAAW,IAAI,CAAC;AAEtD,YAAM,aAAiC;AAAA,QACtC,MAAM;AAAA,UACL,SAAS,IAAI;AAAA,UACb,QAAQ,IAAI;AAAA,UACZ,OAAO,IAAI;AAAA,UACX,MAAM,CAAC;AAAA,QACR;AAAA,MACD;AAEA,YAAM,QAAQ,UAAM,sBAAK,WAAW,IAAI;AACxC,UAAI,MAAM,MAAM;AACf,cAAM,qBAAsB,QAAQ,sBAAsB;AAC1D,cAAM,WAAW,IAAI,oBAAoB,gBAAY,YAAAC,IAAK;AAC1D,cAAM,aAAa,MAAM,QAAQ,YAAY;AAAA,UAC5C,WAAW;AAAA,UACX;AAAA,UACA,IAAI,eAAe;AAAA,QACpB;AACA,mBAAW,SAAS,EAAE,CAAC,kBAAkB,GAAG,WAAW;AAAA,MACxD;AAEA,aAAO,EAAE,cAAc,cAAc,UAAU,EAAE;AAAA,IAClD,SAAS,OAAO;AACf,YAAM,IAAI,uCAAmB,QAAQ,QAAQ,GAAG,KAAc;AAAA,IAC/D,UAAE;AACD,YAAM,WAAW,QAAQ;AAAA,IAC1B;AAAA,EACD;AACD;", "names": ["import_promises", "isbot", "tmpFile", "uuid"]}