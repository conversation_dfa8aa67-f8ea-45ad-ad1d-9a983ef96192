{"version": 3, "sources": ["../../../../../nodes/Microsoft/Entra/descriptions/UserDescription.ts"], "sourcesContent": ["import { merge } from 'lodash';\nimport type { DateTime } from 'luxon';\nimport {\n\tNodeOperationError,\n\ttype IDataObject,\n\ttype IExecuteSingleFunctions,\n\ttype IHttpRequestOptions,\n\ttype IN8nHttpFullResponse,\n\ttype INodeExecutionData,\n\ttype INodeProperties,\n} from 'n8n-workflow';\n\nimport { handleErrorPostReceive, microsoftApiRequest } from '../GenericFunctions';\n\nexport const userOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Add to Group',\n\t\t\t\tvalue: 'addGroup',\n\t\t\t\tdescription: 'Add user to group',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\turl: '=/groups/{{ $parameter[\"group\"] }}/members/$ref',\n\t\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [\n\t\t\t\t\t\t\thandleErrorPostReceive,\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\ttype: 'set',\n\t\t\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\t\t\tvalue: '={{ { \"added\": true } }}',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\taction: 'Add user to group',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Create',\n\t\t\t\tvalue: 'create',\n\t\t\t\tdescription: 'Create a user',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\turl: '/users',\n\t\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [handleErrorPostReceive],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\taction: 'Create user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'delete',\n\t\t\t\tdescription: 'Delete a user',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'DELETE',\n\t\t\t\t\t\turl: '=/users/{{ $parameter[\"user\"] }}',\n\t\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [\n\t\t\t\t\t\t\thandleErrorPostReceive,\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\ttype: 'set',\n\t\t\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\t\t\tvalue: '={{ { \"deleted\": true } }}',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\taction: 'Delete user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\tdescription: 'Retrieve data for a specific user',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\t\turl: '=/users/{{ $parameter[\"user\"] }}',\n\t\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [handleErrorPostReceive],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\taction: 'Get user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Retrieve a list of users',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\t\turl: '/users',\n\t\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [\n\t\t\t\t\t\t\thandleErrorPostReceive,\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\ttype: 'rootProperty',\n\t\t\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\t\t\tproperty: 'value',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\taction: 'Get many users',\n\t\t\t},\n\t\t\t{\n\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-miscased\n\t\t\t\tname: 'Remove from Group',\n\t\t\t\tvalue: 'removeGroup',\n\t\t\t\tdescription: 'Remove user from group',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'DELETE',\n\t\t\t\t\t\turl: '=/groups/{{ $parameter[\"group\"] }}/members/{{ $parameter[\"user\"] }}/$ref',\n\t\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [\n\t\t\t\t\t\t\thandleErrorPostReceive,\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\ttype: 'set',\n\t\t\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\t\t\tvalue: '={{ { \"removed\": true } }}',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\taction: 'Remove user from group',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Update',\n\t\t\t\tvalue: 'update',\n\t\t\t\tdescription: 'Update a user',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'PATCH',\n\t\t\t\t\t\turl: '=/users/{{ $parameter[\"user\"] }}',\n\t\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [\n\t\t\t\t\t\t\thandleErrorPostReceive,\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\ttype: 'set',\n\t\t\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\t\t\tvalue: '={{ { \"updated\": true } }}',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\taction: 'Update user',\n\t\t\t},\n\t\t],\n\t\tdefault: 'getAll',\n\t},\n];\n\nconst addGroupFields: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Group',\n\t\tname: 'group',\n\t\tdefault: {\n\t\t\tmode: 'list',\n\t\t\tvalue: '',\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['addGroup'],\n\t\t\t},\n\t\t},\n\t\tmodes: [\n\t\t\t{\n\t\t\t\tdisplayName: 'From List',\n\t\t\t\tname: 'list',\n\t\t\t\ttype: 'list',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tsearchListMethod: 'getGroups',\n\t\t\t\t\tsearchable: true,\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'By ID',\n\t\t\t\tname: 'id',\n\t\t\t\tplaceholder: 'e.g. 02bd9fd6-8f93-4758-87c3-1fb73740a315',\n\t\t\t\ttype: 'string',\n\t\t\t},\n\t\t],\n\t\trequired: true,\n\t\ttype: 'resourceLocator',\n\t},\n\t{\n\t\tdisplayName: 'User to Add',\n\t\tname: 'user',\n\t\tdefault: {\n\t\t\tmode: 'list',\n\t\t\tvalue: '',\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['addGroup'],\n\t\t\t},\n\t\t},\n\t\tmodes: [\n\t\t\t{\n\t\t\t\tdisplayName: 'From List',\n\t\t\t\tname: 'list',\n\t\t\t\ttype: 'list',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tsearchListMethod: 'getUsers',\n\t\t\t\t\tsearchable: true,\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'By ID',\n\t\t\t\tname: 'id',\n\t\t\t\tplaceholder: 'e.g. 02bd9fd6-8f93-4758-87c3-1fb73740a315',\n\t\t\t\ttype: 'string',\n\t\t\t},\n\t\t],\n\t\trequired: true,\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: '@odata.id',\n\t\t\t\tpropertyInDotNotation: false,\n\t\t\t\ttype: 'body',\n\t\t\t\tvalue: '=https://graph.microsoft.com/v1.0/directoryObjects/{{ $value }}',\n\t\t\t},\n\t\t},\n\t\ttype: 'resourceLocator',\n\t},\n];\n\nconst createFields: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Account Enabled',\n\t\tname: 'accountEnabled',\n\t\tdefault: true,\n\t\tdescription: 'Whether the account is enabled',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\trequired: true,\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'accountEnabled',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t\ttype: 'boolean',\n\t\tvalidateType: 'boolean',\n\t},\n\t{\n\t\tdisplayName: 'Display Name',\n\t\tname: 'displayName',\n\t\tdefault: '',\n\t\tdescription: 'The name to display in the address book for the user',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\tplaceholder: 'e.g. Nathan Smith',\n\t\trequired: true,\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'displayName',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t\ttype: 'string',\n\t\tvalidateType: 'string',\n\t},\n\t{\n\t\tdisplayName: 'User Principal Name',\n\t\tname: 'userPrincipalName',\n\t\tdefault: '',\n\t\tdescription: 'The user principal name (UPN)',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\tplaceholder: 'e.g. <EMAIL>',\n\t\trequired: true,\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'userPrincipalName',\n\t\t\t\ttype: 'body',\n\t\t\t\tpreSend: [\n\t\t\t\t\tasync function (\n\t\t\t\t\t\tthis: IExecuteSingleFunctions,\n\t\t\t\t\t\trequestOptions: IHttpRequestOptions,\n\t\t\t\t\t): Promise<IHttpRequestOptions> {\n\t\t\t\t\t\tconst userPrincipalName = this.getNodeParameter('userPrincipalName') as string;\n\t\t\t\t\t\tif (!/^[A-Za-z0-9'._\\-!#^~@]+$/.test(userPrincipalName)) {\n\t\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t\t\"Only the following characters are allowed for 'User Principal Name': A-Z, a-z, 0-9, ' . - _ ! # ^ ~\",\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn requestOptions;\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t},\n\t\ttype: 'string',\n\t\tvalidateType: 'string',\n\t},\n\t{\n\t\tdisplayName: 'Mail Nickname',\n\t\tname: 'mailNickname',\n\t\tdefault: '',\n\t\tdescription: 'The mail alias for the user',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\tplaceholder: 'e.g. NathanSmith',\n\t\trequired: true,\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'mailNickname',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t\ttype: 'string',\n\t\tvalidateType: 'string',\n\t},\n\t{\n\t\tdisplayName: 'Password',\n\t\tname: 'password',\n\t\tdefault: '',\n\t\tdescription: 'The password for the user',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\trequired: true,\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'passwordProfile.password',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t\ttype: 'string',\n\t\ttypeOptions: {\n\t\t\tpassword: true,\n\t\t},\n\t\tvalidateType: 'string',\n\t},\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'About Me',\n\t\t\t\tname: 'aboutMe',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'A freeform text entry field for the user to describe themselves',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Age Group',\n\t\t\t\tname: 'ageGroup',\n\t\t\t\tdefault: 'Adult',\n\t\t\t\tdescription: 'Sets the age group of the user',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Adult',\n\t\t\t\t\t\tvalue: 'Adult',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Minor',\n\t\t\t\t\t\tvalue: 'Minor',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Not Adult',\n\t\t\t\t\t\tvalue: 'NotAdult',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\ttype: 'options',\n\t\t\t\tvalidateType: 'options',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Birthday',\n\t\t\t\tname: 'birthday',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The birthday of the user',\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tvalidateType: 'dateTime',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Business Phone',\n\t\t\t\tname: 'businessPhones',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The telephone number for the user',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'City',\n\t\t\t\tname: 'city',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The city in which the user is located',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Company Name',\n\t\t\t\tname: 'companyName',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The name of the company associated with the user',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tpreSend: [\n\t\t\t\t\t\t\tasync function (\n\t\t\t\t\t\t\t\tthis: IExecuteSingleFunctions,\n\t\t\t\t\t\t\t\trequestOptions: IHttpRequestOptions,\n\t\t\t\t\t\t\t): Promise<IHttpRequestOptions> {\n\t\t\t\t\t\t\t\tconst companyName = this.getNodeParameter('additionalFields.companyName') as string;\n\t\t\t\t\t\t\t\tif (companyName?.length > 64) {\n\t\t\t\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t\t\t\t\"'Company Name' should have a maximum length of 64\",\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn requestOptions;\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Consent Provided',\n\t\t\t\tname: 'consentProvidedForMinor',\n\t\t\t\tdefault: 'Denied',\n\t\t\t\tdescription: 'Specifies if consent is provided for minors',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Denied',\n\t\t\t\t\t\tvalue: 'Denied',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Granted',\n\t\t\t\t\t\tvalue: 'Granted',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Not Required',\n\t\t\t\t\t\tvalue: 'NotRequired',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\ttype: 'options',\n\t\t\t\tvalidateType: 'options',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Country',\n\t\t\t\tname: 'country',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The country/region of the user',\n\t\t\t\tplaceholder: 'e.g. US',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Department',\n\t\t\t\tname: 'department',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The department name where the user works',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Employee ID',\n\t\t\t\tname: 'employeeId',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Employee identifier assigned by the organization',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tpreSend: [\n\t\t\t\t\t\t\tasync function (\n\t\t\t\t\t\t\t\tthis: IExecuteSingleFunctions,\n\t\t\t\t\t\t\t\trequestOptions: IHttpRequestOptions,\n\t\t\t\t\t\t\t): Promise<IHttpRequestOptions> {\n\t\t\t\t\t\t\t\tconst employeeId = this.getNodeParameter('additionalFields.employeeId') as string;\n\t\t\t\t\t\t\t\tif (employeeId?.length > 16) {\n\t\t\t\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t\t\t\t\"'Employee ID' should have a maximum length of 16\",\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn requestOptions;\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Employee Type',\n\t\t\t\tname: 'employeeType',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Defines enterprise worker type',\n\t\t\t\tplaceholder: 'e.g. Contractor',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Employee Hire Date',\n\t\t\t\tname: 'employeeHireDate',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The hire date of the user',\n\t\t\t\tplaceholder: 'e.g. 2014-01-01T00:00:00Z',\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tvalidateType: 'dateTime',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Employee Leave Date',\n\t\t\t\tname: 'employeeLeaveDateTime',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The date and time when the user left or will leave the organization',\n\t\t\t\tplaceholder: 'e.g. 2014-01-01T00:00:00Z',\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tvalidateType: 'dateTime',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Employee Organization Data',\n\t\t\t\tname: 'employeeOrgData',\n\t\t\t\tdefault: {},\n\t\t\t\tdescription:\n\t\t\t\t\t'Represents organization data (for example, division and costCenter) associated with a user',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Employee Organization Data',\n\t\t\t\t\t\tname: 'employeeOrgValues',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Cost Center',\n\t\t\t\t\t\t\t\tname: 'costCenter',\n\t\t\t\t\t\t\t\tdescription: 'The cost center associated with the user',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Division',\n\t\t\t\t\t\t\t\tname: 'division',\n\t\t\t\t\t\t\t\tdescription: 'The name of the division in which the user works',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'First Name',\n\t\t\t\tname: 'givenName',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The given name (first name) of the user',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Force Change Password',\n\t\t\t\tname: 'forceChangePassword',\n\t\t\t\tdefault: 'forceChangePasswordNextSignIn',\n\t\t\t\tdescription: 'Whether the user must change their password on the next sign-in',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Next Sign In',\n\t\t\t\t\t\tvalue: 'forceChangePasswordNextSignIn',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Next Sign In with MFA',\n\t\t\t\t\t\tvalue: 'forceChangePasswordNextSignInWithMfa',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\ttype: 'options',\n\t\t\t\tvalidateType: 'options',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Interests',\n\t\t\t\tname: 'interests',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'A list for the user to describe their interests',\n\t\t\t\ttype: 'string',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tvalidateType: 'array',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Job Title',\n\t\t\t\tname: 'jobTitle',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The user's job title\",\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Last Name',\n\t\t\t\tname: 'surname',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The user's last name (family name)\",\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Mail',\n\t\t\t\tname: 'mail',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The SMTP address for the user',\n\t\t\t\tplaceholder: 'e.g. <EMAIL>',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Mobile Phone',\n\t\t\t\tname: 'mobilePhone',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The primary cellular telephone number for the user',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'My Site',\n\t\t\t\tname: 'mySite',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The URL for the user's personal site\",\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Office Location',\n\t\t\t\tname: 'officeLocation',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The office location for the user',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'On Premises Immutable ID',\n\t\t\t\tname: 'onPremisesImmutableId',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'This property is used to associate an on-premises Active Directory user account to their Microsoft Entra user object',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Other Emails',\n\t\t\t\tname: 'otherMails',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'Additional email addresses for the user',\n\t\t\t\ttype: 'string',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tvalidateType: 'array',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Password Policies',\n\t\t\t\tname: 'passwordPolicies',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'Specifies password policies',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Disable Password Expiration',\n\t\t\t\t\t\tvalue: 'DisablePasswordExpiration',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Disable Strong Password',\n\t\t\t\t\t\tvalue: 'DisableStrongPassword',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\ttype: 'multiOptions',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Past Projects',\n\t\t\t\tname: 'pastProjects',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'A list of past projects the user has worked on',\n\t\t\t\ttype: 'string',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tvalidateType: 'array',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Postal Code',\n\t\t\t\tname: 'postalCode',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The postal code for the user's address\",\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Preferred Language',\n\t\t\t\tname: 'preferredLanguage',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"User's preferred language in ISO 639-1 code\",\n\t\t\t\tplaceholder: 'e.g. en-US',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Responsibilities',\n\t\t\t\tname: 'responsibilities',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'A list of responsibilities the user has',\n\t\t\t\ttype: 'string',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tvalidateType: 'array',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Schools Attended',\n\t\t\t\tname: 'schools',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'A list of schools the user attended',\n\t\t\t\ttype: 'string',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tvalidateType: 'array',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Skills',\n\t\t\t\tname: 'skills',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'A list of skills the user possesses',\n\t\t\t\ttype: 'string',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tvalidateType: 'array',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'State',\n\t\t\t\tname: 'state',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The state or province of the user's address\",\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Street Address',\n\t\t\t\tname: 'streetAddress',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The street address of the user's place of business\",\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Usage Location',\n\t\t\t\tname: 'usageLocation',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Two-letter country code where the user is located',\n\t\t\t\tplaceholder: 'e.g. US',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'User Type',\n\t\t\t\tname: 'userType',\n\t\t\t\tdefault: 'Guest',\n\t\t\t\tdescription: 'Classifies the user type',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Guest',\n\t\t\t\t\t\tvalue: 'Guest',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Member',\n\t\t\t\t\t\tvalue: 'Member',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\ttype: 'options',\n\t\t\t\tvalidateType: 'options',\n\t\t\t},\n\t\t],\n\t\tplaceholder: 'Add Field',\n\t\trouting: {\n\t\t\toutput: {\n\t\t\t\tpostReceive: [\n\t\t\t\t\tasync function (\n\t\t\t\t\t\tthis: IExecuteSingleFunctions,\n\t\t\t\t\t\titems: INodeExecutionData[],\n\t\t\t\t\t\t_response: IN8nHttpFullResponse,\n\t\t\t\t\t): Promise<INodeExecutionData[]> {\n\t\t\t\t\t\tfor (const item of items) {\n\t\t\t\t\t\t\tconst userId = item.json.id as string;\n\t\t\t\t\t\t\tconst fields = this.getNodeParameter('additionalFields', item.index) as IDataObject;\n\t\t\t\t\t\t\tif (Object.keys(fields).length) {\n\t\t\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\t\t\t...fields,\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\tif (body.birthday) {\n\t\t\t\t\t\t\t\t\tbody.birthday = (body.birthday as DateTime).toUTC().toISO();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (body.businessPhones) {\n\t\t\t\t\t\t\t\t\tbody.businessPhones = [body.businessPhones as string];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (body.employeeHireDate) {\n\t\t\t\t\t\t\t\t\tbody.employeeHireDate = (body.employeeHireDate as DateTime).toUTC().toISO();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (body.employeeLeaveDateTime) {\n\t\t\t\t\t\t\t\t\tbody.employeeLeaveDateTime = (body.employeeLeaveDateTime as DateTime)\n\t\t\t\t\t\t\t\t\t\t.toUTC()\n\t\t\t\t\t\t\t\t\t\t.toISO();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (body.employeeOrgData) {\n\t\t\t\t\t\t\t\t\tbody.employeeOrgData = (body.employeeOrgData as IDataObject).employeeOrgValues;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (body.passwordPolicies) {\n\t\t\t\t\t\t\t\t\tbody.passwordPolicies = (body.passwordPolicies as string[]).join(',');\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t// forceChangePasswordNextSignInWithMfa doesn't seem to take effect when providing it in the initial create request,\n\t\t\t\t\t\t\t\t// so we add it in the update request\n\t\t\t\t\t\t\t\tif (body.forceChangePassword) {\n\t\t\t\t\t\t\t\t\tif (body.forceChangePassword === 'forceChangePasswordNextSignIn') {\n\t\t\t\t\t\t\t\t\t\tbody.passwordProfile ??= {};\n\t\t\t\t\t\t\t\t\t\t(body.passwordProfile as IDataObject).forceChangePasswordNextSignIn = true;\n\t\t\t\t\t\t\t\t\t} else if (body.forceChangePassword === 'forceChangePasswordNextSignInWithMfa') {\n\t\t\t\t\t\t\t\t\t\tbody.passwordProfile ??= {};\n\t\t\t\t\t\t\t\t\t\t(body.passwordProfile as IDataObject).forceChangePasswordNextSignInWithMfa =\n\t\t\t\t\t\t\t\t\t\t\ttrue;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tdelete body.forceChangePassword;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t// To update the following properties, you must specify them in their own PATCH request, without including the other properties\n\t\t\t\t\t\t\t\tconst separateProperties = [\n\t\t\t\t\t\t\t\t\t'aboutMe',\n\t\t\t\t\t\t\t\t\t'birthday',\n\t\t\t\t\t\t\t\t\t'interests',\n\t\t\t\t\t\t\t\t\t'mySite',\n\t\t\t\t\t\t\t\t\t'pastProjects',\n\t\t\t\t\t\t\t\t\t'responsibilities',\n\t\t\t\t\t\t\t\t\t'schools',\n\t\t\t\t\t\t\t\t\t'skills',\n\t\t\t\t\t\t\t\t];\n\t\t\t\t\t\t\t\tconst separateBody: IDataObject = {};\n\t\t\t\t\t\t\t\tfor (const [key, value] of Object.entries(body)) {\n\t\t\t\t\t\t\t\t\tif (separateProperties.includes(key)) {\n\t\t\t\t\t\t\t\t\t\tseparateBody[key] = value;\n\t\t\t\t\t\t\t\t\t\tdelete body[key];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tif (Object.keys(separateBody).length) {\n\t\t\t\t\t\t\t\t\t\tawait microsoftApiRequest.call(this, 'PATCH', `/users/${userId}`, separateBody);\n\t\t\t\t\t\t\t\t\t\tmerge(item.json, separateBody);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tif (Object.keys(body).length) {\n\t\t\t\t\t\t\t\t\t\tawait microsoftApiRequest.call(this, 'PATCH', `/users/${userId}`, body);\n\t\t\t\t\t\t\t\t\t\tmerge(item.json, body);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t\tawait microsoftApiRequest.call(this, 'DELETE', `/users/${userId}`);\n\t\t\t\t\t\t\t\t\t} catch {}\n\t\t\t\t\t\t\t\t\tthrow error;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn items;\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t},\n\t\ttype: 'collection',\n\t},\n];\n\nconst deleteFields: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'User to Delete',\n\t\tname: 'user',\n\t\tdefault: {\n\t\t\tmode: 'list',\n\t\t\tvalue: '',\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['delete'],\n\t\t\t},\n\t\t},\n\t\tmodes: [\n\t\t\t{\n\t\t\t\tdisplayName: 'From List',\n\t\t\t\tname: 'list',\n\t\t\t\ttype: 'list',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tsearchListMethod: 'getUsers',\n\t\t\t\t\tsearchable: true,\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'By ID',\n\t\t\t\tname: 'id',\n\t\t\t\tplaceholder: 'e.g. 02bd9fd6-8f93-4758-87c3-1fb73740a315',\n\t\t\t\ttype: 'string',\n\t\t\t},\n\t\t],\n\t\trequired: true,\n\t\ttype: 'resourceLocator',\n\t},\n];\n\nconst getFields: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'User to Get',\n\t\tname: 'user',\n\t\tdefault: {\n\t\t\tmode: 'list',\n\t\t\tvalue: '',\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t\tmodes: [\n\t\t\t{\n\t\t\t\tdisplayName: 'From List',\n\t\t\t\tname: 'list',\n\t\t\t\ttype: 'list',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tsearchListMethod: 'getUsers',\n\t\t\t\t\tsearchable: true,\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'By ID',\n\t\t\t\tname: 'id',\n\t\t\t\tplaceholder: 'e.g. 02bd9fd6-8f93-4758-87c3-1fb73740a315',\n\t\t\t\ttype: 'string',\n\t\t\t},\n\t\t],\n\t\trequired: true,\n\t\ttype: 'resourceLocator',\n\t},\n\t{\n\t\tdisplayName: 'Output',\n\t\tname: 'output',\n\t\tdefault: 'simple',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Simplified',\n\t\t\t\tvalue: 'simple',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: '$select',\n\t\t\t\t\t\ttype: 'query',\n\t\t\t\t\t\tvalue:\n\t\t\t\t\t\t\t'id,createdDateTime,displayName,userPrincipalName,mail,mailNickname,securityIdentifier',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Raw',\n\t\t\t\tvalue: 'raw',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: '$select',\n\t\t\t\t\t\ttype: 'query',\n\t\t\t\t\t\tvalue:\n\t\t\t\t\t\t\t'id,accountEnabled,ageGroup,assignedLicenses,assignedPlans,authorizationInfo,businessPhones,city,companyName,consentProvidedForMinor,country,createdDateTime,creationType,customSecurityAttributes,deletedDateTime,department,displayName,employeeHireDate,employeeId,employeeLeaveDateTime,employeeOrgData,employeeType,externalUserState,externalUserStateChangeDateTime,faxNumber,givenName,identities,imAddresses,isManagementRestricted,isResourceAccount,jobTitle,lastPasswordChangeDateTime,legalAgeGroupClassification,licenseAssignmentStates,mail,mailNickname,mobilePhone,officeLocation,onPremisesDistinguishedName,onPremisesDomainName,onPremisesExtensionAttributes,onPremisesImmutableId,onPremisesLastSyncDateTime,onPremisesProvisioningErrors,onPremisesSamAccountName,onPremisesSecurityIdentifier,onPremisesSyncEnabled,onPremisesUserPrincipalName,otherMails,passwordPolicies,passwordProfile,postalCode,preferredDataLocation,preferredLanguage,provisionedPlans,proxyAddresses,securityIdentifier,serviceProvisioningErrors,showInAddressList,signInSessionsValidFromDateTime,state,streetAddress,surname,usageLocation,userPrincipalName,userType',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Selected Fields',\n\t\t\t\tvalue: 'fields',\n\t\t\t},\n\t\t],\n\t\ttype: 'options',\n\t},\n\t{\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-wrong-for-dynamic-multi-options\n\t\tdisplayName: 'Fields',\n\t\tname: 'fields',\n\t\tdefault: [],\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-description-wrong-for-dynamic-multi-options\n\t\tdescription: 'The fields to add to the output',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['get'],\n\t\t\t\toutput: ['fields'],\n\t\t\t},\n\t\t},\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: '$select',\n\t\t\t\ttype: 'query',\n\t\t\t\tvalue: '={{ $value.concat(\"id\").join(\",\") }}',\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getUserProperties',\n\t\t},\n\t\ttype: 'multiOptions',\n\t},\n];\n\nconst getAllFields: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tpaginate: '={{ $value }}',\n\t\t\t},\n\t\t\toperations: {\n\t\t\t\tpagination: {\n\t\t\t\t\ttype: 'generic',\n\t\t\t\t\tproperties: {\n\t\t\t\t\t\tcontinue: '={{ !!$response.body?.[\"@odata.nextLink\"] }}',\n\t\t\t\t\t\trequest: {\n\t\t\t\t\t\t\turl: '={{ $response.body?.[\"@odata.nextLink\"] ?? $request.url }}',\n\t\t\t\t\t\t\tqs: {\n\t\t\t\t\t\t\t\t$filter:\n\t\t\t\t\t\t\t\t\t'={{ !!$response.body?.[\"@odata.nextLink\"] ? undefined : $request.qs?.$filter }}',\n\t\t\t\t\t\t\t\t$select:\n\t\t\t\t\t\t\t\t\t'={{ !!$response.body?.[\"@odata.nextLink\"] ? undefined : $request.qs?.$select }}',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\ttype: 'boolean',\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\tdefault: 50,\n\t\tdescription: 'Max number of results to return',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: '$top',\n\t\t\t\ttype: 'query',\n\t\t\t\tvalue: '={{ $value }}',\n\t\t\t},\n\t\t},\n\t\ttype: 'number',\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t},\n\t\tvalidateType: 'number',\n\t},\n\t{\n\t\tdisplayName: 'Filter',\n\t\tname: 'filter',\n\t\tdefault: '',\n\t\tdescription:\n\t\t\t'<a href=\"https://docs.microsoft.com/en-us/graph/query-parameters#filter-parameter\">Query parameter</a> to filter results by',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\tplaceholder: \"e.g. startswith(displayName, 'a')\",\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: '$filter',\n\t\t\t\ttype: 'query',\n\t\t\t\tvalue: '={{ $value ? $value : undefined }}',\n\t\t\t},\n\t\t},\n\t\ttype: 'string',\n\t\tvalidateType: 'string',\n\t},\n\t{\n\t\tdisplayName: 'Output',\n\t\tname: 'output',\n\t\tdefault: 'simple',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Simplified',\n\t\t\t\tvalue: 'simple',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: '$select',\n\t\t\t\t\t\ttype: 'query',\n\t\t\t\t\t\tvalue:\n\t\t\t\t\t\t\t'id,createdDateTime,displayName,userPrincipalName,mail,mailNickname,securityIdentifier',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Raw',\n\t\t\t\tvalue: 'raw',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: '$select',\n\t\t\t\t\t\ttype: 'query',\n\t\t\t\t\t\tvalue:\n\t\t\t\t\t\t\t'id,accountEnabled,ageGroup,assignedLicenses,assignedPlans,authorizationInfo,businessPhones,city,companyName,consentProvidedForMinor,country,createdDateTime,creationType,customSecurityAttributes,deletedDateTime,department,displayName,employeeHireDate,employeeId,employeeLeaveDateTime,employeeOrgData,employeeType,externalUserState,externalUserStateChangeDateTime,faxNumber,givenName,identities,imAddresses,isManagementRestricted,isResourceAccount,jobTitle,lastPasswordChangeDateTime,legalAgeGroupClassification,licenseAssignmentStates,mail,mailNickname,mobilePhone,officeLocation,onPremisesDistinguishedName,onPremisesDomainName,onPremisesExtensionAttributes,onPremisesImmutableId,onPremisesLastSyncDateTime,onPremisesProvisioningErrors,onPremisesSamAccountName,onPremisesSecurityIdentifier,onPremisesSyncEnabled,onPremisesUserPrincipalName,otherMails,passwordPolicies,passwordProfile,postalCode,preferredDataLocation,preferredLanguage,provisionedPlans,proxyAddresses,securityIdentifier,serviceProvisioningErrors,showInAddressList,signInSessionsValidFromDateTime,state,streetAddress,surname,usageLocation,userPrincipalName,userType',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Selected Fields',\n\t\t\t\tvalue: 'fields',\n\t\t\t},\n\t\t],\n\t\ttype: 'options',\n\t},\n\t{\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-wrong-for-dynamic-multi-options\n\t\tdisplayName: 'Fields',\n\t\tname: 'fields',\n\t\tdefault: [],\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-description-wrong-for-dynamic-multi-options\n\t\tdescription: 'The fields to add to the output',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\toutput: ['fields'],\n\t\t\t},\n\t\t},\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: '$select',\n\t\t\t\ttype: 'query',\n\t\t\t\tvalue: '={{ $value.concat(\"id\").join(\",\") }}',\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getUserPropertiesGetAll',\n\t\t},\n\t\ttype: 'multiOptions',\n\t},\n];\n\nconst removeGroupFields: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Group',\n\t\tname: 'group',\n\t\tdefault: {\n\t\t\tmode: 'list',\n\t\t\tvalue: '',\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['removeGroup'],\n\t\t\t},\n\t\t},\n\t\tmodes: [\n\t\t\t{\n\t\t\t\tdisplayName: 'From List',\n\t\t\t\tname: 'list',\n\t\t\t\ttype: 'list',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tsearchListMethod: 'getGroups',\n\t\t\t\t\tsearchable: true,\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'By ID',\n\t\t\t\tname: 'id',\n\t\t\t\tplaceholder: 'e.g. 02bd9fd6-8f93-4758-87c3-1fb73740a315',\n\t\t\t\ttype: 'string',\n\t\t\t},\n\t\t],\n\t\trequired: true,\n\t\ttype: 'resourceLocator',\n\t},\n\t{\n\t\tdisplayName: 'User to Remove',\n\t\tname: 'user',\n\t\tdefault: {\n\t\t\tmode: 'list',\n\t\t\tvalue: '',\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['removeGroup'],\n\t\t\t},\n\t\t},\n\t\tmodes: [\n\t\t\t{\n\t\t\t\tdisplayName: 'From List',\n\t\t\t\tname: 'list',\n\t\t\t\ttype: 'list',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tsearchListMethod: 'getUsers',\n\t\t\t\t\tsearchable: true,\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'By ID',\n\t\t\t\tname: 'id',\n\t\t\t\tplaceholder: 'e.g. 02bd9fd6-8f93-4758-87c3-1fb73740a315',\n\t\t\t\ttype: 'string',\n\t\t\t},\n\t\t],\n\t\trequired: true,\n\t\ttype: 'resourceLocator',\n\t},\n];\n\nconst updateFields: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'User to Update',\n\t\tname: 'user',\n\t\tdefault: {\n\t\t\tmode: 'list',\n\t\t\tvalue: '',\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t\tmodes: [\n\t\t\t{\n\t\t\t\tdisplayName: 'From List',\n\t\t\t\tname: 'list',\n\t\t\t\ttype: 'list',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tsearchListMethod: 'getUsers',\n\t\t\t\t\tsearchable: true,\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'By ID',\n\t\t\t\tname: 'id',\n\t\t\t\tplaceholder: 'e.g. 02bd9fd6-8f93-4758-87c3-1fb73740a315',\n\t\t\t\ttype: 'string',\n\t\t\t},\n\t\t],\n\t\trequired: true,\n\t\ttype: 'resourceLocator',\n\t},\n\t{\n\t\tdisplayName: 'Update Fields',\n\t\tname: 'updateFields',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'About Me',\n\t\t\t\tname: 'aboutMe',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'A freeform text entry field for the user to describe themselves',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Account Enabled',\n\t\t\t\tname: 'accountEnabled',\n\t\t\t\tdefault: true,\n\t\t\t\tdescription: 'Whether the account is enabled',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'accountEnabled',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'boolean',\n\t\t\t\tvalidateType: 'boolean',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Age Group',\n\t\t\t\tname: 'ageGroup',\n\t\t\t\tdefault: 'Adult',\n\t\t\t\tdescription: 'Sets the age group of the user',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Adult',\n\t\t\t\t\t\tvalue: 'Adult',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Minor',\n\t\t\t\t\t\tvalue: 'Minor',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Not Adult',\n\t\t\t\t\t\tvalue: 'NotAdult',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'ageGroup',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'options',\n\t\t\t\tvalidateType: 'options',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Birthday',\n\t\t\t\tname: 'birthday',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The birthday of the user',\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tvalidateType: 'dateTime',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Business Phone',\n\t\t\t\tname: 'businessPhones',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The telephone number for the user',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'businessPhones',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t\tvalue: '={{ $value ? [$value] : [] }}',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'City',\n\t\t\t\tname: 'city',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The city in which the user is located',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'city',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Company Name',\n\t\t\t\tname: 'companyName',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The name of the company associated with the user',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'companyName',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t\tpreSend: [\n\t\t\t\t\t\t\tasync function (\n\t\t\t\t\t\t\t\tthis: IExecuteSingleFunctions,\n\t\t\t\t\t\t\t\trequestOptions: IHttpRequestOptions,\n\t\t\t\t\t\t\t): Promise<IHttpRequestOptions> {\n\t\t\t\t\t\t\t\tconst companyName = this.getNodeParameter('updateFields.companyName') as string;\n\t\t\t\t\t\t\t\tif (companyName?.length > 64) {\n\t\t\t\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t\t\t\t\"'Company Name' should have a maximum length of 64\",\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn requestOptions;\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Consent Provided',\n\t\t\t\tname: 'consentProvidedForMinor',\n\t\t\t\tdefault: 'Denied',\n\t\t\t\tdescription: 'Specifies if consent is provided for minors',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Denied',\n\t\t\t\t\t\tvalue: 'Denied',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Granted',\n\t\t\t\t\t\tvalue: 'Granted',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Not Required',\n\t\t\t\t\t\tvalue: 'NotRequired',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'consentProvidedForMinor',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'options',\n\t\t\t\tvalidateType: 'options',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Country',\n\t\t\t\tname: 'country',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The country/region of the user',\n\t\t\t\tplaceholder: 'e.g. US',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'country',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Department',\n\t\t\t\tname: 'department',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The department name where the user works',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'department',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Display Name',\n\t\t\t\tname: 'displayName',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The name to display in the address book for the user',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'displayName',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\n\t\t\t{\n\t\t\t\tdisplayName: 'Employee ID',\n\t\t\t\tname: 'employeeId',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Employee identifier assigned by the organization',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'employeeId',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t\tpreSend: [\n\t\t\t\t\t\t\tasync function (\n\t\t\t\t\t\t\t\tthis: IExecuteSingleFunctions,\n\t\t\t\t\t\t\t\trequestOptions: IHttpRequestOptions,\n\t\t\t\t\t\t\t): Promise<IHttpRequestOptions> {\n\t\t\t\t\t\t\t\tconst employeeId = this.getNodeParameter('updateFields.employeeId') as string;\n\t\t\t\t\t\t\t\tif (employeeId?.length > 16) {\n\t\t\t\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t\t\t\t\"'Employee ID' should have a maximum length of 16\",\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn requestOptions;\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Employee Type',\n\t\t\t\tname: 'employeeType',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Defines enterprise worker type',\n\t\t\t\tplaceholder: 'e.g. Contractor',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'employeeType',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'First Name',\n\t\t\t\tname: 'givenName',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The given name (first name) of the user',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'givenName',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Employee Hire Date',\n\t\t\t\tname: 'employeeHireDate',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The hire date of the user',\n\t\t\t\tplaceholder: 'e.g. 2014-01-01T00:00:00Z',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'employeeHireDate',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t\tvalue: '={{ $value?.toUTC().toISO() }}',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tvalidateType: 'dateTime',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Employee Leave Date',\n\t\t\t\tname: 'employeeLeaveDateTime',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The date and time when the user left or will leave the organization',\n\t\t\t\tplaceholder: 'e.g. 2014-01-01T00:00:00Z',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'employeeLeaveDateTime',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t\tvalue: '={{ $value?.toUTC().toISO() }}',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tvalidateType: 'dateTime',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Employee Organization Data',\n\t\t\t\tname: 'employeeOrgData',\n\t\t\t\tdefault: {},\n\t\t\t\tdescription:\n\t\t\t\t\t'Represents organization data (for example, division and costCenter) associated with a user',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Employee Organization Data',\n\t\t\t\t\t\tname: 'employeeOrgValues',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Cost Center',\n\t\t\t\t\t\t\t\tname: 'costCenter',\n\t\t\t\t\t\t\t\tdescription: 'The cost center associated with the user',\n\t\t\t\t\t\t\t\trouting: {\n\t\t\t\t\t\t\t\t\tsend: {\n\t\t\t\t\t\t\t\t\t\tproperty: 'employeeOrgData.costCenter',\n\t\t\t\t\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Division',\n\t\t\t\t\t\t\t\tname: 'division',\n\t\t\t\t\t\t\t\tdescription: 'The name of the division in which the user works',\n\t\t\t\t\t\t\t\trouting: {\n\t\t\t\t\t\t\t\t\tsend: {\n\t\t\t\t\t\t\t\t\t\tproperty: 'employeeOrgData.division',\n\t\t\t\t\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Force Change Password',\n\t\t\t\tname: 'forceChangePassword',\n\t\t\t\tdefault: 'forceChangePasswordNextSignIn',\n\t\t\t\tdescription: 'Whether the user must change their password on the next sign-in',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Next Sign In',\n\t\t\t\t\t\tvalue: 'forceChangePasswordNextSignIn',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Next Sign In with MFA',\n\t\t\t\t\t\tvalue: 'forceChangePasswordNextSignInWithMfa',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tpreSend: [\n\t\t\t\t\t\t\tasync function (\n\t\t\t\t\t\t\t\tthis: IExecuteSingleFunctions,\n\t\t\t\t\t\t\t\trequestOptions: IHttpRequestOptions,\n\t\t\t\t\t\t\t): Promise<IHttpRequestOptions> {\n\t\t\t\t\t\t\t\tconst forceChangePassword = this.getNodeParameter(\n\t\t\t\t\t\t\t\t\t'updateFields.forceChangePassword',\n\t\t\t\t\t\t\t\t) as string;\n\t\t\t\t\t\t\t\tif (forceChangePassword === 'forceChangePasswordNextSignIn') {\n\t\t\t\t\t\t\t\t\t(requestOptions.body as IDataObject).passwordProfile ??= {};\n\t\t\t\t\t\t\t\t\t(\n\t\t\t\t\t\t\t\t\t\t(requestOptions.body as IDataObject).passwordProfile as IDataObject\n\t\t\t\t\t\t\t\t\t).forceChangePasswordNextSignIn = true;\n\t\t\t\t\t\t\t\t} else if (forceChangePassword === 'forceChangePasswordNextSignInWithMfa') {\n\t\t\t\t\t\t\t\t\t(\n\t\t\t\t\t\t\t\t\t\t(requestOptions.body as IDataObject).passwordProfile as IDataObject\n\t\t\t\t\t\t\t\t\t).forceChangePasswordNextSignInWithMfa = true;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn requestOptions;\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'options',\n\t\t\t\tvalidateType: 'options',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Interests',\n\t\t\t\tname: 'interests',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'A list for the user to describe their interests',\n\t\t\t\ttype: 'string',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tvalidateType: 'array',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Job Title',\n\t\t\t\tname: 'jobTitle',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The user's job title\",\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'jobTitle',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Last Name',\n\t\t\t\tname: 'surname',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The user's last name (family name)\",\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'surname',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Mail',\n\t\t\t\tname: 'mail',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The SMTP address for the user',\n\t\t\t\tplaceholder: 'e.g. <EMAIL>',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'mail',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Mail Nickname',\n\t\t\t\tname: 'mailNickname',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The mail alias for the user',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'mailNickname',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Mobile Phone',\n\t\t\t\tname: 'mobilePhone',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The primary cellular telephone number for the user',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'mobilePhone',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'My Site',\n\t\t\t\tname: 'mySite',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The URL for the user's personal site\",\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Office Location',\n\t\t\t\tname: 'officeLocation',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The office location for the user',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'officeLocation',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'On Premises Immutable ID',\n\t\t\t\tname: 'onPremisesImmutableId',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'This property is used to associate an on-premises Active Directory user account to their Microsoft Entra user object',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'onPremisesImmutableId',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Other Emails',\n\t\t\t\tname: 'otherMails',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'Additional email addresses for the user',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'otherMails',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tvalidateType: 'array',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Password',\n\t\t\t\tname: 'password',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'The password for the user. The password must satisfy minimum requirements as specified by the passwordPolicies property.',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'passwordProfile.password',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tpassword: true,\n\t\t\t\t},\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Password Policies',\n\t\t\t\tname: 'passwordPolicies',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'Specifies password policies',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Disable Password Expiration',\n\t\t\t\t\t\tvalue: 'DisablePasswordExpiration',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Disable Strong Password',\n\t\t\t\t\t\tvalue: 'DisableStrongPassword',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'passwordPolicies',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t\tvalue: '={{ $value?.join(\",\") }}',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'multiOptions',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Past Projects',\n\t\t\t\tname: 'pastProjects',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'A list of past projects the user has worked on',\n\t\t\t\ttype: 'string',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tvalidateType: 'array',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Postal Code',\n\t\t\t\tname: 'postalCode',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The postal code for the user's address\",\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'postalCode',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Preferred Language',\n\t\t\t\tname: 'preferredLanguage',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"User's preferred language in ISO 639-1 code\",\n\t\t\t\tplaceholder: 'e.g. en-US',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'preferredLanguage',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Responsibilities',\n\t\t\t\tname: 'responsibilities',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'A list of responsibilities the user has',\n\t\t\t\ttype: 'string',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tvalidateType: 'array',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Schools Attended',\n\t\t\t\tname: 'schools',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'A list of schools the user attended',\n\t\t\t\ttype: 'string',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tvalidateType: 'array',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Skills',\n\t\t\t\tname: 'skills',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'A list of skills the user possesses',\n\t\t\t\ttype: 'string',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tvalidateType: 'array',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'State',\n\t\t\t\tname: 'state',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The state or province of the user's address\",\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'state',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Street Address',\n\t\t\t\tname: 'streetAddress',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The street address of the user's place of business\",\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'streetAddress',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Usage Location',\n\t\t\t\tname: 'usageLocation',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Two-letter country code where the user is located',\n\t\t\t\tplaceholder: 'e.g. US',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'usageLocation',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'User Principal Name',\n\t\t\t\tname: 'userPrincipalName',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The user principal name (UPN)',\n\t\t\t\tplaceholder: 'e.g. <EMAIL>',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'userPrincipalName',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t\tpreSend: [\n\t\t\t\t\t\t\tasync function (\n\t\t\t\t\t\t\t\tthis: IExecuteSingleFunctions,\n\t\t\t\t\t\t\t\trequestOptions: IHttpRequestOptions,\n\t\t\t\t\t\t\t): Promise<IHttpRequestOptions> {\n\t\t\t\t\t\t\t\tconst userPrincipalName = this.getNodeParameter(\n\t\t\t\t\t\t\t\t\t'updateFields.userPrincipalName',\n\t\t\t\t\t\t\t\t) as string;\n\t\t\t\t\t\t\t\tif (!/^[A-Za-z0-9'._\\-!#^~@]+$/.test(userPrincipalName)) {\n\t\t\t\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t\t\t\t\"Only the following characters are allowed for 'User Principal Name': A-Z, a-z, 0-9, ' . - _ ! # ^ ~\",\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn requestOptions;\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidateType: 'string',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'User Type',\n\t\t\t\tname: 'userType',\n\t\t\t\tdefault: 'Guest',\n\t\t\t\tdescription: 'Classifies the user type',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Guest',\n\t\t\t\t\t\tvalue: 'Guest',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Member',\n\t\t\t\t\t\tvalue: 'Member',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tproperty: 'userType',\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'options',\n\t\t\t\tvalidateType: 'options',\n\t\t\t},\n\t\t],\n\t\tplaceholder: 'Add Field',\n\t\trouting: {\n\t\t\toutput: {\n\t\t\t\tpostReceive: [\n\t\t\t\t\tasync function (\n\t\t\t\t\t\tthis: IExecuteSingleFunctions,\n\t\t\t\t\t\titems: INodeExecutionData[],\n\t\t\t\t\t\t_response: IN8nHttpFullResponse,\n\t\t\t\t\t): Promise<INodeExecutionData[]> {\n\t\t\t\t\t\tfor (const item of items) {\n\t\t\t\t\t\t\tconst userId = this.getNodeParameter('user.value', item.index) as string;\n\t\t\t\t\t\t\tconst fields = this.getNodeParameter('updateFields', item.index) as IDataObject;\n\t\t\t\t\t\t\t// To update the following properties, you must specify them in their own PATCH request, without including the other properties\n\t\t\t\t\t\t\tconst separateProperties = [\n\t\t\t\t\t\t\t\t'aboutMe',\n\t\t\t\t\t\t\t\t'birthday',\n\t\t\t\t\t\t\t\t'interests',\n\t\t\t\t\t\t\t\t'mySite',\n\t\t\t\t\t\t\t\t'pastProjects',\n\t\t\t\t\t\t\t\t'responsibilities',\n\t\t\t\t\t\t\t\t'schools',\n\t\t\t\t\t\t\t\t'skills',\n\t\t\t\t\t\t\t];\n\t\t\t\t\t\t\tconst separateFields = Object.keys(fields)\n\t\t\t\t\t\t\t\t.filter((key) => separateProperties.includes(key))\n\t\t\t\t\t\t\t\t.reduce((obj, key) => {\n\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\t...obj,\n\t\t\t\t\t\t\t\t\t\t[key]: fields[key],\n\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t}, {});\n\t\t\t\t\t\t\tif (Object.keys(separateFields).length) {\n\t\t\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\t\t\t...separateFields,\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\tif (body.birthday) {\n\t\t\t\t\t\t\t\t\tbody.birthday = (body.birthday as DateTime).toUTC().toISO();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tawait microsoftApiRequest.call(this, 'PATCH', `/users/${userId}`, body);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn items;\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t},\n\t\ttype: 'collection',\n\t},\n];\n\nexport const userFields: INodeProperties[] = [\n\t...addGroupFields,\n\t...createFields,\n\t...deleteFields,\n\t...getFields,\n\t...getAllFields,\n\t...removeGroupFields,\n\t...updateFields,\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAAsB;AAEtB,0BAQO;AAEP,8BAA4D;AAErD,MAAM,iBAAoC;AAAA,EAChD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,wBAAwB;AAAA,UACzB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa;AAAA,cACZ;AAAA,cACA;AAAA,gBACC,MAAM;AAAA,gBACN,YAAY;AAAA,kBACX,OAAO;AAAA,gBACR;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,wBAAwB;AAAA,UACzB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa,CAAC,8CAAsB;AAAA,UACrC;AAAA,QACD;AAAA,QACA,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,wBAAwB;AAAA,UACzB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa;AAAA,cACZ;AAAA,cACA;AAAA,gBACC,MAAM;AAAA,gBACN,YAAY;AAAA,kBACX,OAAO;AAAA,gBACR;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,wBAAwB;AAAA,UACzB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa,CAAC,8CAAsB;AAAA,UACrC;AAAA,QACD;AAAA,QACA,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,wBAAwB;AAAA,UACzB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa;AAAA,cACZ;AAAA,cACA;AAAA,gBACC,MAAM;AAAA,gBACN,YAAY;AAAA,kBACX,UAAU;AAAA,gBACX;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,QAAQ;AAAA,MACT;AAAA,MACA;AAAA;AAAA,QAEC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,wBAAwB;AAAA,UACzB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa;AAAA,cACZ;AAAA,cACA;AAAA,gBACC,MAAM;AAAA,gBACN,YAAY;AAAA,kBACX,OAAO;AAAA,gBACR;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,wBAAwB;AAAA,UACzB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa;AAAA,cACZ;AAAA,cACA;AAAA,gBACC,MAAM;AAAA,gBACN,YAAY;AAAA,kBACX,OAAO;AAAA,gBACR;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEA,MAAM,iBAAoC;AAAA,EACzC;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,UAAU;AAAA,MACvB;AAAA,IACD;AAAA,IACA,OAAO;AAAA,MACN;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACb;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,MACP;AAAA,IACD;AAAA,IACA,UAAU;AAAA,IACV,MAAM;AAAA,EACP;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,UAAU;AAAA,MACvB;AAAA,IACD;AAAA,IACA,OAAO;AAAA,MACN;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACb;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,MACP;AAAA,IACD;AAAA,IACA,UAAU;AAAA,IACV,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,uBAAuB;AAAA,QACvB,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,MAAM;AAAA,EACP;AACD;AAEA,MAAM,eAAkC;AAAA,EACvC;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,UAAU;AAAA,IACV,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,IACA,MAAM;AAAA,IACN,cAAc;AAAA,EACf;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,IACb,UAAU;AAAA,IACV,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,IACA,MAAM;AAAA,IACN,cAAc;AAAA,EACf;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,IACb,UAAU;AAAA,IACV,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,QACN,SAAS;AAAA,UACR,eAEC,gBAC+B;AAC/B,kBAAM,oBAAoB,KAAK,iBAAiB,mBAAmB;AACnE,gBAAI,CAAC,2BAA2B,KAAK,iBAAiB,GAAG;AACxD,oBAAM,IAAI;AAAA,gBACT,KAAK,QAAQ;AAAA,gBACb;AAAA,cACD;AAAA,YACD;AACA,mBAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IACA,MAAM;AAAA,IACN,cAAc;AAAA,EACf;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,IACb,UAAU;AAAA,IACV,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,IACA,MAAM;AAAA,IACN,cAAc;AAAA,EACf;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,UAAU;AAAA,IACV,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,IACA,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,UAAU;AAAA,IACX;AAAA,IACA,cAAc;AAAA,EACf;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,SAAS;AAAA,cACR,eAEC,gBAC+B;AAC/B,sBAAM,cAAc,KAAK,iBAAiB,8BAA8B;AACxE,oBAAI,aAAa,SAAS,IAAI;AAC7B,wBAAM,IAAI;AAAA,oBACT,KAAK,QAAQ;AAAA,oBACb;AAAA,kBACD;AAAA,gBACD;AACA,uBAAO;AAAA,cACR;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,SAAS;AAAA,cACR,eAEC,gBAC+B;AAC/B,sBAAM,aAAa,KAAK,iBAAiB,6BAA6B;AACtE,oBAAI,YAAY,SAAS,IAAI;AAC5B,wBAAM,IAAI;AAAA,oBACT,KAAK,QAAQ;AAAA,oBACb;AAAA,kBACD;AAAA,gBACD;AACA,uBAAO;AAAA,cACR;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aACC;AAAA,QACD,SAAS;AAAA,UACR;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,SAAS;AAAA,cACV;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,SAAS;AAAA,cACV;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,QACD,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,MAAM;AAAA,MACP;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,IACD;AAAA,IACA,aAAa;AAAA,IACb,SAAS;AAAA,MACR,QAAQ;AAAA,QACP,aAAa;AAAA,UACZ,eAEC,OACA,WACgC;AAChC,uBAAW,QAAQ,OAAO;AACzB,oBAAM,SAAS,KAAK,KAAK;AACzB,oBAAM,SAAS,KAAK,iBAAiB,oBAAoB,KAAK,KAAK;AACnE,kBAAI,OAAO,KAAK,MAAM,EAAE,QAAQ;AAC/B,sBAAM,OAAoB;AAAA,kBACzB,GAAG;AAAA,gBACJ;AACA,oBAAI,KAAK,UAAU;AAClB,uBAAK,WAAY,KAAK,SAAsB,MAAM,EAAE,MAAM;AAAA,gBAC3D;AACA,oBAAI,KAAK,gBAAgB;AACxB,uBAAK,iBAAiB,CAAC,KAAK,cAAwB;AAAA,gBACrD;AACA,oBAAI,KAAK,kBAAkB;AAC1B,uBAAK,mBAAoB,KAAK,iBAA8B,MAAM,EAAE,MAAM;AAAA,gBAC3E;AACA,oBAAI,KAAK,uBAAuB;AAC/B,uBAAK,wBAAyB,KAAK,sBACjC,MAAM,EACN,MAAM;AAAA,gBACT;AACA,oBAAI,KAAK,iBAAiB;AACzB,uBAAK,kBAAmB,KAAK,gBAAgC;AAAA,gBAC9D;AACA,oBAAI,KAAK,kBAAkB;AAC1B,uBAAK,mBAAoB,KAAK,iBAA8B,KAAK,GAAG;AAAA,gBACrE;AAGA,oBAAI,KAAK,qBAAqB;AAC7B,sBAAI,KAAK,wBAAwB,iCAAiC;AACjE,yBAAK,oBAAoB,CAAC;AAC1B,oBAAC,KAAK,gBAAgC,gCAAgC;AAAA,kBACvE,WAAW,KAAK,wBAAwB,wCAAwC;AAC/E,yBAAK,oBAAoB,CAAC;AAC1B,oBAAC,KAAK,gBAAgC,uCACrC;AAAA,kBACF;AACA,yBAAO,KAAK;AAAA,gBACb;AAGA,sBAAM,qBAAqB;AAAA,kBAC1B;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,gBACD;AACA,sBAAM,eAA4B,CAAC;AACnC,2BAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,IAAI,GAAG;AAChD,sBAAI,mBAAmB,SAAS,GAAG,GAAG;AACrC,iCAAa,GAAG,IAAI;AACpB,2BAAO,KAAK,GAAG;AAAA,kBAChB;AAAA,gBACD;AAEA,oBAAI;AACH,sBAAI,OAAO,KAAK,YAAY,EAAE,QAAQ;AACrC,0BAAM,4CAAoB,KAAK,MAAM,SAAS,UAAU,MAAM,IAAI,YAAY;AAC9E,6CAAM,KAAK,MAAM,YAAY;AAAA,kBAC9B;AACA,sBAAI,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC7B,0BAAM,4CAAoB,KAAK,MAAM,SAAS,UAAU,MAAM,IAAI,IAAI;AACtE,6CAAM,KAAK,MAAM,IAAI;AAAA,kBACtB;AAAA,gBACD,SAAS,OAAO;AACf,sBAAI;AACH,0BAAM,4CAAoB,KAAK,MAAM,UAAU,UAAU,MAAM,EAAE;AAAA,kBAClE,QAAQ;AAAA,kBAAC;AACT,wBAAM;AAAA,gBACP;AAAA,cACD;AAAA,YACD;AACA,mBAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IACA,MAAM;AAAA,EACP;AACD;AAEA,MAAM,eAAkC;AAAA,EACvC;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,OAAO;AAAA,MACN;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACb;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,MACP;AAAA,IACD;AAAA,IACA,UAAU;AAAA,IACV,MAAM;AAAA,EACP;AACD;AAEA,MAAM,YAA+B;AAAA,EACpC;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,OAAO;AAAA,MACN;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACb;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,MACP;AAAA,IACD;AAAA,IACA,UAAU;AAAA,IACV,MAAM;AAAA,EACP;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,YACN,OACC;AAAA,UACF;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,YACN,OACC;AAAA,UACF;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,MAAM;AAAA,EACP;AAAA,EACA;AAAA;AAAA,IAEC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS,CAAC;AAAA;AAAA,IAEV,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,KAAK;AAAA,QACjB,QAAQ,CAAC,QAAQ;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,MAAM;AAAA,EACP;AACD;AAEA,MAAM,eAAkC;AAAA,EACvC;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,MACX;AAAA,MACA,YAAY;AAAA,QACX,YAAY;AAAA,UACX,MAAM;AAAA,UACN,YAAY;AAAA,YACX,UAAU;AAAA,YACV,SAAS;AAAA,cACR,KAAK;AAAA,cACL,IAAI;AAAA,gBACH,SACC;AAAA,gBACD,SACC;AAAA,cACF;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IACA,MAAM;AAAA,EACP;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,QACpB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,UAAU;AAAA,IACX;AAAA,IACA,cAAc;AAAA,EACf;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aACC;AAAA,IACD,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,IACb,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,MAAM;AAAA,IACN,cAAc;AAAA,EACf;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,YACN,OACC;AAAA,UACF;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,YACN,OACC;AAAA,UACF;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,MAAM;AAAA,EACP;AAAA,EACA;AAAA;AAAA,IAEC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS,CAAC;AAAA;AAAA,IAEV,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,QACpB,QAAQ,CAAC,QAAQ;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,MAAM;AAAA,EACP;AACD;AAEA,MAAM,oBAAuC;AAAA,EAC5C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,aAAa;AAAA,MAC1B;AAAA,IACD;AAAA,IACA,OAAO;AAAA,MACN;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACb;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,MACP;AAAA,IACD;AAAA,IACA,UAAU;AAAA,IACV,MAAM;AAAA,EACP;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,aAAa;AAAA,MAC1B;AAAA,IACD;AAAA,IACA,OAAO;AAAA,MACN;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACb;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,MACP;AAAA,IACD;AAAA,IACA,UAAU;AAAA,IACV,MAAM;AAAA,EACP;AACD;AAEA,MAAM,eAAkC;AAAA,EACvC;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,OAAO;AAAA,MACN;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACb;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,MACP;AAAA,IACD;AAAA,IACA,UAAU;AAAA,IACV,MAAM;AAAA,EACP;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,YACN,SAAS;AAAA,cACR,eAEC,gBAC+B;AAC/B,sBAAM,cAAc,KAAK,iBAAiB,0BAA0B;AACpE,oBAAI,aAAa,SAAS,IAAI;AAC7B,wBAAM,IAAI;AAAA,oBACT,KAAK,QAAQ;AAAA,oBACb;AAAA,kBACD;AAAA,gBACD;AACA,uBAAO;AAAA,cACR;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MAEA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,YACN,SAAS;AAAA,cACR,eAEC,gBAC+B;AAC/B,sBAAM,aAAa,KAAK,iBAAiB,yBAAyB;AAClE,oBAAI,YAAY,SAAS,IAAI;AAC5B,wBAAM,IAAI;AAAA,oBACT,KAAK,QAAQ;AAAA,oBACb;AAAA,kBACD;AAAA,gBACD;AACA,uBAAO;AAAA,cACR;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aACC;AAAA,QACD,SAAS;AAAA,UACR;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,aAAa;AAAA,gBACb,SAAS;AAAA,kBACR,MAAM;AAAA,oBACL,UAAU;AAAA,oBACV,MAAM;AAAA,kBACP;AAAA,gBACD;AAAA,gBACA,MAAM;AAAA,gBACN,SAAS;AAAA,cACV;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,aAAa;AAAA,gBACb,SAAS;AAAA,kBACR,MAAM;AAAA,oBACL,UAAU;AAAA,oBACV,MAAM;AAAA,kBACP;AAAA,gBACD;AAAA,gBACA,MAAM;AAAA,gBACN,SAAS;AAAA,cACV;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,SAAS;AAAA,UACR,MAAM;AAAA,YACL,SAAS;AAAA,cACR,eAEC,gBAC+B;AAC/B,sBAAM,sBAAsB,KAAK;AAAA,kBAChC;AAAA,gBACD;AACA,oBAAI,wBAAwB,iCAAiC;AAC5D,kBAAC,eAAe,KAAqB,oBAAoB,CAAC;AAC1D,kBACE,eAAe,KAAqB,gBACpC,gCAAgC;AAAA,gBACnC,WAAW,wBAAwB,wCAAwC;AAC1E,kBACE,eAAe,KAAqB,gBACpC,uCAAuC;AAAA,gBAC1C;AACA,uBAAO;AAAA,cACR;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,QACD,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,QACD,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,MAAM;AAAA,MACP;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,YACN,SAAS;AAAA,cACR,eAEC,gBAC+B;AAC/B,sBAAM,oBAAoB,KAAK;AAAA,kBAC9B;AAAA,gBACD;AACA,oBAAI,CAAC,2BAA2B,KAAK,iBAAiB,GAAG;AACxD,wBAAM,IAAI;AAAA,oBACT,KAAK,QAAQ;AAAA,oBACb;AAAA,kBACD;AAAA,gBACD;AACA,uBAAO;AAAA,cACR;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,SAAS;AAAA,UACR,MAAM;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,IACD;AAAA,IACA,aAAa;AAAA,IACb,SAAS;AAAA,MACR,QAAQ;AAAA,QACP,aAAa;AAAA,UACZ,eAEC,OACA,WACgC;AAChC,uBAAW,QAAQ,OAAO;AACzB,oBAAM,SAAS,KAAK,iBAAiB,cAAc,KAAK,KAAK;AAC7D,oBAAM,SAAS,KAAK,iBAAiB,gBAAgB,KAAK,KAAK;AAE/D,oBAAM,qBAAqB;AAAA,gBAC1B;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACD;AACA,oBAAM,iBAAiB,OAAO,KAAK,MAAM,EACvC,OAAO,CAAC,QAAQ,mBAAmB,SAAS,GAAG,CAAC,EAChD,OAAO,CAAC,KAAK,QAAQ;AACrB,uBAAO;AAAA,kBACN,GAAG;AAAA,kBACH,CAAC,GAAG,GAAG,OAAO,GAAG;AAAA,gBAClB;AAAA,cACD,GAAG,CAAC,CAAC;AACN,kBAAI,OAAO,KAAK,cAAc,EAAE,QAAQ;AACvC,sBAAM,OAAoB;AAAA,kBACzB,GAAG;AAAA,gBACJ;AACA,oBAAI,KAAK,UAAU;AAClB,uBAAK,WAAY,KAAK,SAAsB,MAAM,EAAE,MAAM;AAAA,gBAC3D;AACA,sBAAM,4CAAoB,KAAK,MAAM,SAAS,UAAU,MAAM,IAAI,IAAI;AAAA,cACvE;AAAA,YACD;AACA,mBAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IACA,MAAM;AAAA,EACP;AACD;AAEO,MAAM,aAAgC;AAAA,EAC5C,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACJ;", "names": []}