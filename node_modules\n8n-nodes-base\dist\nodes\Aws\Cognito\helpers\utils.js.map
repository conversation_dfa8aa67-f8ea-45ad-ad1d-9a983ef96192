{"version": 3, "sources": ["../../../../../nodes/Aws/Cognito/helpers/utils.ts"], "sourcesContent": ["import type {\n\tIHttpRequestOptions,\n\tILoadOptionsFunctions,\n\tIDataObject,\n\tIExecuteSingleFunctions,\n\tIN8nHttpFullResponse,\n\tINodeExecutionData,\n} from 'n8n-workflow';\nimport { jsonParse, NodeApiError, NodeOperationError } from 'n8n-workflow';\n\nimport type {\n\tIGroup,\n\tIGroupWithUserResponse,\n\tIListGroupsResponse,\n\tIUser,\n\tIUserAttribute,\n\tIUserAttributeInput,\n\tIUserPool,\n} from './interfaces';\nimport { awsApiRequest, awsApiRequestAllItems } from '../transport';\n\nconst validateEmail = (email: string): boolean => /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);\nconst validatePhoneNumber = (phone: string): boolean => /^\\+[0-9]\\d{1,14}$/.test(phone);\n\nexport async function preSendStringifyBody(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tif (requestOptions.body) {\n\t\trequestOptions.body = JSON.stringify(requestOptions.body);\n\t}\n\treturn requestOptions;\n}\n\nexport async function getUserPool(\n\tthis: IExecuteSingleFunctions | ILoadOptionsFunctions,\n\tuserPoolId: string,\n): Promise<IUserPool> {\n\tif (!userPoolId) {\n\t\tthrow new NodeOperationError(this.getNode(), 'User Pool ID is required');\n\t}\n\n\tconst response = (await awsApiRequest.call(\n\t\tthis,\n\t\t'POST',\n\t\t'DescribeUserPool',\n\t\tJSON.stringify({ UserPoolId: userPoolId }),\n\t)) as { UserPool: IUserPool };\n\n\tif (!response?.UserPool) {\n\t\tthrow new NodeOperationError(this.getNode(), 'User Pool not found in response');\n\t}\n\n\treturn response.UserPool;\n}\n\nexport async function getUsersInGroup(\n\tthis: IExecuteSingleFunctions | ILoadOptionsFunctions,\n\tgroupName: string,\n\tuserPoolId: string,\n): Promise<IUser[]> {\n\tif (!userPoolId) {\n\t\tthrow new NodeOperationError(this.getNode(), 'User Pool ID is required');\n\t}\n\n\tconst requestBody: IDataObject = {\n\t\tUserPoolId: userPoolId,\n\t\tGroupName: groupName,\n\t};\n\n\tconst allUsers = (await awsApiRequestAllItems.call(\n\t\tthis,\n\t\t'POST',\n\t\t'ListUsersInGroup',\n\t\trequestBody,\n\t\t'Users',\n\t)) as unknown as IUser[];\n\n\treturn allUsers;\n}\n\nexport async function getUserNameFromExistingUsers(\n\tthis: IExecuteSingleFunctions | ILoadOptionsFunctions,\n\tuserName: string,\n\tuserPoolId: string,\n\tisEmailOrPhone: boolean,\n): Promise<string | undefined> {\n\tif (isEmailOrPhone) {\n\t\treturn userName;\n\t}\n\n\tconst usersResponse = (await awsApiRequest.call(\n\t\tthis,\n\t\t'POST',\n\t\t'ListUsers',\n\t\tJSON.stringify({\n\t\t\tUserPoolId: userPoolId,\n\t\t\tFilter: `sub = \"${userName}\"`,\n\t\t}),\n\t)) as { Users: IUser[] };\n\n\tconst username =\n\t\tusersResponse.Users && usersResponse.Users.length > 0\n\t\t\t? usersResponse.Users[0].Username\n\t\t\t: undefined;\n\n\treturn username;\n}\n\nexport async function preSendUserFields(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst operation = this.getNodeParameter('operation') as string;\n\tconst userPoolId = this.getNodeParameter('userPool', undefined, {\n\t\textractValue: true,\n\t}) as string;\n\n\tconst userPool = await getUserPool.call(this, userPoolId);\n\n\tconst usernameAttributes = userPool.UsernameAttributes ?? [];\n\tconst isEmailAuth = usernameAttributes.includes('email');\n\tconst isPhoneAuth = usernameAttributes.includes('phone_number');\n\tconst isEmailOrPhone = isEmailAuth || isPhoneAuth;\n\n\tconst getValidatedNewUserName = (): string => {\n\t\tconst newUsername = this.getNodeParameter('newUserName') as string;\n\n\t\tif (isEmailAuth && !validateEmail(newUsername)) {\n\t\t\tthrow new NodeApiError(this.getNode(), {\n\t\t\t\tmessage: 'Invalid email format',\n\t\t\t\tdescription: 'Please provide a valid email (e.g., <EMAIL>)',\n\t\t\t});\n\t\t}\n\t\tif (isPhoneAuth && !validatePhoneNumber(newUsername)) {\n\t\t\tthrow new NodeApiError(this.getNode(), {\n\t\t\t\tmessage: 'Invalid phone number format',\n\t\t\t\tdescription: 'Please provide a valid phone number (e.g., +14155552671)',\n\t\t\t});\n\t\t}\n\n\t\treturn newUsername;\n\t};\n\n\tconst finalUserName =\n\t\toperation === 'create'\n\t\t\t? getValidatedNewUserName()\n\t\t\t: await getUserNameFromExistingUsers.call(\n\t\t\t\t\tthis,\n\t\t\t\t\tthis.getNodeParameter('user', undefined, { extractValue: true }) as string,\n\t\t\t\t\tuserPoolId,\n\t\t\t\t\tisEmailOrPhone,\n\t\t\t\t);\n\n\tconst body = jsonParse<IDataObject>(String(requestOptions.body), {\n\t\tacceptJSObject: true,\n\t\terrorMessage: 'Invalid request body. Request body must be valid JSON.',\n\t});\n\n\treturn {\n\t\t...requestOptions,\n\t\tbody: JSON.stringify({\n\t\t\t...body,\n\t\t\t...(finalUserName ? { Username: finalUserName } : {}),\n\t\t}),\n\t};\n}\n\nexport async function processGroup(\n\tthis: IExecuteSingleFunctions,\n\titems: INodeExecutionData[],\n\tresponse: IN8nHttpFullResponse,\n): Promise<INodeExecutionData[]> {\n\tconst userPoolId = this.getNodeParameter('userPool', undefined, {\n\t\textractValue: true,\n\t}) as string;\n\tconst includeUsers = this.getNodeParameter('includeUsers') as boolean;\n\tconst body = response.body as IDataObject;\n\n\tif (body.Group) {\n\t\tconst group = body.Group as IGroup;\n\n\t\tif (!includeUsers) {\n\t\t\treturn this.helpers.returnJsonArray({ ...group });\n\t\t}\n\n\t\tconst users = await getUsersInGroup.call(this, group.GroupName, userPoolId);\n\t\treturn this.helpers.returnJsonArray({ ...group, Users: users });\n\t}\n\n\tconst groups = (response.body as IListGroupsResponse).Groups ?? [];\n\n\tif (!includeUsers) {\n\t\treturn items;\n\t}\n\n\tconst processedGroups: IGroupWithUserResponse[] = [];\n\tfor (const group of groups) {\n\t\tconst users = await getUsersInGroup.call(this, group.GroupName, userPoolId);\n\t\tprocessedGroups.push({\n\t\t\t...group,\n\t\t\tUsers: users,\n\t\t});\n\t}\n\n\treturn items.map((item) => ({ json: { ...item.json, Groups: processedGroups } }));\n}\n\nexport async function validateArn(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst arn = this.getNodeParameter('additionalFields.arn', '') as string;\n\tconst arnRegex =\n\t\t/^arn:[-.\\w+=/,@]+:[-.\\w+=/,@]+:([-.\\w+=/,@]*)?:[0-9]+:[-.\\w+=/,@]+(:[-.\\w+=/,@]+)?(:[-.\\w+=/,@]+)?$/;\n\n\tif (!arnRegex.test(arn)) {\n\t\tthrow new NodeApiError(this.getNode(), {\n\t\t\tmessage: 'Invalid ARN format',\n\t\t\tdescription:\n\t\t\t\t'Please provide a valid AWS ARN (e.g., arn:aws:iam::123456789012:role/GroupRole).',\n\t\t});\n\t}\n\n\treturn requestOptions;\n}\n\nexport async function simplifyUserPool(\n\tthis: IExecuteSingleFunctions,\n\titems: INodeExecutionData[],\n\t_response: IN8nHttpFullResponse,\n): Promise<INodeExecutionData[]> {\n\tconst simple = this.getNodeParameter('simple') as boolean;\n\n\tif (!simple) {\n\t\treturn items;\n\t}\n\n\treturn items\n\t\t.map((item) => {\n\t\t\tconst data = item.json?.UserPool as IUserPool;\n\n\t\t\tif (!data) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst {\n\t\t\t\tAccountRecoverySetting,\n\t\t\t\tAdminCreateUserConfig,\n\t\t\t\tEmailConfiguration,\n\t\t\t\tLambdaConfig,\n\t\t\t\tPolicies,\n\t\t\t\tSchemaAttributes,\n\t\t\t\tUserAttributeUpdateSettings,\n\t\t\t\tUserPoolTags,\n\t\t\t\tUserPoolTier,\n\t\t\t\tVerificationMessageTemplate,\n\t\t\t\t...selectedData\n\t\t\t} = data;\n\n\t\t\treturn { json: { UserPool: { ...selectedData } } };\n\t\t})\n\t\t.filter(Boolean) as INodeExecutionData[];\n}\n\nexport async function simplifyUser(\n\tthis: IExecuteSingleFunctions,\n\titems: INodeExecutionData[],\n\t_response: IN8nHttpFullResponse,\n): Promise<INodeExecutionData[]> {\n\tconst simple = this.getNodeParameter('simple') as boolean;\n\n\tif (!simple) {\n\t\treturn items;\n\t}\n\n\treturn items\n\t\t.map((item) => {\n\t\t\tconst data = item.json;\n\n\t\t\tif (!data) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (Array.isArray(data.Users)) {\n\t\t\t\tconst users = data.Users as IUser[];\n\n\t\t\t\tconst simplifiedUsers = users.map((user) => {\n\t\t\t\t\tconst attributesArray = user.Attributes ?? [];\n\n\t\t\t\t\tconst userAttributes = Object.fromEntries(\n\t\t\t\t\t\tattributesArray\n\t\t\t\t\t\t\t.filter(({ Name }) => Name?.trim())\n\t\t\t\t\t\t\t.map(({ Name, Value }) => [Name, Value ?? '']),\n\t\t\t\t\t);\n\n\t\t\t\t\tconst { Attributes, ...rest } = user;\n\t\t\t\t\treturn { ...rest, ...userAttributes };\n\t\t\t\t});\n\n\t\t\t\treturn { json: { ...data, Users: simplifiedUsers } };\n\t\t\t}\n\n\t\t\tif (Array.isArray(data.UserAttributes)) {\n\t\t\t\tconst attributesArray = data.UserAttributes as IUserAttribute[];\n\n\t\t\t\tconst userAttributes = Object.fromEntries(\n\t\t\t\t\tattributesArray\n\t\t\t\t\t\t.filter(({ Name }) => Name?.trim())\n\t\t\t\t\t\t.map(({ Name, Value }) => [Name, Value ?? '']),\n\t\t\t\t);\n\n\t\t\t\tconst { UserAttributes, ...rest } = data;\n\t\t\t\treturn { json: { ...rest, ...userAttributes } };\n\t\t\t}\n\n\t\t\treturn item;\n\t\t})\n\t\t.filter(Boolean) as INodeExecutionData[];\n}\n\nexport async function preSendAttributes(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst operation = this.getNodeParameter('operation', 0) as string;\n\n\tconst parameterName =\n\t\toperation === 'create'\n\t\t\t? 'additionalFields.userAttributes.attributes'\n\t\t\t: 'userAttributes.attributes';\n\tconst attributes = this.getNodeParameter(parameterName, []) as IUserAttributeInput[];\n\n\tif (operation === 'update' && (!attributes || attributes.length === 0)) {\n\t\tthrow new NodeOperationError(this.getNode(), 'No user attributes provided', {\n\t\t\tdescription: 'At least one user attribute must be provided for the update operation.',\n\t\t});\n\t}\n\n\tif (operation === 'create') {\n\t\tconst hasEmail = attributes.some((a) => a.standardName === 'email');\n\t\tconst hasEmailVerified = attributes.some(\n\t\t\t(a) => a.standardName === 'email_verified' && a.value === 'true',\n\t\t);\n\n\t\tif (hasEmailVerified && !hasEmail) {\n\t\t\tthrow new NodeOperationError(this.getNode(), 'Missing required \"email\" attribute', {\n\t\t\t\tdescription:\n\t\t\t\t\t'\"email_verified\" is set to true, but the corresponding \"email\" attribute is not provided.',\n\t\t\t});\n\t\t}\n\n\t\tconst hasPhone = attributes.some((a) => a.standardName === 'phone_number');\n\t\tconst hasPhoneVerified = attributes.some(\n\t\t\t(a) => a.standardName === 'phone_number_verified' && a.value === 'true',\n\t\t);\n\n\t\tif (hasPhoneVerified && !hasPhone) {\n\t\t\tthrow new NodeOperationError(this.getNode(), 'Missing required \"phone_number\" attribute', {\n\t\t\t\tdescription:\n\t\t\t\t\t'\"phone_number_verified\" is set to true, but the corresponding \"phone_number\" attribute is not provided.',\n\t\t\t});\n\t\t}\n\t}\n\n\tconst body = jsonParse<IDataObject>(String(requestOptions.body), {\n\t\tacceptJSObject: true,\n\t\terrorMessage: 'Invalid request body. Request body must be valid JSON.',\n\t});\n\n\tbody.UserAttributes = attributes.map(({ attributeType, standardName, customName, value }) => {\n\t\tif (!value || !attributeType || !(standardName ?? customName)) {\n\t\t\tthrow new NodeOperationError(this.getNode(), 'Invalid User Attribute', {\n\t\t\t\tdescription: 'Each attribute must have a valid name and value.',\n\t\t\t});\n\t\t}\n\n\t\tconst attributeName =\n\t\t\tattributeType === 'standard'\n\t\t\t\t? standardName\n\t\t\t\t: `custom:${customName?.startsWith('custom:') ? customName : customName}`;\n\n\t\treturn { Name: attributeName, Value: value };\n\t});\n\n\trequestOptions.body = JSON.stringify(body);\n\n\treturn requestOptions;\n}\n\nexport async function preSendDesiredDeliveryMediums(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst desiredDeliveryMediums = this.getNodeParameter(\n\t\t'additionalFields.desiredDeliveryMediums',\n\t\t[],\n\t) as string[];\n\n\tconst attributes = this.getNodeParameter(\n\t\t'additionalFields.userAttributes.attributes',\n\t\t[],\n\t) as IUserAttributeInput[];\n\n\tconst hasEmail = attributes.some((attr) => attr.standardName === 'email' && !!attr.value?.trim());\n\tconst hasPhone = attributes.some(\n\t\t(attr) => attr.standardName === 'phone_number' && !!attr.value?.trim(),\n\t);\n\n\tif (desiredDeliveryMediums.includes('EMAIL') && !hasEmail) {\n\t\tthrow new NodeOperationError(this.getNode(), 'Missing required \"email\" attribute', {\n\t\t\tdescription: 'Email is selected as a delivery medium but no email attribute is provided.',\n\t\t});\n\t}\n\n\tif (desiredDeliveryMediums.includes('SMS') && !hasPhone) {\n\t\tthrow new NodeOperationError(this.getNode(), 'Missing required \"phone_number\" attribute', {\n\t\t\tdescription:\n\t\t\t\t'SMS is selected as a delivery medium but no phone_number attribute is provided.',\n\t\t});\n\t}\n\n\treturn requestOptions;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,0BAA4D;AAW5D,uBAAqD;AAErD,MAAM,gBAAgB,CAAC,UAA2B,6BAA6B,KAAK,KAAK;AACzF,MAAM,sBAAsB,CAAC,UAA2B,oBAAoB,KAAK,KAAK;AAEtF,eAAsB,qBAErB,gBAC+B;AAC/B,MAAI,eAAe,MAAM;AACxB,mBAAe,OAAO,KAAK,UAAU,eAAe,IAAI;AAAA,EACzD;AACA,SAAO;AACR;AAEA,eAAsB,YAErB,YACqB;AACrB,MAAI,CAAC,YAAY;AAChB,UAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,0BAA0B;AAAA,EACxE;AAEA,QAAM,WAAY,MAAM,+BAAc;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,UAAU,EAAE,YAAY,WAAW,CAAC;AAAA,EAC1C;AAEA,MAAI,CAAC,UAAU,UAAU;AACxB,UAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,iCAAiC;AAAA,EAC/E;AAEA,SAAO,SAAS;AACjB;AAEA,eAAsB,gBAErB,WACA,YACmB;AACnB,MAAI,CAAC,YAAY;AAChB,UAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,0BAA0B;AAAA,EACxE;AAEA,QAAM,cAA2B;AAAA,IAChC,YAAY;AAAA,IACZ,WAAW;AAAA,EACZ;AAEA,QAAM,WAAY,MAAM,uCAAsB;AAAA,IAC7C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAEA,SAAO;AACR;AAEA,eAAsB,6BAErB,UACA,YACA,gBAC8B;AAC9B,MAAI,gBAAgB;AACnB,WAAO;AAAA,EACR;AAEA,QAAM,gBAAiB,MAAM,+BAAc;AAAA,IAC1C;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,UAAU;AAAA,MACd,YAAY;AAAA,MACZ,QAAQ,UAAU,QAAQ;AAAA,IAC3B,CAAC;AAAA,EACF;AAEA,QAAM,WACL,cAAc,SAAS,cAAc,MAAM,SAAS,IACjD,cAAc,MAAM,CAAC,EAAE,WACvB;AAEJ,SAAO;AACR;AAEA,eAAsB,kBAErB,gBAC+B;AAC/B,QAAM,YAAY,KAAK,iBAAiB,WAAW;AACnD,QAAM,aAAa,KAAK,iBAAiB,YAAY,QAAW;AAAA,IAC/D,cAAc;AAAA,EACf,CAAC;AAED,QAAM,WAAW,MAAM,YAAY,KAAK,MAAM,UAAU;AAExD,QAAM,qBAAqB,SAAS,sBAAsB,CAAC;AAC3D,QAAM,cAAc,mBAAmB,SAAS,OAAO;AACvD,QAAM,cAAc,mBAAmB,SAAS,cAAc;AAC9D,QAAM,iBAAiB,eAAe;AAEtC,QAAM,0BAA0B,MAAc;AAC7C,UAAM,cAAc,KAAK,iBAAiB,aAAa;AAEvD,QAAI,eAAe,CAAC,cAAc,WAAW,GAAG;AAC/C,YAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG;AAAA,QACtC,SAAS;AAAA,QACT,aAAa;AAAA,MACd,CAAC;AAAA,IACF;AACA,QAAI,eAAe,CAAC,oBAAoB,WAAW,GAAG;AACrD,YAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG;AAAA,QACtC,SAAS;AAAA,QACT,aAAa;AAAA,MACd,CAAC;AAAA,IACF;AAEA,WAAO;AAAA,EACR;AAEA,QAAM,gBACL,cAAc,WACX,wBAAwB,IACxB,MAAM,6BAA6B;AAAA,IACnC;AAAA,IACA,KAAK,iBAAiB,QAAQ,QAAW,EAAE,cAAc,KAAK,CAAC;AAAA,IAC/D;AAAA,IACA;AAAA,EACD;AAEH,QAAM,WAAO,+BAAuB,OAAO,eAAe,IAAI,GAAG;AAAA,IAChE,gBAAgB;AAAA,IAChB,cAAc;AAAA,EACf,CAAC;AAED,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,KAAK,UAAU;AAAA,MACpB,GAAG;AAAA,MACH,GAAI,gBAAgB,EAAE,UAAU,cAAc,IAAI,CAAC;AAAA,IACpD,CAAC;AAAA,EACF;AACD;AAEA,eAAsB,aAErB,OACA,UACgC;AAChC,QAAM,aAAa,KAAK,iBAAiB,YAAY,QAAW;AAAA,IAC/D,cAAc;AAAA,EACf,CAAC;AACD,QAAM,eAAe,KAAK,iBAAiB,cAAc;AACzD,QAAM,OAAO,SAAS;AAEtB,MAAI,KAAK,OAAO;AACf,UAAM,QAAQ,KAAK;AAEnB,QAAI,CAAC,cAAc;AAClB,aAAO,KAAK,QAAQ,gBAAgB,EAAE,GAAG,MAAM,CAAC;AAAA,IACjD;AAEA,UAAM,QAAQ,MAAM,gBAAgB,KAAK,MAAM,MAAM,WAAW,UAAU;AAC1E,WAAO,KAAK,QAAQ,gBAAgB,EAAE,GAAG,OAAO,OAAO,MAAM,CAAC;AAAA,EAC/D;AAEA,QAAM,SAAU,SAAS,KAA6B,UAAU,CAAC;AAEjE,MAAI,CAAC,cAAc;AAClB,WAAO;AAAA,EACR;AAEA,QAAM,kBAA4C,CAAC;AACnD,aAAW,SAAS,QAAQ;AAC3B,UAAM,QAAQ,MAAM,gBAAgB,KAAK,MAAM,MAAM,WAAW,UAAU;AAC1E,oBAAgB,KAAK;AAAA,MACpB,GAAG;AAAA,MACH,OAAO;AAAA,IACR,CAAC;AAAA,EACF;AAEA,SAAO,MAAM,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,KAAK,MAAM,QAAQ,gBAAgB,EAAE,EAAE;AACjF;AAEA,eAAsB,YAErB,gBAC+B;AAC/B,QAAM,MAAM,KAAK,iBAAiB,wBAAwB,EAAE;AAC5D,QAAM,WACL;AAED,MAAI,CAAC,SAAS,KAAK,GAAG,GAAG;AACxB,UAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG;AAAA,MACtC,SAAS;AAAA,MACT,aACC;AAAA,IACF,CAAC;AAAA,EACF;AAEA,SAAO;AACR;AAEA,eAAsB,iBAErB,OACA,WACgC;AAChC,QAAM,SAAS,KAAK,iBAAiB,QAAQ;AAE7C,MAAI,CAAC,QAAQ;AACZ,WAAO;AAAA,EACR;AAEA,SAAO,MACL,IAAI,CAAC,SAAS;AACd,UAAM,OAAO,KAAK,MAAM;AAExB,QAAI,CAAC,MAAM;AACV;AAAA,IACD;AAEA,UAAM;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACJ,IAAI;AAEJ,WAAO,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,aAAa,EAAE,EAAE;AAAA,EAClD,CAAC,EACA,OAAO,OAAO;AACjB;AAEA,eAAsB,aAErB,OACA,WACgC;AAChC,QAAM,SAAS,KAAK,iBAAiB,QAAQ;AAE7C,MAAI,CAAC,QAAQ;AACZ,WAAO;AAAA,EACR;AAEA,SAAO,MACL,IAAI,CAAC,SAAS;AACd,UAAM,OAAO,KAAK;AAElB,QAAI,CAAC,MAAM;AACV;AAAA,IACD;AAEA,QAAI,MAAM,QAAQ,KAAK,KAAK,GAAG;AAC9B,YAAM,QAAQ,KAAK;AAEnB,YAAM,kBAAkB,MAAM,IAAI,CAAC,SAAS;AAC3C,cAAM,kBAAkB,KAAK,cAAc,CAAC;AAE5C,cAAM,iBAAiB,OAAO;AAAA,UAC7B,gBACE,OAAO,CAAC,EAAE,KAAK,MAAM,MAAM,KAAK,CAAC,EACjC,IAAI,CAAC,EAAE,MAAM,MAAM,MAAM,CAAC,MAAM,SAAS,EAAE,CAAC;AAAA,QAC/C;AAEA,cAAM,EAAE,YAAY,GAAG,KAAK,IAAI;AAChC,eAAO,EAAE,GAAG,MAAM,GAAG,eAAe;AAAA,MACrC,CAAC;AAED,aAAO,EAAE,MAAM,EAAE,GAAG,MAAM,OAAO,gBAAgB,EAAE;AAAA,IACpD;AAEA,QAAI,MAAM,QAAQ,KAAK,cAAc,GAAG;AACvC,YAAM,kBAAkB,KAAK;AAE7B,YAAM,iBAAiB,OAAO;AAAA,QAC7B,gBACE,OAAO,CAAC,EAAE,KAAK,MAAM,MAAM,KAAK,CAAC,EACjC,IAAI,CAAC,EAAE,MAAM,MAAM,MAAM,CAAC,MAAM,SAAS,EAAE,CAAC;AAAA,MAC/C;AAEA,YAAM,EAAE,gBAAgB,GAAG,KAAK,IAAI;AACpC,aAAO,EAAE,MAAM,EAAE,GAAG,MAAM,GAAG,eAAe,EAAE;AAAA,IAC/C;AAEA,WAAO;AAAA,EACR,CAAC,EACA,OAAO,OAAO;AACjB;AAEA,eAAsB,kBAErB,gBAC+B;AAC/B,QAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,QAAM,gBACL,cAAc,WACX,+CACA;AACJ,QAAM,aAAa,KAAK,iBAAiB,eAAe,CAAC,CAAC;AAE1D,MAAI,cAAc,aAAa,CAAC,cAAc,WAAW,WAAW,IAAI;AACvE,UAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,+BAA+B;AAAA,MAC3E,aAAa;AAAA,IACd,CAAC;AAAA,EACF;AAEA,MAAI,cAAc,UAAU;AAC3B,UAAM,WAAW,WAAW,KAAK,CAAC,MAAM,EAAE,iBAAiB,OAAO;AAClE,UAAM,mBAAmB,WAAW;AAAA,MACnC,CAAC,MAAM,EAAE,iBAAiB,oBAAoB,EAAE,UAAU;AAAA,IAC3D;AAEA,QAAI,oBAAoB,CAAC,UAAU;AAClC,YAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,sCAAsC;AAAA,QAClF,aACC;AAAA,MACF,CAAC;AAAA,IACF;AAEA,UAAM,WAAW,WAAW,KAAK,CAAC,MAAM,EAAE,iBAAiB,cAAc;AACzE,UAAM,mBAAmB,WAAW;AAAA,MACnC,CAAC,MAAM,EAAE,iBAAiB,2BAA2B,EAAE,UAAU;AAAA,IAClE;AAEA,QAAI,oBAAoB,CAAC,UAAU;AAClC,YAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,6CAA6C;AAAA,QACzF,aACC;AAAA,MACF,CAAC;AAAA,IACF;AAAA,EACD;AAEA,QAAM,WAAO,+BAAuB,OAAO,eAAe,IAAI,GAAG;AAAA,IAChE,gBAAgB;AAAA,IAChB,cAAc;AAAA,EACf,CAAC;AAED,OAAK,iBAAiB,WAAW,IAAI,CAAC,EAAE,eAAe,cAAc,YAAY,MAAM,MAAM;AAC5F,QAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE,gBAAgB,aAAa;AAC9D,YAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,0BAA0B;AAAA,QACtE,aAAa;AAAA,MACd,CAAC;AAAA,IACF;AAEA,UAAM,gBACL,kBAAkB,aACf,eACA,UAAU,YAAY,WAAW,SAAS,IAAI,aAAa,UAAU;AAEzE,WAAO,EAAE,MAAM,eAAe,OAAO,MAAM;AAAA,EAC5C,CAAC;AAED,iBAAe,OAAO,KAAK,UAAU,IAAI;AAEzC,SAAO;AACR;AAEA,eAAsB,8BAErB,gBAC+B;AAC/B,QAAM,yBAAyB,KAAK;AAAA,IACnC;AAAA,IACA,CAAC;AAAA,EACF;AAEA,QAAM,aAAa,KAAK;AAAA,IACvB;AAAA,IACA,CAAC;AAAA,EACF;AAEA,QAAM,WAAW,WAAW,KAAK,CAAC,SAAS,KAAK,iBAAiB,WAAW,CAAC,CAAC,KAAK,OAAO,KAAK,CAAC;AAChG,QAAM,WAAW,WAAW;AAAA,IAC3B,CAAC,SAAS,KAAK,iBAAiB,kBAAkB,CAAC,CAAC,KAAK,OAAO,KAAK;AAAA,EACtE;AAEA,MAAI,uBAAuB,SAAS,OAAO,KAAK,CAAC,UAAU;AAC1D,UAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,sCAAsC;AAAA,MAClF,aAAa;AAAA,IACd,CAAC;AAAA,EACF;AAEA,MAAI,uBAAuB,SAAS,KAAK,KAAK,CAAC,UAAU;AACxD,UAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,6CAA6C;AAAA,MACzF,aACC;AAAA,IACF,CAAC;AAAA,EACF;AAEA,SAAO;AACR;", "names": []}