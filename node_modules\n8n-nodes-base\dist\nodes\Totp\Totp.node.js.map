{"version": 3, "sources": ["../../../nodes/Totp/Totp.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tINodeExecutionData,\n\tINodeType,\n\tINodeTypeDescription,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes, NodeOperationError } from 'n8n-workflow';\nimport OTPAuth from 'otpauth';\n\nexport class Totp implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'TOTP',\n\t\tname: 'totp',\n\t\ticon: 'fa:fingerprint',\n\t\tgroup: ['transform'],\n\t\tversion: 1,\n\t\tsubtitle: '={{ $parameter[\"operation\"] }}',\n\t\tdescription: 'Generate a time-based one-time password',\n\t\tdefaults: {\n\t\t\tname: 'TOTP',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'totpApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Operation',\n\t\t\t\tname: 'operation',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Generate Secret',\n\t\t\t\t\t\tvalue: 'generateSecret',\n\t\t\t\t\t\taction: 'Generate secret',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'generateSecret',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Options',\n\t\t\t\tname: 'options',\n\t\t\t\ttype: 'collection',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\toperation: ['generateSecret'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdefault: {},\n\t\t\t\tplaceholder: 'Add option',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Algorithm',\n\t\t\t\t\t\tname: 'algorithm',\n\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\tdefault: 'SHA1',\n\t\t\t\t\t\tdescription: 'HMAC hashing algorithm. Defaults to SHA1.',\n\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'SHA1',\n\t\t\t\t\t\t\t\tvalue: 'SHA1',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'SHA224',\n\t\t\t\t\t\t\t\tvalue: 'SHA224',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'SHA256',\n\t\t\t\t\t\t\t\tvalue: 'SHA256',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'SHA3-224',\n\t\t\t\t\t\t\t\tvalue: 'SHA3-224',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'SHA3-256',\n\t\t\t\t\t\t\t\tvalue: 'SHA3-256',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'SHA3-384',\n\t\t\t\t\t\t\t\tvalue: 'SHA3-384',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'SHA3-512',\n\t\t\t\t\t\t\t\tvalue: 'SHA3-512',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'SHA384',\n\t\t\t\t\t\t\t\tvalue: 'SHA384',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'SHA512',\n\t\t\t\t\t\t\t\tvalue: 'SHA512',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Digits',\n\t\t\t\t\t\tname: 'digits',\n\t\t\t\t\t\ttype: 'number',\n\t\t\t\t\t\tdefault: 6,\n\t\t\t\t\t\tdescription: 'Number of digits in the generated TOTP code. Defaults to 6 digits.',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Period',\n\t\t\t\t\t\tname: 'period',\n\t\t\t\t\t\ttype: 'number',\n\t\t\t\t\t\tdefault: 30,\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'How many seconds the generated TOTP code is valid for. Defaults to 30 seconds.',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: INodeExecutionData[] = [];\n\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\tconst credentials = await this.getCredentials<{ label: string; secret: string }>('totpApi');\n\n\t\tif (!credentials.label.includes(':')) {\n\t\t\tthrow new NodeOperationError(this.getNode(), 'Malformed label - expected `issuer:username`');\n\t\t}\n\n\t\tconst options = this.getNodeParameter('options', 0) as {\n\t\t\talgorithm?: string;\n\t\t\tdigits?: number;\n\t\t\tperiod?: number;\n\t\t};\n\n\t\tif (!options.algorithm) options.algorithm = 'SHA1';\n\t\tif (!options.digits) options.digits = 6;\n\t\tif (!options.period) options.period = 30;\n\n\t\tconst [issuer] = credentials.label.split(':');\n\n\t\tconst totp = new OTPAuth.TOTP({\n\t\t\tissuer,\n\t\t\tlabel: credentials.label,\n\t\t\tsecret: credentials.secret,\n\t\t\talgorithm: options.algorithm,\n\t\t\tdigits: options.digits,\n\t\t\tperiod: options.period,\n\t\t});\n\n\t\tconst token = totp.generate();\n\n\t\tconst secondsRemaining =\n\t\t\t(options.period * (1 - ((Date.now() / 1000 / options.period) % 1))) | 0;\n\n\t\tif (operation === 'generateSecret') {\n\t\t\tfor (let i = 0; i < items.length; i++) {\n\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\tthis.helpers.returnJsonArray({ token, secondsRemaining }),\n\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t);\n\n\t\t\t\treturnData.push(...executionData);\n\t\t\t}\n\t\t}\n\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,0BAAwD;AACxD,qBAAoB;AAEb,MAAM,KAA0B;AAAA,EAAhC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,WAAW;AAAA,MACnB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,QAAQ;AAAA,YACT;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,WAAW,CAAC,gBAAgB;AAAA,YAC7B;AAAA,UACD;AAAA,UACA,SAAS,CAAC;AAAA,UACV,aAAa;AAAA,UACb,SAAS;AAAA,YACR;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,cACb,SAAS;AAAA,gBACR;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,gBACA;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,gBACA;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,gBACA;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,gBACA;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,gBACA;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,gBACA;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,gBACA;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,gBACA;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,cACD;AAAA,YACD;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aACC;AAAA,YACF;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAAmC,CAAC;AAE1C,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,UAAM,cAAc,MAAM,KAAK,eAAkD,SAAS;AAE1F,QAAI,CAAC,YAAY,MAAM,SAAS,GAAG,GAAG;AACrC,YAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,8CAA8C;AAAA,IAC5F;AAEA,UAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAMlD,QAAI,CAAC,QAAQ,UAAW,SAAQ,YAAY;AAC5C,QAAI,CAAC,QAAQ,OAAQ,SAAQ,SAAS;AACtC,QAAI,CAAC,QAAQ,OAAQ,SAAQ,SAAS;AAEtC,UAAM,CAAC,MAAM,IAAI,YAAY,MAAM,MAAM,GAAG;AAE5C,UAAM,OAAO,IAAI,eAAAA,QAAQ,KAAK;AAAA,MAC7B;AAAA,MACA,OAAO,YAAY;AAAA,MACnB,QAAQ,YAAY;AAAA,MACpB,WAAW,QAAQ;AAAA,MACnB,QAAQ,QAAQ;AAAA,MAChB,QAAQ,QAAQ;AAAA,IACjB,CAAC;AAED,UAAM,QAAQ,KAAK,SAAS;AAE5B,UAAM,mBACJ,QAAQ,UAAU,IAAM,KAAK,IAAI,IAAI,MAAO,QAAQ,SAAU,KAAO;AAEvE,QAAI,cAAc,kBAAkB;AACnC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,cAAM,gBAAgB,KAAK,QAAQ;AAAA,UAClC,KAAK,QAAQ,gBAAgB,EAAE,OAAO,iBAAiB,CAAC;AAAA,UACxD,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,QACzB;AAEA,mBAAW,KAAK,GAAG,aAAa;AAAA,MACjC;AAAA,IACD;AAEA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": ["OTPAuth"]}