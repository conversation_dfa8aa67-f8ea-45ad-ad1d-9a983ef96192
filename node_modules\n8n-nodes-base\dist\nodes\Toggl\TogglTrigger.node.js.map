{"version": 3, "sources": ["../../../nodes/Toggl/TogglTrigger.node.ts"], "sourcesContent": ["import { DateTime } from 'luxon';\nimport moment from 'moment-timezone';\nimport type {\n\tIPollFunctions,\n\tIDataObject,\n\tINodeExecutionData,\n\tINodeType,\n\tINodeTypeDescription,\n\tJsonObject,\n} from 'n8n-workflow';\nimport { NodeApiError, NodeConnectionTypes, NodeOperationError } from 'n8n-workflow';\n\nimport { togglApiRequest } from './GenericFunctions';\n\nexport class TogglTrigger implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Toggl Trigger',\n\t\tname: 'togglTrigger',\n\t\t// eslint-disable-next-line n8n-nodes-base/node-class-description-icon-not-svg\n\t\ticon: 'file:toggl.png',\n\t\tgroup: ['trigger'],\n\t\tversion: 1,\n\t\tdescription: 'Starts the workflow when Toggl events occur',\n\t\tdefaults: {\n\t\t\tname: 'Toggl Trigger',\n\t\t},\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'togglApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tpolling: true,\n\t\tinputs: [],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Event',\n\t\t\t\tname: 'event',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'New Time Entry',\n\t\t\t\t\t\tvalue: 'newTimeEntry',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\trequired: true,\n\t\t\t\tdefault: 'newTimeEntry',\n\t\t\t},\n\t\t],\n\t};\n\n\tasync poll(this: IPollFunctions): Promise<INodeExecutionData[][] | null> {\n\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\tconst event = this.getNodeParameter('event') as string;\n\t\tlet endpoint: string;\n\n\t\tif (event === 'newTimeEntry') {\n\t\t\tendpoint = '/time_entries';\n\t\t} else {\n\t\t\tthrow new NodeOperationError(this.getNode(), `The defined event \"${event}\" is not supported`);\n\t\t}\n\n\t\tconst qs: IDataObject = {};\n\t\tlet timeEntries = [];\n\t\tqs.start_date = webhookData.lastTimeChecked ?? DateTime.now().toISODate();\n\t\tqs.end_date = moment().format();\n\n\t\ttry {\n\t\t\ttimeEntries = await togglApiRequest.call(this, 'GET', endpoint, {}, qs);\n\t\t\twebhookData.lastTimeChecked = qs.end_date;\n\t\t} catch (error) {\n\t\t\tthrow new NodeApiError(this.getNode(), error as JsonObject);\n\t\t}\n\t\tif (Array.isArray(timeEntries) && timeEntries.length !== 0) {\n\t\t\treturn [this.helpers.returnJsonArray(timeEntries)];\n\t\t}\n\n\t\treturn null;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAyB;AACzB,6BAAmB;AASnB,0BAAsE;AAEtE,8BAAgC;AAEzB,MAAM,aAAkC;AAAA,EAAxC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA;AAAA,MAEN,MAAM;AAAA,MACN,OAAO,CAAC,SAAS;AAAA,MACjB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,SAAS;AAAA,MACT,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,UAAU;AAAA,UACV,SAAS;AAAA,QACV;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,OAAmE;AACxE,UAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,UAAM,QAAQ,KAAK,iBAAiB,OAAO;AAC3C,QAAI;AAEJ,QAAI,UAAU,gBAAgB;AAC7B,iBAAW;AAAA,IACZ,OAAO;AACN,YAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,sBAAsB,KAAK,oBAAoB;AAAA,IAC7F;AAEA,UAAM,KAAkB,CAAC;AACzB,QAAI,cAAc,CAAC;AACnB,OAAG,aAAa,YAAY,mBAAmB,sBAAS,IAAI,EAAE,UAAU;AACxE,OAAG,eAAW,uBAAAA,SAAO,EAAE,OAAO;AAE9B,QAAI;AACH,oBAAc,MAAM,wCAAgB,KAAK,MAAM,OAAO,UAAU,CAAC,GAAG,EAAE;AACtE,kBAAY,kBAAkB,GAAG;AAAA,IAClC,SAAS,OAAO;AACf,YAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,KAAmB;AAAA,IAC3D;AACA,QAAI,MAAM,QAAQ,WAAW,KAAK,YAAY,WAAW,GAAG;AAC3D,aAAO,CAAC,KAAK,QAAQ,gBAAgB,WAAW,CAAC;AAAA,IAClD;AAEA,WAAO;AAAA,EACR;AACD;", "names": ["moment"]}