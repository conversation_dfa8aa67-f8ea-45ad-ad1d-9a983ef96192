{"version": 3, "sources": ["../../../../nodes/Twitter/V1/TweetInterface.ts"], "sourcesContent": ["import type { IDataObject } from 'n8n-workflow';\n\nexport interface ITweet {\n\tauto_populate_reply_metadata?: boolean;\n\tdisplay_coordinates?: boolean;\n\tlat?: number;\n\tlong?: number;\n\tmedia_ids?: string;\n\tpossibly_sensitive?: boolean;\n\tstatus: string;\n\tin_reply_to_status_id?: string;\n}\n\nexport interface ITweetCreate {\n\ttype: 'message_create';\n\tmessage_create: {\n\t\ttarget: {\n\t\t\trecipient_id: string;\n\t\t};\n\t\tmessage_data: {\n\t\t\ttext: string;\n\t\t\tattachment?: IDataObject;\n\t\t};\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}