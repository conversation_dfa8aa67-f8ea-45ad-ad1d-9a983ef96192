import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_GetExecutionFlowSnapshotCommand, se_GetExecutionFlowSnapshotCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class GetExecutionFlowSnapshotCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockAgentRunTimeService", "GetExecutionFlowSnapshot", {})
    .n("BedrockAgentRuntimeClient", "GetExecutionFlowSnapshotCommand")
    .f(void 0, void 0)
    .ser(se_GetExecutionFlowSnapshotCommand)
    .de(de_GetExecutionFlowSnapshotCommand)
    .build() {
}
