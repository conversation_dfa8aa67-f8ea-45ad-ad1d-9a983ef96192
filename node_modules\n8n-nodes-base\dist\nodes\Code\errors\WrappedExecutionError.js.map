{"version": 3, "sources": ["../../../../nodes/Code/errors/WrappedExecutionError.ts"], "sourcesContent": ["import { ApplicationError } from 'n8n-workflow';\n\nexport type WrappableError = Record<string, unknown>;\n\n/**\n * Errors received from the task runner are not instances of Error.\n * This class wraps them in an Error instance and makes all their\n * properties available.\n */\nexport class WrappedExecutionError extends ApplicationError {\n\t[key: string]: unknown;\n\n\tconstructor(error: WrappableError) {\n\t\tconst message = typeof error.message === 'string' ? error.message : 'Unknown error';\n\t\tsuper(message, {\n\t\t\tcause: error,\n\t\t});\n\n\t\tthis.copyErrorProperties(error);\n\t}\n\n\tprivate copyErrorProperties(error: WrappableError) {\n\t\tfor (const key of Object.getOwnPropertyNames(error)) {\n\t\t\tthis[key] = error[key];\n\t\t}\n\t}\n}\n\nexport function isWrappableError(error: unknown): error is WrappableError {\n\treturn typeof error === 'object' && error !== null;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAAiC;AAS1B,MAAM,8BAA8B,qCAAiB;AAAA,EAG3D,YAAY,OAAuB;AAClC,UAAM,UAAU,OAAO,MAAM,YAAY,WAAW,MAAM,UAAU;AACpE,UAAM,SAAS;AAAA,MACd,OAAO;AAAA,IACR,CAAC;AAED,SAAK,oBAAoB,KAAK;AAAA,EAC/B;AAAA,EAEQ,oBAAoB,OAAuB;AAClD,eAAW,OAAO,OAAO,oBAAoB,KAAK,GAAG;AACpD,WAAK,GAAG,IAAI,MAAM,GAAG;AAAA,IACtB;AAAA,EACD;AACD;AAEO,SAAS,iBAAiB,OAAyC;AACzE,SAAO,OAAO,UAAU,YAAY,UAAU;AAC/C;", "names": []}