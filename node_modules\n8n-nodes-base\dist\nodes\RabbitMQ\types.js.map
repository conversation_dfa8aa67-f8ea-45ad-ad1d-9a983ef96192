{"version": 3, "sources": ["../../../nodes/RabbitMQ/types.ts"], "sourcesContent": ["type Argument = {\n\tkey: string;\n\tvalue?: string;\n};\n\ntype Binding = {\n\texchange: string;\n\troutingKey: string;\n};\n\ntype Header = {\n\tkey: string;\n\tvalue?: string;\n};\n\nexport type Options = {\n\tautoDelete: boolean;\n\tassertExchange: boolean;\n\tassertQueue: boolean;\n\tdurable: boolean;\n\texclusive: boolean;\n\targuments: {\n\t\targument: Argument[];\n\t};\n\theaders: {\n\t\theader: Header[];\n\t};\n};\n\ntype ContentOptions =\n\t| {\n\t\t\tcontentIsBinary: true;\n\t  }\n\t| {\n\t\t\tcontentIsBinary: false;\n\t\t\tjsonParseBody: boolean;\n\t\t\tonlyContent: boolean;\n\t  };\n\nexport type TriggerOptions = Options & {\n\tacknowledge:\n\t\t| 'executionFinishes'\n\t\t| 'executionFinishesSuccessfully'\n\t\t| 'immediately'\n\t\t| 'laterMessageNode';\n\tparallelMessages: number;\n\tbinding: {\n\t\tbindings: Binding[];\n\t};\n} & ContentOptions;\n\nexport type RabbitMQCredentials = {\n\thostname: string;\n\tport: number;\n\tusername: string;\n\tpassword: string;\n\tvhost: string;\n} & (\n\t| { ssl: false }\n\t| ({ ssl: true; ca: string } & (\n\t\t\t| { passwordless: false }\n\t\t\t| {\n\t\t\t\t\tpasswordless: true;\n\t\t\t\t\tcert: string;\n\t\t\t\t\tkey: string;\n\t\t\t\t\tpassphrase: string;\n\t\t\t  }\n\t  ))\n);\n\nexport type ExchangeType = 'direct' | 'topic' | 'headers' | 'fanout';\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}