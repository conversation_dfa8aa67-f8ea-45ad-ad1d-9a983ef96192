{"version": 3, "sources": ["../../credentials/XeroOAuth2Api.credentials.ts"], "sourcesContent": ["import type { ICredentialType, INodeProperties } from 'n8n-workflow';\n\nconst scopes = [\n\t'offline_access',\n\t'accounting.transactions',\n\t'accounting.settings',\n\t'accounting.contacts',\n];\n\nexport class XeroOAuth2Api implements ICredentialType {\n\tname = 'xeroOAuth2Api';\n\n\textends = ['oAuth2Api'];\n\n\tdisplayName = 'Xero OAuth2 API';\n\n\tdocumentationUrl = 'xero';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Grant Type',\n\t\t\tname: 'grantType',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: 'authorizationCode',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Authorization URL',\n\t\t\tname: 'authUrl',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: 'https://login.xero.com/identity/connect/authorize',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Access Token URL',\n\t\t\tname: 'accessTokenUrl',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: 'https://identity.xero.com/connect/token',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Scope',\n\t\t\tname: 'scope',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: scopes.join(' '),\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Auth URI Query Parameters',\n\t\t\tname: 'authQueryParameters',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Authentication',\n\t\t\tname: 'authentication',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: 'header',\n\t\t},\n\t];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,MAAM,SAAS;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEO,MAAM,cAAyC;AAAA,EAA/C;AACN,gBAAO;AAEP,mBAAU,CAAC,WAAW;AAEtB,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,OAAO,KAAK,GAAG;AAAA,MACzB;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,IACD;AAAA;AACD;", "names": []}