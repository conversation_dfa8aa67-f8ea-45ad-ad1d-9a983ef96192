{"version": 3, "sources": ["../../../../nodes/Webflow/V2/WebflowV2.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tINodeType,\n\tINodeTypeBaseDescription,\n\tINodeTypeDescription,\n} from 'n8n-workflow';\n\nimport { router } from './actions/router';\nimport { versionDescription } from './actions/versionDescription';\nimport { getSites, getCollections, getFields } from '../GenericFunctions';\n\nexport class WebflowV2 implements INodeType {\n\tdescription: INodeTypeDescription;\n\n\tconstructor(baseDescription: INodeTypeBaseDescription) {\n\t\tthis.description = {\n\t\t\t...baseDescription,\n\t\t\t...versionDescription,\n\t\t\tusableAsTool: true,\n\t\t};\n\t}\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\tgetSites,\n\t\t\tgetCollections,\n\t\t\tgetFields,\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions) {\n\t\treturn await router.call(this);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,oBAAuB;AACvB,gCAAmC;AACnC,8BAAoD;AAE7C,MAAM,UAA+B;AAAA,EAG3C,YAAY,iBAA2C;AAQvD,mBAAU;AAAA,MACT,aAAa;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAbC,SAAK,cAAc;AAAA,MAClB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,cAAc;AAAA,IACf;AAAA,EACD;AAAA,EAUA,MAAM,UAAiC;AACtC,WAAO,MAAM,qBAAO,KAAK,IAAI;AAAA,EAC9B;AACD;", "names": []}