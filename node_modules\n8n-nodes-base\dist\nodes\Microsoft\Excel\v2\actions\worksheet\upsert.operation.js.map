{"version": 3, "sources": ["../../../../../../../nodes/Microsoft/Excel/v2/actions/worksheet/upsert.operation.ts"], "sourcesContent": ["import type {\n\tIDataObject,\n\tIExecuteFunctions,\n\tINodeExecutionData,\n\tINodeProperties,\n} from 'n8n-workflow';\nimport { NodeOperationError } from 'n8n-workflow';\n\nimport { generatePairedItemData, processJsonInput, updateDisplayOptions } from '@utils/utilities';\n\nimport type { ExcelResponse, UpdateSummary } from '../../helpers/interfaces';\nimport {\n\tcheckRange,\n\tprepareOutput,\n\tupdateByAutoMaping,\n\tupdateByDefinedValues,\n} from '../../helpers/utils';\nimport { microsoftApiRequest } from '../../transport';\nimport { workbookRLC, worksheetRLC } from '../common.descriptions';\n\nconst properties: INodeProperties[] = [\n\tworkbookRLC,\n\tworksheetRLC,\n\t{\n\t\tdisplayName: 'Select a Range',\n\t\tname: 'useRange',\n\t\ttype: 'boolean',\n\t\tdefault: false,\n\t},\n\t{\n\t\tdisplayName: 'Range',\n\t\tname: 'range',\n\t\ttype: 'string',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tdataMode: ['autoMap', 'define'],\n\t\t\t\tuseRange: [true],\n\t\t\t},\n\t\t},\n\t\tplaceholder: 'e.g. A1:B2',\n\t\tdefault: '',\n\t\tdescription:\n\t\t\t'The sheet range to read the data from specified using a A1-style notation, has to be specific e.g A1:B5, generic ranges like A:B are not supported. Leave blank to use whole used range in the sheet.',\n\t\thint: 'First row must contain column names',\n\t},\n\t{\n\t\tdisplayName: 'Data Mode',\n\t\tname: 'dataMode',\n\t\ttype: 'options',\n\t\tdefault: 'define',\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Auto-Map Input Data to Columns',\n\t\t\t\tvalue: 'autoMap',\n\t\t\t\tdescription: 'Use when node input properties match destination column names',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Map Each Column Below',\n\t\t\t\tvalue: 'define',\n\t\t\t\tdescription: 'Set the value for each destination column',\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-miscased, n8n-nodes-base/node-param-display-name-wrong-for-dynamic-options\n\t\tdisplayName: 'Column to match on',\n\t\tname: 'columnToMatchOn',\n\t\ttype: 'options',\n\t\tdescription:\n\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\ttypeOptions: {\n\t\t\tloadOptionsDependsOn: ['worksheet.value', 'workbook.value', 'range'],\n\t\t\tloadOptionsMethod: 'getWorksheetColumnRow',\n\t\t},\n\t\tdefault: '',\n\t\thint: \"Used to find the correct row to update. Doesn't get changed.\",\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tdataMode: ['autoMap', 'define'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Value of Column to Match On',\n\t\tname: 'valueToMatchOn',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tdataMode: ['define'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Values to Send',\n\t\tname: 'fieldsUi',\n\t\tplaceholder: 'Add Field',\n\t\ttype: 'fixedCollection',\n\t\ttypeOptions: {\n\t\t\tmultipleValues: true,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tdataMode: ['define'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Field',\n\t\t\t\tname: 'values',\n\t\t\t\tvalues: [\n\t\t\t\t\t{\n\t\t\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-wrong-for-dynamic-options\n\t\t\t\t\t\tdisplayName: 'Column',\n\t\t\t\t\t\tname: 'column',\n\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\tloadOptionsDependsOn: ['columnToMatchOn', 'range'],\n\t\t\t\t\t\t\tloadOptionsMethod: 'getWorksheetColumnRowSkipColumnToMatchOn',\n\t\t\t\t\t\t},\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Value',\n\t\t\t\t\t\tname: 'fieldValue',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Options',\n\t\tname: 'options',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add option',\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Append After Selected Range',\n\t\t\t\tname: 'appendAfterSelectedRange',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether to append data after the selected range or used range',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\t'/dataMode': ['autoMap', 'define'],\n\t\t\t\t\t\t'/useRange': [true],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'RAW Data',\n\t\t\t\tname: 'rawData',\n\t\t\t\ttype: 'boolean',\n\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-default-wrong-for-boolean\n\t\t\t\tdefault: 0,\n\t\t\t\tdescription:\n\t\t\t\t\t'Whether the data should be returned RAW instead of parsed into keys according to their header',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Data Property',\n\t\t\t\tname: 'dataProperty',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: 'data',\n\t\t\t\trequired: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\trawData: [true],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdescription: 'The name of the property into which to write the RAW data',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Update All Matches',\n\t\t\t\tname: 'updateAll',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether to update all matching rows or just the first match',\n\t\t\t},\n\t\t],\n\t},\n];\n\nconst displayOptions = {\n\tshow: {\n\t\tresource: ['worksheet'],\n\t\toperation: ['upsert'],\n\t},\n};\n\nexport const description = updateDisplayOptions(displayOptions, properties);\n\nexport async function execute(\n\tthis: IExecuteFunctions,\n\titems: INodeExecutionData[],\n): Promise<INodeExecutionData[]> {\n\tconst returnData: INodeExecutionData[] = [];\n\tconst nodeVersion = this.getNode().typeVersion;\n\n\ttry {\n\t\tconst workbookId = this.getNodeParameter('workbook', 0, undefined, {\n\t\t\textractValue: true,\n\t\t}) as string;\n\n\t\tconst worksheetId = this.getNodeParameter('worksheet', 0, undefined, {\n\t\t\textractValue: true,\n\t\t}) as string;\n\n\t\tlet range = this.getNodeParameter('range', 0, '') as string;\n\t\tcheckRange(this.getNode(), range);\n\n\t\tconst dataMode = this.getNodeParameter('dataMode', 0) as string;\n\n\t\tlet worksheetData: IDataObject = {};\n\n\t\tif (range && dataMode !== 'raw') {\n\t\t\tworksheetData = await microsoftApiRequest.call(\n\t\t\t\tthis,\n\t\t\t\t'PATCH',\n\t\t\t\t`/drive/items/${workbookId}/workbook/worksheets/${worksheetId}/range(address='${range}')`,\n\t\t\t);\n\t\t}\n\n\t\t//get used range if range not provided; if 'raw' mode fetch only address information\n\t\tif (range === '') {\n\t\t\tconst query: IDataObject = {};\n\t\t\tif (dataMode === 'raw') {\n\t\t\t\tquery.select = 'address';\n\t\t\t}\n\n\t\t\tworksheetData = await microsoftApiRequest.call(\n\t\t\t\tthis,\n\t\t\t\t'GET',\n\t\t\t\t`/drive/items/${workbookId}/workbook/worksheets/${worksheetId}/usedRange`,\n\t\t\t\tundefined,\n\t\t\t\tquery,\n\t\t\t);\n\n\t\t\trange = (worksheetData.address as string).split('!')[1];\n\t\t}\n\n\t\tlet responseData;\n\t\tif (dataMode === 'raw') {\n\t\t\tconst data = this.getNodeParameter('data', 0);\n\n\t\t\tconst values = processJsonInput(data, 'Data') as string[][];\n\n\t\t\tresponseData = await microsoftApiRequest.call(\n\t\t\t\tthis,\n\t\t\t\t'PATCH',\n\t\t\t\t`/drive/items/${workbookId}/workbook/worksheets/${worksheetId}/range(address='${range}')`,\n\t\t\t\t{ values },\n\t\t\t);\n\t\t}\n\n\t\tif (\n\t\t\tdataMode !== 'raw' &&\n\t\t\t(worksheetData.values === undefined || (worksheetData.values as string[][]).length <= 1)\n\t\t) {\n\t\t\tthrow new NodeOperationError(\n\t\t\t\tthis.getNode(),\n\t\t\t\t'No data found in the specified range, mapping not possible, you can use raw mode instead to update selected range',\n\t\t\t);\n\t\t}\n\n\t\tconst updateAll = this.getNodeParameter('options.updateAll', 0, false) as boolean;\n\n\t\tlet updateSummary: UpdateSummary = {\n\t\t\tupdatedData: [],\n\t\t\tupdatedRows: [],\n\t\t\tappendData: [],\n\t\t};\n\n\t\tif (dataMode === 'define') {\n\t\t\tupdateSummary = updateByDefinedValues.call(\n\t\t\t\tthis,\n\t\t\t\titems.length,\n\t\t\t\tworksheetData.values as string[][],\n\t\t\t\tupdateAll,\n\t\t\t);\n\t\t}\n\n\t\tif (dataMode === 'autoMap') {\n\t\t\tconst columnToMatchOn = this.getNodeParameter('columnToMatchOn', 0) as string;\n\n\t\t\tif (!items.some(({ json }) => json[columnToMatchOn] !== undefined)) {\n\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t`Any item in input data contains column '${columnToMatchOn}', that is selected to match on`,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tupdateSummary = updateByAutoMaping(\n\t\t\t\titems,\n\t\t\t\tworksheetData.values as string[][],\n\t\t\t\tcolumnToMatchOn,\n\t\t\t\tupdateAll,\n\t\t\t);\n\t\t}\n\n\t\tconst appendAfterSelectedRange = this.getNodeParameter(\n\t\t\t'options.appendAfterSelectedRange',\n\t\t\t0,\n\t\t\tfalse,\n\t\t) as boolean;\n\n\t\t//remove empty rows from the end\n\t\tif (nodeVersion > 2 && !appendAfterSelectedRange && updateSummary.updatedData.length) {\n\t\t\tfor (let i = updateSummary.updatedData.length - 1; i >= 0; i--) {\n\t\t\t\tif (\n\t\t\t\t\tupdateSummary.updatedData[i].every(\n\t\t\t\t\t\t(item) => item === '' || item === undefined || item === null,\n\t\t\t\t\t)\n\t\t\t\t) {\n\t\t\t\t\tupdateSummary.updatedData.pop();\n\t\t\t\t} else {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (updateSummary.appendData.length) {\n\t\t\tconst appendValues: string[][] = [];\n\t\t\tconst columnsRow = (worksheetData.values as string[][])[0];\n\n\t\t\tfor (const [index, item] of updateSummary.appendData.entries()) {\n\t\t\t\tconst updateRow: string[] = [];\n\n\t\t\t\tfor (const column of columnsRow) {\n\t\t\t\t\tupdateRow.push(item[column] as string);\n\t\t\t\t}\n\n\t\t\t\tappendValues.push(updateRow);\n\t\t\t\tupdateSummary.updatedRows.push(index + updateSummary.updatedData.length);\n\t\t\t}\n\n\t\t\tupdateSummary.updatedData = updateSummary.updatedData.concat(appendValues);\n\t\t\tconst [rangeFrom, rangeTo] = range.split(':');\n\n\t\t\tconst cellDataTo = rangeTo.match(/([a-zA-Z]{1,10})([0-9]{0,10})/) || [];\n\t\t\tlet lastRow = cellDataTo[2];\n\n\t\t\tif (nodeVersion > 2 && !appendAfterSelectedRange) {\n\t\t\t\tconst { address } = await microsoftApiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t`/drive/items/${workbookId}/workbook/worksheets/${worksheetId}/usedRange`,\n\t\t\t\t\tundefined,\n\t\t\t\t\t{ select: 'address' },\n\t\t\t\t);\n\n\t\t\t\tconst addressTo = (address as string).split('!')[1].split(':')[1];\n\t\t\t\tlastRow = addressTo.match(/([a-zA-Z]{1,10})([0-9]{0,10})/)![2];\n\t\t\t}\n\n\t\t\trange = `${rangeFrom}:${cellDataTo[1]}${Number(lastRow) + appendValues.length}`;\n\t\t}\n\n\t\tresponseData = await microsoftApiRequest.call(\n\t\t\tthis,\n\t\t\t'PATCH',\n\t\t\t`/drive/items/${workbookId}/workbook/worksheets/${worksheetId}/range(address='${range}')`,\n\t\t\t{ values: updateSummary.updatedData },\n\t\t);\n\n\t\tconst { updatedRows } = updateSummary;\n\n\t\tconst rawData = this.getNodeParameter('options.rawData', 0, false) as boolean;\n\t\tconst dataProperty = this.getNodeParameter('options.dataProperty', 0, 'data') as string;\n\n\t\treturnData.push(\n\t\t\t...prepareOutput.call(this, this.getNode(), responseData as ExcelResponse, {\n\t\t\t\tupdatedRows,\n\t\t\t\trawData,\n\t\t\t\tdataProperty,\n\t\t\t}),\n\t\t);\n\t} catch (error) {\n\t\tif (this.continueOnFail()) {\n\t\t\tconst itemData = generatePairedItemData(this.getInputData().length);\n\t\t\tconst executionErrorData = this.helpers.constructExecutionMetaData(\n\t\t\t\tthis.helpers.returnJsonArray({ error: error.message }),\n\t\t\t\t{ itemData },\n\t\t\t);\n\t\t\treturnData.push(...executionErrorData);\n\t\t} else {\n\t\t\tthrow error;\n\t\t}\n\t}\n\n\treturn returnData;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,0BAAmC;AAEnC,uBAA+E;AAG/E,mBAKO;AACP,uBAAoC;AACpC,oBAA0C;AAE1C,MAAM,aAAgC;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW,QAAQ;AAAA,QAC9B,UAAU,CAAC,IAAI;AAAA,MAChB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,IACb,SAAS;AAAA,IACT,aACC;AAAA,IACD,MAAM;AAAA,EACP;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA;AAAA,IAEC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aACC;AAAA,IACD,aAAa;AAAA,MACZ,sBAAsB,CAAC,mBAAmB,kBAAkB,OAAO;AAAA,MACnE,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,IACT,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW,QAAQ;AAAA,MAC/B;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,MACpB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,gBAAgB;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,MACpB;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,QAAQ;AAAA,UACP;AAAA;AAAA,YAEC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,aACC;AAAA,YACD,aAAa;AAAA,cACZ,sBAAsB,CAAC,mBAAmB,OAAO;AAAA,cACjD,mBAAmB;AAAA,YACpB;AAAA,YACA,SAAS;AAAA,UACV;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,gBAAgB;AAAA,UACf,MAAM;AAAA,YACL,aAAa,CAAC,WAAW,QAAQ;AAAA,YACjC,aAAa,CAAC,IAAI;AAAA,UACnB;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA;AAAA,QAEN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,QACV,gBAAgB;AAAA,UACf,MAAM;AAAA,YACL,SAAS,CAAC,IAAI;AAAA,UACf;AAAA,QACD;AAAA,QACA,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AACD;AAEA,MAAM,iBAAiB;AAAA,EACtB,MAAM;AAAA,IACL,UAAU,CAAC,WAAW;AAAA,IACtB,WAAW,CAAC,QAAQ;AAAA,EACrB;AACD;AAEO,MAAM,kBAAc,uCAAqB,gBAAgB,UAAU;AAE1E,eAAsB,QAErB,OACgC;AAChC,QAAM,aAAmC,CAAC;AAC1C,QAAM,cAAc,KAAK,QAAQ,EAAE;AAEnC,MAAI;AACH,UAAM,aAAa,KAAK,iBAAiB,YAAY,GAAG,QAAW;AAAA,MAClE,cAAc;AAAA,IACf,CAAC;AAED,UAAM,cAAc,KAAK,iBAAiB,aAAa,GAAG,QAAW;AAAA,MACpE,cAAc;AAAA,IACf,CAAC;AAED,QAAI,QAAQ,KAAK,iBAAiB,SAAS,GAAG,EAAE;AAChD,iCAAW,KAAK,QAAQ,GAAG,KAAK;AAEhC,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAEpD,QAAI,gBAA6B,CAAC;AAElC,QAAI,SAAS,aAAa,OAAO;AAChC,sBAAgB,MAAM,qCAAoB;AAAA,QACzC;AAAA,QACA;AAAA,QACA,gBAAgB,UAAU,wBAAwB,WAAW,mBAAmB,KAAK;AAAA,MACtF;AAAA,IACD;AAGA,QAAI,UAAU,IAAI;AACjB,YAAM,QAAqB,CAAC;AAC5B,UAAI,aAAa,OAAO;AACvB,cAAM,SAAS;AAAA,MAChB;AAEA,sBAAgB,MAAM,qCAAoB;AAAA,QACzC;AAAA,QACA;AAAA,QACA,gBAAgB,UAAU,wBAAwB,WAAW;AAAA,QAC7D;AAAA,QACA;AAAA,MACD;AAEA,cAAS,cAAc,QAAmB,MAAM,GAAG,EAAE,CAAC;AAAA,IACvD;AAEA,QAAI;AACJ,QAAI,aAAa,OAAO;AACvB,YAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAE5C,YAAM,aAAS,mCAAiB,MAAM,MAAM;AAE5C,qBAAe,MAAM,qCAAoB;AAAA,QACxC;AAAA,QACA;AAAA,QACA,gBAAgB,UAAU,wBAAwB,WAAW,mBAAmB,KAAK;AAAA,QACrF,EAAE,OAAO;AAAA,MACV;AAAA,IACD;AAEA,QACC,aAAa,UACZ,cAAc,WAAW,UAAc,cAAc,OAAsB,UAAU,IACrF;AACD,YAAM,IAAI;AAAA,QACT,KAAK,QAAQ;AAAA,QACb;AAAA,MACD;AAAA,IACD;AAEA,UAAM,YAAY,KAAK,iBAAiB,qBAAqB,GAAG,KAAK;AAErE,QAAI,gBAA+B;AAAA,MAClC,aAAa,CAAC;AAAA,MACd,aAAa,CAAC;AAAA,MACd,YAAY,CAAC;AAAA,IACd;AAEA,QAAI,aAAa,UAAU;AAC1B,sBAAgB,mCAAsB;AAAA,QACrC;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,QACd;AAAA,MACD;AAAA,IACD;AAEA,QAAI,aAAa,WAAW;AAC3B,YAAM,kBAAkB,KAAK,iBAAiB,mBAAmB,CAAC;AAElE,UAAI,CAAC,MAAM,KAAK,CAAC,EAAE,KAAK,MAAM,KAAK,eAAe,MAAM,MAAS,GAAG;AACnE,cAAM,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,2CAA2C,eAAe;AAAA,QAC3D;AAAA,MACD;AAEA,0BAAgB;AAAA,QACf;AAAA,QACA,cAAc;AAAA,QACd;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAEA,UAAM,2BAA2B,KAAK;AAAA,MACrC;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAGA,QAAI,cAAc,KAAK,CAAC,4BAA4B,cAAc,YAAY,QAAQ;AACrF,eAAS,IAAI,cAAc,YAAY,SAAS,GAAG,KAAK,GAAG,KAAK;AAC/D,YACC,cAAc,YAAY,CAAC,EAAE;AAAA,UAC5B,CAAC,SAAS,SAAS,MAAM,SAAS,UAAa,SAAS;AAAA,QACzD,GACC;AACD,wBAAc,YAAY,IAAI;AAAA,QAC/B,OAAO;AACN;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,QAAI,cAAc,WAAW,QAAQ;AACpC,YAAM,eAA2B,CAAC;AAClC,YAAM,aAAc,cAAc,OAAsB,CAAC;AAEzD,iBAAW,CAAC,OAAO,IAAI,KAAK,cAAc,WAAW,QAAQ,GAAG;AAC/D,cAAM,YAAsB,CAAC;AAE7B,mBAAW,UAAU,YAAY;AAChC,oBAAU,KAAK,KAAK,MAAM,CAAW;AAAA,QACtC;AAEA,qBAAa,KAAK,SAAS;AAC3B,sBAAc,YAAY,KAAK,QAAQ,cAAc,YAAY,MAAM;AAAA,MACxE;AAEA,oBAAc,cAAc,cAAc,YAAY,OAAO,YAAY;AACzE,YAAM,CAAC,WAAW,OAAO,IAAI,MAAM,MAAM,GAAG;AAE5C,YAAM,aAAa,QAAQ,MAAM,+BAA+B,KAAK,CAAC;AACtE,UAAI,UAAU,WAAW,CAAC;AAE1B,UAAI,cAAc,KAAK,CAAC,0BAA0B;AACjD,cAAM,EAAE,QAAQ,IAAI,MAAM,qCAAoB;AAAA,UAC7C;AAAA,UACA;AAAA,UACA,gBAAgB,UAAU,wBAAwB,WAAW;AAAA,UAC7D;AAAA,UACA,EAAE,QAAQ,UAAU;AAAA,QACrB;AAEA,cAAM,YAAa,QAAmB,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AAChE,kBAAU,UAAU,MAAM,+BAA+B,EAAG,CAAC;AAAA,MAC9D;AAEA,cAAQ,GAAG,SAAS,IAAI,WAAW,CAAC,CAAC,GAAG,OAAO,OAAO,IAAI,aAAa,MAAM;AAAA,IAC9E;AAEA,mBAAe,MAAM,qCAAoB;AAAA,MACxC;AAAA,MACA;AAAA,MACA,gBAAgB,UAAU,wBAAwB,WAAW,mBAAmB,KAAK;AAAA,MACrF,EAAE,QAAQ,cAAc,YAAY;AAAA,IACrC;AAEA,UAAM,EAAE,YAAY,IAAI;AAExB,UAAM,UAAU,KAAK,iBAAiB,mBAAmB,GAAG,KAAK;AACjE,UAAM,eAAe,KAAK,iBAAiB,wBAAwB,GAAG,MAAM;AAE5E,eAAW;AAAA,MACV,GAAG,2BAAc,KAAK,MAAM,KAAK,QAAQ,GAAG,cAA+B;AAAA,QAC1E;AAAA,QACA;AAAA,QACA;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD,SAAS,OAAO;AACf,QAAI,KAAK,eAAe,GAAG;AAC1B,YAAM,eAAW,yCAAuB,KAAK,aAAa,EAAE,MAAM;AAClE,YAAM,qBAAqB,KAAK,QAAQ;AAAA,QACvC,KAAK,QAAQ,gBAAgB,EAAE,OAAO,MAAM,QAAQ,CAAC;AAAA,QACrD,EAAE,SAAS;AAAA,MACZ;AACA,iBAAW,KAAK,GAAG,kBAAkB;AAAA,IACtC,OAAO;AACN,YAAM;AAAA,IACP;AAAA,EACD;AAEA,SAAO;AACR;", "names": []}