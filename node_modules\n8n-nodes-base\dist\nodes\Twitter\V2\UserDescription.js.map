{"version": 3, "sources": ["../../../../nodes/Twitter/V2/UserDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const userOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'searchUser',\n\t\t\t\tdescription: 'Retrieve a user by username',\n\t\t\t\taction: 'Get User',\n\t\t\t},\n\t\t],\n\t\tdefault: 'searchUser',\n\t},\n];\n\nexport const userFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                                user:searchUser                        */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'User',\n\t\tname: 'user',\n\t\ttype: 'resourceLocator',\n\t\tdefault: { mode: 'username', value: '' },\n\t\trequired: true,\n\t\tdescription: 'The user you want to search',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['searchUser'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t\thide: {\n\t\t\t\tme: [true],\n\t\t\t},\n\t\t},\n\t\tmodes: [\n\t\t\t{\n\t\t\t\tdisplayName: 'By Username',\n\t\t\t\tname: 'username',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidation: [],\n\t\t\t\tplaceholder: 'e.g. n8n',\n\t\t\t\turl: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'By ID',\n\t\t\t\tname: 'id',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidation: [],\n\t\t\t\tplaceholder: 'e.g. 1068479892537384960',\n\t\t\t\turl: '',\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Me',\n\t\tname: 'me',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['searchUser'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription: 'Whether you want to search the authenticated user',\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,iBAAoC;AAAA,EAChD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,aAAgC;AAAA;AAAA;AAAA;AAAA,EAI5C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,EAAE,MAAM,YAAY,OAAO,GAAG;AAAA,IACvC,UAAU;AAAA,IACV,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,YAAY;AAAA,QACxB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,MACA,MAAM;AAAA,QACL,IAAI,CAAC,IAAI;AAAA,MACV;AAAA,IACD;AAAA,IACA,OAAO;AAAA,MACN;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,YAAY,CAAC;AAAA,QACb,aAAa;AAAA,QACb,KAAK;AAAA,MACN;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,YAAY,CAAC;AAAA,QACb,aAAa;AAAA,QACb,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,YAAY;AAAA,QACxB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AACD;", "names": []}