{"version": 3, "sources": ["../../../nodes/Harvest/UserDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nconst resource = ['user'];\n\nexport const userOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource,\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Create',\n\t\t\t\tvalue: 'create',\n\t\t\t\tdescription: 'Create a user',\n\t\t\t\taction: 'Create a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'delete',\n\t\t\t\tdescription: 'Delete a user',\n\t\t\t\taction: 'Delete a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\tdescription: 'Get data of a user',\n\t\t\t\taction: 'Get data of a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Get data of many users',\n\t\t\t\taction: 'Get data of all users',\n\t\t\t},\n\n\t\t\t{\n\t\t\t\tname: 'Me',\n\t\t\t\tvalue: 'me',\n\t\t\t\tdescription: 'Get data of authenticated user',\n\t\t\t\taction: 'Get data of authenticated user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Update',\n\t\t\t\tvalue: 'update',\n\t\t\t\tdescription: 'Update a user',\n\t\t\t\taction: 'Update a user',\n\t\t\t},\n\t\t],\n\t\tdefault: 'me',\n\t},\n];\n\nexport const userFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                                user:getAll                                 */\n\t/* -------------------------------------------------------------------------- */\n\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource,\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource,\n\t\t\t\toperation: ['getAll'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 100,\n\t\t},\n\t\tdefault: 100,\n\t\tdescription: 'Max number of results to return',\n\t},\n\t{\n\t\tdisplayName: 'Filters',\n\t\tname: 'filters',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Filter',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource,\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Is Active',\n\t\t\t\tname: 'is_active',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: true,\n\t\t\t\tdescription: 'Whether to only return active users and false to return inactive users',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Updated Since',\n\t\t\t\tname: 'updated_since',\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Only return users belonging to the user with the given ID',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Page',\n\t\t\t\tname: 'page',\n\t\t\t\ttype: 'number',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tminValue: 1,\n\t\t\t\t},\n\t\t\t\tdefault: 1,\n\t\t\t\tdescription: 'The page number to use in pagination',\n\t\t\t},\n\t\t],\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                user:get                                    */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'User ID',\n\t\tname: 'id',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['get'],\n\t\t\t\tresource,\n\t\t\t},\n\t\t},\n\t\tdescription: 'The ID of the user you are retrieving',\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                user:delete                                 */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'User ID',\n\t\tname: 'id',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['delete'],\n\t\t\t\tresource,\n\t\t\t},\n\t\t},\n\t\tdescription: 'The ID of the user you want to delete',\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                user:create                                 */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'First Name',\n\t\tname: 'firstName',\n\t\ttype: 'string',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['create'],\n\t\t\t\tresource,\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdescription: 'The first name of the user',\n\t},\n\t{\n\t\tdisplayName: 'Last Name',\n\t\tname: 'lastName',\n\t\ttype: 'string',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['create'],\n\t\t\t\tresource,\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdescription: 'The last name of the user',\n\t},\n\t{\n\t\tdisplayName: 'Email',\n\t\tname: 'email',\n\t\ttype: 'string',\n\t\tplaceholder: '<EMAIL>',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['create'],\n\t\t\t\tresource,\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdescription: 'The email of the user',\n\t},\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['create'],\n\t\t\t\tresource,\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Can Create Invoices',\n\t\t\t\tname: 'can_create_invoices',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user can create invoices. Only applicable to Project Managers.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Can Create Projects',\n\t\t\t\tname: 'can_create_projects',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user can create projects. Only applicable to Project Managers.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Can See Rates',\n\t\t\t\tname: 'can_see_rates',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription:\n\t\t\t\t\t'Whether the user can see billable rates on projects. Only applicable to Project Managers.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Cost Rate',\n\t\t\t\tname: 'cost_rate',\n\t\t\t\ttype: 'number',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tminValue: 0,\n\t\t\t\t},\n\t\t\t\tdefault: 0,\n\t\t\t\tdescription:\n\t\t\t\t\t'The cost rate to use for this user when calculating a project’s costs vs billable amount',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Default Hourly Rate',\n\t\t\t\tname: 'default_hourly_rate',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '0',\n\t\t\t\tdescription: 'The billable rate to use for this user when they are added to a project',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Has Access To All Future Projects',\n\t\t\t\tname: 'has_access_to_all_future_projects',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user should be automatically added to future projects',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Is Active',\n\t\t\t\tname: 'is_active',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: true,\n\t\t\t\tdescription: 'Whether the user is active or archived',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Is Admin',\n\t\t\t\tname: 'is_admin',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user has Admin permissions',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Is Contractor',\n\t\t\t\tname: 'is_contractor',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user is a contractor or an employee',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Is Project Manager',\n\t\t\t\tname: 'is_project_manager',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user has Project Manager permissions',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Roles',\n\t\t\t\tname: 'roles',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The role names assigned to this person',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Timezone',\n\t\t\t\tname: 'timezone',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-description-url-missing-protocol\n\t\t\t\tdescription:\n\t\t\t\t\t'The user’s timezone. Defaults to the company’s timezone. See a list of <a href=\"/api-v2/introduction/overview/supported-timezones/\">supported time zones</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Weekly Capacity',\n\t\t\t\tname: 'weekly_capacity',\n\t\t\t\ttype: 'number',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tminValue: 0,\n\t\t\t\t},\n\t\t\t\tdefault: 126000,\n\t\t\t\tdescription:\n\t\t\t\t\t'The number of hours per week this person is available to work in seconds. Defaults to <code class=\"language-plaintext highlighter-rouge\">126000</code> seconds (35 hours).',\n\t\t\t},\n\t\t],\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                user:update                                 */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Time Entry ID',\n\t\tname: 'id',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['update'],\n\t\t\t\tresource,\n\t\t\t},\n\t\t},\n\t\tdescription: 'The ID of the time entry to update',\n\t},\n\t{\n\t\tdisplayName: 'Update Fields',\n\t\tname: 'updateFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['update'],\n\t\t\t\tresource,\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Can Create Invoices',\n\t\t\t\tname: 'can_create_invoices',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user can create invoices. Only applicable to Project Managers.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Can Create Projects',\n\t\t\t\tname: 'can_create_projects',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user can create projects. Only applicable to Project Managers.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Can See Rates',\n\t\t\t\tname: 'can_see_rates',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription:\n\t\t\t\t\t'Whether the user can see billable rates on projects. Only applicable to Project Managers.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Cost Rate',\n\t\t\t\tname: 'cost_rate',\n\t\t\t\ttype: 'number',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tminValue: 0,\n\t\t\t\t},\n\t\t\t\tdefault: 0,\n\t\t\t\tdescription:\n\t\t\t\t\t'The cost rate to use for this user when calculating a project’s costs vs billable amount',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Default Hourly Rate',\n\t\t\t\tname: 'default_hourly_rate',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '0',\n\t\t\t\tdescription: 'The billable rate to use for this user when they are added to a project',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Email',\n\t\t\t\tname: 'email',\n\t\t\t\ttype: 'string',\n\t\t\t\tplaceholder: '<EMAIL>',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The user email',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'First Name',\n\t\t\t\tname: 'first_name',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The user first name',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Has Access To All Future Projects',\n\t\t\t\tname: 'has_access_to_all_future_projects',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user should be automatically added to future projects',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Is Active',\n\t\t\t\tname: 'is_active',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: true,\n\t\t\t\tdescription: 'Whether the user is active or archived',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Is Admin',\n\t\t\t\tname: 'is_admin',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user has Admin permissions',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Is Contractor',\n\t\t\t\tname: 'is_contractor',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user is a contractor or an employee',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Is Project Manager',\n\t\t\t\tname: 'is_project_manager',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user has Project Manager permissions',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Last Name',\n\t\t\t\tname: 'last_name',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The user last name',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Roles',\n\t\t\t\tname: 'roles',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The role names assigned to this person',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Timezone',\n\t\t\t\tname: 'timezone',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-description-url-missing-protocol\n\t\t\t\tdescription:\n\t\t\t\t\t'The user’s timezone. Defaults to the company’s timezone. See a list of <a href=\"/api-v2/introduction/overview/supported-timezones/\">supported time zones</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Weekly Capacity',\n\t\t\t\tname: 'weekly_capacity',\n\t\t\t\ttype: 'number',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tminValue: 0,\n\t\t\t\t},\n\t\t\t\tdefault: 126000,\n\t\t\t\tdescription:\n\t\t\t\t\t'The number of hours per week this person is available to work in seconds. Defaults to <code class=\"language-plaintext highlighter-rouge\">126000</code> seconds (35 hours).',\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,MAAM,WAAW,CAAC,MAAM;AAEjB,MAAM,iBAAoC;AAAA,EAChD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL;AAAA,MACD;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MAEA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,aAAgC;AAAA;AAAA;AAAA;AAAA,EAK5C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL;AAAA,QACA,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL;AAAA,QACA,WAAW,CAAC,QAAQ;AAAA,QACpB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL;AAAA,QACA,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,KAAK;AAAA,QACjB;AAAA,MACD;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB;AAAA,MACD;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB;AAAA,MACD;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB;AAAA,MACD;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB;AAAA,MACD;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB;AAAA,MACD;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA;AAAA,QAET,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB;AAAA,MACD;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB;AAAA,MACD;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA;AAAA,QAET,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AACD;", "names": []}