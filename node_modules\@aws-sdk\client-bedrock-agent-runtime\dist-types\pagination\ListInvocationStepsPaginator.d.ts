import { Paginator } from "@smithy/types";
import { ListInvocationStepsCommandInput, ListInvocationStepsCommandOutput } from "../commands/ListInvocationStepsCommand";
import { BedrockAgentRuntimePaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListInvocationSteps: (config: BedrockAgentRuntimePaginationConfiguration, input: ListInvocationStepsCommandInput, ...rest: any[]) => Paginator<ListInvocationStepsCommandOutput>;
