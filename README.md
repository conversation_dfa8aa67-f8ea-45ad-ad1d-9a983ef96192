# n8n Server Setup

This project contains a local n8n server setup for workflow automation.

## What is n8n?

n8n is a free and open-source workflow automation tool that allows you to connect different services and automate tasks. It provides a visual interface for creating workflows with nodes that represent different services and actions.

## Getting Started

### Prerequisites

- Node.js (version 18 or higher)
- npm (comes with Node.js)

### Installation

The dependencies are already installed. If you need to reinstall them:

```bash
npm install
```

### Starting the Server

#### Option 1: Basic Start
```bash
npm start
```

#### Option 2: Development Mode (with tunnel for webhooks)
```bash
npm run dev
```

#### Option 3: Direct n8n command
```bash
npx n8n start
```

### Accessing n8n

Once the server is running, open your browser and navigate to:
```
http://localhost:5678
```

### Available Scripts

- `npm start` - Start n8n server
- `npm run dev` - Start n8n with tunnel (useful for webhooks)
- `npm run setup` - Run n8n setup wizard
- `npm run import` - Import workflows
- `npm run export` - Export workflows

### Configuration

The server configuration is stored in the `.env` file. You can modify settings like:

- Port number (default: 5678)
- Database type (default: SQLite)
- Authentication settings
- Logging level

### First Time Setup

1. Start the server with `npm start`
2. Open http://localhost:5678 in your browser
3. Follow the setup wizard to create your admin account
4. Start creating workflows!

### Data Storage

By default, n8n uses SQLite database stored in `database.sqlite` file. All your workflows and execution data will be saved there.

### Stopping the Server

Press `Ctrl+C` in the terminal where the server is running.

## Learn More

- [n8n Documentation](https://docs.n8n.io/)
- [n8n Community](https://community.n8n.io/)
- [n8n GitHub](https://github.com/n8n-io/n8n)
