{"version": 3, "sources": ["../../../nodes/UnleashedSoftware/UnleashedSoftware.node.ts"], "sourcesContent": ["import moment from 'moment-timezone';\nimport {\n\ttype IExecuteFunctions,\n\ttype IDataObject,\n\ttype INodeExecutionData,\n\ttype INodeType,\n\ttype INodeTypeDescription,\n\tNodeConnectionTypes,\n} from 'n8n-workflow';\n\nimport {\n\tconvertNETDates,\n\tunleashedApiRequest,\n\tunleashedApiRequestAllItems,\n} from './GenericFunctions';\nimport { salesOrderFields, salesOrderOperations } from './SalesOrderDescription';\nimport { stockOnHandFields, stockOnHandOperations } from './StockOnHandDescription';\n\nexport class UnleashedSoftware implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Unleashed Software',\n\t\tname: 'unleashedSoftware',\n\t\tgroup: ['transform'],\n\t\tsubtitle: '={{$parameter[\"operation\"] + \":\" + $parameter[\"resource\"]}}',\n\t\t// eslint-disable-next-line n8n-nodes-base/node-class-description-icon-not-svg\n\t\ticon: 'file:unleashedSoftware.png',\n\t\tversion: 1,\n\t\tdescription: 'Consume Unleashed Software API',\n\t\tdefaults: {\n\t\t\tname: 'Unleashed Software',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'unleashedSoftwareApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Sales Order',\n\t\t\t\t\t\tvalue: 'salesOrder',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Stock On Hand',\n\t\t\t\t\t\tvalue: 'stockOnHand',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'salesOrder',\n\t\t\t},\n\t\t\t...salesOrderOperations,\n\t\t\t...salesOrderFields,\n\n\t\t\t...stockOnHandOperations,\n\t\t\t...stockOnHandFields,\n\t\t],\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: INodeExecutionData[] = [];\n\t\tconst length = items.length;\n\t\tconst qs: IDataObject = {};\n\t\tlet responseData: IDataObject | IDataObject[] = [];\n\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\t\tconst operation = this.getNodeParameter('operation', 0);\n\n\t\t\t//https://apidocs.unleashedsoftware.com/SalesOrders\n\t\t\tif (resource === 'salesOrder') {\n\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\tconst filters = this.getNodeParameter('filters', i);\n\n\t\t\t\t\tif (filters.startDate) {\n\t\t\t\t\t\tfilters.startDate = moment(filters.startDate as string).format('YYYY-MM-DD');\n\t\t\t\t\t}\n\n\t\t\t\t\tif (filters.endDate) {\n\t\t\t\t\t\tfilters.endDate = moment(filters.endDate as string).format('YYYY-MM-DD');\n\t\t\t\t\t}\n\n\t\t\t\t\tif (filters.modifiedSince) {\n\t\t\t\t\t\tfilters.modifiedSince = moment(filters.modifiedSince as string).format('YYYY-MM-DD');\n\t\t\t\t\t}\n\n\t\t\t\t\tif (filters.orderStatus) {\n\t\t\t\t\t\tfilters.orderStatus = (filters.orderStatus as string[]).join(',');\n\t\t\t\t\t}\n\n\t\t\t\t\tObject.assign(qs, filters);\n\n\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\tresponseData = await unleashedApiRequestAllItems.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'Items',\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t'/SalesOrders',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconst limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\tqs.pageSize = limit;\n\t\t\t\t\t\tresponseData = (await unleashedApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t'/SalesOrders',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t1,\n\t\t\t\t\t\t)) as IDataObject;\n\t\t\t\t\t\tresponseData = responseData.Items as IDataObject[];\n\t\t\t\t\t}\n\t\t\t\t\tconvertNETDates(responseData);\n\t\t\t\t\tresponseData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\t\tthis.helpers.returnJsonArray(responseData),\n\t\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t//https://apidocs.unleashedsoftware.com/StockOnHand\n\t\t\tif (resource === 'stockOnHand') {\n\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\tconst filters = this.getNodeParameter('filters', i);\n\n\t\t\t\t\tif (filters.asAtDate) {\n\t\t\t\t\t\tfilters.asAtDate = moment(filters.asAtDate as string).format('YYYY-MM-DD');\n\t\t\t\t\t}\n\n\t\t\t\t\tif (filters.modifiedSince) {\n\t\t\t\t\t\tfilters.modifiedSince = moment(filters.modifiedSince as string).format('YYYY-MM-DD');\n\t\t\t\t\t}\n\n\t\t\t\t\tif (filters.orderBy) {\n\t\t\t\t\t\tfilters.orderBy = (filters.orderBy as string).trim();\n\t\t\t\t\t}\n\n\t\t\t\t\tObject.assign(qs, filters);\n\n\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\tresponseData = await unleashedApiRequestAllItems.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'Items',\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t'/StockOnHand',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconst limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\tqs.pageSize = limit;\n\t\t\t\t\t\tresponseData = (await unleashedApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t'/StockOnHand',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t1,\n\t\t\t\t\t\t)) as IDataObject;\n\t\t\t\t\t\tresponseData = responseData.Items as IDataObject[];\n\t\t\t\t\t}\n\n\t\t\t\t\tconvertNETDates(responseData);\n\t\t\t\t\tresponseData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\t\tthis.helpers.returnJsonArray(responseData),\n\t\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t\t);\n\t\t\t\t}\n\n\t\t\t\tif (operation === 'get') {\n\t\t\t\t\tconst productId = this.getNodeParameter('productId', i) as string;\n\t\t\t\t\tresponseData = await unleashedApiRequest.call(this, 'GET', `/StockOnHand/${productId}`);\n\t\t\t\t\tconvertNETDates(responseData);\n\t\t\t\t}\n\t\t\t}\n\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\tthis.helpers.returnJsonArray(responseData),\n\t\t\t\t{ itemData: { item: i } },\n\t\t\t);\n\t\t\treturnData.push(...executionData);\n\t\t}\n\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6BAAmB;AACnB,0BAOO;AAEP,8BAIO;AACP,mCAAuD;AACvD,oCAAyD;AAElD,MAAM,kBAAuC;AAAA,EAA7C;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO,CAAC,WAAW;AAAA,MACnB,UAAU;AAAA;AAAA,MAEV,MAAM;AAAA,MACN,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,QAEH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAAmC,CAAC;AAC1C,UAAM,SAAS,MAAM;AACrB,UAAM,KAAkB,CAAC;AACzB,QAAI,eAA4C,CAAC;AAEjD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,YAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,YAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAGtD,UAAI,aAAa,cAAc;AAC9B,YAAI,cAAc,UAAU;AAC3B,gBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,gBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,cAAI,QAAQ,WAAW;AACtB,oBAAQ,gBAAY,uBAAAA,SAAO,QAAQ,SAAmB,EAAE,OAAO,YAAY;AAAA,UAC5E;AAEA,cAAI,QAAQ,SAAS;AACpB,oBAAQ,cAAU,uBAAAA,SAAO,QAAQ,OAAiB,EAAE,OAAO,YAAY;AAAA,UACxE;AAEA,cAAI,QAAQ,eAAe;AAC1B,oBAAQ,oBAAgB,uBAAAA,SAAO,QAAQ,aAAuB,EAAE,OAAO,YAAY;AAAA,UACpF;AAEA,cAAI,QAAQ,aAAa;AACxB,oBAAQ,cAAe,QAAQ,YAAyB,KAAK,GAAG;AAAA,UACjE;AAEA,iBAAO,OAAO,IAAI,OAAO;AAEzB,cAAI,WAAW;AACd,2BAAe,MAAM,oDAA4B;AAAA,cAChD;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD,OAAO;AACN,kBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,eAAG,WAAW;AACd,2BAAgB,MAAM,4CAAoB;AAAA,cACzC;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD;AAAA,cACA;AAAA,YACD;AACA,2BAAe,aAAa;AAAA,UAC7B;AACA,uDAAgB,YAAY;AAC5B,yBAAe,KAAK,QAAQ;AAAA,YAC3B,KAAK,QAAQ,gBAAgB,YAAY;AAAA,YACzC,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,UACzB;AAAA,QACD;AAAA,MACD;AAGA,UAAI,aAAa,eAAe;AAC/B,YAAI,cAAc,UAAU;AAC3B,gBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,gBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,cAAI,QAAQ,UAAU;AACrB,oBAAQ,eAAW,uBAAAA,SAAO,QAAQ,QAAkB,EAAE,OAAO,YAAY;AAAA,UAC1E;AAEA,cAAI,QAAQ,eAAe;AAC1B,oBAAQ,oBAAgB,uBAAAA,SAAO,QAAQ,aAAuB,EAAE,OAAO,YAAY;AAAA,UACpF;AAEA,cAAI,QAAQ,SAAS;AACpB,oBAAQ,UAAW,QAAQ,QAAmB,KAAK;AAAA,UACpD;AAEA,iBAAO,OAAO,IAAI,OAAO;AAEzB,cAAI,WAAW;AACd,2BAAe,MAAM,oDAA4B;AAAA,cAChD;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD,OAAO;AACN,kBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,eAAG,WAAW;AACd,2BAAgB,MAAM,4CAAoB;AAAA,cACzC;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD;AAAA,cACA;AAAA,YACD;AACA,2BAAe,aAAa;AAAA,UAC7B;AAEA,uDAAgB,YAAY;AAC5B,yBAAe,KAAK,QAAQ;AAAA,YAC3B,KAAK,QAAQ,gBAAgB,YAAY;AAAA,YACzC,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,UACzB;AAAA,QACD;AAEA,YAAI,cAAc,OAAO;AACxB,gBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,yBAAe,MAAM,4CAAoB,KAAK,MAAM,OAAO,gBAAgB,SAAS,EAAE;AACtF,uDAAgB,YAAY;AAAA,QAC7B;AAAA,MACD;AACA,YAAM,gBAAgB,KAAK,QAAQ;AAAA,QAClC,KAAK,QAAQ,gBAAgB,YAAY;AAAA,QACzC,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,MACzB;AACA,iBAAW,KAAK,GAAG,aAAa;AAAA,IACjC;AAEA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": ["moment"]}