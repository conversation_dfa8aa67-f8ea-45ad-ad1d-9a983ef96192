{"version": 3, "sources": ["../../../nodes/Html/utils.ts"], "sourcesContent": ["import { convert } from 'html-to-text';\nimport type { IDataObject } from 'n8n-workflow';\n\nimport type { IValueData, Cheerio } from './types';\n\n// The extraction functions\nconst extractFunctions: {\n\t[key: string]: ($: Cheerio, valueData: IValueData, nodeVersion: number) => string | undefined;\n} = {\n\tattribute: ($: Cheerio, valueData: IValueData): string | undefined =>\n\t\t$.attr(valueData.attribute!),\n\thtml: ($: Cheerio, _valueData: IValueData): string | undefined => $.html() || undefined,\n\ttext: ($: Cheerio, _valueData: IValueData, nodeVersion: number): string | undefined => {\n\t\tif (nodeVersion <= 1.1) return $.text() || undefined;\n\n\t\tconst html = $.html() || '';\n\n\t\tlet options;\n\t\tif (_valueData.skipSelectors) {\n\t\t\toptions = {\n\t\t\t\tselectors: _valueData.skipSelectors.split(',').map((s) => ({\n\t\t\t\t\tselector: s.trim(),\n\t\t\t\t\tformat: 'skip',\n\t\t\t\t})),\n\t\t\t};\n\t\t}\n\t\treturn convert(html, options);\n\t},\n\tvalue: ($: Cheerio, _valueData: IValueData): string | undefined => $.val(),\n};\n\n/**\n * Simple helper function which applies options\n */\nexport function getValue(\n\t$: Cheerio,\n\tvalueData: IValueData,\n\toptions: IDataObject,\n\tnodeVersion: number,\n) {\n\tlet value = extractFunctions[valueData.returnValue]($, valueData, nodeVersion);\n\n\tif (value === undefined) {\n\t\treturn value;\n\t}\n\n\tif (options.trimValues) {\n\t\tvalue = value.trim();\n\t}\n\n\tif (options.cleanUpText) {\n\t\tvalue = value\n\t\t\t.replace(/^\\s+|\\s+$/g, '')\n\t\t\t.replace(/(\\r\\n|\\n|\\r)/gm, '')\n\t\t\t.replace(/\\s+/g, ' ');\n\t}\n\n\treturn value;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAAwB;AAMxB,MAAM,mBAEF;AAAA,EACH,WAAW,CAAC,GAAY,cACvB,EAAE,KAAK,UAAU,SAAU;AAAA,EAC5B,MAAM,CAAC,GAAY,eAA+C,EAAE,KAAK,KAAK;AAAA,EAC9E,MAAM,CAAC,GAAY,YAAwB,gBAA4C;AACtF,QAAI,eAAe,IAAK,QAAO,EAAE,KAAK,KAAK;AAE3C,UAAM,OAAO,EAAE,KAAK,KAAK;AAEzB,QAAI;AACJ,QAAI,WAAW,eAAe;AAC7B,gBAAU;AAAA,QACT,WAAW,WAAW,cAAc,MAAM,GAAG,EAAE,IAAI,CAAC,OAAO;AAAA,UAC1D,UAAU,EAAE,KAAK;AAAA,UACjB,QAAQ;AAAA,QACT,EAAE;AAAA,MACH;AAAA,IACD;AACA,eAAO,6BAAQ,MAAM,OAAO;AAAA,EAC7B;AAAA,EACA,OAAO,CAAC,GAAY,eAA+C,EAAE,IAAI;AAC1E;AAKO,SAAS,SACf,GACA,WACA,SACA,aACC;AACD,MAAI,QAAQ,iBAAiB,UAAU,WAAW,EAAE,GAAG,WAAW,WAAW;AAE7E,MAAI,UAAU,QAAW;AACxB,WAAO;AAAA,EACR;AAEA,MAAI,QAAQ,YAAY;AACvB,YAAQ,MAAM,KAAK;AAAA,EACpB;AAEA,MAAI,QAAQ,aAAa;AACxB,YAAQ,MACN,QAAQ,cAAc,EAAE,EACxB,QAAQ,kBAAkB,EAAE,EAC5B,QAAQ,QAAQ,GAAG;AAAA,EACtB;AAEA,SAAO;AACR;", "names": []}