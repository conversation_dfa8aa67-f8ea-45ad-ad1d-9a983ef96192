{"version": 3, "sources": ["../../../../nodes/Wise/descriptions/TransferDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const transferOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdefault: 'get',\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Create',\n\t\t\t\tvalue: 'create',\n\t\t\t\taction: 'Create a transfer',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'delete',\n\t\t\t\taction: 'Delete a transfer',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Execute',\n\t\t\t\tvalue: 'execute',\n\t\t\t\taction: 'Execute a transfer',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\taction: 'Get a transfer',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\taction: 'Get many transfers',\n\t\t\t},\n\t\t],\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transfer'],\n\t\t\t},\n\t\t},\n\t},\n];\n\nexport const transferFields: INodeProperties[] = [\n\t// ----------------------------------\n\t//         transfer: create\n\t// ----------------------------------\n\t{\n\t\tdisplayName: 'Profile Name or ID',\n\t\tname: 'profileId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\tdefault: [],\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getProfiles',\n\t\t\tloadOptionsDependsOn: ['profileId'],\n\t\t},\n\t\tdescription:\n\t\t\t'ID of the user profile to retrieve the balance of. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transfer'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Quote ID',\n\t\tname: 'quoteId',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdescription: 'ID of the quote based on which to create the transfer',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transfer'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Target Account Name or ID',\n\t\tname: 'targetAccountId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\tdefault: [],\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getRecipients',\n\t\t},\n\t\tdescription:\n\t\t\t'ID of the account that will receive the funds. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transfer'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transfer'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Reference',\n\t\t\t\tname: 'reference',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"Reference text to show in the recipient's bank statement\",\n\t\t\t},\n\t\t],\n\t},\n\n\t// ----------------------------------\n\t//         transfer: delete\n\t// ----------------------------------\n\t{\n\t\tdisplayName: 'Transfer ID',\n\t\tname: 'transferId',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdescription: 'ID of the transfer to delete',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transfer'],\n\t\t\t\toperation: ['delete'],\n\t\t\t},\n\t\t},\n\t},\n\n\t// ----------------------------------\n\t//        transfer: execute\n\t// ----------------------------------\n\t{\n\t\tdisplayName: 'Profile Name or ID',\n\t\tname: 'profileId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\tdefault: [],\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getProfiles',\n\t\t},\n\t\tdescription:\n\t\t\t'ID of the user profile to execute the transfer under. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transfer'],\n\t\t\t\toperation: ['execute'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Transfer ID',\n\t\tname: 'transferId',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdescription: 'ID of the transfer to execute',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transfer'],\n\t\t\t\toperation: ['execute'],\n\t\t\t},\n\t\t},\n\t},\n\n\t// ----------------------------------\n\t//         transfer: get\n\t// ----------------------------------\n\t{\n\t\tdisplayName: 'Transfer ID',\n\t\tname: 'transferId',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdescription: 'ID of the transfer to retrieve',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transfer'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Download Receipt',\n\t\tname: 'downloadReceipt',\n\t\ttype: 'boolean',\n\t\trequired: true,\n\t\tdefault: false,\n\t\tdescription:\n\t\t\t\"Whether to download the transfer receipt as a PDF file. Only for executed transfers, having status 'Outgoing Payment Sent'.\",\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transfer'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Put Output File in Field',\n\t\tname: 'binaryProperty',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: 'data',\n\t\thint: 'The name of the output binary field to put the file in',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transfer'],\n\t\t\t\toperation: ['get'],\n\t\t\t\tdownloadReceipt: [true],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'File Name',\n\t\tname: 'fileName',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tplaceholder: 'data.pdf',\n\t\tdescription: 'Name of the file that will be downloaded',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transfer'],\n\t\t\t\toperation: ['get'],\n\t\t\t\tdownloadReceipt: [true],\n\t\t\t},\n\t\t},\n\t},\n\n\t// ----------------------------------\n\t//        transfer: getAll\n\t// ----------------------------------\n\t{\n\t\tdisplayName: 'Profile Name or ID',\n\t\tname: 'profileId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\tdefault: [],\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getProfiles',\n\t\t},\n\t\tdescription:\n\t\t\t'ID of the user profile to retrieve. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transfer'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transfer'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdefault: 5,\n\t\tdescription: 'Max number of results to return',\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 1000,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transfer'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Filters',\n\t\tname: 'filters',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Filter',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transfer'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Range',\n\t\t\t\tname: 'range',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\tplaceholder: 'Add Range',\n\t\t\t\tdescription: 'Range of time for filtering the transfers',\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Range Properties',\n\t\t\t\t\t\tname: 'rangeProperties',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Created Date Start',\n\t\t\t\t\t\t\t\tname: 'createdDateStart',\n\t\t\t\t\t\t\t\ttype: 'dateTime',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Created Date End',\n\t\t\t\t\t\t\t\tname: 'createdDateEnd',\n\t\t\t\t\t\t\t\ttype: 'dateTime',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Source Currency',\n\t\t\t\tname: 'sourceCurrency',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Code of the source currency for filtering the transfers',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Status',\n\t\t\t\tname: 'status',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'processing',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Bounced Back',\n\t\t\t\t\t\tvalue: 'bounced_back',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Cancelled',\n\t\t\t\t\t\tvalue: 'cancelled',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Charged Back',\n\t\t\t\t\t\tvalue: 'charged_back',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Funds Converted',\n\t\t\t\t\t\tvalue: 'funds_converted',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Funds Refunded',\n\t\t\t\t\t\tvalue: 'funds_refunded',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Incoming Payment Waiting',\n\t\t\t\t\t\tvalue: 'incoming_payment_waiting',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Outgoing Payment Sent',\n\t\t\t\t\t\tvalue: 'outgoing_payment_sent',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Processing',\n\t\t\t\t\t\tvalue: 'processing',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Unknown',\n\t\t\t\t\t\tvalue: 'unknown',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Waiting for Recipient Input to Proceed',\n\t\t\t\t\t\tvalue: 'waiting_recipient_input_to_proceed',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Target Currency',\n\t\t\t\tname: 'targetCurrency',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Code of the target currency for filtering the transfers',\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,qBAAwC;AAAA,EACpD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,MACtB;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,iBAAoC;AAAA;AAAA;AAAA;AAAA,EAIhD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS,CAAC;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,MACnB,sBAAsB,CAAC,WAAW;AAAA,IACnC;AAAA,IACA,aACC;AAAA,IACD,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS,CAAC;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,aACC;AAAA,IACD,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS,CAAC;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,aACC;AAAA,IACD,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,SAAS;AAAA,MACtB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,SAAS;AAAA,MACtB;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aACC;AAAA,IACD,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,KAAK;AAAA,QACjB,iBAAiB,CAAC,IAAI;AAAA,MACvB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,KAAK;AAAA,QACjB,iBAAiB,CAAC,IAAI;AAAA,MACvB;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS,CAAC;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,aACC;AAAA,IACD,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,QACpB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,aAAa;AAAA,QACb,SAAS,CAAC;AAAA,QACV,SAAS;AAAA,UACR;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,cACV;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,cACV;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AACD;", "names": []}