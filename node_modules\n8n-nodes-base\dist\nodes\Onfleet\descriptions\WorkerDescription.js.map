{"version": 3, "sources": ["../../../../nodes/Onfleet/descriptions/WorkerDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const workerOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Create',\n\t\t\t\tvalue: 'create',\n\t\t\t\tdescription: 'Create a new Onfleet worker',\n\t\t\t\taction: 'Create a worker',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'delete',\n\t\t\t\tdescription: 'Delete an Onfleet worker',\n\t\t\t\taction: 'Delete a worker',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\tdescription: 'Get a specific Onfleet worker',\n\t\t\t\taction: 'Get a worker',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Get many Onfleet workers',\n\t\t\t\taction: 'Get many workers',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Schedule',\n\t\t\t\tvalue: 'getSchedule',\n\t\t\t\tdescription: 'Get a specific Onfleet worker schedule',\n\t\t\t\taction: 'Get the schedule for a worker',\n\t\t\t},\n\t\t\t// {\n\t\t\t// \tname: 'Set Worker\\'s Schedule',\n\t\t\t// \tvalue: 'setSchedule',\n\t\t\t// \tdescription: 'Set the worker\\'s schedule',\n\t\t\t// },\n\t\t\t{\n\t\t\t\tname: 'Update',\n\t\t\t\tvalue: 'update',\n\t\t\t\tdescription: 'Update an Onfleet worker',\n\t\t\t\taction: 'Update a worker',\n\t\t\t},\n\t\t],\n\t\tdefault: 'get',\n\t},\n];\n\nconst byLocationField = {\n\tdisplayName: 'Search by Location',\n\tname: 'byLocation',\n\ttype: 'boolean',\n\tdefault: false,\n\tdescription:\n\t\t'Whether to search for only those workers who are currently within a certain target area',\n} as INodeProperties;\n\nconst nameField = {\n\tdisplayName: 'Name',\n\tname: 'name',\n\ttype: 'string',\n\tdefault: '',\n\tdescription: \"The worker's name\",\n} as INodeProperties;\n\nconst phoneField = {\n\tdisplayName: 'Phone',\n\tname: 'phone',\n\ttype: 'string',\n\tdefault: '',\n\tdescription: 'A list of worker’s phone numbers',\n} as INodeProperties;\n\nconst capacityField = {\n\tdisplayName: 'Capacity',\n\tname: 'capacity',\n\ttype: 'number',\n\tdefault: 0,\n\tdescription: 'The maximum number of units this worker can carry, for route optimization purposes',\n} as INodeProperties;\n\nconst displayNameField = {\n\tdisplayName: 'Display Name',\n\tname: 'displayName',\n\ttype: 'string',\n\tdefault: '',\n\tdescription:\n\t\t\"This value is used in place of the worker's actual name within sms notifications, delivery tracking pages, and across organization boundaries\",\n} as INodeProperties;\n\nconst vehicleTypeField = {\n\tdisplayName: 'Type',\n\tname: 'type',\n\ttype: 'options',\n\toptions: [\n\t\t{\n\t\t\tname: 'Bicycle',\n\t\t\tvalue: 'BICYCLE',\n\t\t},\n\t\t{\n\t\t\tname: 'Car',\n\t\t\tvalue: 'CAR',\n\t\t},\n\t\t{\n\t\t\tname: 'Motorcycle',\n\t\t\tvalue: 'MOTORCYCLE',\n\t\t},\n\t\t{\n\t\t\tname: 'Truck',\n\t\t\tvalue: 'TRUCK',\n\t\t},\n\t],\n\tdefault: '',\n\tdescription:\n\t\t\"Whether the worker has vehicle or not. If it's not provided, this worker will be treated as if on foot.\",\n} as INodeProperties;\n\nconst vehicleDescriptionField = {\n\tdisplayName: 'Description',\n\tname: 'description',\n\ttype: 'string',\n\tdefault: '',\n\tdescription: \"The vehicle's make, model, year, or any other relevant identifying details\",\n} as INodeProperties;\n\nconst vehicleLicensePlateField = {\n\tdisplayName: 'License Plate',\n\tname: 'licensePlate',\n\ttype: 'string',\n\tdefault: '',\n\tdescription: \"The vehicle's license plate number\",\n} as INodeProperties;\n\nconst vehicleColorField = {\n\tdisplayName: 'Color',\n\tname: 'color',\n\t// eslint-disable-next-line n8n-nodes-base/node-param-color-type-unused\n\ttype: 'string',\n\tdefault: '',\n\tdescription: \"The vehicle's color\",\n} as INodeProperties;\n\nconst teamsField = {\n\tdisplayName: 'Team Names or IDs',\n\tname: 'teams',\n\ttype: 'multiOptions',\n\ttypeOptions: {\n\t\tloadOptionsMethod: 'getTeams',\n\t},\n\tdefault: [],\n\tdescription:\n\t\t'One or more teams of which the worker is a member. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n} as INodeProperties;\n\nconst teamsFilterField = {\n\tdisplayName: 'Team Names or IDs',\n\tname: 'teams',\n\ttype: 'multiOptions',\n\ttypeOptions: {\n\t\tloadOptionsMethod: 'getTeams',\n\t},\n\tdefault: [],\n\tdescription:\n\t\t'A list of the teams that workers must be part of. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n} as INodeProperties;\n\nconst statesFilterField = {\n\tdisplayName: 'States',\n\tname: 'states',\n\ttype: 'multiOptions',\n\toptions: [\n\t\t{\n\t\t\tname: 'Active (On-Duty, Active Task)',\n\t\t\tvalue: 2,\n\t\t},\n\t\t{\n\t\t\tname: 'Idle (On-Duty, No Active Task)',\n\t\t\tvalue: 1,\n\t\t},\n\t\t{\n\t\t\tname: 'Off-Duty',\n\t\t\tvalue: 0,\n\t\t},\n\t],\n\tdefault: [],\n\tdescription: 'List of worker states',\n} as INodeProperties;\n\nconst phonesFilterField = {\n\tdisplayName: 'Phones',\n\tname: 'phones',\n\ttype: 'string',\n\ttypeOptions: {\n\t\tmultipleValues: true,\n\t\tmultipleValueButtonText: 'Add Phone',\n\t},\n\tdefault: [],\n\tdescription: \"A list of workers' phone numbers\",\n} as INodeProperties;\n\nconst filterField = {\n\tdisplayName: 'Fields to Return',\n\tname: 'filter',\n\ttype: 'multiOptions',\n\toptions: [\n\t\t{\n\t\t\tname: 'Account Status',\n\t\t\tvalue: 'accountStatus',\n\t\t},\n\t\t{\n\t\t\tname: 'Active Task',\n\t\t\tvalue: 'activeTask',\n\t\t},\n\t\t{\n\t\t\tname: 'Capacity',\n\t\t\tvalue: 'capacity',\n\t\t},\n\t\t{\n\t\t\tname: 'Delay Time',\n\t\t\tvalue: 'delayTime',\n\t\t},\n\t\t{\n\t\t\tname: 'Display Name',\n\t\t\tvalue: 'displayName',\n\t\t},\n\t\t{\n\t\t\tname: 'Image Url',\n\t\t\tvalue: 'imageUrl',\n\t\t},\n\t\t{\n\t\t\tname: 'Location',\n\t\t\tvalue: 'location',\n\t\t},\n\t\t{\n\t\t\tname: 'Metadata',\n\t\t\tvalue: 'metadata',\n\t\t},\n\t\t{\n\t\t\tname: 'Name',\n\t\t\tvalue: 'name',\n\t\t},\n\t\t{\n\t\t\tname: 'On Duty',\n\t\t\tvalue: 'onDuty',\n\t\t},\n\t\t{\n\t\t\tname: 'Organization',\n\t\t\tvalue: 'organization',\n\t\t},\n\t\t{\n\t\t\tname: 'Phone',\n\t\t\tvalue: 'phone',\n\t\t},\n\t\t{\n\t\t\tname: 'Tasks',\n\t\t\tvalue: 'tasks',\n\t\t},\n\t\t{\n\t\t\tname: 'Teams',\n\t\t\tvalue: 'teams',\n\t\t},\n\t\t{\n\t\t\tname: 'Time Created',\n\t\t\tvalue: 'timeCreated',\n\t\t},\n\t\t{\n\t\t\tname: 'Time Last Modified',\n\t\t\tvalue: 'timeLastModified',\n\t\t},\n\t\t{\n\t\t\tname: 'Time Last Seen',\n\t\t\tvalue: 'timeLastSeen',\n\t\t},\n\t\t{\n\t\t\tname: 'User Data',\n\t\t\tvalue: 'userData',\n\t\t},\n\t\t{\n\t\t\tname: 'Vehicle',\n\t\t\tvalue: 'vehicle',\n\t\t},\n\t\t{\n\t\t\tname: 'Worker ID',\n\t\t\tvalue: 'id',\n\t\t},\n\t],\n\tdefault: [],\n\tdescription: 'A list of fields to show in the response, if all are not desired',\n} as INodeProperties;\n\nconst longitudeFilterField = {\n\tdisplayName: 'Longitude',\n\tname: 'longitude',\n\ttype: 'number',\n\ttypeOptions: {\n\t\tnumberPrecision: 14,\n\t},\n\tdefault: 0,\n\tdescription: 'The longitude component of the coordinate pair',\n} as INodeProperties;\n\nconst latitudeFilterField = {\n\tdisplayName: 'Latitude',\n\tname: 'latitude',\n\ttype: 'number',\n\ttypeOptions: {\n\t\tnumberPrecision: 14,\n\t},\n\tdefault: 0,\n\tdescription: 'The latitude component of the coordinate pair',\n} as INodeProperties;\n\nconst radiusFilterField = {\n\tdisplayName: 'Radius',\n\tname: 'radius',\n\ttype: 'number',\n\ttypeOptions: {\n\t\tmaxValue: 10000,\n\t\tminValue: 0,\n\t},\n\tdefault: 1000,\n\tdescription:\n\t\t'The length in meters of the radius of the spherical area in which to look for workers. Defaults to 1000 if missing. Maximum value is 10000.',\n} as INodeProperties;\n\nconst scheduleDateField = {\n\tdisplayName: 'Date',\n\tname: 'date',\n\ttype: 'dateTime',\n\tdefault: '',\n\tdescription: \"Schedule's date\",\n} as INodeProperties;\n\nconst scheduleTimezoneField = {\n\tdisplayName: 'Timezone Name or ID',\n\tname: 'timezone',\n\ttype: 'options',\n\ttypeOptions: {\n\t\tloadOptionsMethod: 'getTimezones',\n\t},\n\tdefault: '',\n\tdescription:\n\t\t'A valid timezone. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n} as INodeProperties;\n\nconst scheduleStartField = {\n\tdisplayName: 'Start',\n\tname: 'start',\n\ttype: 'dateTime',\n\tdefault: '',\n\tdescription: 'Start time',\n} as INodeProperties;\n\nconst scheduleEndField = {\n\tdisplayName: 'End',\n\tname: 'end',\n\ttype: 'dateTime',\n\tdefault: '',\n\tdescription: 'End time',\n} as INodeProperties;\n\nexport const workerFields: INodeProperties[] = [\n\t{\n\t\t...byLocationField,\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Worker ID',\n\t\tname: 'id',\n\t\ttype: 'string',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t\toperation: ['get', 'getSchedule', 'setSchedule', 'update', 'delete'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdescription: 'The ID of the worker object for lookup',\n\t},\n\t{\n\t\t...nameField,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\trequired: true,\n\t},\n\t{\n\t\t...phoneField,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\trequired: true,\n\t},\n\t{\n\t\t...teamsField,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\trequired: true,\n\t},\n\t{\n\t\t...longitudeFilterField,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tbyLocation: [true],\n\t\t\t},\n\t\t},\n\t\trequired: true,\n\t},\n\t{\n\t\t...latitudeFilterField,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tbyLocation: [true],\n\t\t\t},\n\t\t},\n\t\trequired: true,\n\t},\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 64,\n\t\t},\n\t\tdefault: 64,\n\t\tdescription: 'Max number of results to return',\n\t},\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\tcapacityField,\n\t\t\tdisplayNameField,\n\t\t\t{\n\t\t\t\tdisplayName: 'Vehicle',\n\t\t\t\tname: 'vehicle',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\tplaceholder: 'Add Vehicle',\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Vehicle Properties',\n\t\t\t\t\t\tname: 'vehicleProperties',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t...vehicleTypeField,\n\t\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t...vehicleDescriptionField,\n\t\t\t\t\t\t\t\trequired: false,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t...vehicleLicensePlateField,\n\t\t\t\t\t\t\t\trequired: false,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t...vehicleColorField,\n\t\t\t\t\t\t\t\trequired: false,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Update Fields',\n\t\tname: 'updateFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t\toptions: [capacityField, displayNameField, nameField, teamsField],\n\t},\n\t{\n\t\tdisplayName: 'Filters',\n\t\tname: 'filters',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tbyLocation: [true],\n\t\t\t},\n\t\t},\n\t\toptions: [radiusFilterField],\n\t},\n\t{\n\t\tdisplayName: 'Filters',\n\t\tname: 'filters',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tbyLocation: [false],\n\t\t\t},\n\t\t},\n\t\toptions: [statesFilterField, teamsFilterField, phonesFilterField],\n\t},\n\t{\n\t\tdisplayName: 'Options',\n\t\tname: 'options',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\toptions: [filterField],\n\t},\n\t{\n\t\tdisplayName: 'Options',\n\t\tname: 'options',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Analytics',\n\t\t\t\tname: 'analytics',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: true,\n\t\t\t\tdescription:\n\t\t\t\t\t'Whether a more detailed response is needed, includes basic worker duty event, traveled distance (meters) and time analytics',\n\t\t\t},\n\t\t\t{\n\t\t\t\t...filterField,\n\t\t\t\trequired: false,\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Schedule',\n\t\tname: 'schedule',\n\t\ttype: 'fixedCollection',\n\t\tplaceholder: 'Add Schedule',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worker'],\n\t\t\t\toperation: ['setSchedule'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\ttypeOptions: {\n\t\t\tmultipleValues: true,\n\t\t\tmultipleValueButtonText: 'Add Schedule',\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Schedule Properties',\n\t\t\t\tname: 'scheduleProperties',\n\t\t\t\tdefault: {},\n\t\t\t\tvalues: [\n\t\t\t\t\t{\n\t\t\t\t\t\t...scheduleDateField,\n\t\t\t\t\t\trequired: true,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\t...scheduleTimezoneField,\n\t\t\t\t\t\trequired: true,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Shifts',\n\t\t\t\t\t\tname: 'shifts',\n\t\t\t\t\t\ttype: 'fixedCollection',\n\t\t\t\t\t\tdefault: {},\n\t\t\t\t\t\tplaceholder: 'Add Shift',\n\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\tmultipleValues: true,\n\t\t\t\t\t\t},\n\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Shifts Properties',\n\t\t\t\t\t\t\t\tname: 'shiftsProperties',\n\t\t\t\t\t\t\t\tdefault: {},\n\t\t\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t...scheduleStartField,\n\t\t\t\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t...scheduleEndField,\n\t\t\t\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,mBAAsC;AAAA,EAClD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,MACpB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEA,MAAM,kBAAkB;AAAA,EACvB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aACC;AACF;AAEA,MAAM,YAAY;AAAA,EACjB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aAAa;AACd;AAEA,MAAM,aAAa;AAAA,EAClB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aAAa;AACd;AAEA,MAAM,gBAAgB;AAAA,EACrB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aAAa;AACd;AAEA,MAAM,mBAAmB;AAAA,EACxB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aACC;AACF;AAEA,MAAM,mBAAmB;AAAA,EACxB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,IACR;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,EACD;AAAA,EACA,SAAS;AAAA,EACT,aACC;AACF;AAEA,MAAM,0BAA0B;AAAA,EAC/B,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aAAa;AACd;AAEA,MAAM,2BAA2B;AAAA,EAChC,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aAAa;AACd;AAEA,MAAM,oBAAoB;AAAA,EACzB,aAAa;AAAA,EACb,MAAM;AAAA;AAAA,EAEN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aAAa;AACd;AAEA,MAAM,aAAa;AAAA,EAClB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,aAAa;AAAA,IACZ,mBAAmB;AAAA,EACpB;AAAA,EACA,SAAS,CAAC;AAAA,EACV,aACC;AACF;AAEA,MAAM,mBAAmB;AAAA,EACxB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,aAAa;AAAA,IACZ,mBAAmB;AAAA,EACpB;AAAA,EACA,SAAS,CAAC;AAAA,EACV,aACC;AACF;AAEA,MAAM,oBAAoB;AAAA,EACzB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,IACR;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,EACD;AAAA,EACA,SAAS,CAAC;AAAA,EACV,aAAa;AACd;AAEA,MAAM,oBAAoB;AAAA,EACzB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,aAAa;AAAA,IACZ,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,EAC1B;AAAA,EACA,SAAS,CAAC;AAAA,EACV,aAAa;AACd;AAEA,MAAM,cAAc;AAAA,EACnB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,IACR;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,EACD;AAAA,EACA,SAAS,CAAC;AAAA,EACV,aAAa;AACd;AAEA,MAAM,uBAAuB;AAAA,EAC5B,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,aAAa;AAAA,IACZ,iBAAiB;AAAA,EAClB;AAAA,EACA,SAAS;AAAA,EACT,aAAa;AACd;AAEA,MAAM,sBAAsB;AAAA,EAC3B,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,aAAa;AAAA,IACZ,iBAAiB;AAAA,EAClB;AAAA,EACA,SAAS;AAAA,EACT,aAAa;AACd;AAEA,MAAM,oBAAoB;AAAA,EACzB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,aAAa;AAAA,IACZ,UAAU;AAAA,IACV,UAAU;AAAA,EACX;AAAA,EACA,SAAS;AAAA,EACT,aACC;AACF;AAEA,MAAM,oBAAoB;AAAA,EACzB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aAAa;AACd;AAEA,MAAM,wBAAwB;AAAA,EAC7B,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,aAAa;AAAA,IACZ,mBAAmB;AAAA,EACpB;AAAA,EACA,SAAS;AAAA,EACT,aACC;AACF;AAEA,MAAM,qBAAqB;AAAA,EAC1B,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aAAa;AACd;AAEA,MAAM,mBAAmB;AAAA,EACxB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aAAa;AACd;AAEO,MAAM,eAAkC;AAAA,EAC9C;AAAA,IACC,GAAG;AAAA,IACH,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,OAAO,eAAe,eAAe,UAAU,QAAQ;AAAA,MACpE;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,GAAG;AAAA,IACH,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,UAAU;AAAA,EACX;AAAA,EACA;AAAA,IACC,GAAG;AAAA,IACH,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,UAAU;AAAA,EACX;AAAA,EACA;AAAA,IACC,GAAG;AAAA,IACH,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,UAAU;AAAA,EACX;AAAA,EACA;AAAA,IACC,GAAG;AAAA,IACH,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,QAAQ;AAAA,QACpB,YAAY,CAAC,IAAI;AAAA,MAClB;AAAA,IACD;AAAA,IACA,UAAU;AAAA,EACX;AAAA,EACA;AAAA,IACC,GAAG;AAAA,IACH,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,QAAQ;AAAA,QACpB,YAAY,CAAC,IAAI;AAAA,MAClB;AAAA,IACD;AAAA,IACA,UAAU;AAAA,EACX;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,QAAQ;AAAA,QACpB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS,CAAC;AAAA,QACV,SAAS;AAAA,UACR;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,QAAQ;AAAA,cACP;AAAA,gBACC,GAAG;AAAA,gBACH,UAAU;AAAA,cACX;AAAA,cACA;AAAA,gBACC,GAAG;AAAA,gBACH,UAAU;AAAA,cACX;AAAA,cACA;AAAA,gBACC,GAAG;AAAA,gBACH,UAAU;AAAA,cACX;AAAA,cACA;AAAA,gBACC,GAAG;AAAA,gBACH,UAAU;AAAA,cACX;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS,CAAC,eAAe,kBAAkB,WAAW,UAAU;AAAA,EACjE;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,QAAQ;AAAA,QACpB,YAAY,CAAC,IAAI;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS,CAAC,iBAAiB;AAAA,EAC5B;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,QAAQ;AAAA,QACpB,YAAY,CAAC,KAAK;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS,CAAC,mBAAmB,kBAAkB,iBAAiB;AAAA,EACjE;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS,CAAC,WAAW;AAAA,EACtB;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,GAAG;AAAA,QACH,UAAU;AAAA,MACX;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,aAAa;AAAA,MAC1B;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,aAAa;AAAA,MACZ,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,IAC1B;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,QAAQ;AAAA,UACP;AAAA,YACC,GAAG;AAAA,YACH,UAAU;AAAA,UACX;AAAA,UACA;AAAA,YACC,GAAG;AAAA,YACH,UAAU;AAAA,UACX;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS,CAAC;AAAA,YACV,aAAa;AAAA,YACb,aAAa;AAAA,cACZ,gBAAgB;AAAA,YACjB;AAAA,YACA,SAAS;AAAA,cACR;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,SAAS,CAAC;AAAA,gBACV,QAAQ;AAAA,kBACP;AAAA,oBACC,GAAG;AAAA,oBACH,UAAU;AAAA,kBACX;AAAA,kBACA;AAAA,oBACC,GAAG;AAAA,oBACH,UAAU;AAAA,kBACX;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;", "names": []}