{"version": 3, "sources": ["../../../../../nodes/Set/v2/helpers/utils.ts"], "sourcesContent": ["import get from 'lodash/get';\nimport set from 'lodash/set';\nimport unset from 'lodash/unset';\nimport {\n\tApplicationError,\n\tNodeOperationError,\n\tdeepCopy,\n\tgetValueDescription,\n\tjsonParse,\n\tvalidateFieldType,\n} from 'n8n-workflow';\nimport type {\n\tFieldType,\n\tIDataObject,\n\tIExecuteFunctions,\n\tINode,\n\tINodeExecutionData,\n\tISupplyDataFunctions,\n} from 'n8n-workflow';\n\nimport type { SetNodeOptions } from './interfaces';\nimport { INCLUDE } from './interfaces';\nimport { getResolvables, sanitizeDataPathKey } from '../../../../utils/utilities';\n\nconst configureFieldHelper = (dotNotation?: boolean) => {\n\tif (dotNotation !== false) {\n\t\treturn {\n\t\t\tset: (item: IDataObject, key: string, value: IDataObject) => {\n\t\t\t\tset(item, key, value);\n\t\t\t},\n\t\t\tget: (item: IDataObject, key: string) => {\n\t\t\t\treturn get(item, key);\n\t\t\t},\n\t\t\tunset: (item: IDataObject, key: string) => {\n\t\t\t\tunset(item, key);\n\t\t\t},\n\t\t};\n\t} else {\n\t\treturn {\n\t\t\tset: (item: IDataObject, key: string, value: IDataObject) => {\n\t\t\t\titem[sanitizeDataPathKey(item, key)] = value;\n\t\t\t},\n\t\t\tget: (item: IDataObject, key: string) => {\n\t\t\t\treturn item[sanitizeDataPathKey(item, key)];\n\t\t\t},\n\t\t\tunset: (item: IDataObject, key: string) => {\n\t\t\t\tdelete item[sanitizeDataPathKey(item, key)];\n\t\t\t},\n\t\t};\n\t}\n};\n\nexport function composeReturnItem(\n\tthis: IExecuteFunctions | ISupplyDataFunctions,\n\titemIndex: number,\n\tinputItem: INodeExecutionData,\n\tnewFields: IDataObject,\n\toptions: SetNodeOptions,\n\tnodeVersion: number,\n) {\n\tconst newItem: INodeExecutionData = {\n\t\tjson: {},\n\t\tpairedItem: { item: itemIndex },\n\t};\n\n\tconst includeBinary =\n\t\t(nodeVersion >= 3.4 && !options.stripBinary && options.include !== 'none') ||\n\t\t(nodeVersion < 3.4 && !!options.includeBinary);\n\tif (includeBinary && inputItem.binary !== undefined) {\n\t\t// Create a shallow copy of the binary data so that the old\n\t\t// data references which do not get changed still stay behind\n\t\t// but the incoming data does not get changed.\n\t\tnewItem.binary = {};\n\t\tObject.assign(newItem.binary, inputItem.binary);\n\t}\n\n\tconst fieldHelper = configureFieldHelper(options.dotNotation);\n\n\tswitch (options.include) {\n\t\tcase INCLUDE.ALL:\n\t\t\tnewItem.json = deepCopy(inputItem.json);\n\t\t\tbreak;\n\t\tcase INCLUDE.SELECTED:\n\t\t\tconst includeFields = (this.getNodeParameter('includeFields', itemIndex) as string)\n\t\t\t\t.split(',')\n\t\t\t\t.map((item) => item.trim())\n\t\t\t\t.filter((item) => item);\n\n\t\t\tfor (const key of includeFields) {\n\t\t\t\tconst fieldValue = fieldHelper.get(inputItem.json, key) as IDataObject;\n\t\t\t\tlet keyToSet = key;\n\t\t\t\tif (options.dotNotation !== false && key.includes('.')) {\n\t\t\t\t\tkeyToSet = key.split('.').pop() as string;\n\t\t\t\t}\n\t\t\t\tfieldHelper.set(newItem.json, keyToSet, fieldValue);\n\t\t\t}\n\t\t\tbreak;\n\t\tcase INCLUDE.EXCEPT:\n\t\t\tconst excludeFields = (this.getNodeParameter('excludeFields', itemIndex) as string)\n\t\t\t\t.split(',')\n\t\t\t\t.map((item) => item.trim())\n\t\t\t\t.filter((item) => item);\n\n\t\t\tconst inputData = deepCopy(inputItem.json);\n\n\t\t\tfor (const key of excludeFields) {\n\t\t\t\tfieldHelper.unset(inputData, key);\n\t\t\t}\n\n\t\t\tnewItem.json = inputData;\n\t\t\tbreak;\n\t\tcase INCLUDE.NONE:\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tthrow new ApplicationError(`The include option \"${options.include}\" is not known!`, {\n\t\t\t\tlevel: 'warning',\n\t\t\t});\n\t}\n\n\tfor (const key of Object.keys(newFields)) {\n\t\tfieldHelper.set(newItem.json, key, newFields[key] as IDataObject);\n\t}\n\n\treturn newItem;\n}\n\nexport const parseJsonParameter = (\n\tjsonData: string | IDataObject,\n\tnode: INode,\n\ti: number,\n\tentryName?: string,\n) => {\n\tlet returnData: IDataObject;\n\tconst location = entryName ? `entry \"${entryName}\" inside 'Fields to Set'` : \"'JSON Output'\";\n\n\tif (typeof jsonData === 'string') {\n\t\ttry {\n\t\t\treturnData = jsonParse<IDataObject>(jsonData);\n\t\t} catch (error) {\n\t\t\tlet recoveredData = '';\n\t\t\ttry {\n\t\t\t\trecoveredData = jsonData\n\t\t\t\t\t.replace(/'/g, '\"') // Replace single quotes with double quotes\n\t\t\t\t\t.replace(/(['\"])?([a-zA-Z0-9_]+)(['\"])?:/g, '\"$2\":') // Wrap keys in double quotes\n\t\t\t\t\t.replace(/,\\s*([\\]}])/g, '$1') // Remove trailing commas from objects\n\t\t\t\t\t.replace(/,+$/, ''); // Remove trailing comma\n\t\t\t\treturnData = jsonParse<IDataObject>(recoveredData);\n\t\t\t} catch (err) {\n\t\t\t\tconst description =\n\t\t\t\t\trecoveredData === jsonData ? jsonData : `${recoveredData};\\n Original input: ${jsonData}`;\n\t\t\t\tthrow new NodeOperationError(node, `The ${location} in item ${i} contains invalid JSON`, {\n\t\t\t\t\tdescription,\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t} else {\n\t\treturnData = jsonData;\n\t}\n\n\tif (returnData === undefined || typeof returnData !== 'object' || Array.isArray(returnData)) {\n\t\tthrow new NodeOperationError(\n\t\t\tnode,\n\t\t\t`The ${location} in item ${i} does not contain a valid JSON object`,\n\t\t);\n\t}\n\n\treturn returnData;\n};\n\nexport const validateEntry = (\n\tname: string,\n\ttype: FieldType,\n\tvalue: unknown,\n\tnode: INode,\n\titemIndex: number,\n\tignoreErrors = false,\n\tnodeVersion?: number,\n) => {\n\tif (nodeVersion && nodeVersion >= 3.2 && (value === undefined || value === null)) {\n\t\treturn { name, value: null };\n\t}\n\n\tconst description = `To fix the error try to change the type for the field \"${name}\" or activate the option “Ignore Type Conversion Errors” to apply a less strict type validation`;\n\n\tif (type === 'string') {\n\t\tif (nodeVersion && nodeVersion > 3 && (value === undefined || value === null)) {\n\t\t\tif (ignoreErrors) {\n\t\t\t\treturn { name, value: null };\n\t\t\t} else {\n\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\tnode,\n\t\t\t\t\t`'${name}' expects a ${type} but we got ${getValueDescription(value)} [item ${itemIndex}]`,\n\t\t\t\t\t{ description },\n\t\t\t\t);\n\t\t\t}\n\t\t} else if (typeof value === 'object') {\n\t\t\tvalue = JSON.stringify(value);\n\t\t} else {\n\t\t\tvalue = String(value);\n\t\t}\n\t}\n\n\tconst validationResult = validateFieldType(name, value, type);\n\n\tif (!validationResult.valid) {\n\t\tif (ignoreErrors) {\n\t\t\treturn { name, value: value ?? null };\n\t\t} else {\n\t\t\tconst message = `${validationResult.errorMessage} [item ${itemIndex}]`;\n\t\t\tthrow new NodeOperationError(node, message, {\n\t\t\t\titemIndex,\n\t\t\t\tdescription,\n\t\t\t});\n\t\t}\n\t}\n\n\treturn {\n\t\tname,\n\t\tvalue: validationResult.newValue ?? null,\n\t};\n};\n\nexport function resolveRawData(\n\tthis: IExecuteFunctions | ISupplyDataFunctions,\n\trawData: string,\n\ti: number,\n) {\n\tconst resolvables = getResolvables(rawData);\n\tlet returnData: string = rawData;\n\n\tif (resolvables.length) {\n\t\tfor (const resolvable of resolvables) {\n\t\t\tconst resolvedValue = this.evaluateExpression(`${resolvable}`, i);\n\n\t\t\tif (typeof resolvedValue === 'object' && resolvedValue !== null) {\n\t\t\t\treturnData = returnData.replace(resolvable, JSON.stringify(resolvedValue));\n\t\t\t} else {\n\t\t\t\treturnData = returnData.replace(resolvable, resolvedValue as string);\n\t\t\t}\n\t\t}\n\t}\n\treturn returnData;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAgB;AAChB,iBAAgB;AAChB,mBAAkB;AAClB,0BAOO;AAWP,wBAAwB;AACxB,uBAAoD;AAEpD,MAAM,uBAAuB,CAAC,gBAA0B;AACvD,MAAI,gBAAgB,OAAO;AAC1B,WAAO;AAAA,MACN,KAAK,CAAC,MAAmB,KAAa,UAAuB;AAC5D,uBAAAA,SAAI,MAAM,KAAK,KAAK;AAAA,MACrB;AAAA,MACA,KAAK,CAAC,MAAmB,QAAgB;AACxC,mBAAO,WAAAC,SAAI,MAAM,GAAG;AAAA,MACrB;AAAA,MACA,OAAO,CAAC,MAAmB,QAAgB;AAC1C,yBAAAC,SAAM,MAAM,GAAG;AAAA,MAChB;AAAA,IACD;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,KAAK,CAAC,MAAmB,KAAa,UAAuB;AAC5D,iBAAK,sCAAoB,MAAM,GAAG,CAAC,IAAI;AAAA,MACxC;AAAA,MACA,KAAK,CAAC,MAAmB,QAAgB;AACxC,eAAO,SAAK,sCAAoB,MAAM,GAAG,CAAC;AAAA,MAC3C;AAAA,MACA,OAAO,CAAC,MAAmB,QAAgB;AAC1C,eAAO,SAAK,sCAAoB,MAAM,GAAG,CAAC;AAAA,MAC3C;AAAA,IACD;AAAA,EACD;AACD;AAEO,SAAS,kBAEf,WACA,WACA,WACA,SACA,aACC;AACD,QAAM,UAA8B;AAAA,IACnC,MAAM,CAAC;AAAA,IACP,YAAY,EAAE,MAAM,UAAU;AAAA,EAC/B;AAEA,QAAM,gBACJ,eAAe,OAAO,CAAC,QAAQ,eAAe,QAAQ,YAAY,UAClE,cAAc,OAAO,CAAC,CAAC,QAAQ;AACjC,MAAI,iBAAiB,UAAU,WAAW,QAAW;AAIpD,YAAQ,SAAS,CAAC;AAClB,WAAO,OAAO,QAAQ,QAAQ,UAAU,MAAM;AAAA,EAC/C;AAEA,QAAM,cAAc,qBAAqB,QAAQ,WAAW;AAE5D,UAAQ,QAAQ,SAAS;AAAA,IACxB,KAAK,0BAAQ;AACZ,cAAQ,WAAO,8BAAS,UAAU,IAAI;AACtC;AAAA,IACD,KAAK,0BAAQ;AACZ,YAAM,gBAAiB,KAAK,iBAAiB,iBAAiB,SAAS,EACrE,MAAM,GAAG,EACT,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,EACzB,OAAO,CAAC,SAAS,IAAI;AAEvB,iBAAW,OAAO,eAAe;AAChC,cAAM,aAAa,YAAY,IAAI,UAAU,MAAM,GAAG;AACtD,YAAI,WAAW;AACf,YAAI,QAAQ,gBAAgB,SAAS,IAAI,SAAS,GAAG,GAAG;AACvD,qBAAW,IAAI,MAAM,GAAG,EAAE,IAAI;AAAA,QAC/B;AACA,oBAAY,IAAI,QAAQ,MAAM,UAAU,UAAU;AAAA,MACnD;AACA;AAAA,IACD,KAAK,0BAAQ;AACZ,YAAM,gBAAiB,KAAK,iBAAiB,iBAAiB,SAAS,EACrE,MAAM,GAAG,EACT,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,EACzB,OAAO,CAAC,SAAS,IAAI;AAEvB,YAAM,gBAAY,8BAAS,UAAU,IAAI;AAEzC,iBAAW,OAAO,eAAe;AAChC,oBAAY,MAAM,WAAW,GAAG;AAAA,MACjC;AAEA,cAAQ,OAAO;AACf;AAAA,IACD,KAAK,0BAAQ;AACZ;AAAA,IACD;AACC,YAAM,IAAI,qCAAiB,uBAAuB,QAAQ,OAAO,mBAAmB;AAAA,QACnF,OAAO;AAAA,MACR,CAAC;AAAA,EACH;AAEA,aAAW,OAAO,OAAO,KAAK,SAAS,GAAG;AACzC,gBAAY,IAAI,QAAQ,MAAM,KAAK,UAAU,GAAG,CAAgB;AAAA,EACjE;AAEA,SAAO;AACR;AAEO,MAAM,qBAAqB,CACjC,UACA,MACA,GACA,cACI;AACJ,MAAI;AACJ,QAAM,WAAW,YAAY,UAAU,SAAS,6BAA6B;AAE7E,MAAI,OAAO,aAAa,UAAU;AACjC,QAAI;AACH,uBAAa,+BAAuB,QAAQ;AAAA,IAC7C,SAAS,OAAO;AACf,UAAI,gBAAgB;AACpB,UAAI;AACH,wBAAgB,SACd,QAAQ,MAAM,GAAG,EACjB,QAAQ,mCAAmC,OAAO,EAClD,QAAQ,gBAAgB,IAAI,EAC5B,QAAQ,OAAO,EAAE;AACnB,yBAAa,+BAAuB,aAAa;AAAA,MAClD,SAAS,KAAK;AACb,cAAM,cACL,kBAAkB,WAAW,WAAW,GAAG,aAAa;AAAA,mBAAuB,QAAQ;AACxF,cAAM,IAAI,uCAAmB,MAAM,OAAO,QAAQ,YAAY,CAAC,0BAA0B;AAAA,UACxF;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD;AAAA,EACD,OAAO;AACN,iBAAa;AAAA,EACd;AAEA,MAAI,eAAe,UAAa,OAAO,eAAe,YAAY,MAAM,QAAQ,UAAU,GAAG;AAC5F,UAAM,IAAI;AAAA,MACT;AAAA,MACA,OAAO,QAAQ,YAAY,CAAC;AAAA,IAC7B;AAAA,EACD;AAEA,SAAO;AACR;AAEO,MAAM,gBAAgB,CAC5B,MACA,MACA,OACA,MACA,WACA,eAAe,OACf,gBACI;AACJ,MAAI,eAAe,eAAe,QAAQ,UAAU,UAAa,UAAU,OAAO;AACjF,WAAO,EAAE,MAAM,OAAO,KAAK;AAAA,EAC5B;AAEA,QAAM,cAAc,0DAA0D,IAAI;AAElF,MAAI,SAAS,UAAU;AACtB,QAAI,eAAe,cAAc,MAAM,UAAU,UAAa,UAAU,OAAO;AAC9E,UAAI,cAAc;AACjB,eAAO,EAAE,MAAM,OAAO,KAAK;AAAA,MAC5B,OAAO;AACN,cAAM,IAAI;AAAA,UACT;AAAA,UACA,IAAI,IAAI,eAAe,IAAI,mBAAe,yCAAoB,KAAK,CAAC,UAAU,SAAS;AAAA,UACvF,EAAE,YAAY;AAAA,QACf;AAAA,MACD;AAAA,IACD,WAAW,OAAO,UAAU,UAAU;AACrC,cAAQ,KAAK,UAAU,KAAK;AAAA,IAC7B,OAAO;AACN,cAAQ,OAAO,KAAK;AAAA,IACrB;AAAA,EACD;AAEA,QAAM,uBAAmB,uCAAkB,MAAM,OAAO,IAAI;AAE5D,MAAI,CAAC,iBAAiB,OAAO;AAC5B,QAAI,cAAc;AACjB,aAAO,EAAE,MAAM,OAAO,SAAS,KAAK;AAAA,IACrC,OAAO;AACN,YAAM,UAAU,GAAG,iBAAiB,YAAY,UAAU,SAAS;AACnE,YAAM,IAAI,uCAAmB,MAAM,SAAS;AAAA,QAC3C;AAAA,QACA;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AAEA,SAAO;AAAA,IACN;AAAA,IACA,OAAO,iBAAiB,YAAY;AAAA,EACrC;AACD;AAEO,SAAS,eAEf,SACA,GACC;AACD,QAAM,kBAAc,iCAAe,OAAO;AAC1C,MAAI,aAAqB;AAEzB,MAAI,YAAY,QAAQ;AACvB,eAAW,cAAc,aAAa;AACrC,YAAM,gBAAgB,KAAK,mBAAmB,GAAG,UAAU,IAAI,CAAC;AAEhE,UAAI,OAAO,kBAAkB,YAAY,kBAAkB,MAAM;AAChE,qBAAa,WAAW,QAAQ,YAAY,KAAK,UAAU,aAAa,CAAC;AAAA,MAC1E,OAAO;AACN,qBAAa,WAAW,QAAQ,YAAY,aAAuB;AAAA,MACpE;AAAA,IACD;AAAA,EACD;AACA,SAAO;AACR;", "names": ["set", "get", "unset"]}