import { Paginator } from "@smithy/types";
import { ListFlowExecutionEventsCommandInput, ListFlowExecutionEventsCommandOutput } from "../commands/ListFlowExecutionEventsCommand";
import { BedrockAgentRuntimePaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListFlowExecutionEvents: (config: BedrockAgentRuntimePaginationConfiguration, input: ListFlowExecutionEventsCommandInput, ...rest: any[]) => Paginator<ListFlowExecutionEventsCommandOutput>;
