{"version": 3, "sources": ["../../../nodes/Okta/UserDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nimport { getCursorPaginator, simplifyGetAllResponse, simplifyGetResponse } from './UserFunctions';\nconst BASE_API_URL = '/api/v1/users/';\nexport const userOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t// Create Operation\n\t\t\t{\n\t\t\t\tname: 'Create',\n\t\t\t\tvalue: 'create',\n\t\t\t\tdescription: 'Create a new user',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\turl: BASE_API_URL,\n\t\t\t\t\t\tqs: { activate: '={{$parameter[\"activate\"]}}' },\n\t\t\t\t\t\treturnFullResponse: true,\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\taction: 'Create a new user',\n\t\t\t},\n\t\t\t// Delete Operation\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'delete',\n\t\t\t\tdescription: 'Delete an existing user',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'DELETE',\n\t\t\t\t\t\turl: '={{\"/api/v1/users/\" + $parameter[\"userId\"]}}',\n\t\t\t\t\t\treturnFullResponse: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\ttype: 'set',\n\t\t\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\t\t\tvalue: '={{ { \"success\": true } }}',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\taction: 'Delete a user',\n\t\t\t},\n\t\t\t// Get Operation\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\tdescription: 'Get details of a user',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\t\turl: '={{\"/api/v1/users/\" + $parameter[\"userId\"]}}',\n\t\t\t\t\t\treturnFullResponse: true,\n\t\t\t\t\t\tqs: {},\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [simplifyGetResponse],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\taction: 'Get a user',\n\t\t\t},\n\t\t\t// Get All Operation\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Get many users',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\t\turl: BASE_API_URL,\n\t\t\t\t\t\tqs: { search: '={{$parameter[\"searchQuery\"]}}' },\n\t\t\t\t\t\treturnFullResponse: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [simplifyGetAllResponse],\n\t\t\t\t\t},\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tpaginate: true,\n\t\t\t\t\t},\n\t\t\t\t\toperations: {\n\t\t\t\t\t\tpagination: getCursorPaginator(),\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\taction: 'Get many users',\n\t\t\t},\n\t\t\t// Update Operation\n\t\t\t{\n\t\t\t\tname: 'Update',\n\t\t\t\tvalue: 'update',\n\t\t\t\tdescription: 'Update an existing user',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\turl: '={{\"/api/v1/users/\" + $parameter[\"userId\"]}}',\n\t\t\t\t\t\treturnFullResponse: true,\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\taction: 'Update a user',\n\t\t\t},\n\t\t],\n\t\tdefault: 'getAll',\n\t},\n];\nconst mainProfileFields: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'First Name',\n\t\tname: 'firstName',\n\t\ttype: 'string',\n\t\tplaceholder: 'e.g. Nathan',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.firstName',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Last Name',\n\t\tname: 'lastName',\n\t\ttype: 'string',\n\t\tplaceholder: 'e.g. Smith',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.lastName',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Username',\n\t\tname: 'login',\n\t\ttype: 'string',\n\t\tplaceholder: 'e.g. <EMAIL>',\n\t\thint: 'Unique identifier for the user, must be an email',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.login',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Email',\n\t\tname: 'email',\n\t\ttype: 'string',\n\t\tplaceholder: 'e.g. <EMAIL>',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.email',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n];\nconst createFields: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'City',\n\t\tname: 'city',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.city',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Cost Center',\n\t\tname: 'costCenter',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.costCenter',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Country Code',\n\t\tname: 'countryCode',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.countryCode',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Department',\n\t\tname: 'department',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.department',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Display Name',\n\t\tname: 'displayName',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.displayName',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Division',\n\t\tname: 'division',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.division',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Employee Number',\n\t\tname: 'employeeNumber',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.employeeNumber',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Honorific Prefix',\n\t\tname: 'honorificPrefix',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.honorificPrefix',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Honorific Suffix',\n\t\tname: 'honorificSuffix',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.honorificSuffix',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Locale',\n\t\tname: 'locale',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.locale',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Manager',\n\t\tname: 'manager',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.manager',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'ManagerId',\n\t\tname: 'managerId',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.managerId',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Middle Name',\n\t\tname: 'middleName',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.middleName',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Mobile Phone',\n\t\tname: 'mobilePhone',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.mobilePhone',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Nick Name',\n\t\tname: 'nickName',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.nickName',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Password',\n\t\tname: 'password',\n\t\ttype: 'string',\n\t\ttypeOptions: { password: true },\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'credentials.password.value',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Organization',\n\t\tname: 'organization',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.organization',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Postal Address',\n\t\tname: 'postalAddress',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.postalAddress',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Preferred Language',\n\t\tname: 'preferredLanguage',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.preferredLanguage',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Primary Phone',\n\t\tname: 'primaryPhone',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.primaryPhone',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Profile Url',\n\t\tname: 'profileUrl',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.profileUrl',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Recovery Question Answer',\n\t\tname: 'recoveryQuestionAnswer',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'credentials.recovery_question.answer',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Recovery Question Question',\n\t\tname: 'recoveryQuestionQuestion',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'credentials.recovery_question.question',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Second Email',\n\t\tname: 'secondEmail',\n\t\ttype: 'string',\n\t\ttypeOptions: { email: true },\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.secondEmail',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'State',\n\t\tname: 'state',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.state',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Street Address',\n\t\tname: 'streetAddress',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.streetAddress',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Timezone',\n\t\tname: 'timezone',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.timezone',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Title',\n\t\tname: 'title',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.title',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'User Type',\n\t\tname: 'userType',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.userType',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Zip Code',\n\t\tname: 'zipCode',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.zipCode',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n];\nconst updateFields: INodeProperties[] = createFields\n\t.concat(mainProfileFields)\n\t.sort((a, b) => a.displayName.localeCompare(b.displayName));\n\nexport const userFields: INodeProperties[] = [\n\t// Fields for 'get', 'update', and 'delete' operations\n\n\t{\n\t\tdisplayName: 'User',\n\t\tname: 'userId',\n\t\ttype: 'resourceLocator',\n\t\tdefault: { mode: 'list', value: '' },\n\t\trequired: true,\n\t\tmodes: [\n\t\t\t{\n\t\t\t\tdisplayName: 'From List',\n\t\t\t\tname: 'list',\n\t\t\t\ttype: 'list',\n\t\t\t\tplaceholder: 'Select a user...',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tsearchListMethod: 'getUsers',\n\t\t\t\t\tsearchable: true,\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'By username',\n\t\t\t\tname: 'login',\n\t\t\t\ttype: 'string',\n\t\t\t\tplaceholder: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'ID',\n\t\t\t\tname: 'id',\n\t\t\t\ttype: 'string',\n\t\t\t\tplaceholder: 'e.g. 00u1abcd2345EfGHIjk6',\n\t\t\t},\n\t\t],\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['get', 'update', 'delete'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'The user you want to operate on. Choose from the list, or specify an ID.',\n\t},\n\t//  Fields specific to 'create' operation\n\t{\n\t\tdisplayName: 'First Name',\n\t\tname: 'firstName',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tplaceholder: 'e.g. Nathan',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.firstName',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Last Name',\n\t\tname: 'lastName',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tplaceholder: 'e.g. Smith',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.lastName',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Username',\n\t\tname: 'login',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tplaceholder: 'e.g. <EMAIL>',\n\t\thint: 'Unique identifier for the user, must be an email',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.login',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Email',\n\t\tname: 'email',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tplaceholder: 'e.g. <EMAIL>',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tproperty: 'profile.email',\n\t\t\t\ttype: 'body',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Activate',\n\t\tname: 'activate',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\tdefault: true,\n\t\tdescription: 'Whether to activate the user and allow access to all assigned applications',\n\t},\n\t{\n\t\tdisplayName: 'Fields',\n\t\tname: 'getCreateFields',\n\t\ttype: 'collection',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\tplaceholder: 'Add field',\n\t\toptions: createFields,\n\t},\n\n\t// Fields for 'update' operations\n\t{\n\t\tdisplayName: 'Fields',\n\t\tname: 'getUpdateFields',\n\t\ttype: 'collection',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\tplaceholder: 'Add field',\n\t\toptions: updateFields,\n\t},\n\n\t// Fields specific to 'getAll' operation\n\t{\n\t\tdisplayName: 'Search Query',\n\t\tname: 'searchQuery',\n\t\ttype: 'string',\n\t\tplaceholder: 'e.g. profile.lastName sw \"Smi\"',\n\t\thint: 'Filter users by using the allowed syntax. <a href=\"https://developer.okta.com/docs/reference/core-okta-api/#filter\" target=\"_blank\">More info</a>.',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\trouting: {\n\t\t\trequest: {\n\t\t\t\tqs: {\n\t\t\t\t\tprefix: '={{$value}}',\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 200,\n\t\t},\n\t\tdefault: 20,\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\ttype: 'query',\n\t\t\t\tproperty: 'limit',\n\t\t\t},\n\t\t\toutput: {\n\t\t\t\tmaxResults: '={{$value}}', // Set maxResults to the value of current parameter\n\t\t\t},\n\t\t},\n\t\tdescription: 'Max number of results to return',\n\t},\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t},\n\t// Fields for 'get' and 'getAll' operations\n\t{\n\t\tdisplayName: 'Simplify',\n\t\tname: 'simplify',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['get', 'getAll'],\n\t\t\t},\n\t\t},\n\t\tdefault: true,\n\t\tdescription: 'Whether to return a simplified version of the response instead of the raw data',\n\t},\n\t// Fields specific to 'delete' operation\n\t{\n\t\tdisplayName: 'Send Email',\n\t\tname: 'sendEmail',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['delete'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription: 'Whether to send a deactivation email to the administrator',\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,2BAAgF;AAChF,MAAM,eAAe;AACd,MAAM,iBAAoC;AAAA,EAChD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA;AAAA,MAER;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,IAAI,EAAE,UAAU,8BAA8B;AAAA,YAC9C,oBAAoB;AAAA,UACrB;AAAA,QACD;AAAA,QACA,QAAQ;AAAA,MACT;AAAA;AAAA,MAEA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,oBAAoB;AAAA,UACrB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa;AAAA,cACZ;AAAA,gBACC,MAAM;AAAA,gBACN,YAAY;AAAA,kBACX,OAAO;AAAA,gBACR;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,QAAQ;AAAA,MACT;AAAA;AAAA,MAEA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,oBAAoB;AAAA,YACpB,IAAI,CAAC;AAAA,UACN;AAAA,UACA,QAAQ;AAAA,YACP,aAAa,CAAC,wCAAmB;AAAA,UAClC;AAAA,QACD;AAAA,QACA,QAAQ;AAAA,MACT;AAAA;AAAA,MAEA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,IAAI,EAAE,QAAQ,iCAAiC;AAAA,YAC/C,oBAAoB;AAAA,UACrB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa,CAAC,2CAAsB;AAAA,UACrC;AAAA,UACA,MAAM;AAAA,YACL,UAAU;AAAA,UACX;AAAA,UACA,YAAY;AAAA,YACX,gBAAY,yCAAmB;AAAA,UAChC;AAAA,QACD;AAAA,QACA,QAAQ;AAAA,MACT;AAAA;AAAA,MAEA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,oBAAoB;AAAA,UACrB;AAAA,QACD;AAAA,QACA,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AACA,MAAM,oBAAuC;AAAA,EAC5C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AACD;AACA,MAAM,eAAkC;AAAA,EACvC;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,EAAE,UAAU,KAAK;AAAA,IAC9B,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,EAAE,OAAO,KAAK;AAAA,IAC3B,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AACD;AACA,MAAM,eAAkC,aACtC,OAAO,iBAAiB,EACxB,KAAK,CAAC,GAAG,MAAM,EAAE,YAAY,cAAc,EAAE,WAAW,CAAC;AAEpD,MAAM,aAAgC;AAAA;AAAA,EAG5C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,EAAE,MAAM,QAAQ,OAAO,GAAG;AAAA,IACnC,UAAU;AAAA,IACV,OAAO;AAAA,MACN;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,aAAa;AAAA,UACZ,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACb;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACd;AAAA,IACD;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,OAAO,UAAU,QAAQ;AAAA,MACtC;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA;AAAA,EAEA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,aAAa;AAAA,IACb,SAAS;AAAA,EACV;AAAA;AAAA,EAGA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,aAAa;AAAA,IACb,SAAS;AAAA,EACV;AAAA;AAAA,EAGA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,SAAS;AAAA,MACR,SAAS;AAAA,QACR,IAAI;AAAA,UACH,QAAQ;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,QACpB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,SAAS;AAAA,MACR,MAAM;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,MACX;AAAA,MACA,QAAQ;AAAA,QACP,YAAY;AAAA;AAAA,MACb;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA;AAAA,EAEA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,OAAO,QAAQ;AAAA,MAC5B;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA;AAAA,EAEA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AACD;", "names": []}