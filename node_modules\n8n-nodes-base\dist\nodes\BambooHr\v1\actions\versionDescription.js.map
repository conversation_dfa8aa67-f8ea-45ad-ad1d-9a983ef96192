{"version": 3, "sources": ["../../../../../nodes/BambooHr/v1/actions/versionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport { NodeConnectionTypes, type INodeTypeDescription } from 'n8n-workflow';\n\nimport * as companyReport from './companyReport';\nimport * as employee from './employee';\nimport * as employeeDocument from './employeeDocument';\nimport * as file from './file';\n\nexport const versionDescription: INodeTypeDescription = {\n\tcredentials: [\n\t\t{\n\t\t\tname: 'bambooHrApi',\n\t\t\trequired: true,\n\t\t\ttestedBy: 'bambooHrApiCredentialTest',\n\t\t},\n\t],\n\tdefaults: {\n\t\tname: 'BambooHR',\n\t},\n\tdescription: 'Consume BambooHR API',\n\tdisplayName: 'BambooHR',\n\tgroup: ['transform'],\n\t// eslint-disable-next-line n8n-nodes-base/node-class-description-icon-not-svg\n\ticon: 'file:bambooHr.png',\n\tinputs: [NodeConnectionTypes.Main],\n\tname: 'bambooHr',\n\toutputs: [NodeConnectionTypes.Main],\n\tproperties: [\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Company Report',\n\t\t\t\t\tvalue: 'companyReport',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Employee',\n\t\t\t\t\tvalue: 'employee',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Employee Document',\n\t\t\t\t\tvalue: 'employeeDocument',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'File',\n\t\t\t\t\tvalue: 'file',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'employee',\n\t\t},\n\t\t...employee.descriptions,\n\t\t...employeeDocument.descriptions,\n\t\t...file.descriptions,\n\t\t...companyReport.descriptions,\n\t],\n\tsubtitle: '={{$parameter[\"resource\"] + \": \" + $parameter[\"operation\"]}}',\n\tversion: 1,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAA+D;AAE/D,oBAA+B;AAC/B,eAA0B;AAC1B,uBAAkC;AAClC,WAAsB;AAEf,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,IACZ;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,EACD;AAAA,EACA,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,aAAa;AAAA,EACb,aAAa;AAAA,EACb,OAAO,CAAC,WAAW;AAAA;AAAA,EAEnB,MAAM;AAAA,EACN,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,MAAM;AAAA,EACN,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,YAAY;AAAA,IACX;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA,GAAG,SAAS;AAAA,IACZ,GAAG,iBAAiB;AAAA,IACpB,GAAG,KAAK;AAAA,IACR,GAAG,cAAc;AAAA,EAClB;AAAA,EACA,UAAU;AAAA,EACV,SAAS;AACV;", "names": []}