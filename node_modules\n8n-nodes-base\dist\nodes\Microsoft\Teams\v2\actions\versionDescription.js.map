{"version": 3, "sources": ["../../../../../../nodes/Microsoft/Teams/v2/actions/versionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport { NodeConnectionTypes, type INodeTypeDescription } from 'n8n-workflow';\n\nimport * as channel from './channel';\nimport * as channelMessage from './channelMessage';\nimport * as chatMessage from './chatMessage';\nimport * as task from './task';\nimport { sendAndWaitWebhooksDescription } from '../../../../../utils/sendAndWait/descriptions';\n\nexport const versionDescription: INodeTypeDescription = {\n\tdisplayName: 'Microsoft Teams',\n\tname: 'microsoftTeams',\n\ticon: 'file:teams.svg',\n\tgroup: ['input'],\n\tversion: 2,\n\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\tdescription: 'Consume Microsoft Teams API',\n\tdefaults: {\n\t\tname: 'Microsoft Teams',\n\t},\n\tinputs: [NodeConnectionTypes.Main],\n\toutputs: [NodeConnectionTypes.Main],\n\tcredentials: [\n\t\t{\n\t\t\tname: 'microsoftTeamsOAuth2Api',\n\t\t\trequired: true,\n\t\t},\n\t],\n\twebhooks: sendAndWaitWebhooksDescription,\n\tproperties: [\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Channel',\n\t\t\t\t\tvalue: 'channel',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Channel Message',\n\t\t\t\t\tvalue: 'channelMessage',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Chat Message',\n\t\t\t\t\tvalue: 'chatMessage',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Task',\n\t\t\t\t\tvalue: 'task',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'channel',\n\t\t},\n\n\t\t...channel.description,\n\t\t...channelMessage.description,\n\t\t...chatMessage.description,\n\t\t...task.description,\n\t],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAA+D;AAE/D,cAAyB;AACzB,qBAAgC;AAChC,kBAA6B;AAC7B,WAAsB;AACtB,0BAA+C;AAExC,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,OAAO;AAAA,EACf,SAAS;AAAA,EACT,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,aAAa;AAAA,IACZ;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA,EACD;AAAA,EACA,UAAU;AAAA,EACV,YAAY;AAAA,IACX;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IAEA,GAAG,QAAQ;AAAA,IACX,GAAG,eAAe;AAAA,IAClB,GAAG,YAAY;AAAA,IACf,GAAG,KAAK;AAAA,EACT;AACD;", "names": []}