{"version": 3, "sources": ["../../credentials/VerticaApi.credentials.ts"], "sourcesContent": ["import type {\n\tIAuthenticateGeneric,\n\tICredentialTestRequest,\n\tICredentialType,\n\tINodeProperties,\n} from 'n8n-workflow';\n\nexport class VerticaApi implements ICredentialType {\n\tname = 'verticaApi';\n\n\tdisplayName = 'Vertica API';\n\n\tdocumentationUrl = 'vertica';\n\n\thttpRequestNode = {\n\t\tname: 'Vertica',\n\t\tdocsUrl: 'vertica',\n\t\tapiBaseUrlPlaceholder: 'http://<server>:<port>/v1/',\n\t};\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'URL',\n\t\t\tname: 'url',\n\t\t\trequired: true,\n\t\t\ttype: 'string',\n\t\t\tdefault: 'https://localhost:8443',\n\t\t\tplaceholder: 'https://<server>:<port>',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Username',\n\t\t\tname: 'username',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\tdescription: 'The username for accessing the Vertica database.',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Password',\n\t\t\tname: 'password',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\tdefault: '',\n\t\t\tdescription: 'The password for accessing the Vertica database.',\n\t\t},\n\t];\n\n\tauthenticate: IAuthenticateGeneric = {\n\t\ttype: 'generic',\n\t\tproperties: {\n\t\t\tauth: {\n\t\t\t\tusername: '={{$credentials.username}}',\n\t\t\t\tpassword: '={{$credentials.password}}',\n\t\t\t},\n\t\t},\n\t};\n\n\ttest: ICredentialTestRequest = {\n\t\trequest: {\n\t\t\tbaseURL: '={{$credentials.url}}'.replace(/\\/$/, ''),\n\t\t\turl: '/v1/health',\n\t\t\tmethod: 'GET',\n\t\t\tskipSslCertificateValidation: true,\n\t\t},\n\t\trules: [\n\t\t\t{\n\t\t\t\ttype: 'responseCode',\n\t\t\t\tproperties: {\n\t\t\t\t\tvalue: 403,\n\t\t\t\t\tmessage: 'Connection failed: Invalid credentials or insufficient permissions',\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\ttype: 'responseCode',\n\t\t\t\tproperties: {\n\t\t\t\t\tvalue: 503,\n\t\t\t\t\tmessage: 'Service unavailable: Server is overloaded or under maintenance',\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\ttype: 'responseCode',\n\t\t\t\tproperties: {\n\t\t\t\t\tvalue: 504,\n\t\t\t\t\tmessage: 'Gateway timeout: Upstream server took too long to respond',\n\t\t\t\t},\n\t\t\t},\n\t\t],\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOO,MAAM,WAAsC;AAAA,EAA5C;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,2BAAkB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS;AAAA,MACT,uBAAuB;AAAA,IACxB;AAEA,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAEA,wBAAqC;AAAA,MACpC,MAAM;AAAA,MACN,YAAY;AAAA,QACX,MAAM;AAAA,UACL,UAAU;AAAA,UACV,UAAU;AAAA,QACX;AAAA,MACD;AAAA,IACD;AAEA,gBAA+B;AAAA,MAC9B,SAAS;AAAA,QACR,SAAS,wBAAwB,QAAQ,OAAO,EAAE;AAAA,QAClD,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,8BAA8B;AAAA,MAC/B;AAAA,MACA,OAAO;AAAA,QACN;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,YACX,OAAO;AAAA,YACP,SAAS;AAAA,UACV;AAAA,QACD;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,YACX,OAAO;AAAA,YACP,SAAS;AAAA,UACV;AAAA,QACD;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,YACX,OAAO;AAAA,YACP,SAAS;AAAA,UACV;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA;AACD;", "names": []}