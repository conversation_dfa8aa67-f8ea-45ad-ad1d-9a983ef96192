{"version": 3, "sources": ["../../../../../../../nodes/Microsoft/Excel/v2/actions/worksheet/Worksheet.resource.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nimport * as append from './append.operation';\nimport * as clear from './clear.operation';\nimport * as deleteWorksheet from './deleteWorksheet.operation';\nimport * as getAll from './getAll.operation';\nimport * as readRows from './readRows.operation';\nimport * as update from './update.operation';\nimport * as upsert from './upsert.operation';\n\nexport { append, clear, deleteWorksheet, getAll, readRows, update, upsert };\n\nexport const description: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worksheet'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Append',\n\t\t\t\tvalue: 'append',\n\t\t\t\tdescription: 'Append data to sheet',\n\t\t\t\taction: 'Append data to sheet',\n\t\t\t},\n\t\t\t{\n\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-option-name-wrong-for-upsert\n\t\t\t\tname: 'Append or Update',\n\t\t\t\tvalue: 'upsert',\n\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-description-wrong-for-upsert\n\t\t\t\tdescription: 'Append a new row or update the current one if it already exists (upsert)',\n\t\t\t\taction: 'Append or update a sheet',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Clear',\n\t\t\t\tvalue: 'clear',\n\t\t\t\tdescription: 'Clear sheet',\n\t\t\t\taction: 'Clear sheet',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'deleteWorksheet',\n\t\t\t\tdescription: 'Delete sheet',\n\t\t\t\taction: 'Delete sheet',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Get a list of sheets',\n\t\t\t\taction: 'Get sheets',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Rows',\n\t\t\t\tvalue: 'readRows',\n\t\t\t\tdescription: 'Retrieve a list of sheet rows',\n\t\t\t\taction: 'Get rows from sheet',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Update',\n\t\t\t\tvalue: 'update',\n\t\t\t\tdescription: 'Update rows of a sheet or sheet range',\n\t\t\t\taction: 'Update sheet',\n\t\t\t},\n\t\t],\n\t\tdefault: 'getAll',\n\t},\n\t...append.description,\n\t...clear.description,\n\t...deleteWorksheet.description,\n\t...getAll.description,\n\t...readRows.description,\n\t...update.description,\n\t...upsert.description,\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,aAAwB;AACxB,YAAuB;AACvB,sBAAiC;AACjC,aAAwB;AACxB,eAA0B;AAC1B,aAAwB;AACxB,aAAwB;AAIjB,MAAM,cAAiC;AAAA,EAC7C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,MACvB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA;AAAA,QAEC,MAAM;AAAA,QACN,OAAO;AAAA;AAAA,QAEP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA,GAAG,OAAO;AAAA,EACV,GAAG,MAAM;AAAA,EACT,GAAG,gBAAgB;AAAA,EACnB,GAAG,OAAO;AAAA,EACV,GAAG,SAAS;AAAA,EACZ,GAAG,OAAO;AAAA,EACV,GAAG,OAAO;AACX;", "names": []}