{"version": 3, "sources": ["../../../../nodes/Webflow/V1/WebflowV1.node.ts"], "sourcesContent": ["import {\n\ttype IExecuteFunctions,\n\ttype IDataObject,\n\ttype INodeTypeBaseDescription,\n\ttype INodeExecutionData,\n\ttype INodeType,\n\ttype INodeTypeDescription,\n\tNodeConnectionTypes,\n} from 'n8n-workflow';\n\nimport { itemFields, itemOperations } from './ItemDescription';\nimport {\n\twebflowApiRequest,\n\twebflowApiRequestAllItems,\n\tgetSites,\n\tgetCollections,\n\tgetFields,\n} from '../GenericFunctions';\n\nexport class WebflowV1 implements INodeType {\n\tdescription: INodeTypeDescription;\n\n\tconstructor(baseDescription: INodeTypeBaseDescription) {\n\t\tthis.description = {\n\t\t\t...baseDescription,\n\t\t\tversion: 1,\n\t\t\tdescription: 'Consume the Webflow API',\n\t\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\t\tdefaults: {\n\t\t\t\tname: 'Webflow',\n\t\t\t},\n\t\t\tinputs: [NodeConnectionTypes.Main],\n\t\t\toutputs: [NodeConnectionTypes.Main],\n\t\t\tcredentials: [\n\t\t\t\t{\n\t\t\t\t\tname: 'webflow<PERSON><PERSON>',\n\t\t\t\t\trequired: true,\n\t\t\t\t\tdisplayOptions: {\n\t\t\t\t\t\tshow: {\n\t\t\t\t\t\t\tauthentication: ['accessToken'],\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'webflowOAuth2Api',\n\t\t\t\t\trequired: true,\n\t\t\t\t\tdisplayOptions: {\n\t\t\t\t\t\tshow: {\n\t\t\t\t\t\t\tauthentication: ['oAuth2'],\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t],\n\t\t\tproperties: [\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Authentication',\n\t\t\t\t\tname: 'authentication',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\toptions: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Access Token',\n\t\t\t\t\t\t\tvalue: 'accessToken',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'OAuth2',\n\t\t\t\t\t\t\tvalue: 'oAuth2',\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t\tdefault: 'accessToken',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Resource',\n\t\t\t\t\tname: 'resource',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\tnoDataExpression: true,\n\t\t\t\t\toptions: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Item',\n\t\t\t\t\t\t\tvalue: 'item',\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t\tdefault: 'item',\n\t\t\t\t},\n\t\t\t\t...itemOperations,\n\t\t\t\t...itemFields,\n\t\t\t],\n\t\t};\n\t}\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\tgetSites,\n\t\t\tgetCollections,\n\t\t\tgetFields,\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\tlet responseData;\n\t\tconst returnData: INodeExecutionData[] = [];\n\n\t\tfor (let i = 0; i < items.length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'item') {\n\t\t\t\t\t// *********************************************************************\n\t\t\t\t\t//                             item\n\t\t\t\t\t// *********************************************************************\n\n\t\t\t\t\t// https://developers.webflow.com/#item-model\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         item: create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://developers.webflow.com/#create-new-collection-item\n\n\t\t\t\t\t\tconst collectionId = this.getNodeParameter('collectionId', i) as string;\n\n\t\t\t\t\t\tconst properties = this.getNodeParameter(\n\t\t\t\t\t\t\t'fieldsUi.fieldValues',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t[],\n\t\t\t\t\t\t) as IDataObject[];\n\n\t\t\t\t\t\tconst live = this.getNodeParameter('live', i) as boolean;\n\n\t\t\t\t\t\tconst fields = {} as IDataObject;\n\n\t\t\t\t\t\tproperties.forEach((data) => (fields[data.fieldId as string] = data.fieldValue));\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tfields,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tresponseData = await webflowApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t`/collections/${collectionId}/items`,\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t\t{ live },\n\t\t\t\t\t\t);\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         item: delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://developers.webflow.com/#remove-collection-item\n\n\t\t\t\t\t\tconst collectionId = this.getNodeParameter('collectionId', i) as string;\n\t\t\t\t\t\tconst itemId = this.getNodeParameter('itemId', i) as string;\n\t\t\t\t\t\tresponseData = await webflowApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'DELETE',\n\t\t\t\t\t\t\t`/collections/${collectionId}/items/${itemId}`,\n\t\t\t\t\t\t);\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         item: get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://developers.webflow.com/#get-single-item\n\n\t\t\t\t\t\tconst collectionId = this.getNodeParameter('collectionId', i) as string;\n\t\t\t\t\t\tconst itemId = this.getNodeParameter('itemId', i) as string;\n\t\t\t\t\t\tresponseData = await webflowApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t`/collections/${collectionId}/items/${itemId}`,\n\t\t\t\t\t\t);\n\t\t\t\t\t\tresponseData = responseData.items;\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         item: getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://developers.webflow.com/#get-all-items-for-a-collection\n\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', 0);\n\t\t\t\t\t\tconst collectionId = this.getNodeParameter('collectionId', i) as string;\n\t\t\t\t\t\tconst qs: IDataObject = {};\n\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await webflowApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t`/collections/${collectionId}/items`,\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tqs.limit = this.getNodeParameter('limit', 0);\n\t\t\t\t\t\t\tresponseData = await webflowApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t`/collections/${collectionId}/items`,\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tresponseData = responseData.items;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         item: update\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://developers.webflow.com/#update-collection-item\n\n\t\t\t\t\t\tconst collectionId = this.getNodeParameter('collectionId', i) as string;\n\n\t\t\t\t\t\tconst itemId = this.getNodeParameter('itemId', i) as string;\n\n\t\t\t\t\t\tconst properties = this.getNodeParameter(\n\t\t\t\t\t\t\t'fieldsUi.fieldValues',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t[],\n\t\t\t\t\t\t) as IDataObject[];\n\n\t\t\t\t\t\tconst live = this.getNodeParameter('live', i) as boolean;\n\n\t\t\t\t\t\tconst fields = {} as IDataObject;\n\n\t\t\t\t\t\tproperties.forEach((data) => (fields[data.fieldId as string] = data.fieldValue));\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tfields,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tresponseData = await webflowApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'PUT',\n\t\t\t\t\t\t\t`/collections/${collectionId}/items/${itemId}`,\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t\t{ live },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\tthis.helpers.returnJsonArray(responseData as IDataObject[]),\n\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t);\n\t\t\t\treturnData.push(...executionData);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ json: { error: error.message } });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAQO;AAEP,6BAA2C;AAC3C,8BAMO;AAEA,MAAM,UAA+B;AAAA,EAG3C,YAAY,iBAA2C;AAmEvD,mBAAU;AAAA,MACT,aAAa;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAxEC,SAAK,cAAc;AAAA,MAClB,GAAG;AAAA,MACH,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,gBAAgB,CAAC,aAAa;AAAA,YAC/B;AAAA,UACD;AAAA,QACD;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,gBAAgB,CAAC,QAAQ;AAAA,YAC1B;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAAA,EACD;AAAA,EAUA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAEhC,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,QAAI;AACJ,UAAM,aAAmC,CAAC;AAE1C,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,UAAI;AACH,YAAI,aAAa,QAAQ;AAOxB,cAAI,cAAc,UAAU;AAO3B,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,kBAAM,aAAa,KAAK;AAAA,cACvB;AAAA,cACA;AAAA,cACA,CAAC;AAAA,YACF;AAEA,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAE5C,kBAAM,SAAS,CAAC;AAEhB,uBAAW,QAAQ,CAAC,SAAU,OAAO,KAAK,OAAiB,IAAI,KAAK,UAAW;AAE/E,kBAAM,OAAoB;AAAA,cACzB;AAAA,YACD;AAEA,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA,gBAAgB,YAAY;AAAA,cAC5B;AAAA,cACA,EAAE,KAAK;AAAA,YACR;AAAA,UACD,WAAW,cAAc,UAAU;AAOlC,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA,gBAAgB,YAAY,UAAU,MAAM;AAAA,YAC7C;AAAA,UACD,WAAW,cAAc,OAAO;AAO/B,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA,gBAAgB,YAAY,UAAU,MAAM;AAAA,YAC7C;AACA,2BAAe,aAAa;AAAA,UAC7B,WAAW,cAAc,UAAU;AAOlC,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,kBAAM,KAAkB,CAAC;AAEzB,gBAAI,WAAW;AACd,6BAAe,MAAM,kDAA0B;AAAA,gBAC9C;AAAA,gBACA;AAAA,gBACA,gBAAgB,YAAY;AAAA,gBAC5B,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,iBAAG,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC3C,6BAAe,MAAM,0CAAkB;AAAA,gBACtC;AAAA,gBACA;AAAA,gBACA,gBAAgB,YAAY;AAAA,gBAC5B,CAAC;AAAA,gBACD;AAAA,cACD;AACA,6BAAe,aAAa;AAAA,YAC7B;AAAA,UACD,WAAW,cAAc,UAAU;AAOlC,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,kBAAM,aAAa,KAAK;AAAA,cACvB;AAAA,cACA;AAAA,cACA,CAAC;AAAA,YACF;AAEA,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAE5C,kBAAM,SAAS,CAAC;AAEhB,uBAAW,QAAQ,CAAC,SAAU,OAAO,KAAK,OAAiB,IAAI,KAAK,UAAW;AAE/E,kBAAM,OAAoB;AAAA,cACzB;AAAA,YACD;AAEA,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA,gBAAgB,YAAY,UAAU,MAAM;AAAA,cAC5C;AAAA,cACA,EAAE,KAAK;AAAA,YACR;AAAA,UACD;AAAA,QACD;AACA,cAAM,gBAAgB,KAAK,QAAQ;AAAA,UAClC,KAAK,QAAQ,gBAAgB,YAA6B;AAAA,UAC1D,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,QACzB;AACA,mBAAW,KAAK,GAAG,aAAa;AAAA,MACjC,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,MAAM,EAAE,OAAO,MAAM,QAAQ,EAAE,CAAC;AAClD;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AAEA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": []}