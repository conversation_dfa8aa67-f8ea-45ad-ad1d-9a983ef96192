{"version": 3, "sources": ["../../../nodes/Uplead/Uplead.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tIDataObject,\n\tINodeExecutionData,\n\tINodeType,\n\tINodeTypeDescription,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes } from 'n8n-workflow';\n\nimport { companyFields, companyOperations } from './CompanyDesciption';\nimport { upleadApiRequest } from './GenericFunctions';\nimport { personFields, personOperations } from './PersonDescription';\n\nexport class Uplead implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Uplead',\n\t\tname: 'uplead',\n\t\t// eslint-disable-next-line n8n-nodes-base/node-class-description-icon-not-svg\n\t\ticon: 'file:uplead.png',\n\t\tgroup: ['output'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \":\" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume Uplead API',\n\t\tdefaults: {\n\t\t\tname: 'Uplead',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'upleadApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Company',\n\t\t\t\t\t\tvalue: 'company',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Company API lets you lookup company data via a domain name or company name',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Person',\n\t\t\t\t\t\tvalue: 'person',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Person API lets you lookup a person based on an email address OR based on a domain name + first name + last name',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'company',\n\t\t\t},\n\t\t\t...companyOperations,\n\t\t\t...companyFields,\n\t\t\t...personOperations,\n\t\t\t...personFields,\n\t\t],\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: IDataObject[] = [];\n\t\tconst length = items.length;\n\t\tconst qs: IDataObject = {};\n\t\tlet responseData;\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'person') {\n\t\t\t\t\tif (operation === 'enrich') {\n\t\t\t\t\t\tconst email = this.getNodeParameter('email', i) as string;\n\t\t\t\t\t\tconst firstname = this.getNodeParameter('firstname', i) as string;\n\t\t\t\t\t\tconst lastname = this.getNodeParameter('lastname', i) as string;\n\t\t\t\t\t\tconst domain = this.getNodeParameter('domain', i) as string;\n\t\t\t\t\t\tif (email) {\n\t\t\t\t\t\t\tqs.email = email;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (firstname) {\n\t\t\t\t\t\t\tqs.first_name = firstname;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (lastname) {\n\t\t\t\t\t\t\tqs.last_name = lastname;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (domain) {\n\t\t\t\t\t\t\tqs.domain = domain;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await upleadApiRequest.call(this, 'GET', '/person-search', {}, qs);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (resource === 'company') {\n\t\t\t\t\tif (operation === 'enrich') {\n\t\t\t\t\t\tconst domain = this.getNodeParameter('domain', i) as string;\n\t\t\t\t\t\tconst company = this.getNodeParameter('company', i) as string;\n\t\t\t\t\t\tif (domain) {\n\t\t\t\t\t\t\tqs.domain = domain;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (company) {\n\t\t\t\t\t\t\tqs.company = company;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await upleadApiRequest.call(this, 'GET', '/company-search', {}, qs);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (Array.isArray(responseData.data)) {\n\t\t\t\t\treturnData.push.apply(returnData, responseData.data as IDataObject[]);\n\t\t\t\t} else {\n\t\t\t\t\tif (responseData.data !== null) {\n\t\t\t\t\t\treturnData.push(responseData.data as IDataObject);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ error: error.message });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\t\treturn [this.helpers.returnJsonArray(returnData)];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,0BAAoC;AAEpC,+BAAiD;AACjD,8BAAiC;AACjC,+BAA+C;AAExC,MAAM,OAA4B;AAAA,EAAlC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA;AAAA,MAEN,MAAM;AAAA,MACN,OAAO,CAAC,QAAQ;AAAA,MAChB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aACC;AAAA,YACF;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aACC;AAAA,YACF;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAA4B,CAAC;AACnC,UAAM,SAAS,MAAM;AACrB,UAAM,KAAkB,CAAC;AACzB,QAAI;AACJ,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,YAAI,aAAa,UAAU;AAC1B,cAAI,cAAc,UAAU;AAC3B,kBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,gBAAI,OAAO;AACV,iBAAG,QAAQ;AAAA,YACZ;AACA,gBAAI,WAAW;AACd,iBAAG,aAAa;AAAA,YACjB;AACA,gBAAI,UAAU;AACb,iBAAG,YAAY;AAAA,YAChB;AACA,gBAAI,QAAQ;AACX,iBAAG,SAAS;AAAA,YACb;AACA,2BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,kBAAkB,CAAC,GAAG,EAAE;AAAA,UACjF;AAAA,QACD;AACA,YAAI,aAAa,WAAW;AAC3B,cAAI,cAAc,UAAU;AAC3B,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,gBAAI,QAAQ;AACX,iBAAG,SAAS;AAAA,YACb;AACA,gBAAI,SAAS;AACZ,iBAAG,UAAU;AAAA,YACd;AACA,2BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,mBAAmB,CAAC,GAAG,EAAE;AAAA,UAClF;AAAA,QACD;AACA,YAAI,MAAM,QAAQ,aAAa,IAAI,GAAG;AACrC,qBAAW,KAAK,MAAM,YAAY,aAAa,IAAqB;AAAA,QACrE,OAAO;AACN,cAAI,aAAa,SAAS,MAAM;AAC/B,uBAAW,KAAK,aAAa,IAAmB;AAAA,UACjD;AAAA,QACD;AAAA,MACD,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,OAAO,MAAM,QAAQ,CAAC;AACxC;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AACA,WAAO,CAAC,KAAK,QAAQ,gBAAgB,UAAU,CAAC;AAAA,EACjD;AACD;", "names": []}