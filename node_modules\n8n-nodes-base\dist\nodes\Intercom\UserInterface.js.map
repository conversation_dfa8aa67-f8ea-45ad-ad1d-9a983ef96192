{"version": 3, "sources": ["../../../nodes/Intercom/UserInterface.ts"], "sourcesContent": ["import type { IDataObject } from 'n8n-workflow';\n\nexport interface IUserCompany {\n\tcompany_id?: string;\n}\n\nexport interface IAvatar {\n\ttype?: string;\n\timage_url?: string;\n}\n\nexport interface IUser {\n\tuser_id?: string;\n\tid?: string;\n\temail?: string;\n\tphone?: string;\n\tname?: string;\n\tcustom_attributes?: IDataObject;\n\tcompanies?: IUserCompany[];\n\tlast_request_at?: number;\n\tsigned_up_at?: string;\n\tunsubscribed_from_emails?: boolean;\n\tupdate_last_request_at?: boolean;\n\tlast_seen_user_agent?: boolean;\n\tsession_count?: number;\n\tavatar?: IAvatar;\n\tutm_source?: string;\n\tutm_medium?: string;\n\tutm_campaign?: string;\n\tutm_term?: string;\n\tutm_content?: string;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}