{"version": 3, "sources": ["../../../../../nodes/Merge/v3/helpers/utils.ts"], "sourcesContent": ["import assign from 'lodash/assign';\nimport assignWith from 'lodash/assignWith';\nimport get from 'lodash/get';\nimport merge from 'lodash/merge';\nimport mergeWith from 'lodash/mergeWith';\nimport type {\n\tGenericValue,\n\tIBinaryKeyData,\n\tIDataObject,\n\tIExecuteFunctions,\n\tINodeExecutionData,\n\tINodeParameters,\n\tIPairedItemData,\n} from 'n8n-workflow';\nimport { ApplicationError, NodeConnectionTypes, NodeHelpers } from 'n8n-workflow';\n\nimport { fuzzyCompare, preparePairedItemDataArray } from '@utils/utilities';\n\nimport type { ClashResolveOptions, MatchFieldsJoinMode, MatchFieldsOptions } from './interfaces';\n\ntype PairToMatch = {\n\tfield1: string;\n\tfield2: string;\n};\n\ntype EntryMatches = {\n\tentry: INodeExecutionData;\n\tmatches: INodeExecutionData[];\n};\n\ntype CompareFunction = <T, U>(a: T, b: U) => boolean;\n\nexport function addSuffixToEntriesKeys(data: INodeExecutionData[], suffix: string) {\n\treturn data.map((entry) => {\n\t\tconst json: IDataObject = {};\n\t\tObject.keys(entry.json).forEach((key) => {\n\t\t\tjson[`${key}_${suffix}`] = entry.json[key];\n\t\t});\n\t\treturn { ...entry, json };\n\t});\n}\n\nfunction findAllMatches(\n\tdata: INodeExecutionData[],\n\tlookup: IDataObject,\n\tdisableDotNotation: boolean,\n\tisEntriesEqual: CompareFunction,\n) {\n\treturn data.reduce((acc, entry2, i) => {\n\t\tif (entry2 === undefined) return acc;\n\n\t\tfor (const key of Object.keys(lookup)) {\n\t\t\tconst expectedValue = lookup[key];\n\t\t\tlet entry2FieldValue;\n\n\t\t\tif (disableDotNotation) {\n\t\t\t\tentry2FieldValue = entry2.json[key];\n\t\t\t} else {\n\t\t\t\tentry2FieldValue = get(entry2.json, key);\n\t\t\t}\n\n\t\t\tif (!isEntriesEqual(expectedValue, entry2FieldValue)) {\n\t\t\t\treturn acc;\n\t\t\t}\n\t\t}\n\n\t\treturn acc.concat({\n\t\t\tentry: entry2,\n\t\t\tindex: i,\n\t\t});\n\t}, [] as IDataObject[]);\n}\n\nfunction findFirstMatch(\n\tdata: INodeExecutionData[],\n\tlookup: IDataObject,\n\tdisableDotNotation: boolean,\n\tisEntriesEqual: CompareFunction,\n) {\n\tconst index = data.findIndex((entry2) => {\n\t\tif (entry2 === undefined) return false;\n\n\t\tfor (const key of Object.keys(lookup)) {\n\t\t\tconst expectedValue = lookup[key];\n\t\t\tlet entry2FieldValue;\n\n\t\t\tif (disableDotNotation) {\n\t\t\t\tentry2FieldValue = entry2.json[key];\n\t\t\t} else {\n\t\t\t\tentry2FieldValue = get(entry2.json, key);\n\t\t\t}\n\n\t\t\tif (!isEntriesEqual(expectedValue, entry2FieldValue)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn true;\n\t});\n\tif (index === -1) return [];\n\n\treturn [{ entry: data[index], index }];\n}\n\nexport function findMatches(\n\tinput1: INodeExecutionData[],\n\tinput2: INodeExecutionData[],\n\tfieldsToMatch: PairToMatch[],\n\toptions: MatchFieldsOptions,\n) {\n\tconst data1 = [...input1];\n\tconst data2 = [...input2];\n\n\tconst isEntriesEqual = fuzzyCompare(options.fuzzyCompare as boolean);\n\tconst disableDotNotation = options.disableDotNotation || false;\n\tconst multipleMatches = (options.multipleMatches as string) || 'all';\n\n\tconst filteredData = {\n\t\tmatched: [] as EntryMatches[],\n\t\tmatched2: [] as INodeExecutionData[],\n\t\tunmatched1: [] as INodeExecutionData[],\n\t\tunmatched2: [] as INodeExecutionData[],\n\t};\n\n\tconst matchedInInput2 = new Set<number>();\n\n\tmatchesLoop: for (const entry1 of data1) {\n\t\tconst lookup: IDataObject = {};\n\n\t\tfieldsToMatch.forEach((matchCase) => {\n\t\t\tlet valueToCompare;\n\t\t\tif (disableDotNotation) {\n\t\t\t\tvalueToCompare = entry1.json[matchCase.field1];\n\t\t\t} else {\n\t\t\t\tvalueToCompare = get(entry1.json, matchCase.field1);\n\t\t\t}\n\t\t\tlookup[matchCase.field2] = valueToCompare;\n\t\t});\n\n\t\tfor (const fieldValue of Object.values(lookup)) {\n\t\t\tif (fieldValue === undefined) {\n\t\t\t\tfilteredData.unmatched1.push(entry1);\n\t\t\t\tcontinue matchesLoop;\n\t\t\t}\n\t\t}\n\n\t\tconst foundedMatches =\n\t\t\tmultipleMatches === 'all'\n\t\t\t\t? findAllMatches(data2, lookup, disableDotNotation, isEntriesEqual)\n\t\t\t\t: findFirstMatch(data2, lookup, disableDotNotation, isEntriesEqual);\n\n\t\tconst matches = foundedMatches.map((match) => match.entry) as INodeExecutionData[];\n\t\tfoundedMatches.map((match) => matchedInInput2.add(match.index as number));\n\n\t\tif (matches.length) {\n\t\t\tif (\n\t\t\t\toptions.outputDataFrom === 'both' ||\n\t\t\t\toptions.joinMode === 'enrichInput1' ||\n\t\t\t\toptions.joinMode === 'enrichInput2'\n\t\t\t) {\n\t\t\t\tmatches.forEach((match) => {\n\t\t\t\t\tfilteredData.matched.push({\n\t\t\t\t\t\tentry: entry1,\n\t\t\t\t\t\tmatches: [match],\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tfilteredData.matched.push({\n\t\t\t\t\tentry: entry1,\n\t\t\t\t\tmatches,\n\t\t\t\t});\n\t\t\t}\n\t\t} else {\n\t\t\tfilteredData.unmatched1.push(entry1);\n\t\t}\n\t}\n\n\tdata2.forEach((entry, i) => {\n\t\tif (matchedInInput2.has(i)) {\n\t\t\tfilteredData.matched2.push(entry);\n\t\t} else {\n\t\t\tfilteredData.unmatched2.push(entry);\n\t\t}\n\t});\n\n\treturn filteredData;\n}\n\nexport function selectMergeMethod(clashResolveOptions: ClashResolveOptions) {\n\tconst mergeMode = clashResolveOptions.mergeMode as string;\n\n\tif (clashResolveOptions.overrideEmpty) {\n\t\tfunction customizer(targetValue: GenericValue, srcValue: GenericValue) {\n\t\t\tif (srcValue === undefined || srcValue === null || srcValue === '') {\n\t\t\t\treturn targetValue;\n\t\t\t}\n\t\t}\n\t\tif (mergeMode === 'deepMerge') {\n\t\t\treturn (target: IDataObject, ...source: IDataObject[]) => {\n\t\t\t\tconst targetCopy = Object.assign({}, target);\n\t\t\t\treturn mergeWith(targetCopy, ...source, customizer);\n\t\t\t};\n\t\t}\n\t\tif (mergeMode === 'shallowMerge') {\n\t\t\treturn (target: IDataObject, ...source: IDataObject[]) => {\n\t\t\t\tconst targetCopy = Object.assign({}, target);\n\t\t\t\treturn assignWith(targetCopy, ...source, customizer);\n\t\t\t};\n\t\t}\n\t} else {\n\t\tif (mergeMode === 'deepMerge') {\n\t\t\treturn (target: IDataObject, ...source: IDataObject[]) => merge({}, target, ...source);\n\t\t}\n\t\tif (mergeMode === 'shallowMerge') {\n\t\t\treturn (target: IDataObject, ...source: IDataObject[]) => assign({}, target, ...source);\n\t\t}\n\t}\n\treturn (target: IDataObject, ...source: IDataObject[]) => merge({}, target, ...source);\n}\n\nexport function mergeMatched(\n\tmatched: EntryMatches[],\n\tclashResolveOptions: ClashResolveOptions,\n\tjoinMode?: MatchFieldsJoinMode,\n) {\n\tconst returnData: INodeExecutionData[] = [];\n\tlet resolveClash = clashResolveOptions.resolveClash as string;\n\n\tconst mergeIntoSingleObject = selectMergeMethod(clashResolveOptions);\n\n\tfor (const match of matched) {\n\t\tlet { entry, matches } = match;\n\n\t\tlet json: IDataObject = {};\n\t\tlet binary: IBinaryKeyData = {};\n\t\tlet pairedItem: IPairedItemData[] = [];\n\n\t\tif (resolveClash === 'addSuffix') {\n\t\t\tconst suffix1 = '1';\n\t\t\tconst suffix2 = '2';\n\n\t\t\t[entry] = addSuffixToEntriesKeys([entry], suffix1);\n\t\t\tmatches = addSuffixToEntriesKeys(matches, suffix2);\n\n\t\t\tjson = mergeIntoSingleObject({ ...entry.json }, ...matches.map((item) => item.json));\n\t\t\tbinary = mergeIntoSingleObject(\n\t\t\t\t{ ...entry.binary },\n\t\t\t\t...matches.map((item) => item.binary as IDataObject),\n\t\t\t);\n\t\t\tpairedItem = [\n\t\t\t\t...preparePairedItemDataArray(entry.pairedItem),\n\t\t\t\t...matches.map((item) => preparePairedItemDataArray(item.pairedItem)).flat(),\n\t\t\t];\n\t\t} else {\n\t\t\tconst preferInput1 = 'preferInput1';\n\t\t\tconst preferLast = 'preferLast';\n\n\t\t\tif (resolveClash === undefined) {\n\t\t\t\tif (joinMode !== 'enrichInput2') {\n\t\t\t\t\tresolveClash = 'preferLast';\n\t\t\t\t} else {\n\t\t\t\t\tresolveClash = 'preferInput1';\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (resolveClash === preferInput1) {\n\t\t\t\tconst [firstMatch, ...restMatches] = matches;\n\t\t\t\tjson = mergeIntoSingleObject(\n\t\t\t\t\t{ ...firstMatch.json },\n\t\t\t\t\t...restMatches.map((item) => item.json),\n\t\t\t\t\tentry.json,\n\t\t\t\t);\n\t\t\t\tbinary = mergeIntoSingleObject(\n\t\t\t\t\t{ ...firstMatch.binary },\n\t\t\t\t\t...restMatches.map((item) => item.binary as IDataObject),\n\t\t\t\t\tentry.binary as IDataObject,\n\t\t\t\t);\n\n\t\t\t\tpairedItem = [\n\t\t\t\t\t...preparePairedItemDataArray(firstMatch.pairedItem),\n\t\t\t\t\t...restMatches.map((item) => preparePairedItemDataArray(item.pairedItem)).flat(),\n\t\t\t\t\t...preparePairedItemDataArray(entry.pairedItem),\n\t\t\t\t];\n\t\t\t}\n\n\t\t\tif (resolveClash === preferLast) {\n\t\t\t\tjson = mergeIntoSingleObject({ ...entry.json }, ...matches.map((item) => item.json));\n\t\t\t\tbinary = mergeIntoSingleObject(\n\t\t\t\t\t{ ...entry.binary },\n\t\t\t\t\t...matches.map((item) => item.binary as IDataObject),\n\t\t\t\t);\n\t\t\t\tpairedItem = [\n\t\t\t\t\t...preparePairedItemDataArray(entry.pairedItem),\n\t\t\t\t\t...matches.map((item) => preparePairedItemDataArray(item.pairedItem)).flat(),\n\t\t\t\t];\n\t\t\t}\n\t\t}\n\n\t\treturnData.push({\n\t\t\tjson,\n\t\t\tbinary,\n\t\t\tpairedItem,\n\t\t});\n\t}\n\n\treturn returnData;\n}\n\nexport function checkMatchFieldsInput(data: IDataObject[]) {\n\tif (data.length === 1 && data[0].field1 === '' && data[0].field2 === '') {\n\t\tthrow new ApplicationError(\n\t\t\t'You need to define at least one pair of fields in \"Fields to Match\" to match on',\n\t\t\t{ level: 'warning' },\n\t\t);\n\t}\n\tfor (const [index, pair] of data.entries()) {\n\t\tif (pair.field1 === '' || pair.field2 === '') {\n\t\t\tthrow new ApplicationError(\n\t\t\t\t`You need to define both fields in \"Fields to Match\" for pair ${index + 1},\n\t\t\t\t field 1 = '${pair.field1}'\n\t\t\t\t field 2 = '${pair.field2}'`,\n\t\t\t\t{ level: 'warning' },\n\t\t\t);\n\t\t}\n\t}\n\treturn data as PairToMatch[];\n}\n\nexport function checkInput(\n\tinput: INodeExecutionData[],\n\tfields: string[],\n\tdisableDotNotation: boolean,\n\tinputLabel: string,\n) {\n\tfor (const field of fields) {\n\t\tconst isPresent = (input || []).some((entry) => {\n\t\t\tif (disableDotNotation) {\n\t\t\t\treturn entry.json.hasOwnProperty(field);\n\t\t\t}\n\t\t\treturn get(entry.json, field, undefined) !== undefined;\n\t\t});\n\t\tif (!isPresent) {\n\t\t\tthrow new ApplicationError(\n\t\t\t\t`Field '${field}' is not present in any of items in '${inputLabel}'`,\n\t\t\t\t{ level: 'warning' },\n\t\t\t);\n\t\t}\n\t}\n\treturn input;\n}\n\nexport function addSourceField(data: INodeExecutionData[], sourceField: string) {\n\treturn data.map((entry) => {\n\t\tconst json = {\n\t\t\t...entry.json,\n\t\t\t_source: sourceField,\n\t\t};\n\t\treturn {\n\t\t\t...entry,\n\t\t\tjson,\n\t\t};\n\t});\n}\n\nexport const configuredInputs = (parameters: INodeParameters) => {\n\treturn Array.from({ length: (parameters.numberInputs as number) || 2 }, (_, i) => ({\n\t\ttype: 'main',\n\t\tdisplayName: `Input ${(i + 1).toString()}`,\n\t}));\n};\n\nexport function getNodeInputsData(this: IExecuteFunctions) {\n\tconst returnData: INodeExecutionData[][] = [];\n\n\tconst inputs = NodeHelpers.getConnectionTypes(this.getNodeInputs()).filter(\n\t\t(type) => type === NodeConnectionTypes.Main,\n\t);\n\n\tfor (let i = 0; i < inputs.length; i++) {\n\t\ttry {\n\t\t\treturnData.push(this.getInputData(i) ?? []);\n\t\t} catch (error) {\n\t\t\treturnData.push([]);\n\t\t}\n\t}\n\n\treturn returnData;\n}\n\nexport const rowToExecutionData = (data: IDataObject): INodeExecutionData => {\n\tconst keys = Object.keys(data);\n\tconst pairedItem: IPairedItemData[] = [];\n\tconst json: IDataObject = {};\n\n\tfor (const key of keys) {\n\t\tif (key.startsWith('pairedItem')) {\n\t\t\tif (data[key] === undefined) continue;\n\t\t\tpairedItem.push(data[key] as IPairedItemData);\n\t\t} else {\n\t\t\tjson[key] = data[key];\n\t\t}\n\t}\n\n\treturn { json, pairedItem };\n};\n\nexport function modifySelectQuery(userQuery: string, inputLength: number): string {\n\tconst selectMatch = userQuery.match(/SELECT\\s+(.+?)\\s+FROM/i);\n\tif (!selectMatch) return userQuery;\n\n\tlet selectedColumns = selectMatch[1].trim();\n\n\tif (selectedColumns === '*') {\n\t\treturn userQuery;\n\t}\n\n\tconst pairedItemColumns = [];\n\n\tfor (let i = 1; i <= inputLength; i++) {\n\t\tif (userQuery.includes(`input${i}`)) {\n\t\t\tpairedItemColumns.push(`input${i}.pairedItem AS pairedItem${i}`);\n\t\t}\n\t}\n\n\tselectedColumns += pairedItemColumns.length ? ', ' + pairedItemColumns.join(', ') : '';\n\n\treturn userQuery.replace(selectMatch[0], `SELECT ${selectedColumns} FROM`);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAAmB;AACnB,wBAAuB;AACvB,iBAAgB;AAChB,mBAAkB;AAClB,uBAAsB;AAUtB,0BAAmE;AAEnE,uBAAyD;AAgBlD,SAAS,uBAAuB,MAA4B,QAAgB;AAClF,SAAO,KAAK,IAAI,CAAC,UAAU;AAC1B,UAAM,OAAoB,CAAC;AAC3B,WAAO,KAAK,MAAM,IAAI,EAAE,QAAQ,CAAC,QAAQ;AACxC,WAAK,GAAG,GAAG,IAAI,MAAM,EAAE,IAAI,MAAM,KAAK,GAAG;AAAA,IAC1C,CAAC;AACD,WAAO,EAAE,GAAG,OAAO,KAAK;AAAA,EACzB,CAAC;AACF;AAEA,SAAS,eACR,MACA,QACA,oBACA,gBACC;AACD,SAAO,KAAK,OAAO,CAAC,KAAK,QAAQ,MAAM;AACtC,QAAI,WAAW,OAAW,QAAO;AAEjC,eAAW,OAAO,OAAO,KAAK,MAAM,GAAG;AACtC,YAAM,gBAAgB,OAAO,GAAG;AAChC,UAAI;AAEJ,UAAI,oBAAoB;AACvB,2BAAmB,OAAO,KAAK,GAAG;AAAA,MACnC,OAAO;AACN,+BAAmB,WAAAA,SAAI,OAAO,MAAM,GAAG;AAAA,MACxC;AAEA,UAAI,CAAC,eAAe,eAAe,gBAAgB,GAAG;AACrD,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO,IAAI,OAAO;AAAA,MACjB,OAAO;AAAA,MACP,OAAO;AAAA,IACR,CAAC;AAAA,EACF,GAAG,CAAC,CAAkB;AACvB;AAEA,SAAS,eACR,MACA,QACA,oBACA,gBACC;AACD,QAAM,QAAQ,KAAK,UAAU,CAAC,WAAW;AACxC,QAAI,WAAW,OAAW,QAAO;AAEjC,eAAW,OAAO,OAAO,KAAK,MAAM,GAAG;AACtC,YAAM,gBAAgB,OAAO,GAAG;AAChC,UAAI;AAEJ,UAAI,oBAAoB;AACvB,2BAAmB,OAAO,KAAK,GAAG;AAAA,MACnC,OAAO;AACN,+BAAmB,WAAAA,SAAI,OAAO,MAAM,GAAG;AAAA,MACxC;AAEA,UAAI,CAAC,eAAe,eAAe,gBAAgB,GAAG;AACrD,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO;AAAA,EACR,CAAC;AACD,MAAI,UAAU,GAAI,QAAO,CAAC;AAE1B,SAAO,CAAC,EAAE,OAAO,KAAK,KAAK,GAAG,MAAM,CAAC;AACtC;AAEO,SAAS,YACf,QACA,QACA,eACA,SACC;AACD,QAAM,QAAQ,CAAC,GAAG,MAAM;AACxB,QAAM,QAAQ,CAAC,GAAG,MAAM;AAExB,QAAM,qBAAiB,+BAAa,QAAQ,YAAuB;AACnE,QAAM,qBAAqB,QAAQ,sBAAsB;AACzD,QAAM,kBAAmB,QAAQ,mBAA8B;AAE/D,QAAM,eAAe;AAAA,IACpB,SAAS,CAAC;AAAA,IACV,UAAU,CAAC;AAAA,IACX,YAAY,CAAC;AAAA,IACb,YAAY,CAAC;AAAA,EACd;AAEA,QAAM,kBAAkB,oBAAI,IAAY;AAExC,cAAa,YAAW,UAAU,OAAO;AACxC,UAAM,SAAsB,CAAC;AAE7B,kBAAc,QAAQ,CAAC,cAAc;AACpC,UAAI;AACJ,UAAI,oBAAoB;AACvB,yBAAiB,OAAO,KAAK,UAAU,MAAM;AAAA,MAC9C,OAAO;AACN,6BAAiB,WAAAA,SAAI,OAAO,MAAM,UAAU,MAAM;AAAA,MACnD;AACA,aAAO,UAAU,MAAM,IAAI;AAAA,IAC5B,CAAC;AAED,eAAW,cAAc,OAAO,OAAO,MAAM,GAAG;AAC/C,UAAI,eAAe,QAAW;AAC7B,qBAAa,WAAW,KAAK,MAAM;AACnC,iBAAS;AAAA,MACV;AAAA,IACD;AAEA,UAAM,iBACL,oBAAoB,QACjB,eAAe,OAAO,QAAQ,oBAAoB,cAAc,IAChE,eAAe,OAAO,QAAQ,oBAAoB,cAAc;AAEpE,UAAM,UAAU,eAAe,IAAI,CAAC,UAAU,MAAM,KAAK;AACzD,mBAAe,IAAI,CAAC,UAAU,gBAAgB,IAAI,MAAM,KAAe,CAAC;AAExE,QAAI,QAAQ,QAAQ;AACnB,UACC,QAAQ,mBAAmB,UAC3B,QAAQ,aAAa,kBACrB,QAAQ,aAAa,gBACpB;AACD,gBAAQ,QAAQ,CAAC,UAAU;AAC1B,uBAAa,QAAQ,KAAK;AAAA,YACzB,OAAO;AAAA,YACP,SAAS,CAAC,KAAK;AAAA,UAChB,CAAC;AAAA,QACF,CAAC;AAAA,MACF,OAAO;AACN,qBAAa,QAAQ,KAAK;AAAA,UACzB,OAAO;AAAA,UACP;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD,OAAO;AACN,mBAAa,WAAW,KAAK,MAAM;AAAA,IACpC;AAAA,EACD;AAEA,QAAM,QAAQ,CAAC,OAAO,MAAM;AAC3B,QAAI,gBAAgB,IAAI,CAAC,GAAG;AAC3B,mBAAa,SAAS,KAAK,KAAK;AAAA,IACjC,OAAO;AACN,mBAAa,WAAW,KAAK,KAAK;AAAA,IACnC;AAAA,EACD,CAAC;AAED,SAAO;AACR;AAEO,SAAS,kBAAkB,qBAA0C;AAC3E,QAAM,YAAY,oBAAoB;AAEtC,MAAI,oBAAoB,eAAe;AACtC,QAASC,cAAT,SAAoB,aAA2B,UAAwB;AACtE,UAAI,aAAa,UAAa,aAAa,QAAQ,aAAa,IAAI;AACnE,eAAO;AAAA,MACR;AAAA,IACD;AAJS,qBAAAA;AAKT,QAAI,cAAc,aAAa;AAC9B,aAAO,CAAC,WAAwB,WAA0B;AACzD,cAAM,aAAa,OAAO,OAAO,CAAC,GAAG,MAAM;AAC3C,mBAAO,iBAAAC,SAAU,YAAY,GAAG,QAAQD,WAAU;AAAA,MACnD;AAAA,IACD;AACA,QAAI,cAAc,gBAAgB;AACjC,aAAO,CAAC,WAAwB,WAA0B;AACzD,cAAM,aAAa,OAAO,OAAO,CAAC,GAAG,MAAM;AAC3C,mBAAO,kBAAAE,SAAW,YAAY,GAAG,QAAQF,WAAU;AAAA,MACpD;AAAA,IACD;AAAA,EACD,OAAO;AACN,QAAI,cAAc,aAAa;AAC9B,aAAO,CAAC,WAAwB,eAA0B,aAAAG,SAAM,CAAC,GAAG,QAAQ,GAAG,MAAM;AAAA,IACtF;AACA,QAAI,cAAc,gBAAgB;AACjC,aAAO,CAAC,WAAwB,eAA0B,cAAAC,SAAO,CAAC,GAAG,QAAQ,GAAG,MAAM;AAAA,IACvF;AAAA,EACD;AACA,SAAO,CAAC,WAAwB,eAA0B,aAAAD,SAAM,CAAC,GAAG,QAAQ,GAAG,MAAM;AACtF;AAEO,SAAS,aACf,SACA,qBACA,UACC;AACD,QAAM,aAAmC,CAAC;AAC1C,MAAI,eAAe,oBAAoB;AAEvC,QAAM,wBAAwB,kBAAkB,mBAAmB;AAEnE,aAAW,SAAS,SAAS;AAC5B,QAAI,EAAE,OAAO,QAAQ,IAAI;AAEzB,QAAI,OAAoB,CAAC;AACzB,QAAI,SAAyB,CAAC;AAC9B,QAAI,aAAgC,CAAC;AAErC,QAAI,iBAAiB,aAAa;AACjC,YAAM,UAAU;AAChB,YAAM,UAAU;AAEhB,OAAC,KAAK,IAAI,uBAAuB,CAAC,KAAK,GAAG,OAAO;AACjD,gBAAU,uBAAuB,SAAS,OAAO;AAEjD,aAAO,sBAAsB,EAAE,GAAG,MAAM,KAAK,GAAG,GAAG,QAAQ,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC;AACnF,eAAS;AAAA,QACR,EAAE,GAAG,MAAM,OAAO;AAAA,QAClB,GAAG,QAAQ,IAAI,CAAC,SAAS,KAAK,MAAqB;AAAA,MACpD;AACA,mBAAa;AAAA,QACZ,OAAG,6CAA2B,MAAM,UAAU;AAAA,QAC9C,GAAG,QAAQ,IAAI,CAAC,aAAS,6CAA2B,KAAK,UAAU,CAAC,EAAE,KAAK;AAAA,MAC5E;AAAA,IACD,OAAO;AACN,YAAM,eAAe;AACrB,YAAM,aAAa;AAEnB,UAAI,iBAAiB,QAAW;AAC/B,YAAI,aAAa,gBAAgB;AAChC,yBAAe;AAAA,QAChB,OAAO;AACN,yBAAe;AAAA,QAChB;AAAA,MACD;AAEA,UAAI,iBAAiB,cAAc;AAClC,cAAM,CAAC,YAAY,GAAG,WAAW,IAAI;AACrC,eAAO;AAAA,UACN,EAAE,GAAG,WAAW,KAAK;AAAA,UACrB,GAAG,YAAY,IAAI,CAAC,SAAS,KAAK,IAAI;AAAA,UACtC,MAAM;AAAA,QACP;AACA,iBAAS;AAAA,UACR,EAAE,GAAG,WAAW,OAAO;AAAA,UACvB,GAAG,YAAY,IAAI,CAAC,SAAS,KAAK,MAAqB;AAAA,UACvD,MAAM;AAAA,QACP;AAEA,qBAAa;AAAA,UACZ,OAAG,6CAA2B,WAAW,UAAU;AAAA,UACnD,GAAG,YAAY,IAAI,CAAC,aAAS,6CAA2B,KAAK,UAAU,CAAC,EAAE,KAAK;AAAA,UAC/E,OAAG,6CAA2B,MAAM,UAAU;AAAA,QAC/C;AAAA,MACD;AAEA,UAAI,iBAAiB,YAAY;AAChC,eAAO,sBAAsB,EAAE,GAAG,MAAM,KAAK,GAAG,GAAG,QAAQ,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC;AACnF,iBAAS;AAAA,UACR,EAAE,GAAG,MAAM,OAAO;AAAA,UAClB,GAAG,QAAQ,IAAI,CAAC,SAAS,KAAK,MAAqB;AAAA,QACpD;AACA,qBAAa;AAAA,UACZ,OAAG,6CAA2B,MAAM,UAAU;AAAA,UAC9C,GAAG,QAAQ,IAAI,CAAC,aAAS,6CAA2B,KAAK,UAAU,CAAC,EAAE,KAAK;AAAA,QAC5E;AAAA,MACD;AAAA,IACD;AAEA,eAAW,KAAK;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAC;AAAA,EACF;AAEA,SAAO;AACR;AAEO,SAAS,sBAAsB,MAAqB;AAC1D,MAAI,KAAK,WAAW,KAAK,KAAK,CAAC,EAAE,WAAW,MAAM,KAAK,CAAC,EAAE,WAAW,IAAI;AACxE,UAAM,IAAI;AAAA,MACT;AAAA,MACA,EAAE,OAAO,UAAU;AAAA,IACpB;AAAA,EACD;AACA,aAAW,CAAC,OAAO,IAAI,KAAK,KAAK,QAAQ,GAAG;AAC3C,QAAI,KAAK,WAAW,MAAM,KAAK,WAAW,IAAI;AAC7C,YAAM,IAAI;AAAA,QACT,gEAAgE,QAAQ,CAAC;AAAA,kBAC3D,KAAK,MAAM;AAAA,kBACX,KAAK,MAAM;AAAA,QACzB,EAAE,OAAO,UAAU;AAAA,MACpB;AAAA,IACD;AAAA,EACD;AACA,SAAO;AACR;AAEO,SAAS,WACf,OACA,QACA,oBACA,YACC;AACD,aAAW,SAAS,QAAQ;AAC3B,UAAM,aAAa,SAAS,CAAC,GAAG,KAAK,CAAC,UAAU;AAC/C,UAAI,oBAAoB;AACvB,eAAO,MAAM,KAAK,eAAe,KAAK;AAAA,MACvC;AACA,iBAAO,WAAAJ,SAAI,MAAM,MAAM,OAAO,MAAS,MAAM;AAAA,IAC9C,CAAC;AACD,QAAI,CAAC,WAAW;AACf,YAAM,IAAI;AAAA,QACT,UAAU,KAAK,wCAAwC,UAAU;AAAA,QACjE,EAAE,OAAO,UAAU;AAAA,MACpB;AAAA,IACD;AAAA,EACD;AACA,SAAO;AACR;AAEO,SAAS,eAAe,MAA4B,aAAqB;AAC/E,SAAO,KAAK,IAAI,CAAC,UAAU;AAC1B,UAAM,OAAO;AAAA,MACZ,GAAG,MAAM;AAAA,MACT,SAAS;AAAA,IACV;AACA,WAAO;AAAA,MACN,GAAG;AAAA,MACH;AAAA,IACD;AAAA,EACD,CAAC;AACF;AAEO,MAAM,mBAAmB,CAAC,eAAgC;AAChE,SAAO,MAAM,KAAK,EAAE,QAAS,WAAW,gBAA2B,EAAE,GAAG,CAAC,GAAG,OAAO;AAAA,IAClF,MAAM;AAAA,IACN,aAAa,UAAU,IAAI,GAAG,SAAS,CAAC;AAAA,EACzC,EAAE;AACH;AAEO,SAAS,oBAA2C;AAC1D,QAAM,aAAqC,CAAC;AAE5C,QAAM,SAAS,gCAAY,mBAAmB,KAAK,cAAc,CAAC,EAAE;AAAA,IACnE,CAAC,SAAS,SAAS,wCAAoB;AAAA,EACxC;AAEA,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACvC,QAAI;AACH,iBAAW,KAAK,KAAK,aAAa,CAAC,KAAK,CAAC,CAAC;AAAA,IAC3C,SAAS,OAAO;AACf,iBAAW,KAAK,CAAC,CAAC;AAAA,IACnB;AAAA,EACD;AAEA,SAAO;AACR;AAEO,MAAM,qBAAqB,CAAC,SAA0C;AAC5E,QAAM,OAAO,OAAO,KAAK,IAAI;AAC7B,QAAM,aAAgC,CAAC;AACvC,QAAM,OAAoB,CAAC;AAE3B,aAAW,OAAO,MAAM;AACvB,QAAI,IAAI,WAAW,YAAY,GAAG;AACjC,UAAI,KAAK,GAAG,MAAM,OAAW;AAC7B,iBAAW,KAAK,KAAK,GAAG,CAAoB;AAAA,IAC7C,OAAO;AACN,WAAK,GAAG,IAAI,KAAK,GAAG;AAAA,IACrB;AAAA,EACD;AAEA,SAAO,EAAE,MAAM,WAAW;AAC3B;AAEO,SAAS,kBAAkB,WAAmB,aAA6B;AACjF,QAAM,cAAc,UAAU,MAAM,wBAAwB;AAC5D,MAAI,CAAC,YAAa,QAAO;AAEzB,MAAI,kBAAkB,YAAY,CAAC,EAAE,KAAK;AAE1C,MAAI,oBAAoB,KAAK;AAC5B,WAAO;AAAA,EACR;AAEA,QAAM,oBAAoB,CAAC;AAE3B,WAAS,IAAI,GAAG,KAAK,aAAa,KAAK;AACtC,QAAI,UAAU,SAAS,QAAQ,CAAC,EAAE,GAAG;AACpC,wBAAkB,KAAK,QAAQ,CAAC,4BAA4B,CAAC,EAAE;AAAA,IAChE;AAAA,EACD;AAEA,qBAAmB,kBAAkB,SAAS,OAAO,kBAAkB,KAAK,IAAI,IAAI;AAEpF,SAAO,UAAU,QAAQ,YAAY,CAAC,GAAG,UAAU,eAAe,OAAO;AAC1E;", "names": ["get", "customizer", "mergeWith", "assignWith", "merge", "assign"]}