import { Paginator } from "@smithy/types";
import {
  ListInvocationStepsCommandInput,
  ListInvocationStepsCommandOutput,
} from "../commands/ListInvocationStepsCommand";
import { BedrockAgentRuntimePaginationConfiguration } from "./Interfaces";
export declare const paginateListInvocationSteps: (
  config: BedrockAgentRuntimePaginationConfiguration,
  input: ListInvocationStepsCommandInput,
  ...rest: any[]
) => Paginator<ListInvocationStepsCommandOutput>;
