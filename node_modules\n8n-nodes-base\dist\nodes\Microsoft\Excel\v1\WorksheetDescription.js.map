{"version": 3, "sources": ["../../../../../nodes/Microsoft/Excel/v1/WorksheetDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const worksheetOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['worksheet'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Get many worksheets',\n\t\t\t\taction: 'Get many worksheets',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Content',\n\t\t\t\tvalue: 'getContent',\n\t\t\t\tdescription: 'Get worksheet content',\n\t\t\t\taction: 'Get a worksheet',\n\t\t\t},\n\t\t],\n\t\tdefault: 'create',\n\t},\n];\n\nexport const worksheetFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 worksheet:getAll                           */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Workbook Name or ID',\n\t\tname: 'workbook',\n\t\ttype: 'options',\n\t\tdescription:\n\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getWorkbooks',\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['worksheet'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['worksheet'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['worksheet'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 500,\n\t\t},\n\t\tdefault: 100,\n\t\tdescription: 'Max number of results to return',\n\t},\n\t{\n\t\tdisplayName: 'Filters',\n\t\tname: 'filters',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Filter',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['worksheet'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Fields',\n\t\t\t\tname: 'fields',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Fields the response will containt. Multiple can be added separated by ,.',\n\t\t\t},\n\t\t],\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 worksheet:getContent                       */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Workbook Name or ID',\n\t\tname: 'workbook',\n\t\ttype: 'options',\n\t\tdescription:\n\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getWorkbooks',\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getContent'],\n\t\t\t\tresource: ['worksheet'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Worksheet Name or ID',\n\t\tname: 'worksheet',\n\t\ttype: 'options',\n\t\tdescription:\n\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getworksheets',\n\t\t\tloadOptionsDependsOn: ['workbook'],\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getContent'],\n\t\t\t\tresource: ['worksheet'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Range',\n\t\tname: 'range',\n\t\ttype: 'string',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getContent'],\n\t\t\t\tresource: ['worksheet'],\n\t\t\t},\n\t\t},\n\t\tdefault: 'A1:C3',\n\t\trequired: true,\n\t\tdescription:\n\t\t\t'The address or the name of the range. If not specified, the entire worksheet range is returned.',\n\t},\n\t{\n\t\tdisplayName: 'RAW Data',\n\t\tname: 'rawData',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getContent'],\n\t\t\t\tresource: ['worksheet'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription:\n\t\t\t'Whether the data should be returned RAW instead of parsed into keys according to their header',\n\t},\n\t{\n\t\tdisplayName: 'Data Property',\n\t\tname: 'dataProperty',\n\t\ttype: 'string',\n\t\tdefault: 'data',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getContent'],\n\t\t\t\tresource: ['worksheet'],\n\t\t\t\trawData: [true],\n\t\t\t},\n\t\t},\n\t\tdescription: 'The name of the property into which to write the RAW data',\n\t},\n\t{\n\t\tdisplayName: 'Data Start Row',\n\t\tname: 'dataStartRow',\n\t\ttype: 'number',\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t},\n\t\tdefault: 1,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getContent'],\n\t\t\t\tresource: ['worksheet'],\n\t\t\t},\n\t\t\thide: {\n\t\t\t\trawData: [true],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'Index of the first row which contains the actual data and not the keys. Starts with 0.',\n\t},\n\t{\n\t\tdisplayName: 'Key Row',\n\t\tname: 'keyRow',\n\t\ttype: 'number',\n\t\ttypeOptions: {\n\t\t\tminValue: 0,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getContent'],\n\t\t\t\tresource: ['worksheet'],\n\t\t\t},\n\t\t\thide: {\n\t\t\t\trawData: [true],\n\t\t\t},\n\t\t},\n\t\tdefault: 0,\n\t\tdescription:\n\t\t\t'Index of the row which contains the keys. Starts at 0. The incoming node data is matched to the keys for assignment. The matching is case sensitve.',\n\t},\n\t{\n\t\tdisplayName: 'Filters',\n\t\tname: 'filters',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Filter',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getContent'],\n\t\t\t\tresource: ['worksheet'],\n\t\t\t\trawData: [true],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Fields',\n\t\t\t\tname: 'fields',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Fields the response will containt. Multiple can be added separated by ,.',\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,sBAAyC;AAAA,EACrD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,MACvB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,kBAAqC;AAAA;AAAA;AAAA;AAAA,EAIjD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aACC;AAAA,IACD,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,WAAW;AAAA,MACvB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,WAAW;AAAA,MACvB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,WAAW;AAAA,MACvB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aACC;AAAA,IACD,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,YAAY;AAAA,QACxB,UAAU,CAAC,WAAW;AAAA,MACvB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aACC;AAAA,IACD,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,MACnB,sBAAsB,CAAC,UAAU;AAAA,IAClC;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,YAAY;AAAA,QACxB,UAAU,CAAC,WAAW;AAAA,MACvB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,YAAY;AAAA,QACxB,UAAU,CAAC,WAAW;AAAA,MACvB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,YAAY;AAAA,QACxB,UAAU,CAAC,WAAW;AAAA,MACvB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,YAAY;AAAA,QACxB,UAAU,CAAC,WAAW;AAAA,QACtB,SAAS,CAAC,IAAI;AAAA,MACf;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,UAAU;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,YAAY;AAAA,QACxB,UAAU,CAAC,WAAW;AAAA,MACvB;AAAA,MACA,MAAM;AAAA,QACL,SAAS,CAAC,IAAI;AAAA,MACf;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,UAAU;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,YAAY;AAAA,QACxB,UAAU,CAAC,WAAW;AAAA,MACvB;AAAA,MACA,MAAM;AAAA,QACL,SAAS,CAAC,IAAI;AAAA,MACf;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,YAAY;AAAA,QACxB,UAAU,CAAC,WAAW;AAAA,QACtB,SAAS,CAAC,IAAI;AAAA,MACf;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AACD;", "names": []}