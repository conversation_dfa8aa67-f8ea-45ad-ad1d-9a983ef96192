{"version": 3, "sources": ["../../../nodes/Twilio/Twilio.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tIDataObject,\n\tINodeExecutionData,\n\tINodeType,\n\tINodeTypeDescription,\n\tIHttpRequestMethods,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes, NodeOperationError } from 'n8n-workflow';\n\nimport { escapeXml, twilioApiRequest } from './GenericFunctions';\n\nexport class Twi<PERSON> implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Twilio',\n\t\tname: 'twilio',\n\t\ticon: 'file:twilio.svg',\n\t\tgroup: ['transform'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Send SMS and WhatsApp messages or make phone calls',\n\t\tdefaults: {\n\t\t\tname: '<PERSON>wi<PERSON>',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'twi<PERSON><PERSON><PERSON>',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Call',\n\t\t\t\t\t\tvalue: 'call',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'SMS',\n\t\t\t\t\t\tvalue: 'sms',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'sms',\n\t\t\t},\n\n\t\t\t{\n\t\t\t\tdisplayName: 'Operation',\n\t\t\t\tname: 'operation',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresource: ['sms'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Send',\n\t\t\t\t\t\tvalue: 'send',\n\t\t\t\t\t\tdescription: 'Send SMS/MMS/WhatsApp message',\n\t\t\t\t\t\taction: 'Send an SMS/MMS/WhatsApp message',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'send',\n\t\t\t},\n\n\t\t\t{\n\t\t\t\tdisplayName: 'Operation',\n\t\t\t\tname: 'operation',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresource: ['call'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Make',\n\t\t\t\t\t\tvalue: 'make',\n\t\t\t\t\t\taction: 'Make a call',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'make',\n\t\t\t},\n\n\t\t\t// ----------------------------------\n\t\t\t//         sms / call\n\t\t\t// ----------------------------------\n\n\t\t\t// ----------------------------------\n\t\t\t//         sms:send / call:make\n\t\t\t// ----------------------------------\n\t\t\t{\n\t\t\t\tdisplayName: 'From',\n\t\t\t\tname: 'from',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: '+14155238886',\n\t\t\t\trequired: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\toperation: ['send', 'make'],\n\t\t\t\t\t\tresource: ['sms', 'call'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdescription: 'The number from which to send the message',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'To',\n\t\t\t\tname: 'to',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: '+14155238886',\n\t\t\t\trequired: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\toperation: ['send', 'make'],\n\t\t\t\t\t\tresource: ['sms', 'call'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdescription: 'The number to which to send the message',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'To Whatsapp',\n\t\t\t\tname: 'toWhatsapp',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\toperation: ['send'],\n\t\t\t\t\t\tresource: ['sms'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdescription: 'Whether the message should be sent to WhatsApp',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Message',\n\t\t\t\tname: 'message',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\trequired: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\toperation: ['send'],\n\t\t\t\t\t\tresource: ['sms'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdescription: 'The message to send',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Use TwiML',\n\t\t\t\tname: 'twiml',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\toperation: ['make'],\n\t\t\t\t\t\tresource: ['call'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdescription:\n\t\t\t\t\t'Whether to use the <a href=\"https://www.twilio.com/docs/voice/twiml\">Twilio Markup Language</a> in the message',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Message',\n\t\t\t\tname: 'message',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\trequired: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\toperation: ['make'],\n\t\t\t\t\t\tresource: ['call'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Options',\n\t\t\t\tname: 'options',\n\t\t\t\ttype: 'collection',\n\t\t\t\tplaceholder: 'Add Field',\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Status Callback',\n\t\t\t\t\t\tname: 'statusCallback',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Status Callbacks allow you to receive events related to the REST resources managed by Twilio: Rooms, Recordings and Compositions',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: IDataObject[] = [];\n\n\t\tlet operation: string;\n\t\tlet resource: string;\n\n\t\t// For Post\n\t\tlet body: IDataObject;\n\t\t// For Query string\n\t\tlet qs: IDataObject;\n\n\t\tlet requestMethod: IHttpRequestMethods;\n\t\tlet endpoint: string;\n\n\t\tfor (let i = 0; i < items.length; i++) {\n\t\t\ttry {\n\t\t\t\trequestMethod = 'GET';\n\t\t\t\tendpoint = '';\n\t\t\t\tbody = {};\n\t\t\t\tqs = {};\n\n\t\t\t\tresource = this.getNodeParameter('resource', i);\n\t\t\t\toperation = this.getNodeParameter('operation', i);\n\n\t\t\t\tif (resource === 'sms') {\n\t\t\t\t\tif (operation === 'send') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         sms:send\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'POST';\n\t\t\t\t\t\tendpoint = '/Messages.json';\n\n\t\t\t\t\t\tbody.From = this.getNodeParameter('from', i) as string;\n\t\t\t\t\t\tbody.To = this.getNodeParameter('to', i) as string;\n\t\t\t\t\t\tbody.Body = this.getNodeParameter('message', i) as string;\n\t\t\t\t\t\tbody.StatusCallback = this.getNodeParameter('options.statusCallback', i, '') as string;\n\n\t\t\t\t\t\tconst toWhatsapp = this.getNodeParameter('toWhatsapp', i) as boolean;\n\n\t\t\t\t\t\tif (toWhatsapp) {\n\t\t\t\t\t\t\tbody.From = `whatsapp:${body.From}`;\n\t\t\t\t\t\t\tbody.To = `whatsapp:${body.To}`;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t`The operation \"${operation}\" is not known!`,\n\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'call') {\n\t\t\t\t\tif (operation === 'make') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         call:make\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'POST';\n\t\t\t\t\t\tendpoint = '/Calls.json';\n\n\t\t\t\t\t\tconst message = this.getNodeParameter('message', i) as string;\n\t\t\t\t\t\tconst useTwiml = this.getNodeParameter('twiml', i) as boolean;\n\t\t\t\t\t\tbody.From = this.getNodeParameter('from', i) as string;\n\t\t\t\t\t\tbody.To = this.getNodeParameter('to', i) as string;\n\n\t\t\t\t\t\tif (useTwiml) {\n\t\t\t\t\t\t\tbody.Twiml = message;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tbody.Twiml = `<Response><Say>${escapeXml(message)}</Say></Response>`;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tbody.StatusCallback = this.getNodeParameter('options.statusCallback', i, '') as string;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t`The operation \"${operation}\" is not known!`,\n\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthrow new NodeOperationError(this.getNode(), `The resource \"${resource}\" is not known!`, {\n\t\t\t\t\t\titemIndex: i,\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tconst responseData = await twilioApiRequest.call(this, requestMethod, endpoint, body, qs);\n\n\t\t\t\treturnData.push(responseData as IDataObject);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ error: error.message });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\n\t\treturn [this.helpers.returnJsonArray(returnData)];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,0BAAwD;AAExD,8BAA4C;AAErC,MAAM,OAA4B;AAAA,EAAlC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,WAAW;AAAA,MACnB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QAEA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,UAAU,CAAC,KAAK;AAAA,YACjB;AAAA,UACD;AAAA,UACA,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,YACT;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QAEA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,UAAU,CAAC,MAAM;AAAA,YAClB;AAAA,UACD;AAAA,UACA,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,QAAQ;AAAA,YACT;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,UACb,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,WAAW,CAAC,QAAQ,MAAM;AAAA,cAC1B,UAAU,CAAC,OAAO,MAAM;AAAA,YACzB;AAAA,UACD;AAAA,UACA,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,UACb,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,WAAW,CAAC,QAAQ,MAAM;AAAA,cAC1B,UAAU,CAAC,OAAO,MAAM;AAAA,YACzB;AAAA,UACD;AAAA,UACA,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,WAAW,CAAC,MAAM;AAAA,cAClB,UAAU,CAAC,KAAK;AAAA,YACjB;AAAA,UACD;AAAA,UACA,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,WAAW,CAAC,MAAM;AAAA,cAClB,UAAU,CAAC,KAAK;AAAA,YACjB;AAAA,UACD;AAAA,UACA,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,WAAW,CAAC,MAAM;AAAA,cAClB,UAAU,CAAC,MAAM;AAAA,YAClB;AAAA,UACD;AAAA,UACA,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,WAAW,CAAC,MAAM;AAAA,cAClB,UAAU,CAAC,MAAM;AAAA,YAClB;AAAA,UACD;AAAA,QACD;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,SAAS,CAAC;AAAA,UACV,SAAS;AAAA,YACR;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aACC;AAAA,YACF;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAA4B,CAAC;AAEnC,QAAI;AACJ,QAAI;AAGJ,QAAI;AAEJ,QAAI;AAEJ,QAAI;AACJ,QAAI;AAEJ,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,UAAI;AACH,wBAAgB;AAChB,mBAAW;AACX,eAAO,CAAC;AACR,aAAK,CAAC;AAEN,mBAAW,KAAK,iBAAiB,YAAY,CAAC;AAC9C,oBAAY,KAAK,iBAAiB,aAAa,CAAC;AAEhD,YAAI,aAAa,OAAO;AACvB,cAAI,cAAc,QAAQ;AAKzB,4BAAgB;AAChB,uBAAW;AAEX,iBAAK,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC3C,iBAAK,KAAK,KAAK,iBAAiB,MAAM,CAAC;AACvC,iBAAK,OAAO,KAAK,iBAAiB,WAAW,CAAC;AAC9C,iBAAK,iBAAiB,KAAK,iBAAiB,0BAA0B,GAAG,EAAE;AAE3E,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AAExD,gBAAI,YAAY;AACf,mBAAK,OAAO,YAAY,KAAK,IAAI;AACjC,mBAAK,KAAK,YAAY,KAAK,EAAE;AAAA,YAC9B;AAAA,UACD,OAAO;AACN,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,kBAAkB,SAAS;AAAA,cAC3B,EAAE,WAAW,EAAE;AAAA,YAChB;AAAA,UACD;AAAA,QACD,WAAW,aAAa,QAAQ;AAC/B,cAAI,cAAc,QAAQ;AAKzB,4BAAgB;AAChB,uBAAW;AAEX,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,WAAW,KAAK,iBAAiB,SAAS,CAAC;AACjD,iBAAK,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC3C,iBAAK,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAEvC,gBAAI,UAAU;AACb,mBAAK,QAAQ;AAAA,YACd,OAAO;AACN,mBAAK,QAAQ,sBAAkB,mCAAU,OAAO,CAAC;AAAA,YAClD;AAEA,iBAAK,iBAAiB,KAAK,iBAAiB,0BAA0B,GAAG,EAAE;AAAA,UAC5E,OAAO;AACN,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,kBAAkB,SAAS;AAAA,cAC3B,EAAE,WAAW,EAAE;AAAA,YAChB;AAAA,UACD;AAAA,QACD,OAAO;AACN,gBAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,iBAAiB,QAAQ,mBAAmB;AAAA,YACxF,WAAW;AAAA,UACZ,CAAC;AAAA,QACF;AAEA,cAAM,eAAe,MAAM,yCAAiB,KAAK,MAAM,eAAe,UAAU,MAAM,EAAE;AAExF,mBAAW,KAAK,YAA2B;AAAA,MAC5C,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,OAAO,MAAM,QAAQ,CAAC;AACxC;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AAEA,WAAO,CAAC,KAAK,QAAQ,gBAAgB,UAAU,CAAC;AAAA,EACjD;AACD;", "names": []}