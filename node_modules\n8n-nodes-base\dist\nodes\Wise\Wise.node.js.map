{"version": 3, "sources": ["../../../nodes/Wise/Wise.node.ts"], "sourcesContent": ["import omit from 'lodash/omit';\nimport moment from 'moment-timezone';\nimport type {\n\tIExecuteFunctions,\n\tIDataObject,\n\tILoadOptionsFunctions,\n\tINodeExecutionData,\n\tINodePropertyOptions,\n\tINodeType,\n\tINodeTypeDescription,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes } from 'n8n-workflow';\nimport { v4 as uuid } from 'uuid';\n\nimport {\n\taccountFields,\n\taccountOperations,\n\texchangeRateFields,\n\texchangeRateOperations,\n\tprofileFields,\n\tprofileOperations,\n\tquoteFields,\n\tquoteOperations,\n\trecipientFields,\n\trecipientOperations,\n\ttransferFields,\n\ttransferOperations,\n} from './descriptions';\nimport type {\n\tBorderlessAccount,\n\tExchangeRateAdditionalFields,\n\tProfile,\n\tRecipient,\n\tStatementAdditionalFields,\n\tTransferFilters,\n} from './GenericFunctions';\nimport { wiseApiRequest } from './GenericFunctions';\n\nexport class Wise implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Wise',\n\t\tname: 'wise',\n\t\ticon: 'file:wise.svg',\n\t\tgroup: ['transform'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume the Wise API',\n\t\tdefaults: {\n\t\t\tname: 'Wise',\n\t\t},\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'wiseApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Account',\n\t\t\t\t\t\tvalue: 'account',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Exchange Rate',\n\t\t\t\t\t\tvalue: 'exchangeRate',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Profile',\n\t\t\t\t\t\tvalue: 'profile',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Quote',\n\t\t\t\t\t\tvalue: 'quote',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Recipient',\n\t\t\t\t\t\tvalue: 'recipient',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Transfer',\n\t\t\t\t\t\tvalue: 'transfer',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'account',\n\t\t\t},\n\t\t\t...accountOperations,\n\t\t\t...accountFields,\n\t\t\t...exchangeRateOperations,\n\t\t\t...exchangeRateFields,\n\t\t\t...profileOperations,\n\t\t\t...profileFields,\n\t\t\t...quoteOperations,\n\t\t\t...quoteFields,\n\t\t\t...recipientOperations,\n\t\t\t...recipientFields,\n\t\t\t...transferOperations,\n\t\t\t...transferFields,\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\tasync getBorderlessAccounts(this: ILoadOptionsFunctions) {\n\t\t\t\tconst qs = {\n\t\t\t\t\tprofileId: this.getNodeParameter('profileId', 0),\n\t\t\t\t};\n\n\t\t\t\tconst accounts = await wiseApiRequest.call(this, 'GET', 'v1/borderless-accounts', {}, qs);\n\n\t\t\t\treturn accounts.map(({ id, balances }: BorderlessAccount) => ({\n\t\t\t\t\tname: balances.map(({ currency }) => currency).join(' - '),\n\t\t\t\t\tvalue: id,\n\t\t\t\t}));\n\t\t\t},\n\n\t\t\tasync getProfiles(this: ILoadOptionsFunctions) {\n\t\t\t\tconst profiles = await wiseApiRequest.call(this, 'GET', 'v1/profiles');\n\n\t\t\t\treturn profiles.map(({ id, type }: Profile) => ({\n\t\t\t\t\tname: type.charAt(0).toUpperCase() + type.slice(1),\n\t\t\t\t\tvalue: id,\n\t\t\t\t}));\n\t\t\t},\n\n\t\t\tasync getRecipients(this: ILoadOptionsFunctions) {\n\t\t\t\tconst qs = {\n\t\t\t\t\tprofileId: this.getNodeParameter('profileId', 0),\n\t\t\t\t};\n\n\t\t\t\tconst recipients = (await wiseApiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t'v1/accounts',\n\t\t\t\t\t{},\n\t\t\t\t\tqs,\n\t\t\t\t)) as Recipient[];\n\n\t\t\t\treturn recipients.reduce<INodePropertyOptions[]>(\n\t\t\t\t\t(activeRecipients, { active, id, accountHolderName, currency, country, type }) => {\n\t\t\t\t\t\tif (active) {\n\t\t\t\t\t\t\tconst recipient = {\n\t\t\t\t\t\t\t\tname: `[${currency}] ${accountHolderName} - (${\n\t\t\t\t\t\t\t\t\tcountry !== null ? country + ' - ' : ''\n\t\t\t\t\t\t\t\t}${type})`,\n\t\t\t\t\t\t\t\tvalue: id,\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tactiveRecipients.push(recipient);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn activeRecipients;\n\t\t\t\t\t},\n\t\t\t\t\t[],\n\t\t\t\t);\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions) {\n\t\tconst items = this.getInputData();\n\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\n\t\tconst timezone = this.getTimezone();\n\n\t\tlet responseData;\n\t\tconst returnData: IDataObject[] = [];\n\t\tlet binaryOutput = false;\n\n\t\tfor (let i = 0; i < items.length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'account') {\n\t\t\t\t\t// *********************************************************************\n\t\t\t\t\t//                             account\n\t\t\t\t\t// *********************************************************************\n\n\t\t\t\t\tif (operation === 'getBalances') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//      account: getBalances\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://api-docs.transferwise.com/#borderless-accounts-get-account-balance\n\n\t\t\t\t\t\tconst qs = {\n\t\t\t\t\t\t\tprofileId: this.getNodeParameter('profileId', i),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tresponseData = await wiseApiRequest.call(this, 'GET', 'v1/borderless-accounts', {}, qs);\n\t\t\t\t\t} else if (operation === 'getCurrencies') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//      account: getCurrencies\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://api-docs.transferwise.com/#borderless-accounts-get-available-currencies\n\n\t\t\t\t\t\tresponseData = await wiseApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t'v1/borderless-accounts/balance-currencies',\n\t\t\t\t\t\t);\n\t\t\t\t\t} else if (operation === 'getStatement') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//      account: getStatement\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://api-docs.transferwise.com/#borderless-accounts-get-account-statement\n\n\t\t\t\t\t\tconst profileId = this.getNodeParameter('profileId', i);\n\t\t\t\t\t\tconst borderlessAccountId = this.getNodeParameter('borderlessAccountId', i);\n\t\t\t\t\t\tconst format = this.getNodeParameter('format', i) as 'json' | 'csv' | 'pdf' | 'xml';\n\t\t\t\t\t\tconst endpoint = `v3/profiles/${profileId}/borderless-accounts/${borderlessAccountId}/statement.${format}`;\n\n\t\t\t\t\t\tconst qs = {\n\t\t\t\t\t\t\tcurrency: this.getNodeParameter('currency', i),\n\t\t\t\t\t\t} as IDataObject;\n\n\t\t\t\t\t\tconst { lineStyle, range } = this.getNodeParameter(\n\t\t\t\t\t\t\t'additionalFields',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t) as StatementAdditionalFields;\n\n\t\t\t\t\t\tif (lineStyle !== undefined) {\n\t\t\t\t\t\t\tqs.type = lineStyle;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (range !== undefined) {\n\t\t\t\t\t\t\tqs.intervalStart = moment\n\t\t\t\t\t\t\t\t.tz(range.rangeProperties.intervalStart, timezone)\n\t\t\t\t\t\t\t\t.utc()\n\t\t\t\t\t\t\t\t.format();\n\t\t\t\t\t\t\tqs.intervalEnd = moment\n\t\t\t\t\t\t\t\t.tz(range.rangeProperties.intervalEnd, timezone)\n\t\t\t\t\t\t\t\t.utc()\n\t\t\t\t\t\t\t\t.format();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tqs.intervalStart = moment().subtract(1, 'months').utc().format();\n\t\t\t\t\t\t\tqs.intervalEnd = moment().utc().format();\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (format === 'json') {\n\t\t\t\t\t\t\tresponseData = await wiseApiRequest.call(this, 'GET', endpoint, {}, qs);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst data = await wiseApiRequest.call(this, 'GET', endpoint, {}, qs, {\n\t\t\t\t\t\t\t\tencoding: 'arraybuffer',\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tconst binaryProperty = this.getNodeParameter('binaryProperty', i);\n\n\t\t\t\t\t\t\titems[i].binary = items[i].binary ?? {};\n\t\t\t\t\t\t\titems[i].binary![binaryProperty] = await this.helpers.prepareBinaryData(\n\t\t\t\t\t\t\t\tdata as Buffer,\n\t\t\t\t\t\t\t\tthis.getNodeParameter('fileName', i) as string,\n\t\t\t\t\t\t\t);\n\n\t\t\t\t\t\t\tresponseData = items;\n\t\t\t\t\t\t\tbinaryOutput = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'exchangeRate') {\n\t\t\t\t\t// *********************************************************************\n\t\t\t\t\t//                             exchangeRate\n\t\t\t\t\t// *********************************************************************\n\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//       exchangeRate: get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://api-docs.transferwise.com/#exchange-rates-list\n\n\t\t\t\t\t\tconst qs = {\n\t\t\t\t\t\t\tsource: this.getNodeParameter('source', i),\n\t\t\t\t\t\t\ttarget: this.getNodeParameter('target', i),\n\t\t\t\t\t\t} as IDataObject;\n\n\t\t\t\t\t\tconst { interval, range, time } = this.getNodeParameter(\n\t\t\t\t\t\t\t'additionalFields',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t) as ExchangeRateAdditionalFields;\n\n\t\t\t\t\t\tif (interval !== undefined) {\n\t\t\t\t\t\t\tqs.group = interval;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (time !== undefined) {\n\t\t\t\t\t\t\tqs.time = time;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (range !== undefined && time === undefined) {\n\t\t\t\t\t\t\tqs.from = moment.tz(range.rangeProperties.from, timezone).utc().format();\n\t\t\t\t\t\t\tqs.to = moment.tz(range.rangeProperties.to, timezone).utc().format();\n\t\t\t\t\t\t} else if (time === undefined) {\n\t\t\t\t\t\t\tqs.from = moment().subtract(1, 'months').utc().format();\n\t\t\t\t\t\t\tqs.to = moment().utc().format();\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await wiseApiRequest.call(this, 'GET', 'v1/rates', {}, qs);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'profile') {\n\t\t\t\t\t// *********************************************************************\n\t\t\t\t\t//                             profile\n\t\t\t\t\t// *********************************************************************\n\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//          profile: get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://api-docs.transferwise.com/#user-profiles-get-by-id\n\n\t\t\t\t\t\tconst profileId = this.getNodeParameter('profileId', i);\n\t\t\t\t\t\tresponseData = await wiseApiRequest.call(this, 'GET', `v1/profiles/${profileId}`);\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         profile: getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://api-docs.transferwise.com/#user-profiles-list\n\n\t\t\t\t\t\tresponseData = await wiseApiRequest.call(this, 'GET', 'v1/profiles');\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'recipient') {\n\t\t\t\t\t// *********************************************************************\n\t\t\t\t\t//                             recipient\n\t\t\t\t\t// *********************************************************************\n\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//       recipient: getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://api-docs.transferwise.com/#recipient-accounts-list\n\n\t\t\t\t\t\tresponseData = await wiseApiRequest.call(this, 'GET', 'v1/accounts');\n\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tif (!returnAll) {\n\t\t\t\t\t\t\tconst limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = responseData.slice(0, limit);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'quote') {\n\t\t\t\t\t// *********************************************************************\n\t\t\t\t\t//                             quote\n\t\t\t\t\t// *********************************************************************\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//          quote: create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://api-docs.transferwise.com/#quotes-create\n\n\t\t\t\t\t\tconst body = {\n\t\t\t\t\t\t\tprofile: this.getNodeParameter('profileId', i),\n\t\t\t\t\t\t\tsourceCurrency: (this.getNodeParameter('sourceCurrency', i) as string).toUpperCase(),\n\t\t\t\t\t\t\ttargetCurrency: (this.getNodeParameter('targetCurrency', i) as string).toUpperCase(),\n\t\t\t\t\t\t} as IDataObject;\n\n\t\t\t\t\t\tconst amountType = this.getNodeParameter('amountType', i) as 'source' | 'target';\n\n\t\t\t\t\t\tif (amountType === 'source') {\n\t\t\t\t\t\t\tbody.sourceAmount = this.getNodeParameter('amount', i);\n\t\t\t\t\t\t} else if (amountType === 'target') {\n\t\t\t\t\t\t\tbody.targetAmount = this.getNodeParameter('amount', i);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await wiseApiRequest.call(this, 'POST', 'v2/quotes', body, {});\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//          quote: get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://api-docs.transferwise.com/#quotes-get-by-id\n\n\t\t\t\t\t\tconst quoteId = this.getNodeParameter('quoteId', i);\n\t\t\t\t\t\tresponseData = await wiseApiRequest.call(this, 'GET', `v2/quotes/${quoteId}`);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'transfer') {\n\t\t\t\t\t// *********************************************************************\n\t\t\t\t\t//                             transfer\n\t\t\t\t\t// *********************************************************************\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         transfer: create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://api-docs.transferwise.com/#transfers-create\n\n\t\t\t\t\t\tconst body = {\n\t\t\t\t\t\t\tquoteUuid: this.getNodeParameter('quoteId', i),\n\t\t\t\t\t\t\ttargetAccount: this.getNodeParameter('targetAccountId', i),\n\t\t\t\t\t\t\tcustomerTransactionId: uuid(),\n\t\t\t\t\t\t} as IDataObject;\n\n\t\t\t\t\t\tconst { reference } = this.getNodeParameter('additionalFields', i) as {\n\t\t\t\t\t\t\treference: string;\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (reference !== undefined) {\n\t\t\t\t\t\t\tbody.details = { reference };\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await wiseApiRequest.call(this, 'POST', 'v1/transfers', body, {});\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//        transfer: delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://api-docs.transferwise.com/#transfers-cancel\n\n\t\t\t\t\t\tconst transferId = this.getNodeParameter('transferId', i);\n\t\t\t\t\t\tresponseData = await wiseApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'PUT',\n\t\t\t\t\t\t\t`v1/transfers/${transferId}/cancel`,\n\t\t\t\t\t\t);\n\t\t\t\t\t} else if (operation === 'execute') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//        transfer: execute\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://api-docs.transferwise.com/#transfers-fund\n\n\t\t\t\t\t\tconst profileId = this.getNodeParameter('profileId', i);\n\t\t\t\t\t\tconst transferId = this.getNodeParameter('transferId', i) as string;\n\n\t\t\t\t\t\tconst endpoint = `v3/profiles/${profileId}/transfers/${transferId}/payments`;\n\t\t\t\t\t\tresponseData = await wiseApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\tendpoint,\n\t\t\t\t\t\t\t{ type: 'BALANCE' },\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\t// in sandbox, simulate transfer completion so that PDF receipt can be downloaded\n\n\t\t\t\t\t\tconst { environment } = await this.getCredentials('wiseApi');\n\n\t\t\t\t\t\tif (environment === 'test') {\n\t\t\t\t\t\t\tfor (const testEndpoint of [\n\t\t\t\t\t\t\t\t'processing',\n\t\t\t\t\t\t\t\t'funds_converted',\n\t\t\t\t\t\t\t\t'outgoing_payment_sent',\n\t\t\t\t\t\t\t]) {\n\t\t\t\t\t\t\t\tawait wiseApiRequest.call(\n\t\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t\t`v1/simulation/transfers/${transferId}/${testEndpoint}`,\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//        transfer: get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\tconst transferId = this.getNodeParameter('transferId', i);\n\t\t\t\t\t\tconst downloadReceipt = this.getNodeParameter('downloadReceipt', i) as boolean;\n\n\t\t\t\t\t\tif (downloadReceipt) {\n\t\t\t\t\t\t\t// https://api-docs.transferwise.com/#transfers-get-receipt-pdf\n\n\t\t\t\t\t\t\tconst data = await wiseApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t`v1/transfers/${transferId}/receipt.pdf`,\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t{ encoding: 'arraybuffer' },\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tconst binaryProperty = this.getNodeParameter('binaryProperty', i);\n\n\t\t\t\t\t\t\titems[i].binary = items[i].binary ?? {};\n\t\t\t\t\t\t\titems[i].binary![binaryProperty] = await this.helpers.prepareBinaryData(\n\t\t\t\t\t\t\t\tdata as Buffer,\n\t\t\t\t\t\t\t\tthis.getNodeParameter('fileName', i) as string,\n\t\t\t\t\t\t\t);\n\n\t\t\t\t\t\t\tresponseData = items;\n\t\t\t\t\t\t\tbinaryOutput = true;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// https://api-docs.transferwise.com/#transfers-get-by-id\n\n\t\t\t\t\t\t\tresponseData = await wiseApiRequest.call(this, 'GET', `v1/transfers/${transferId}`);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//        transfer: getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://api-docs.transferwise.com/#transfers-list\n\n\t\t\t\t\t\tconst qs = {\n\t\t\t\t\t\t\tprofile: this.getNodeParameter('profileId', i),\n\t\t\t\t\t\t} as IDataObject;\n\n\t\t\t\t\t\tconst filters = this.getNodeParameter('filters', i) as TransferFilters;\n\n\t\t\t\t\t\tObject.keys(omit(filters, 'range')).forEach((key) => {\n\t\t\t\t\t\t\tqs[key] = filters[key];\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tif (filters.range !== undefined) {\n\t\t\t\t\t\t\tqs.createdDateStart = moment\n\t\t\t\t\t\t\t\t.tz(filters.range.rangeProperties.createdDateStart, timezone)\n\t\t\t\t\t\t\t\t.utc()\n\t\t\t\t\t\t\t\t.format();\n\t\t\t\t\t\t\tqs.createdDateEnd = moment\n\t\t\t\t\t\t\t\t.tz(filters.range.rangeProperties.createdDateEnd, timezone)\n\t\t\t\t\t\t\t\t.utc()\n\t\t\t\t\t\t\t\t.format();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tqs.createdDateStart = moment().subtract(1, 'months').utc().format();\n\t\t\t\t\t\t\tqs.createdDateEnd = moment().utc().format();\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tif (!returnAll) {\n\t\t\t\t\t\t\tqs.limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await wiseApiRequest.call(this, 'GET', 'v1/transfers', {}, qs);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ error: error.toString() });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tthrow error;\n\t\t\t}\n\n\t\t\tArray.isArray(responseData)\n\t\t\t\t? returnData.push(...(responseData as IDataObject[]))\n\t\t\t\t: returnData.push(responseData as IDataObject);\n\t\t}\n\n\t\tif (binaryOutput && responseData !== undefined) {\n\t\t\treturn [responseData as INodeExecutionData[]];\n\t\t}\n\n\t\treturn [this.helpers.returnJsonArray(returnData)];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAiB;AACjB,6BAAmB;AAUnB,0BAAoC;AACpC,kBAA2B;AAE3B,0BAaO;AASP,8BAA+B;AAExB,MAAM,KAA0B;AAAA,EAAhC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,WAAW;AAAA,MACnB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA,QACZ,MAAM,wBAAmD;AACxD,gBAAM,KAAK;AAAA,YACV,WAAW,KAAK,iBAAiB,aAAa,CAAC;AAAA,UAChD;AAEA,gBAAM,WAAW,MAAM,uCAAe,KAAK,MAAM,OAAO,0BAA0B,CAAC,GAAG,EAAE;AAExF,iBAAO,SAAS,IAAI,CAAC,EAAE,IAAI,SAAS,OAA0B;AAAA,YAC7D,MAAM,SAAS,IAAI,CAAC,EAAE,SAAS,MAAM,QAAQ,EAAE,KAAK,KAAK;AAAA,YACzD,OAAO;AAAA,UACR,EAAE;AAAA,QACH;AAAA,QAEA,MAAM,cAAyC;AAC9C,gBAAM,WAAW,MAAM,uCAAe,KAAK,MAAM,OAAO,aAAa;AAErE,iBAAO,SAAS,IAAI,CAAC,EAAE,IAAI,KAAK,OAAgB;AAAA,YAC/C,MAAM,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,MAAM,CAAC;AAAA,YACjD,OAAO;AAAA,UACR,EAAE;AAAA,QACH;AAAA,QAEA,MAAM,gBAA2C;AAChD,gBAAM,KAAK;AAAA,YACV,WAAW,KAAK,iBAAiB,aAAa,CAAC;AAAA,UAChD;AAEA,gBAAM,aAAc,MAAM,uCAAe;AAAA,YACxC;AAAA,YACA;AAAA,YACA;AAAA,YACA,CAAC;AAAA,YACD;AAAA,UACD;AAEA,iBAAO,WAAW;AAAA,YACjB,CAAC,kBAAkB,EAAE,QAAQ,IAAI,mBAAmB,UAAU,SAAS,KAAK,MAAM;AACjF,kBAAI,QAAQ;AACX,sBAAM,YAAY;AAAA,kBACjB,MAAM,IAAI,QAAQ,KAAK,iBAAiB,OACvC,YAAY,OAAO,UAAU,QAAQ,EACtC,GAAG,IAAI;AAAA,kBACP,OAAO;AAAA,gBACR;AACA,iCAAiB,KAAK,SAAS;AAAA,cAChC;AACA,qBAAO;AAAA,YACR;AAAA,YACA,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAiC;AACtC,UAAM,QAAQ,KAAK,aAAa;AAEhC,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,UAAM,WAAW,KAAK,YAAY;AAElC,QAAI;AACJ,UAAM,aAA4B,CAAC;AACnC,QAAI,eAAe;AAEnB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,UAAI;AACH,YAAI,aAAa,WAAW;AAK3B,cAAI,cAAc,eAAe;AAOhC,kBAAM,KAAK;AAAA,cACV,WAAW,KAAK,iBAAiB,aAAa,CAAC;AAAA,YAChD;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,0BAA0B,CAAC,GAAG,EAAE;AAAA,UACvF,WAAW,cAAc,iBAAiB;AAOzC,2BAAe,MAAM,uCAAe;AAAA,cACnC;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAAA,UACD,WAAW,cAAc,gBAAgB;AAOxC,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,sBAAsB,KAAK,iBAAiB,uBAAuB,CAAC;AAC1E,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,WAAW,eAAe,SAAS,wBAAwB,mBAAmB,cAAc,MAAM;AAExG,kBAAM,KAAK;AAAA,cACV,UAAU,KAAK,iBAAiB,YAAY,CAAC;AAAA,YAC9C;AAEA,kBAAM,EAAE,WAAW,MAAM,IAAI,KAAK;AAAA,cACjC;AAAA,cACA;AAAA,YACD;AAEA,gBAAI,cAAc,QAAW;AAC5B,iBAAG,OAAO;AAAA,YACX;AAEA,gBAAI,UAAU,QAAW;AACxB,iBAAG,gBAAgB,uBAAAA,QACjB,GAAG,MAAM,gBAAgB,eAAe,QAAQ,EAChD,IAAI,EACJ,OAAO;AACT,iBAAG,cAAc,uBAAAA,QACf,GAAG,MAAM,gBAAgB,aAAa,QAAQ,EAC9C,IAAI,EACJ,OAAO;AAAA,YACV,OAAO;AACN,iBAAG,oBAAgB,uBAAAA,SAAO,EAAE,SAAS,GAAG,QAAQ,EAAE,IAAI,EAAE,OAAO;AAC/D,iBAAG,kBAAc,uBAAAA,SAAO,EAAE,IAAI,EAAE,OAAO;AAAA,YACxC;AAEA,gBAAI,WAAW,QAAQ;AACtB,6BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,UAAU,CAAC,GAAG,EAAE;AAAA,YACvE,OAAO;AACN,oBAAM,OAAO,MAAM,uCAAe,KAAK,MAAM,OAAO,UAAU,CAAC,GAAG,IAAI;AAAA,gBACrE,UAAU;AAAA,cACX,CAAC;AACD,oBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAEhE,oBAAM,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,UAAU,CAAC;AACtC,oBAAM,CAAC,EAAE,OAAQ,cAAc,IAAI,MAAM,KAAK,QAAQ;AAAA,gBACrD;AAAA,gBACA,KAAK,iBAAiB,YAAY,CAAC;AAAA,cACpC;AAEA,6BAAe;AACf,6BAAe;AAAA,YAChB;AAAA,UACD;AAAA,QACD,WAAW,aAAa,gBAAgB;AAKvC,cAAI,cAAc,OAAO;AAOxB,kBAAM,KAAK;AAAA,cACV,QAAQ,KAAK,iBAAiB,UAAU,CAAC;AAAA,cACzC,QAAQ,KAAK,iBAAiB,UAAU,CAAC;AAAA,YAC1C;AAEA,kBAAM,EAAE,UAAU,OAAO,KAAK,IAAI,KAAK;AAAA,cACtC;AAAA,cACA;AAAA,YACD;AAEA,gBAAI,aAAa,QAAW;AAC3B,iBAAG,QAAQ;AAAA,YACZ;AAEA,gBAAI,SAAS,QAAW;AACvB,iBAAG,OAAO;AAAA,YACX;AAEA,gBAAI,UAAU,UAAa,SAAS,QAAW;AAC9C,iBAAG,OAAO,uBAAAA,QAAO,GAAG,MAAM,gBAAgB,MAAM,QAAQ,EAAE,IAAI,EAAE,OAAO;AACvE,iBAAG,KAAK,uBAAAA,QAAO,GAAG,MAAM,gBAAgB,IAAI,QAAQ,EAAE,IAAI,EAAE,OAAO;AAAA,YACpE,WAAW,SAAS,QAAW;AAC9B,iBAAG,WAAO,uBAAAA,SAAO,EAAE,SAAS,GAAG,QAAQ,EAAE,IAAI,EAAE,OAAO;AACtD,iBAAG,SAAK,uBAAAA,SAAO,EAAE,IAAI,EAAE,OAAO;AAAA,YAC/B;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,YAAY,CAAC,GAAG,EAAE;AAAA,UACzE;AAAA,QACD,WAAW,aAAa,WAAW;AAKlC,cAAI,cAAc,OAAO;AAOxB,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,eAAe,SAAS,EAAE;AAAA,UACjF,WAAW,cAAc,UAAU;AAOlC,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,aAAa;AAAA,UACpE;AAAA,QACD,WAAW,aAAa,aAAa;AAKpC,cAAI,cAAc,UAAU;AAO3B,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,aAAa;AAEnE,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,gBAAI,CAAC,WAAW;AACf,oBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,6BAAe,aAAa,MAAM,GAAG,KAAK;AAAA,YAC3C;AAAA,UACD;AAAA,QACD,WAAW,aAAa,SAAS;AAKhC,cAAI,cAAc,UAAU;AAO3B,kBAAM,OAAO;AAAA,cACZ,SAAS,KAAK,iBAAiB,aAAa,CAAC;AAAA,cAC7C,gBAAiB,KAAK,iBAAiB,kBAAkB,CAAC,EAAa,YAAY;AAAA,cACnF,gBAAiB,KAAK,iBAAiB,kBAAkB,CAAC,EAAa,YAAY;AAAA,YACpF;AAEA,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AAExD,gBAAI,eAAe,UAAU;AAC5B,mBAAK,eAAe,KAAK,iBAAiB,UAAU,CAAC;AAAA,YACtD,WAAW,eAAe,UAAU;AACnC,mBAAK,eAAe,KAAK,iBAAiB,UAAU,CAAC;AAAA,YACtD;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,aAAa,MAAM,CAAC,CAAC;AAAA,UAC7E,WAAW,cAAc,OAAO;AAO/B,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,aAAa,OAAO,EAAE;AAAA,UAC7E;AAAA,QACD,WAAW,aAAa,YAAY;AAKnC,cAAI,cAAc,UAAU;AAO3B,kBAAM,OAAO;AAAA,cACZ,WAAW,KAAK,iBAAiB,WAAW,CAAC;AAAA,cAC7C,eAAe,KAAK,iBAAiB,mBAAmB,CAAC;AAAA,cACzD,2BAAuB,YAAAC,IAAK;AAAA,YAC7B;AAEA,kBAAM,EAAE,UAAU,IAAI,KAAK,iBAAiB,oBAAoB,CAAC;AAIjE,gBAAI,cAAc,QAAW;AAC5B,mBAAK,UAAU,EAAE,UAAU;AAAA,YAC5B;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,gBAAgB,MAAM,CAAC,CAAC;AAAA,UAChF,WAAW,cAAc,UAAU;AAOlC,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AACxD,2BAAe,MAAM,uCAAe;AAAA,cACnC;AAAA,cACA;AAAA,cACA,gBAAgB,UAAU;AAAA,YAC3B;AAAA,UACD,WAAW,cAAc,WAAW;AAOnC,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AAExD,kBAAM,WAAW,eAAe,SAAS,cAAc,UAAU;AACjE,2BAAe,MAAM,uCAAe;AAAA,cACnC;AAAA,cACA;AAAA,cACA;AAAA,cACA,EAAE,MAAM,UAAU;AAAA,cAClB,CAAC;AAAA,YACF;AAIA,kBAAM,EAAE,YAAY,IAAI,MAAM,KAAK,eAAe,SAAS;AAE3D,gBAAI,gBAAgB,QAAQ;AAC3B,yBAAW,gBAAgB;AAAA,gBAC1B;AAAA,gBACA;AAAA,gBACA;AAAA,cACD,GAAG;AACF,sBAAM,uCAAe;AAAA,kBACpB;AAAA,kBACA;AAAA,kBACA,2BAA2B,UAAU,IAAI,YAAY;AAAA,gBACtD;AAAA,cACD;AAAA,YACD;AAAA,UACD,WAAW,cAAc,OAAO;AAK/B,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AACxD,kBAAM,kBAAkB,KAAK,iBAAiB,mBAAmB,CAAC;AAElE,gBAAI,iBAAiB;AAGpB,oBAAM,OAAO,MAAM,uCAAe;AAAA,gBACjC;AAAA,gBACA;AAAA,gBACA,gBAAgB,UAAU;AAAA,gBAC1B,CAAC;AAAA,gBACD,CAAC;AAAA,gBACD,EAAE,UAAU,cAAc;AAAA,cAC3B;AACA,oBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAEhE,oBAAM,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,UAAU,CAAC;AACtC,oBAAM,CAAC,EAAE,OAAQ,cAAc,IAAI,MAAM,KAAK,QAAQ;AAAA,gBACrD;AAAA,gBACA,KAAK,iBAAiB,YAAY,CAAC;AAAA,cACpC;AAEA,6BAAe;AACf,6BAAe;AAAA,YAChB,OAAO;AAGN,6BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,gBAAgB,UAAU,EAAE;AAAA,YACnF;AAAA,UACD,WAAW,cAAc,UAAU;AAOlC,kBAAM,KAAK;AAAA,cACV,SAAS,KAAK,iBAAiB,aAAa,CAAC;AAAA,YAC9C;AAEA,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,mBAAO,SAAK,YAAAC,SAAK,SAAS,OAAO,CAAC,EAAE,QAAQ,CAAC,QAAQ;AACpD,iBAAG,GAAG,IAAI,QAAQ,GAAG;AAAA,YACtB,CAAC;AAED,gBAAI,QAAQ,UAAU,QAAW;AAChC,iBAAG,mBAAmB,uBAAAF,QACpB,GAAG,QAAQ,MAAM,gBAAgB,kBAAkB,QAAQ,EAC3D,IAAI,EACJ,OAAO;AACT,iBAAG,iBAAiB,uBAAAA,QAClB,GAAG,QAAQ,MAAM,gBAAgB,gBAAgB,QAAQ,EACzD,IAAI,EACJ,OAAO;AAAA,YACV,OAAO;AACN,iBAAG,uBAAmB,uBAAAA,SAAO,EAAE,SAAS,GAAG,QAAQ,EAAE,IAAI,EAAE,OAAO;AAClE,iBAAG,qBAAiB,uBAAAA,SAAO,EAAE,IAAI,EAAE,OAAO;AAAA,YAC3C;AAEA,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,gBAAI,CAAC,WAAW;AACf,iBAAG,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAAA,YAC5C;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,gBAAgB,CAAC,GAAG,EAAE;AAAA,UAC7E;AAAA,QACD;AAAA,MACD,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,OAAO,MAAM,SAAS,EAAE,CAAC;AAC3C;AAAA,QACD;AAEA,cAAM;AAAA,MACP;AAEA,YAAM,QAAQ,YAAY,IACvB,WAAW,KAAK,GAAI,YAA8B,IAClD,WAAW,KAAK,YAA2B;AAAA,IAC/C;AAEA,QAAI,gBAAgB,iBAAiB,QAAW;AAC/C,aAAO,CAAC,YAAoC;AAAA,IAC7C;AAEA,WAAO,CAAC,KAAK,QAAQ,gBAAgB,UAAU,CAAC;AAAA,EACjD;AACD;", "names": ["moment", "uuid", "omit"]}