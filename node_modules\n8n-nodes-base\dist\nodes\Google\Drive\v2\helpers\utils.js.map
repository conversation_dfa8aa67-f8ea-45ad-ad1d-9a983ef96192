{"version": 3, "sources": ["../../../../../../nodes/Google/Drive/v2/helpers/utils.ts"], "sourcesContent": ["import type { IDataObject, IExecuteFunctions } from 'n8n-workflow';\nimport { BINARY_ENCODING, NodeOperationError } from 'n8n-workflow';\nimport type { Readable } from 'stream';\n\nimport { RLC_DRIVE_DEFAULT, RLC_FOLDER_DEFAULT, UPLOAD_CHUNK_SIZE } from './interfaces';\n\nexport function prepareQueryString(fields: string[] | undefined) {\n\tlet queryFields = 'id, name';\n\tif (fields) {\n\t\tif (fields.includes('*')) {\n\t\t\tqueryFields = '*';\n\t\t} else {\n\t\t\tqueryFields = fields.join(', ');\n\t\t}\n\t}\n\treturn queryFields;\n}\n\nexport async function getItemBinaryData(\n\tthis: IExecuteFunctions,\n\tinputDataFieldName: string,\n\ti: number,\n\tchunkSize = UPLOAD_CHUNK_SIZE,\n) {\n\tlet contentLength: number;\n\tlet fileContent: Buffer | Readable;\n\tlet originalFilename: string | undefined;\n\tlet mimeType;\n\n\tif (!inputDataFieldName) {\n\t\tthrow new NodeOperationError(\n\t\t\tthis.getNode(),\n\t\t\t'The name of the input field containing the binary file data must be set',\n\t\t\t{\n\t\t\t\titemIndex: i,\n\t\t\t},\n\t\t);\n\t}\n\tconst binaryData = this.helpers.assertBinaryData(i, inputDataFieldName);\n\n\tif (binaryData.id) {\n\t\t// Stream data in 256KB chunks, and upload the via the resumable upload api\n\t\tfileContent = await this.helpers.getBinaryStream(binaryData.id, chunkSize);\n\t\tconst metadata = await this.helpers.getBinaryMetadata(binaryData.id);\n\t\tcontentLength = metadata.fileSize;\n\t\toriginalFilename = metadata.fileName;\n\t\tif (metadata.mimeType) mimeType = binaryData.mimeType;\n\t} else {\n\t\tfileContent = Buffer.from(binaryData.data, BINARY_ENCODING);\n\t\tcontentLength = fileContent.length;\n\t\toriginalFilename = binaryData.fileName;\n\t\tmimeType = binaryData.mimeType;\n\t}\n\n\treturn {\n\t\tcontentLength,\n\t\tfileContent,\n\t\toriginalFilename,\n\t\tmimeType,\n\t};\n}\n\nexport function setFileProperties(body: IDataObject, options: IDataObject) {\n\tif (options.propertiesUi) {\n\t\tconst values = ((options.propertiesUi as IDataObject).propertyValues as IDataObject[]) || [];\n\n\t\tbody.properties = values.reduce(\n\t\t\t(acc, value) => Object.assign(acc, { [`${value.key}`]: value.value }),\n\t\t\t{} as IDataObject,\n\t\t);\n\t}\n\n\tif (options.appPropertiesUi) {\n\t\tconst values =\n\t\t\t((options.appPropertiesUi as IDataObject).appPropertyValues as IDataObject[]) || [];\n\n\t\tbody.appProperties = values.reduce(\n\t\t\t(acc, value) => Object.assign(acc, { [`${value.key}`]: value.value }),\n\t\t\t{} as IDataObject,\n\t\t);\n\t}\n\n\treturn body;\n}\n\nexport function setUpdateCommonParams(qs: IDataObject, options: IDataObject) {\n\tif (options.keepRevisionForever) {\n\t\tqs.keepRevisionForever = options.keepRevisionForever;\n\t}\n\n\tif (options.ocrLanguage) {\n\t\tqs.ocrLanguage = options.ocrLanguage;\n\t}\n\n\tif (options.useContentAsIndexableText) {\n\t\tqs.useContentAsIndexableText = options.useContentAsIndexableText;\n\t}\n\n\treturn qs;\n}\n\nexport function updateDriveScopes(\n\tqs: IDataObject,\n\tdriveId: string,\n\tdefaultDrive = RLC_DRIVE_DEFAULT,\n) {\n\tif (driveId) {\n\t\tif (driveId === defaultDrive) {\n\t\t\tqs.includeItemsFromAllDrives = false;\n\t\t\tqs.supportsAllDrives = false;\n\t\t\tqs.spaces = 'appDataFolder, drive';\n\t\t\tqs.corpora = 'user';\n\t\t} else {\n\t\t\tqs.driveId = driveId;\n\t\t\tqs.corpora = 'drive';\n\t\t}\n\t}\n}\n\nexport function setParentFolder(\n\tfolderId: string,\n\tdriveId: string,\n\tfolderIdDefault = RLC_FOLDER_DEFAULT,\n\tdriveIdDefault = RLC_DRIVE_DEFAULT,\n) {\n\tif (folderId !== folderIdDefault) {\n\t\treturn folderId;\n\t} else if (driveId && driveId !== driveIdDefault) {\n\t\treturn driveId;\n\t} else {\n\t\treturn 'root';\n\t}\n}\n\nexport async function processInChunks(\n\tstream: Readable,\n\tchunkSize: number,\n\tprocess: (chunk: Buffer, offset: number) => void | Promise<void>,\n) {\n\tlet buffer = Buffer.alloc(0);\n\tlet offset = 0;\n\n\tfor await (const chunk of stream) {\n\t\tbuffer = Buffer.concat([buffer, chunk]);\n\n\t\twhile (buffer.length >= chunkSize) {\n\t\t\tconst chunkToProcess = buffer.subarray(0, chunkSize);\n\t\t\tawait process(chunkToProcess, offset);\n\n\t\t\tbuffer = buffer.subarray(chunkSize);\n\t\t\toffset += chunkSize;\n\t\t}\n\t}\n\n\t// Process last chunk, could be smaller than chunkSize\n\tif (buffer.length > 0) {\n\t\tawait process(buffer, offset);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAAoD;AAGpD,wBAAyE;AAElE,SAAS,mBAAmB,QAA8B;AAChE,MAAI,cAAc;AAClB,MAAI,QAAQ;AACX,QAAI,OAAO,SAAS,GAAG,GAAG;AACzB,oBAAc;AAAA,IACf,OAAO;AACN,oBAAc,OAAO,KAAK,IAAI;AAAA,IAC/B;AAAA,EACD;AACA,SAAO;AACR;AAEA,eAAsB,kBAErB,oBACA,GACA,YAAY,qCACX;AACD,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,MAAI,CAAC,oBAAoB;AACxB,UAAM,IAAI;AAAA,MACT,KAAK,QAAQ;AAAA,MACb;AAAA,MACA;AAAA,QACC,WAAW;AAAA,MACZ;AAAA,IACD;AAAA,EACD;AACA,QAAM,aAAa,KAAK,QAAQ,iBAAiB,GAAG,kBAAkB;AAEtE,MAAI,WAAW,IAAI;AAElB,kBAAc,MAAM,KAAK,QAAQ,gBAAgB,WAAW,IAAI,SAAS;AACzE,UAAM,WAAW,MAAM,KAAK,QAAQ,kBAAkB,WAAW,EAAE;AACnE,oBAAgB,SAAS;AACzB,uBAAmB,SAAS;AAC5B,QAAI,SAAS,SAAU,YAAW,WAAW;AAAA,EAC9C,OAAO;AACN,kBAAc,OAAO,KAAK,WAAW,MAAM,mCAAe;AAC1D,oBAAgB,YAAY;AAC5B,uBAAmB,WAAW;AAC9B,eAAW,WAAW;AAAA,EACvB;AAEA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AAEO,SAAS,kBAAkB,MAAmB,SAAsB;AAC1E,MAAI,QAAQ,cAAc;AACzB,UAAM,SAAW,QAAQ,aAA6B,kBAAoC,CAAC;AAE3F,SAAK,aAAa,OAAO;AAAA,MACxB,CAAC,KAAK,UAAU,OAAO,OAAO,KAAK,EAAE,CAAC,GAAG,MAAM,GAAG,EAAE,GAAG,MAAM,MAAM,CAAC;AAAA,MACpE,CAAC;AAAA,IACF;AAAA,EACD;AAEA,MAAI,QAAQ,iBAAiB;AAC5B,UAAM,SACH,QAAQ,gBAAgC,qBAAuC,CAAC;AAEnF,SAAK,gBAAgB,OAAO;AAAA,MAC3B,CAAC,KAAK,UAAU,OAAO,OAAO,KAAK,EAAE,CAAC,GAAG,MAAM,GAAG,EAAE,GAAG,MAAM,MAAM,CAAC;AAAA,MACpE,CAAC;AAAA,IACF;AAAA,EACD;AAEA,SAAO;AACR;AAEO,SAAS,sBAAsB,IAAiB,SAAsB;AAC5E,MAAI,QAAQ,qBAAqB;AAChC,OAAG,sBAAsB,QAAQ;AAAA,EAClC;AAEA,MAAI,QAAQ,aAAa;AACxB,OAAG,cAAc,QAAQ;AAAA,EAC1B;AAEA,MAAI,QAAQ,2BAA2B;AACtC,OAAG,4BAA4B,QAAQ;AAAA,EACxC;AAEA,SAAO;AACR;AAEO,SAAS,kBACf,IACA,SACA,eAAe,qCACd;AACD,MAAI,SAAS;AACZ,QAAI,YAAY,cAAc;AAC7B,SAAG,4BAA4B;AAC/B,SAAG,oBAAoB;AACvB,SAAG,SAAS;AACZ,SAAG,UAAU;AAAA,IACd,OAAO;AACN,SAAG,UAAU;AACb,SAAG,UAAU;AAAA,IACd;AAAA,EACD;AACD;AAEO,SAAS,gBACf,UACA,SACA,kBAAkB,sCAClB,iBAAiB,qCAChB;AACD,MAAI,aAAa,iBAAiB;AACjC,WAAO;AAAA,EACR,WAAW,WAAW,YAAY,gBAAgB;AACjD,WAAO;AAAA,EACR,OAAO;AACN,WAAO;AAAA,EACR;AACD;AAEA,eAAsB,gBACrB,QACA,WACA,SACC;AACD,MAAI,SAAS,OAAO,MAAM,CAAC;AAC3B,MAAI,SAAS;AAEb,mBAAiB,SAAS,QAAQ;AACjC,aAAS,OAAO,OAAO,CAAC,QAAQ,KAAK,CAAC;AAEtC,WAAO,OAAO,UAAU,WAAW;AAClC,YAAM,iBAAiB,OAAO,SAAS,GAAG,SAAS;AACnD,YAAM,QAAQ,gBAAgB,MAAM;AAEpC,eAAS,OAAO,SAAS,SAAS;AAClC,gBAAU;AAAA,IACX;AAAA,EACD;AAGA,MAAI,OAAO,SAAS,GAAG;AACtB,UAAM,QAAQ,QAAQ,MAAM;AAAA,EAC7B;AACD;", "names": []}