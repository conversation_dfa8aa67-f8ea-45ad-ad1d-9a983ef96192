{"version": 3, "sources": ["../../../../nodes/Transform/RemoveDuplicates/utils.ts"], "sourcesContent": ["import { isEqual, lt, pick } from 'lodash';\nimport get from 'lodash/get';\nimport { NodeOperationError } from 'n8n-workflow';\nimport type { IExecuteFunctions, INode, INodeExecutionData } from 'n8n-workflow';\n\nimport { compareItems, flattenKeys } from '@utils/utilities';\n\nimport { prepareFieldsArray } from '../utils/utils';\n\nexport const validateInputData = (\n\tnode: INode,\n\titems: INodeExecutionData[],\n\tkeysToCompare: string[],\n\tdisableDotNotation: boolean,\n) => {\n\tfor (const key of keysToCompare) {\n\t\tlet type: any = undefined;\n\t\tfor (const [i, item] of items.entries()) {\n\t\t\tif (key === '') {\n\t\t\t\tthrow new NodeOperationError(node, 'Name of field to compare is blank');\n\t\t\t}\n\t\t\tconst value = !disableDotNotation ? get(item.json, key) : item.json[key];\n\t\t\tif (value === null && node.typeVersion > 1) continue;\n\n\t\t\tif (value === undefined && disableDotNotation && key.includes('.')) {\n\t\t\t\tthrow new NodeOperationError(node, `'${key}' field is missing from some input items`, {\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t\"If you're trying to use a nested field, make sure you turn off 'disable dot notation' in the node options\",\n\t\t\t\t});\n\t\t\t} else if (value === undefined) {\n\t\t\t\tthrow new NodeOperationError(node, `'${key}' field is missing from some input items`);\n\t\t\t}\n\t\t\tif (type !== undefined && value !== undefined && type !== typeof value) {\n\t\t\t\tconst description =\n\t\t\t\t\t'The type of this field varies between items' +\n\t\t\t\t\t(node.typeVersion > 1\n\t\t\t\t\t\t? `, in item [${i - 1}] it's a ${type} and in item [${i}] it's a ${typeof value} `\n\t\t\t\t\t\t: '');\n\t\t\t\tthrow new NodeOperationError(node, `'${key}' isn't always the same type`, {\n\t\t\t\t\tdescription,\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\ttype = typeof value;\n\t\t\t}\n\t\t}\n\t}\n};\n\nexport function removeDuplicateInputItems(context: IExecuteFunctions, items: INodeExecutionData[]) {\n\tconst compare = context.getNodeParameter('compare', 0) as string;\n\tconst disableDotNotation = context.getNodeParameter(\n\t\t'options.disableDotNotation',\n\t\t0,\n\t\tfalse,\n\t) as boolean;\n\tconst removeOtherFields = context.getNodeParameter(\n\t\t'options.removeOtherFields',\n\t\t0,\n\t\tfalse,\n\t) as boolean;\n\n\tlet keys = disableDotNotation\n\t\t? Object.keys(items[0].json)\n\t\t: Object.keys(flattenKeys(items[0].json));\n\n\tfor (const item of items) {\n\t\tconst itemKeys = disableDotNotation\n\t\t\t? Object.keys(item.json)\n\t\t\t: Object.keys(flattenKeys(item.json));\n\t\tfor (const key of itemKeys) {\n\t\t\tif (!keys.includes(key)) {\n\t\t\t\tkeys.push(key);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (compare === 'allFieldsExcept') {\n\t\tconst fieldsToExclude = prepareFieldsArray(\n\t\t\tcontext.getNodeParameter('fieldsToExclude', 0, '') as string,\n\t\t\t'Fields To Exclude',\n\t\t);\n\n\t\tif (!fieldsToExclude.length) {\n\t\t\tthrow new NodeOperationError(\n\t\t\t\tcontext.getNode(),\n\t\t\t\t'No fields specified. Please add a field to exclude from comparison',\n\t\t\t);\n\t\t}\n\t\tif (!disableDotNotation) {\n\t\t\tkeys = Object.keys(flattenKeys(items[0].json));\n\t\t}\n\t\tkeys = keys.filter((key) => !fieldsToExclude.includes(key));\n\t}\n\tif (compare === 'selectedFields') {\n\t\tconst fieldsToCompare = prepareFieldsArray(\n\t\t\tcontext.getNodeParameter('fieldsToCompare', 0, '') as string,\n\t\t\t'Fields To Compare',\n\t\t);\n\t\tif (!fieldsToCompare.length) {\n\t\t\tthrow new NodeOperationError(\n\t\t\t\tcontext.getNode(),\n\t\t\t\t'No fields specified. Please add a field to compare on',\n\t\t\t);\n\t\t}\n\t\tif (!disableDotNotation) {\n\t\t\tkeys = Object.keys(flattenKeys(items[0].json));\n\t\t}\n\t\tkeys = fieldsToCompare.map((key) => key.trim());\n\t}\n\n\t// This solution is O(nlogn)\n\t// add original index to the items\n\tconst newItems = items.map(\n\t\t(item, index) =>\n\t\t\t({\n\t\t\t\tjson: { ...item.json, __INDEX: index },\n\t\t\t\tpairedItem: { item: index },\n\t\t\t}) as INodeExecutionData,\n\t);\n\t//sort items using the compare keys\n\tnewItems.sort((a, b) => {\n\t\tlet result = 0;\n\n\t\tfor (const key of keys) {\n\t\t\tlet equal;\n\t\t\tif (!disableDotNotation) {\n\t\t\t\tequal = isEqual(get(a.json, key), get(b.json, key));\n\t\t\t} else {\n\t\t\t\tequal = isEqual(a.json[key], b.json[key]);\n\t\t\t}\n\t\t\tif (!equal) {\n\t\t\t\tlet lessThan;\n\t\t\t\tif (!disableDotNotation) {\n\t\t\t\t\tlessThan = lt(get(a.json, key), get(b.json, key));\n\t\t\t\t} else {\n\t\t\t\t\tlessThan = lt(a.json[key], b.json[key]);\n\t\t\t\t}\n\t\t\t\tresult = lessThan ? -1 : 1;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\treturn result;\n\t});\n\n\tvalidateInputData(context.getNode(), newItems, keys, disableDotNotation);\n\n\t// collect the original indexes of items to be removed\n\tconst removedIndexes: number[] = [];\n\tlet temp = newItems[0];\n\tfor (let index = 1; index < newItems.length; index++) {\n\t\tif (compareItems(newItems[index], temp, keys, disableDotNotation)) {\n\t\t\tremovedIndexes.push(newItems[index].json.__INDEX as unknown as number);\n\t\t} else {\n\t\t\ttemp = newItems[index];\n\t\t}\n\t}\n\tlet updatedItems: INodeExecutionData[] = items.filter(\n\t\t(_, index) => !removedIndexes.includes(index),\n\t);\n\n\tif (removeOtherFields) {\n\t\tupdatedItems = updatedItems.map((item, index) => ({\n\t\t\tjson: pick(item.json, ...keys),\n\t\t\tpairedItem: { item: index },\n\t\t}));\n\t}\n\treturn [updatedItems];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAAkC;AAClC,iBAAgB;AAChB,0BAAmC;AAGnC,uBAA0C;AAE1C,mBAAmC;AAE5B,MAAM,oBAAoB,CAChC,MACA,OACA,eACA,uBACI;AACJ,aAAW,OAAO,eAAe;AAChC,QAAI,OAAY;AAChB,eAAW,CAAC,GAAG,IAAI,KAAK,MAAM,QAAQ,GAAG;AACxC,UAAI,QAAQ,IAAI;AACf,cAAM,IAAI,uCAAmB,MAAM,mCAAmC;AAAA,MACvE;AACA,YAAM,QAAQ,CAAC,yBAAqB,WAAAA,SAAI,KAAK,MAAM,GAAG,IAAI,KAAK,KAAK,GAAG;AACvE,UAAI,UAAU,QAAQ,KAAK,cAAc,EAAG;AAE5C,UAAI,UAAU,UAAa,sBAAsB,IAAI,SAAS,GAAG,GAAG;AACnE,cAAM,IAAI,uCAAmB,MAAM,IAAI,GAAG,4CAA4C;AAAA,UACrF,aACC;AAAA,QACF,CAAC;AAAA,MACF,WAAW,UAAU,QAAW;AAC/B,cAAM,IAAI,uCAAmB,MAAM,IAAI,GAAG,0CAA0C;AAAA,MACrF;AACA,UAAI,SAAS,UAAa,UAAU,UAAa,SAAS,OAAO,OAAO;AACvE,cAAM,cACL,iDACC,KAAK,cAAc,IACjB,cAAc,IAAI,CAAC,YAAY,IAAI,iBAAiB,CAAC,YAAY,OAAO,KAAK,MAC7E;AACJ,cAAM,IAAI,uCAAmB,MAAM,IAAI,GAAG,gCAAgC;AAAA,UACzE;AAAA,QACD,CAAC;AAAA,MACF,OAAO;AACN,eAAO,OAAO;AAAA,MACf;AAAA,IACD;AAAA,EACD;AACD;AAEO,SAAS,0BAA0B,SAA4B,OAA6B;AAClG,QAAM,UAAU,QAAQ,iBAAiB,WAAW,CAAC;AACrD,QAAM,qBAAqB,QAAQ;AAAA,IAClC;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACA,QAAM,oBAAoB,QAAQ;AAAA,IACjC;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAEA,MAAI,OAAO,qBACR,OAAO,KAAK,MAAM,CAAC,EAAE,IAAI,IACzB,OAAO,SAAK,8BAAY,MAAM,CAAC,EAAE,IAAI,CAAC;AAEzC,aAAW,QAAQ,OAAO;AACzB,UAAM,WAAW,qBACd,OAAO,KAAK,KAAK,IAAI,IACrB,OAAO,SAAK,8BAAY,KAAK,IAAI,CAAC;AACrC,eAAW,OAAO,UAAU;AAC3B,UAAI,CAAC,KAAK,SAAS,GAAG,GAAG;AACxB,aAAK,KAAK,GAAG;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAEA,MAAI,YAAY,mBAAmB;AAClC,UAAM,sBAAkB;AAAA,MACvB,QAAQ,iBAAiB,mBAAmB,GAAG,EAAE;AAAA,MACjD;AAAA,IACD;AAEA,QAAI,CAAC,gBAAgB,QAAQ;AAC5B,YAAM,IAAI;AAAA,QACT,QAAQ,QAAQ;AAAA,QAChB;AAAA,MACD;AAAA,IACD;AACA,QAAI,CAAC,oBAAoB;AACxB,aAAO,OAAO,SAAK,8BAAY,MAAM,CAAC,EAAE,IAAI,CAAC;AAAA,IAC9C;AACA,WAAO,KAAK,OAAO,CAAC,QAAQ,CAAC,gBAAgB,SAAS,GAAG,CAAC;AAAA,EAC3D;AACA,MAAI,YAAY,kBAAkB;AACjC,UAAM,sBAAkB;AAAA,MACvB,QAAQ,iBAAiB,mBAAmB,GAAG,EAAE;AAAA,MACjD;AAAA,IACD;AACA,QAAI,CAAC,gBAAgB,QAAQ;AAC5B,YAAM,IAAI;AAAA,QACT,QAAQ,QAAQ;AAAA,QAChB;AAAA,MACD;AAAA,IACD;AACA,QAAI,CAAC,oBAAoB;AACxB,aAAO,OAAO,SAAK,8BAAY,MAAM,CAAC,EAAE,IAAI,CAAC;AAAA,IAC9C;AACA,WAAO,gBAAgB,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC;AAAA,EAC/C;AAIA,QAAM,WAAW,MAAM;AAAA,IACtB,CAAC,MAAM,WACL;AAAA,MACA,MAAM,EAAE,GAAG,KAAK,MAAM,SAAS,MAAM;AAAA,MACrC,YAAY,EAAE,MAAM,MAAM;AAAA,IAC3B;AAAA,EACF;AAEA,WAAS,KAAK,CAAC,GAAG,MAAM;AACvB,QAAI,SAAS;AAEb,eAAW,OAAO,MAAM;AACvB,UAAI;AACJ,UAAI,CAAC,oBAAoB;AACxB,oBAAQ,2BAAQ,WAAAA,SAAI,EAAE,MAAM,GAAG,OAAG,WAAAA,SAAI,EAAE,MAAM,GAAG,CAAC;AAAA,MACnD,OAAO;AACN,oBAAQ,uBAAQ,EAAE,KAAK,GAAG,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,MACzC;AACA,UAAI,CAAC,OAAO;AACX,YAAI;AACJ,YAAI,CAAC,oBAAoB;AACxB,yBAAW,sBAAG,WAAAA,SAAI,EAAE,MAAM,GAAG,OAAG,WAAAA,SAAI,EAAE,MAAM,GAAG,CAAC;AAAA,QACjD,OAAO;AACN,yBAAW,kBAAG,EAAE,KAAK,GAAG,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,QACvC;AACA,iBAAS,WAAW,KAAK;AACzB;AAAA,MACD;AAAA,IACD;AACA,WAAO;AAAA,EACR,CAAC;AAED,oBAAkB,QAAQ,QAAQ,GAAG,UAAU,MAAM,kBAAkB;AAGvE,QAAM,iBAA2B,CAAC;AAClC,MAAI,OAAO,SAAS,CAAC;AACrB,WAAS,QAAQ,GAAG,QAAQ,SAAS,QAAQ,SAAS;AACrD,YAAI,+BAAa,SAAS,KAAK,GAAG,MAAM,MAAM,kBAAkB,GAAG;AAClE,qBAAe,KAAK,SAAS,KAAK,EAAE,KAAK,OAA4B;AAAA,IACtE,OAAO;AACN,aAAO,SAAS,KAAK;AAAA,IACtB;AAAA,EACD;AACA,MAAI,eAAqC,MAAM;AAAA,IAC9C,CAAC,GAAG,UAAU,CAAC,eAAe,SAAS,KAAK;AAAA,EAC7C;AAEA,MAAI,mBAAmB;AACtB,mBAAe,aAAa,IAAI,CAAC,MAAM,WAAW;AAAA,MACjD,UAAM,oBAAK,KAAK,MAAM,GAAG,IAAI;AAAA,MAC7B,YAAY,EAAE,MAAM,MAAM;AAAA,IAC3B,EAAE;AAAA,EACH;AACA,SAAO,CAAC,YAAY;AACrB;", "names": ["get"]}