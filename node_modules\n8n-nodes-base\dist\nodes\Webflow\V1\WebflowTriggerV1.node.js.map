{"version": 3, "sources": ["../../../../nodes/Webflow/V1/WebflowTriggerV1.node.ts"], "sourcesContent": ["import {\n\ttype IHookFunctions,\n\ttype IWebhookFunctions,\n\ttype IDataObject,\n\ttype INodeType,\n\ttype INodeTypeDescription,\n\ttype IWebhookResponseData,\n\ttype INodeTypeBaseDescription,\n\tNodeConnectionTypes,\n} from 'n8n-workflow';\n\nimport { getSites, webflowApiRequest } from '../GenericFunctions';\n\nexport class WebflowTriggerV1 implements INodeType {\n\tdescription: INodeTypeDescription;\n\n\tconstructor(baseDescription: INodeTypeBaseDescription) {\n\t\tthis.description = {\n\t\t\t...baseDescription,\n\t\t\tdisplayName: 'Webflow Trigger',\n\t\t\tname: 'webflowTrigger',\n\t\t\ticon: 'file:webflow.svg',\n\t\t\tgroup: ['trigger'],\n\t\t\tversion: 1,\n\t\t\tdescription: 'Handle Webflow events via webhooks',\n\t\t\tdefaults: {\n\t\t\t\tname: 'Webflow Trigger',\n\t\t\t},\n\n\t\t\tinputs: [],\n\t\t\toutputs: [NodeConnectionTypes.Main],\n\t\t\tcredentials: [\n\t\t\t\t{\n\t\t\t\t\tname: 'webflowApi',\n\t\t\t\t\trequired: true,\n\t\t\t\t\tdisplayOptions: {\n\t\t\t\t\t\tshow: {\n\t\t\t\t\t\t\tauthentication: ['accessToken'],\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'webflowOAuth2Api',\n\t\t\t\t\trequired: true,\n\t\t\t\t\tdisplayOptions: {\n\t\t\t\t\t\tshow: {\n\t\t\t\t\t\t\tauthentication: ['oAuth2'],\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t],\n\t\t\twebhooks: [\n\t\t\t\t{\n\t\t\t\t\tname: 'default',\n\t\t\t\t\thttpMethod: 'POST',\n\t\t\t\t\tresponseMode: 'onReceived',\n\t\t\t\t\tpath: 'webhook',\n\t\t\t\t},\n\t\t\t],\n\t\t\tproperties: [\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Authentication',\n\t\t\t\t\tname: 'authentication',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\toptions: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Access Token',\n\t\t\t\t\t\t\tvalue: 'accessToken',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'OAuth2',\n\t\t\t\t\t\t\tvalue: 'oAuth2',\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t\tdefault: 'accessToken',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Site Name or ID',\n\t\t\t\t\tname: 'site',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\trequired: true,\n\t\t\t\t\tdefault: '',\n\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\tloadOptionsMethod: 'getSites',\n\t\t\t\t\t},\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'Site that will trigger the events. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Event',\n\t\t\t\t\tname: 'event',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\trequired: true,\n\t\t\t\t\toptions: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Collection Item Created',\n\t\t\t\t\t\t\tvalue: 'collection_item_created',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Collection Item Deleted',\n\t\t\t\t\t\t\tvalue: 'collection_item_deleted',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Collection Item Updated',\n\t\t\t\t\t\t\tvalue: 'collection_item_changed',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Ecomm Inventory Changed',\n\t\t\t\t\t\t\tvalue: 'ecomm_inventory_changed',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Ecomm New Order',\n\t\t\t\t\t\t\tvalue: 'ecomm_new_order',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Ecomm Order Changed',\n\t\t\t\t\t\t\tvalue: 'ecomm_order_changed',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Form Submission',\n\t\t\t\t\t\t\tvalue: 'form_submission',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Site Publish',\n\t\t\t\t\t\t\tvalue: 'site_publish',\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t\tdefault: 'form_submission',\n\t\t\t\t},\n\t\t\t],\n\t\t};\n\t}\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\tgetSites,\n\t\t},\n\t};\n\n\twebhookMethods = {\n\t\tdefault: {\n\t\t\tasync checkExists(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\t\t\t\tconst siteId = this.getNodeParameter('site') as string;\n\n\t\t\t\tconst event = this.getNodeParameter('event') as string;\n\t\t\t\tconst registeredWebhooks = await webflowApiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t`/sites/${siteId}/webhooks`,\n\t\t\t\t);\n\n\t\t\t\tconst webhooks = registeredWebhooks.body?.webhooks || registeredWebhooks;\n\n\t\t\t\tfor (const webhook of webhooks) {\n\t\t\t\t\tif (webhook.url === webhookUrl && webhook.triggerType === event) {\n\t\t\t\t\t\twebhookData.webhookId = webhook._id;\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\t\t\t},\n\n\t\t\tasync create(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\tconst siteId = this.getNodeParameter('site') as string;\n\t\t\t\tconst event = this.getNodeParameter('event') as string;\n\t\t\t\tconst endpoint = `/sites/${siteId}/webhooks`;\n\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\tsite_id: siteId,\n\t\t\t\t\ttriggerType: event,\n\t\t\t\t\turl: webhookUrl,\n\t\t\t\t};\n\n\t\t\t\tconst response = await webflowApiRequest.call(this, 'POST', endpoint, body);\n\t\t\t\tconst _id = response.body?._id || response._id;\n\t\t\t\twebhookData.webhookId = _id;\n\t\t\t\treturn true;\n\t\t\t},\n\t\t\tasync delete(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tlet responseData;\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\tconst siteId = this.getNodeParameter('site') as string;\n\t\t\t\tconst endpoint = `/sites/${siteId}/webhooks/${webhookData.webhookId}`;\n\t\t\t\ttry {\n\t\t\t\t\tresponseData = await webflowApiRequest.call(this, 'DELETE', endpoint);\n\t\t\t\t} catch (error) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tconst deleted = responseData.body?.deleted || responseData.deleted;\n\t\t\t\tif (!deleted) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tdelete webhookData.webhookId;\n\t\t\t\treturn true;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync webhook(this: IWebhookFunctions): Promise<IWebhookResponseData> {\n\t\tconst req = this.getRequestObject();\n\t\treturn {\n\t\t\tworkflowData: [this.helpers.returnJsonArray(req.body as IDataObject[])],\n\t\t};\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BASO;AAEP,8BAA4C;AAErC,MAAM,iBAAsC;AAAA,EAGlD,YAAY,iBAA2C;AAqHvD,mBAAU;AAAA,MACT,aAAa;AAAA,QACZ;AAAA,MACD;AAAA,IACD;AAEA,0BAAiB;AAAA,MAChB,SAAS;AAAA,QACR,MAAM,cAAoD;AACzD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AACnD,gBAAM,SAAS,KAAK,iBAAiB,MAAM;AAE3C,gBAAM,QAAQ,KAAK,iBAAiB,OAAO;AAC3C,gBAAM,qBAAqB,MAAM,0CAAkB;AAAA,YAClD;AAAA,YACA;AAAA,YACA,UAAU,MAAM;AAAA,UACjB;AAEA,gBAAM,WAAW,mBAAmB,MAAM,YAAY;AAEtD,qBAAW,WAAW,UAAU;AAC/B,gBAAI,QAAQ,QAAQ,cAAc,QAAQ,gBAAgB,OAAO;AAChE,0BAAY,YAAY,QAAQ;AAChC,qBAAO;AAAA,YACR;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA,QAEA,MAAM,SAA+C;AACpD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AACnD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,gBAAM,SAAS,KAAK,iBAAiB,MAAM;AAC3C,gBAAM,QAAQ,KAAK,iBAAiB,OAAO;AAC3C,gBAAM,WAAW,UAAU,MAAM;AACjC,gBAAM,OAAoB;AAAA,YACzB,SAAS;AAAA,YACT,aAAa;AAAA,YACb,KAAK;AAAA,UACN;AAEA,gBAAM,WAAW,MAAM,0CAAkB,KAAK,MAAM,QAAQ,UAAU,IAAI;AAC1E,gBAAM,MAAM,SAAS,MAAM,OAAO,SAAS;AAC3C,sBAAY,YAAY;AACxB,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,cAAI;AACJ,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,gBAAM,SAAS,KAAK,iBAAiB,MAAM;AAC3C,gBAAM,WAAW,UAAU,MAAM,aAAa,YAAY,SAAS;AACnE,cAAI;AACH,2BAAe,MAAM,0CAAkB,KAAK,MAAM,UAAU,QAAQ;AAAA,UACrE,SAAS,OAAO;AACf,mBAAO;AAAA,UACR;AACA,gBAAM,UAAU,aAAa,MAAM,WAAW,aAAa;AAC3D,cAAI,CAAC,SAAS;AACb,mBAAO;AAAA,UACR;AACA,iBAAO,YAAY;AACnB,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAvLC,SAAK,cAAc;AAAA,MAClB,GAAG;AAAA,MACH,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,SAAS;AAAA,MACjB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MAEA,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,gBAAgB,CAAC,aAAa;AAAA,YAC/B;AAAA,UACD;AAAA,QACD;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,gBAAgB,CAAC,QAAQ;AAAA,YAC1B;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA,UAAU;AAAA,QACT;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,MAAM;AAAA,QACP;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS;AAAA,UACT,aAAa;AAAA,YACZ,mBAAmB;AAAA,UACpB;AAAA,UACA,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EAuEA,MAAM,UAAgE;AACrE,UAAM,MAAM,KAAK,iBAAiB;AAClC,WAAO;AAAA,MACN,cAAc,CAAC,KAAK,QAAQ,gBAAgB,IAAI,IAAqB,CAAC;AAAA,IACvE;AAAA,EACD;AACD;", "names": []}