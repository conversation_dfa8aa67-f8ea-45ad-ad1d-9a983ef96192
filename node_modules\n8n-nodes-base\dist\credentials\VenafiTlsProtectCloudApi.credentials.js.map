{"version": 3, "sources": ["../../credentials/VenafiTlsProtectCloudApi.credentials.ts"], "sourcesContent": ["import type {\n\tIAuthenticateGeneric,\n\tICredentialTestRequest,\n\tICredentialType,\n\tINodeProperties,\n} from 'n8n-workflow';\n\nexport class VenafiTlsProtectCloudApi implements ICredentialType {\n\tname = 'venafiTlsProtectCloudApi';\n\n\tdisplayName = 'Venafi TLS Protect Cloud';\n\n\tdocumentationUrl = 'venafitlsprotectcloud';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Region',\n\t\t\tname: 'region',\n\t\t\ttype: 'options',\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'US',\n\t\t\t\t\tvalue: 'cloud',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'EU',\n\t\t\t\t\tvalue: 'eu',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'cloud',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'API Key',\n\t\t\tname: 'apiKey',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\tdefault: '',\n\t\t},\n\t];\n\n\tauthenticate: IAuthenticateGeneric = {\n\t\ttype: 'generic',\n\t\tproperties: {\n\t\t\theaders: {\n\t\t\t\t'tppl-api-key': '={{$credentials.apiKey}}',\n\t\t\t},\n\t\t},\n\t};\n\n\ttest: ICredentialTestRequest = {\n\t\trequest: {\n\t\t\tbaseURL: '=https://api.venafi.{{$credentials.region ?? \"cloud\"}}',\n\t\t\turl: '/v1/preferences',\n\t\t},\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOO,MAAM,yBAAoD;AAAA,EAA1D;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,MACV;AAAA,IACD;AAEA,wBAAqC;AAAA,MACpC,MAAM;AAAA,MACN,YAAY;AAAA,QACX,SAAS;AAAA,UACR,gBAAgB;AAAA,QACjB;AAAA,MACD;AAAA,IACD;AAEA,gBAA+B;AAAA,MAC9B,SAAS;AAAA,QACR,SAAS;AAAA,QACT,KAAK;AAAA,MACN;AAAA,IACD;AAAA;AACD;", "names": []}