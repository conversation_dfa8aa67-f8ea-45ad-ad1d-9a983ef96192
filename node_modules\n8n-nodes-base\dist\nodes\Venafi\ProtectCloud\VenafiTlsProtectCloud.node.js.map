{"version": 3, "sources": ["../../../../nodes/Venafi/ProtectCloud/VenafiTlsProtectCloud.node.ts"], "sourcesContent": ["import {\n\ttype IExecuteFunctions,\n\ttype IDataObject,\n\ttype ILoadOptionsFunctions,\n\ttype INodeExecutionData,\n\ttype INodePropertyOptions,\n\ttype INodeType,\n\ttype INodeTypeDescription,\n\tNodeConnectionTypes,\n} from 'n8n-workflow';\n\nimport { certificateFields, certificateOperations } from './CertificateDescription';\nimport type {\n\tICertficateKeystoreRequest,\n\tICertficateRequest,\n\tICsrAttributes,\n\tIKeyTypeParameters,\n\tISubjectAltNamesByType,\n} from './CertificateInterface';\nimport {\n\tcertificateRequestFields,\n\tcertificateRequestOperations,\n} from './CertificateRequestDescription';\nimport { encryptPassphrase, venafiApiRequest, venafiApiRequestAllItems } from './GenericFunctions';\n\nexport class VenafiTlsProtectCloud implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Venafi TLS Protect Cloud',\n\t\tname: 'venafiTlsProtectCloud',\n\t\ticon: 'file:../venafi.svg',\n\t\tgroup: ['input'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume Venafi TLS Protect Cloud API',\n\t\tdefaults: {\n\t\t\tname: 'Venafi TLS Protect Cloud',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'venafiTlsProtectCloudApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Certificate',\n\t\t\t\t\t\tvalue: 'certificate',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Certificate Request',\n\t\t\t\t\t\tvalue: 'certificateRequest',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'certificateRequest',\n\t\t\t},\n\t\t\t...certificateOperations,\n\t\t\t...certificateFields,\n\t\t\t...certificateRequestOperations,\n\t\t\t...certificateRequestFields,\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\tasync getApplications(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst { applications } = await venafiApiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/outagedetection/v1/applications',\n\t\t\t\t);\n\n\t\t\t\tfor (const application of applications) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: application.name,\n\t\t\t\t\t\tvalue: application.id,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\tasync getCertificateIssuingTemplates(\n\t\t\t\tthis: ILoadOptionsFunctions,\n\t\t\t): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst currentApplication: string = this.getCurrentNodeParameter('applicationId') as string;\n\n\t\t\t\tconst { certificateIssuingTemplateAliasIdMap } = (await venafiApiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t`/outagedetection/v1/applications/${currentApplication}`,\n\t\t\t\t)) as { certificateIssuingTemplateAliasIdMap: { [key: string]: string } };\n\n\t\t\t\tfor (const [templateName, templateId] of Object.entries(\n\t\t\t\t\tcertificateIssuingTemplateAliasIdMap,\n\t\t\t\t)) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: templateName,\n\t\t\t\t\t\tvalue: templateId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: IDataObject[] = [];\n\t\tconst length = items.length;\n\t\tconst qs: IDataObject = {};\n\t\tlet responseData;\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'certificateRequest') {\n\t\t\t\t\t//https://api.venafi.cloud/webjars/swagger-ui/index.html?configUrl=/v3/api-docs/swagger-config&urls.primaryName=outagedetection-service#//v1/certificaterequests_create\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst applicationId = this.getNodeParameter('applicationId', i) as string;\n\t\t\t\t\t\tconst certificateIssuingTemplateId = this.getNodeParameter(\n\t\t\t\t\t\t\t'certificateIssuingTemplateId',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tconst generateCsr = this.getNodeParameter('generateCsr', i) as boolean;\n\n\t\t\t\t\t\tconst body: ICertficateRequest = {\n\t\t\t\t\t\t\tapplicationId,\n\t\t\t\t\t\t\tcertificateIssuingTemplateId,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (generateCsr) {\n\t\t\t\t\t\t\tconst commonName = this.getNodeParameter('commonName', i) as string;\n\t\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\t\tconst keyTypeDetails: IKeyTypeParameters = {};\n\t\t\t\t\t\t\tconst csrAttributes: ICsrAttributes = {};\n\t\t\t\t\t\t\tconst subjectAltNamesByType: ISubjectAltNamesByType = {};\n\n\t\t\t\t\t\t\tbody.isVaaSGenerated = true;\n\n\t\t\t\t\t\t\tcsrAttributes.commonName = commonName;\n\n\t\t\t\t\t\t\t// Csr Generation\n\t\t\t\t\t\t\tif (additionalFields.organization) {\n\t\t\t\t\t\t\t\tcsrAttributes.organization = additionalFields.organization as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.organizationalUnits) {\n\t\t\t\t\t\t\t\tcsrAttributes.organizationalUnits =\n\t\t\t\t\t\t\t\t\tadditionalFields.organizationalUnits as string[];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.locality) {\n\t\t\t\t\t\t\t\tcsrAttributes.locality = additionalFields.locality as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.state) {\n\t\t\t\t\t\t\t\tcsrAttributes.state = additionalFields.state as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.country) {\n\t\t\t\t\t\t\t\tcsrAttributes.country = additionalFields.country as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbody.csrAttributes = csrAttributes;\n\n\t\t\t\t\t\t\t// Key type\n\t\t\t\t\t\t\tif (additionalFields.keyType) {\n\t\t\t\t\t\t\t\tkeyTypeDetails.keyType = additionalFields.keyType as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.keyCurve) {\n\t\t\t\t\t\t\t\tkeyTypeDetails.keyCurve = additionalFields.keyCurve as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.keyLength) {\n\t\t\t\t\t\t\t\tkeyTypeDetails.keyLength = additionalFields.keyLength as number;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (Object.keys(keyTypeDetails).length !== 0) {\n\t\t\t\t\t\t\t\tbody.csrAttributes.keyTypeParameters = keyTypeDetails;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// SAN\n\t\t\t\t\t\t\tif (additionalFields.SubjectAltNamesUi) {\n\t\t\t\t\t\t\t\tfor (const key of (additionalFields.SubjectAltNamesUi as IDataObject)\n\t\t\t\t\t\t\t\t\t.SubjectAltNamesValues as IDataObject[]) {\n\t\t\t\t\t\t\t\t\tif (key.Typename === 'dnsNames') {\n\t\t\t\t\t\t\t\t\t\tsubjectAltNamesByType.dnsNames\n\t\t\t\t\t\t\t\t\t\t\t? subjectAltNamesByType.dnsNames.push(key.name as string)\n\t\t\t\t\t\t\t\t\t\t\t: (subjectAltNamesByType.dnsNames = [key.name as string]);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/*if (key.Typename === 'ipAddresses') {\n\t\t\t\t\t\t\t\t\t\tsubjectAltNamesByType.ipAddresses ? subjectAltNamesByType.ipAddresses.push(key.name as string) : subjectAltNamesByType.ipAddresses = [key.name as string];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tif (key.Typename === 'rfc822Names') {\n\t\t\t\t\t\t\t\t\t\tsubjectAltNamesByType.rfc822Names ? subjectAltNamesByType.rfc822Names.push(key.name as string) : subjectAltNamesByType.rfc822Names = [key.name as string];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tif (key.Typename === 'uniformResourceIdentifiers') {\n\t\t\t\t\t\t\t\t\t\tsubjectAltNamesByType.uniformResourceIdentifiers ? subjectAltNamesByType.uniformResourceIdentifiers.push(key.name as string) : subjectAltNamesByType.uniformResourceIdentifiers = [key.name as string];\n\t\t\t\t\t\t\t\t\t}*/\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (Object.keys(subjectAltNamesByType).length !== 0) {\n\t\t\t\t\t\t\t\tbody.csrAttributes.subjectAlternativeNamesByType = subjectAltNamesByType;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst certificateSigningRequest = this.getNodeParameter(\n\t\t\t\t\t\t\t\t'certificateSigningRequest',\n\t\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t) as string;\n\t\t\t\t\t\t\tbody.isVaaSGenerated = false;\n\t\t\t\t\t\t\tbody.certificateSigningRequest = certificateSigningRequest;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tObject.assign(body, options);\n\n\t\t\t\t\t\tresponseData = await venafiApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/outagedetection/v1/certificaterequests',\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tresponseData = responseData.certificateRequests;\n\t\t\t\t\t}\n\n\t\t\t\t\t//https://api.venafi.cloud/webjars/swagger-ui/index.html?configUrl=/v3/api-docs/swagger-config&urls.primaryName=outagedetection-service#//v1/certificaterequests_getById\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tconst certificateId = this.getNodeParameter('certificateRequestId', i) as string;\n\n\t\t\t\t\t\tresponseData = await venafiApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t`/outagedetection/v1/certificaterequests/${certificateId}`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\t//https://api.venafi.cloud/webjars/swagger-ui/index.html?configUrl=/v3/api-docs/swagger-config&urls.primaryName=outagedetection-service#//v1/certificaterequests_getAll\n\t\t\t\t\tif (operation === 'getMany') {\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await venafiApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'certificateRequests',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/outagedetection/v1/certificaterequests',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = await venafiApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/outagedetection/v1/certificaterequests',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\n\t\t\t\t\t\t\tresponseData = responseData.certificateRequests.splice(0, limit);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (resource === 'certificate') {\n\t\t\t\t\t//https://api.venafi.cloud/webjars/swagger-ui/index.html?configUrl=%2Fv3%2Fapi-docs%2Fswagger-config&urls.primaryName=outagedetection-service#/%2Fv1/certificateretirement_deleteCertificates\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tconst certificateId = this.getNodeParameter('certificateId', i) as string;\n\n\t\t\t\t\t\tresponseData = await venafiApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/outagedetection/v1/certificates/deletion',\n\t\t\t\t\t\t\t{ certificateIds: [certificateId] },\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tresponseData = responseData.certificates;\n\t\t\t\t\t}\n\n\t\t\t\t\t//https://api.venafi.cloud/webjars/swagger-ui/index.html?configUrl=%2Fv3%2Fapi-docs%2Fswagger-config&urls.primaryName=outagedetection-service#/\n\t\t\t\t\tif (operation === 'download') {\n\t\t\t\t\t\tconst certificateId = this.getNodeParameter('certificateId', i) as string;\n\t\t\t\t\t\tconst binaryProperty = this.getNodeParameter('binaryProperty', i);\n\t\t\t\t\t\tconst downloadItem = this.getNodeParameter('downloadItem', i) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\n\t\t\t\t\t\t// Cert Download\n\t\t\t\t\t\tif (downloadItem === 'certificate') {\n\t\t\t\t\t\t\tObject.assign(qs, options);\n\t\t\t\t\t\t\tresponseData = await venafiApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t`/outagedetection/v1/certificates/${certificateId}/contents`,\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t\t{ encoding: null, json: false, resolveWithFullResponse: true, cert: true },\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst exportFormat = this.getNodeParameter('keystoreType', i) as string;\n\n\t\t\t\t\t\t\tconst body: ICertficateKeystoreRequest = {\n\t\t\t\t\t\t\t\texportFormat,\n\t\t\t\t\t\t\t};\n\n\t\t\t\t\t\t\tconst privateKeyPassphrase = this.getNodeParameter(\n\t\t\t\t\t\t\t\t'privateKeyPassphrase',\n\t\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t) as string;\n\t\t\t\t\t\t\tconst certificateLabel = this.getNodeParameter('certificateLabel', i) as string;\n\n\t\t\t\t\t\t\tbody.certificateLabel = certificateLabel;\n\n\t\t\t\t\t\t\tlet keystorePassphrase = '';\n\n\t\t\t\t\t\t\tif (exportFormat === 'JKS') {\n\t\t\t\t\t\t\t\tkeystorePassphrase = this.getNodeParameter('keystorePassphrase', i) as string;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tconst encryptedValues = (await encryptPassphrase.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\tcertificateId,\n\t\t\t\t\t\t\t\tprivateKeyPassphrase,\n\t\t\t\t\t\t\t\tkeystorePassphrase,\n\t\t\t\t\t\t\t)) as string;\n\t\t\t\t\t\t\tbody.encryptedPrivateKeyPassphrase = encryptedValues[0];\n\t\t\t\t\t\t\tif (exportFormat === 'JKS') {\n\t\t\t\t\t\t\t\tbody.encryptedKeystorePassphrase = encryptedValues[1];\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tresponseData = await venafiApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t\t`/outagedetection/v1/certificates/${certificateId}/keystore`,\n\t\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t{ encoding: null, json: false, resolveWithFullResponse: true },\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst contentDisposition: string = responseData.headers['content-disposition'];\n\t\t\t\t\t\tconst fileNameRegex = /(?<=filename=\").*\\b/;\n\t\t\t\t\t\tconst match = fileNameRegex.exec(contentDisposition);\n\t\t\t\t\t\tlet fileName = '';\n\n\t\t\t\t\t\tif (match !== null) {\n\t\t\t\t\t\t\tfileName = match[0];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst binaryData = await this.helpers.prepareBinaryData(\n\t\t\t\t\t\t\tBuffer.from(responseData.body as Buffer),\n\t\t\t\t\t\t\tfileName,\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tresponseData = {\n\t\t\t\t\t\t\tjson: {},\n\t\t\t\t\t\t\tbinary: {\n\t\t\t\t\t\t\t\t[binaryProperty]: binaryData,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\t//https://api.venafi.cloud/webjars/swagger-ui/index.html?configUrl=%2Fv3%2Fapi-docs%2Fswagger-config&urls.primaryName=outagedetection-service#/%2Fv1/certificates_getById\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tconst certificateId = this.getNodeParameter('certificateId', i) as string;\n\n\t\t\t\t\t\tresponseData = await venafiApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t`/outagedetection/v1/certificates/${certificateId}`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\t//https://api.venafi.cloud/webjars/swagger-ui/index.html?configUrl=%2Fv3%2Fapi-docs%2Fswagger-config&urls.primaryName=outagedetection-service#/%2Fv1/certificates_getAllAsCsv\n\t\t\t\t\tif (operation === 'getMany') {\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tconst filters = this.getNodeParameter('filters', i);\n\n\t\t\t\t\t\tObject.assign(qs, filters);\n\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await venafiApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'certificates',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/outagedetection/v1/certificates',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tqs.limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = await venafiApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/outagedetection/v1/certificates',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\n\t\t\t\t\t\t\tresponseData = responseData.certificates;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t//https://docs.venafi.cloud/api/t-cloud-api-renew-cert/\n\t\t\t\t\tif (operation === 'renew') {\n\t\t\t\t\t\tconst applicationId = this.getNodeParameter('applicationId', i) as string;\n\t\t\t\t\t\tconst certificateIssuingTemplateId = this.getNodeParameter(\n\t\t\t\t\t\t\t'certificateIssuingTemplateId',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t) as string;\n\t\t\t\t\t\tconst certificateSigningRequest = this.getNodeParameter(\n\t\t\t\t\t\t\t'certificateSigningRequest',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t) as string;\n\t\t\t\t\t\tconst existingCertificateId = this.getNodeParameter(\n\t\t\t\t\t\t\t'existingCertificateId',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tcertificateSigningRequest,\n\t\t\t\t\t\t\tcertificateIssuingTemplateId,\n\t\t\t\t\t\t\tapplicationId,\n\t\t\t\t\t\t\texistingCertificateId,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tObject.assign(body, options);\n\n\t\t\t\t\t\tresponseData = await venafiApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/outagedetection/v1/certificaterequests',\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tresponseData = responseData.certificateRequests;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturnData.push(\n\t\t\t\t\t...this.helpers.constructExecutionMetaData(\n\t\t\t\t\t\tthis.helpers.returnJsonArray(responseData as IDataObject[]),\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\titemData: { item: i },\n\t\t\t\t\t\t},\n\t\t\t\t\t),\n\t\t\t\t);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ json: { error: error.message } });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\n\t\treturn [returnData as INodeExecutionData[]];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BASO;AAEP,oCAAyD;AAQzD,2CAGO;AACP,8BAA8E;AAEvE,MAAM,sBAA2C;AAAA,EAAjD;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,OAAO;AAAA,MACf,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA,QACZ,MAAM,kBAA8E;AACnF,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,EAAE,aAAa,IAAI,MAAM,yCAAiB;AAAA,YAC/C;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAEA,qBAAW,eAAe,cAAc;AACvC,uBAAW,KAAK;AAAA,cACf,MAAM,YAAY;AAAA,cAClB,OAAO,YAAY;AAAA,YACpB,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,iCAE6B;AAClC,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,qBAA6B,KAAK,wBAAwB,eAAe;AAE/E,gBAAM,EAAE,qCAAqC,IAAK,MAAM,yCAAiB;AAAA,YACxE;AAAA,YACA;AAAA,YACA,oCAAoC,kBAAkB;AAAA,UACvD;AAEA,qBAAW,CAAC,cAAc,UAAU,KAAK,OAAO;AAAA,YAC/C;AAAA,UACD,GAAG;AACF,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAA4B,CAAC;AACnC,UAAM,SAAS,MAAM;AACrB,UAAM,KAAkB,CAAC;AACzB,QAAI;AACJ,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,YAAI,aAAa,sBAAsB;AAEtC,cAAI,cAAc,UAAU;AAC3B,kBAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,CAAC;AAC9D,kBAAM,+BAA+B,KAAK;AAAA,cACzC;AAAA,cACA;AAAA,YACD;AACA,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAE1D,kBAAM,OAA2B;AAAA,cAChC;AAAA,cACA;AAAA,YACD;AAEA,gBAAI,aAAa;AAChB,oBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AACxD,oBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,oBAAM,iBAAqC,CAAC;AAC5C,oBAAM,gBAAgC,CAAC;AACvC,oBAAM,wBAAgD,CAAC;AAEvD,mBAAK,kBAAkB;AAEvB,4BAAc,aAAa;AAG3B,kBAAI,iBAAiB,cAAc;AAClC,8BAAc,eAAe,iBAAiB;AAAA,cAC/C;AACA,kBAAI,iBAAiB,qBAAqB;AACzC,8BAAc,sBACb,iBAAiB;AAAA,cACnB;AACA,kBAAI,iBAAiB,UAAU;AAC9B,8BAAc,WAAW,iBAAiB;AAAA,cAC3C;AACA,kBAAI,iBAAiB,OAAO;AAC3B,8BAAc,QAAQ,iBAAiB;AAAA,cACxC;AACA,kBAAI,iBAAiB,SAAS;AAC7B,8BAAc,UAAU,iBAAiB;AAAA,cAC1C;AACA,mBAAK,gBAAgB;AAGrB,kBAAI,iBAAiB,SAAS;AAC7B,+BAAe,UAAU,iBAAiB;AAAA,cAC3C;AACA,kBAAI,iBAAiB,UAAU;AAC9B,+BAAe,WAAW,iBAAiB;AAAA,cAC5C;AACA,kBAAI,iBAAiB,WAAW;AAC/B,+BAAe,YAAY,iBAAiB;AAAA,cAC7C;AACA,kBAAI,OAAO,KAAK,cAAc,EAAE,WAAW,GAAG;AAC7C,qBAAK,cAAc,oBAAoB;AAAA,cACxC;AAGA,kBAAI,iBAAiB,mBAAmB;AACvC,2BAAW,OAAQ,iBAAiB,kBAClC,uBAAwC;AACzC,sBAAI,IAAI,aAAa,YAAY;AAChC,0CAAsB,WACnB,sBAAsB,SAAS,KAAK,IAAI,IAAc,IACrD,sBAAsB,WAAW,CAAC,IAAI,IAAc;AAAA,kBACzD;AAAA,gBAUD;AAAA,cACD;AACA,kBAAI,OAAO,KAAK,qBAAqB,EAAE,WAAW,GAAG;AACpD,qBAAK,cAAc,gCAAgC;AAAA,cACpD;AAAA,YACD,OAAO;AACN,oBAAM,4BAA4B,KAAK;AAAA,gBACtC;AAAA,gBACA;AAAA,cACD;AACA,mBAAK,kBAAkB;AACvB,mBAAK,4BAA4B;AAAA,YAClC;AAEA,mBAAO,OAAO,MAAM,OAAO;AAE3B,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAEA,2BAAe,aAAa;AAAA,UAC7B;AAGA,cAAI,cAAc,OAAO;AACxB,kBAAM,gBAAgB,KAAK,iBAAiB,wBAAwB,CAAC;AAErE,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA,2CAA2C,aAAa;AAAA,cACxD,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAGA,cAAI,cAAc,WAAW;AAC5B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,gBAAI,WAAW;AACd,6BAAe,MAAM,iDAAyB;AAAA,gBAC7C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,oBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,6BAAe,MAAM,yCAAiB;AAAA,gBACrC;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAEA,6BAAe,aAAa,oBAAoB,OAAO,GAAG,KAAK;AAAA,YAChE;AAAA,UACD;AAAA,QACD;AAEA,YAAI,aAAa,eAAe;AAE/B,cAAI,cAAc,UAAU;AAC3B,kBAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,CAAC;AAE9D,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA,EAAE,gBAAgB,CAAC,aAAa,EAAE;AAAA,YACnC;AAEA,2BAAe,aAAa;AAAA,UAC7B;AAGA,cAAI,cAAc,YAAY;AAC7B,kBAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,CAAC;AAC9D,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAGlD,gBAAI,iBAAiB,eAAe;AACnC,qBAAO,OAAO,IAAI,OAAO;AACzB,6BAAe,MAAM,yCAAiB;AAAA,gBACrC;AAAA,gBACA;AAAA,gBACA,oCAAoC,aAAa;AAAA,gBACjD,CAAC;AAAA,gBACD;AAAA,gBACA,EAAE,UAAU,MAAM,MAAM,OAAO,yBAAyB,MAAM,MAAM,KAAK;AAAA,cAC1E;AAAA,YACD,OAAO;AACN,oBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,oBAAM,OAAmC;AAAA,gBACxC;AAAA,cACD;AAEA,oBAAM,uBAAuB,KAAK;AAAA,gBACjC;AAAA,gBACA;AAAA,cACD;AACA,oBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,mBAAK,mBAAmB;AAExB,kBAAI,qBAAqB;AAEzB,kBAAI,iBAAiB,OAAO;AAC3B,qCAAqB,KAAK,iBAAiB,sBAAsB,CAAC;AAAA,cACnE;AAEA,oBAAM,kBAAmB,MAAM,0CAAkB;AAAA,gBAChD;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACD;AACA,mBAAK,gCAAgC,gBAAgB,CAAC;AACtD,kBAAI,iBAAiB,OAAO;AAC3B,qBAAK,8BAA8B,gBAAgB,CAAC;AAAA,cACrD;AAEA,6BAAe,MAAM,yCAAiB;AAAA,gBACrC;AAAA,gBACA;AAAA,gBACA,oCAAoC,aAAa;AAAA,gBACjD;AAAA,gBACA,CAAC;AAAA,gBACD,EAAE,UAAU,MAAM,MAAM,OAAO,yBAAyB,KAAK;AAAA,cAC9D;AAAA,YACD;AAEA,kBAAM,qBAA6B,aAAa,QAAQ,qBAAqB;AAC7E,kBAAM,gBAAgB;AACtB,kBAAM,QAAQ,cAAc,KAAK,kBAAkB;AACnD,gBAAI,WAAW;AAEf,gBAAI,UAAU,MAAM;AACnB,yBAAW,MAAM,CAAC;AAAA,YACnB;AAEA,kBAAM,aAAa,MAAM,KAAK,QAAQ;AAAA,cACrC,OAAO,KAAK,aAAa,IAAc;AAAA,cACvC;AAAA,YACD;AAEA,2BAAe;AAAA,cACd,MAAM,CAAC;AAAA,cACP,QAAQ;AAAA,gBACP,CAAC,cAAc,GAAG;AAAA,cACnB;AAAA,YACD;AAAA,UACD;AAGA,cAAI,cAAc,OAAO;AACxB,kBAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,CAAC;AAE9D,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA,oCAAoC,aAAa;AAAA,cACjD,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAGA,cAAI,cAAc,WAAW;AAC5B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,mBAAO,OAAO,IAAI,OAAO;AAEzB,gBAAI,WAAW;AACd,6BAAe,MAAM,iDAAyB;AAAA,gBAC7C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,iBAAG,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC3C,6BAAe,MAAM,yCAAiB;AAAA,gBACrC;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAEA,6BAAe,aAAa;AAAA,YAC7B;AAAA,UACD;AAGA,cAAI,cAAc,SAAS;AAC1B,kBAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,CAAC;AAC9D,kBAAM,+BAA+B,KAAK;AAAA,cACzC;AAAA,cACA;AAAA,YACD;AACA,kBAAM,4BAA4B,KAAK;AAAA,cACtC;AAAA,cACA;AAAA,YACD;AACA,kBAAM,wBAAwB,KAAK;AAAA,cAClC;AAAA,cACA;AAAA,YACD;AACA,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,kBAAM,OAAoB;AAAA,cACzB;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAEA,mBAAO,OAAO,MAAM,OAAO;AAE3B,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAEA,2BAAe,aAAa;AAAA,UAC7B;AAAA,QACD;AAEA,mBAAW;AAAA,UACV,GAAG,KAAK,QAAQ;AAAA,YACf,KAAK,QAAQ,gBAAgB,YAA6B;AAAA,YAC1D;AAAA,cACC,UAAU,EAAE,MAAM,EAAE;AAAA,YACrB;AAAA,UACD;AAAA,QACD;AAAA,MACD,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,MAAM,EAAE,OAAO,MAAM,QAAQ,EAAE,CAAC;AAClD;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AAEA,WAAO,CAAC,UAAkC;AAAA,EAC3C;AACD;", "names": []}