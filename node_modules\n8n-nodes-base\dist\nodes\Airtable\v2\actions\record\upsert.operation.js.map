{"version": 3, "sources": ["../../../../../../nodes/Airtable/v2/actions/record/upsert.operation.ts"], "sourcesContent": ["import type {\n\tIDataObject,\n\tINodeExecutionData,\n\tINodeProperties,\n\tIExecuteFunctions,\n\tNodeApiError,\n} from 'n8n-workflow';\n\nimport { updateDisplayOptions, wrapData } from '../../../../../utils/utilities';\nimport type { UpdateRecord } from '../../helpers/interfaces';\nimport { processAirtableError, removeIgnored } from '../../helpers/utils';\nimport { apiRequest, apiRequestAllItems, batchUpdate } from '../../transport';\nimport { insertUpdateOptions } from '../common.descriptions';\n\nconst properties: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Columns',\n\t\tname: 'columns',\n\t\ttype: 'resourceMapper',\n\t\tnoDataExpression: true,\n\t\tdefault: {\n\t\t\tmappingMode: 'defineBelow',\n\t\t\tvalue: null,\n\t\t},\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tloadOptionsDependsOn: ['table.value', 'base.value'],\n\t\t\tresourceMapper: {\n\t\t\t\tresourceMapperMethod: 'getColumnsWithRecordId',\n\t\t\t\tmode: 'update',\n\t\t\t\tfieldWords: {\n\t\t\t\t\tsingular: 'column',\n\t\t\t\t\tplural: 'columns',\n\t\t\t\t},\n\t\t\t\taddAllFields: true,\n\t\t\t\tmultiKeyMatch: true,\n\t\t\t},\n\t\t},\n\t},\n\t...insertUpdateOptions,\n];\n\nconst displayOptions = {\n\tshow: {\n\t\tresource: ['record'],\n\t\toperation: ['upsert'],\n\t},\n};\n\nexport const description = updateDisplayOptions(displayOptions, properties);\n\nexport async function execute(\n\tthis: IExecuteFunctions,\n\titems: INodeExecutionData[],\n\tbase: string,\n\ttable: string,\n): Promise<INodeExecutionData[]> {\n\tconst returnData: INodeExecutionData[] = [];\n\n\tconst endpoint = `${base}/${table}`;\n\n\tconst dataMode = this.getNodeParameter('columns.mappingMode', 0) as string;\n\n\tconst columnsToMatchOn = this.getNodeParameter('columns.matchingColumns', 0) as string[];\n\n\tfor (let i = 0; i < items.length; i++) {\n\t\ttry {\n\t\t\tconst records: UpdateRecord[] = [];\n\t\t\tconst options = this.getNodeParameter('options', i, {});\n\n\t\t\tif (dataMode === 'autoMapInputData') {\n\t\t\t\tif (columnsToMatchOn.includes('id')) {\n\t\t\t\t\tconst { id, ...fields } = items[i].json;\n\n\t\t\t\t\trecords.push({\n\t\t\t\t\t\tid: id as string,\n\t\t\t\t\t\tfields: removeIgnored(fields, options.ignoreFields as string),\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\trecords.push({ fields: removeIgnored(items[i].json, options.ignoreFields as string) });\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (dataMode === 'defineBelow') {\n\t\t\t\tconst fields = this.getNodeParameter('columns.value', i, []) as IDataObject;\n\n\t\t\t\tif (columnsToMatchOn.includes('id')) {\n\t\t\t\t\tconst id = fields.id as string;\n\t\t\t\t\tdelete fields.id;\n\t\t\t\t\trecords.push({ id, fields });\n\t\t\t\t} else {\n\t\t\t\t\trecords.push({ fields });\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst body: IDataObject = {\n\t\t\t\ttypecast: options.typecast ? true : false,\n\t\t\t};\n\n\t\t\tif (!columnsToMatchOn.includes('id')) {\n\t\t\t\tbody.performUpsert = { fieldsToMergeOn: columnsToMatchOn };\n\t\t\t}\n\n\t\t\tlet responseData;\n\t\t\ttry {\n\t\t\t\tresponseData = await batchUpdate.call(this, endpoint, body, records);\n\t\t\t} catch (error) {\n\t\t\t\tif (error.httpCode === '422' && columnsToMatchOn.includes('id')) {\n\t\t\t\t\tconst createBody = {\n\t\t\t\t\t\t...body,\n\t\t\t\t\t\trecords: records.map(({ fields }) => ({ fields })),\n\t\t\t\t\t};\n\t\t\t\t\tresponseData = await apiRequest.call(this, 'POST', endpoint, createBody);\n\t\t\t\t} else if (error?.description?.includes('Cannot update more than one record')) {\n\t\t\t\t\tconst conditions = columnsToMatchOn\n\t\t\t\t\t\t.map((column) => `{${column}} = '${records[0].fields[column]}'`)\n\t\t\t\t\t\t.join(',');\n\t\t\t\t\tconst response = await apiRequestAllItems.call(\n\t\t\t\t\t\tthis,\n\t\t\t\t\t\t'GET',\n\t\t\t\t\t\tendpoint,\n\t\t\t\t\t\t{},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tfields: columnsToMatchOn,\n\t\t\t\t\t\t\tfilterByFormula: `AND(${conditions})`,\n\t\t\t\t\t\t},\n\t\t\t\t\t);\n\t\t\t\t\tconst matches = response.records as UpdateRecord[];\n\n\t\t\t\t\tconst updateRecords: UpdateRecord[] = [];\n\n\t\t\t\t\tif (options.updateAllMatches) {\n\t\t\t\t\t\tupdateRecords.push(...matches.map(({ id }) => ({ id, fields: records[0].fields })));\n\t\t\t\t\t} else {\n\t\t\t\t\t\tupdateRecords.push({ id: matches[0].id, fields: records[0].fields });\n\t\t\t\t\t}\n\n\t\t\t\t\tresponseData = await batchUpdate.call(this, endpoint, body, updateRecords);\n\t\t\t\t} else {\n\t\t\t\t\tthrow error;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\twrapData(responseData.records as IDataObject[]),\n\t\t\t\t{ itemData: { item: i } },\n\t\t\t);\n\n\t\t\treturnData.push(...executionData);\n\t\t} catch (error) {\n\t\t\terror = processAirtableError(error as NodeApiError, undefined, i);\n\t\t\tif (this.continueOnFail()) {\n\t\t\t\treturnData.push({ json: { message: error.message, error } });\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tthrow error;\n\t\t}\n\t}\n\n\treturn returnData;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,uBAA+C;AAE/C,mBAAoD;AACpD,uBAA4D;AAC5D,oBAAoC;AAEpC,MAAM,aAAgC;AAAA,EACrC;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,SAAS;AAAA,MACR,aAAa;AAAA,MACb,OAAO;AAAA,IACR;AAAA,IACA,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,sBAAsB,CAAC,eAAe,YAAY;AAAA,MAClD,gBAAgB;AAAA,QACf,sBAAsB;AAAA,QACtB,MAAM;AAAA,QACN,YAAY;AAAA,UACX,UAAU;AAAA,UACV,QAAQ;AAAA,QACT;AAAA,QACA,cAAc;AAAA,QACd,eAAe;AAAA,MAChB;AAAA,IACD;AAAA,EACD;AAAA,EACA,GAAG;AACJ;AAEA,MAAM,iBAAiB;AAAA,EACtB,MAAM;AAAA,IACL,UAAU,CAAC,QAAQ;AAAA,IACnB,WAAW,CAAC,QAAQ;AAAA,EACrB;AACD;AAEO,MAAM,kBAAc,uCAAqB,gBAAgB,UAAU;AAE1E,eAAsB,QAErB,OACA,MACA,OACgC;AAChC,QAAM,aAAmC,CAAC;AAE1C,QAAM,WAAW,GAAG,IAAI,IAAI,KAAK;AAEjC,QAAM,WAAW,KAAK,iBAAiB,uBAAuB,CAAC;AAE/D,QAAM,mBAAmB,KAAK,iBAAiB,2BAA2B,CAAC;AAE3E,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,QAAI;AACH,YAAM,UAA0B,CAAC;AACjC,YAAM,UAAU,KAAK,iBAAiB,WAAW,GAAG,CAAC,CAAC;AAEtD,UAAI,aAAa,oBAAoB;AACpC,YAAI,iBAAiB,SAAS,IAAI,GAAG;AACpC,gBAAM,EAAE,IAAI,GAAG,OAAO,IAAI,MAAM,CAAC,EAAE;AAEnC,kBAAQ,KAAK;AAAA,YACZ;AAAA,YACA,YAAQ,4BAAc,QAAQ,QAAQ,YAAsB;AAAA,UAC7D,CAAC;AAAA,QACF,OAAO;AACN,kBAAQ,KAAK,EAAE,YAAQ,4BAAc,MAAM,CAAC,EAAE,MAAM,QAAQ,YAAsB,EAAE,CAAC;AAAA,QACtF;AAAA,MACD;AAEA,UAAI,aAAa,eAAe;AAC/B,cAAM,SAAS,KAAK,iBAAiB,iBAAiB,GAAG,CAAC,CAAC;AAE3D,YAAI,iBAAiB,SAAS,IAAI,GAAG;AACpC,gBAAM,KAAK,OAAO;AAClB,iBAAO,OAAO;AACd,kBAAQ,KAAK,EAAE,IAAI,OAAO,CAAC;AAAA,QAC5B,OAAO;AACN,kBAAQ,KAAK,EAAE,OAAO,CAAC;AAAA,QACxB;AAAA,MACD;AAEA,YAAM,OAAoB;AAAA,QACzB,UAAU,QAAQ,WAAW,OAAO;AAAA,MACrC;AAEA,UAAI,CAAC,iBAAiB,SAAS,IAAI,GAAG;AACrC,aAAK,gBAAgB,EAAE,iBAAiB,iBAAiB;AAAA,MAC1D;AAEA,UAAI;AACJ,UAAI;AACH,uBAAe,MAAM,6BAAY,KAAK,MAAM,UAAU,MAAM,OAAO;AAAA,MACpE,SAAS,OAAO;AACf,YAAI,MAAM,aAAa,SAAS,iBAAiB,SAAS,IAAI,GAAG;AAChE,gBAAM,aAAa;AAAA,YAClB,GAAG;AAAA,YACH,SAAS,QAAQ,IAAI,CAAC,EAAE,OAAO,OAAO,EAAE,OAAO,EAAE;AAAA,UAClD;AACA,yBAAe,MAAM,4BAAW,KAAK,MAAM,QAAQ,UAAU,UAAU;AAAA,QACxE,WAAW,OAAO,aAAa,SAAS,oCAAoC,GAAG;AAC9E,gBAAM,aAAa,iBACjB,IAAI,CAAC,WAAW,IAAI,MAAM,QAAQ,QAAQ,CAAC,EAAE,OAAO,MAAM,CAAC,GAAG,EAC9D,KAAK,GAAG;AACV,gBAAM,WAAW,MAAM,oCAAmB;AAAA,YACzC;AAAA,YACA;AAAA,YACA;AAAA,YACA,CAAC;AAAA,YACD;AAAA,cACC,QAAQ;AAAA,cACR,iBAAiB,OAAO,UAAU;AAAA,YACnC;AAAA,UACD;AACA,gBAAM,UAAU,SAAS;AAEzB,gBAAM,gBAAgC,CAAC;AAEvC,cAAI,QAAQ,kBAAkB;AAC7B,0BAAc,KAAK,GAAG,QAAQ,IAAI,CAAC,EAAE,GAAG,OAAO,EAAE,IAAI,QAAQ,QAAQ,CAAC,EAAE,OAAO,EAAE,CAAC;AAAA,UACnF,OAAO;AACN,0BAAc,KAAK,EAAE,IAAI,QAAQ,CAAC,EAAE,IAAI,QAAQ,QAAQ,CAAC,EAAE,OAAO,CAAC;AAAA,UACpE;AAEA,yBAAe,MAAM,6BAAY,KAAK,MAAM,UAAU,MAAM,aAAa;AAAA,QAC1E,OAAO;AACN,gBAAM;AAAA,QACP;AAAA,MACD;AAEA,YAAM,gBAAgB,KAAK,QAAQ;AAAA,YAClC,2BAAS,aAAa,OAAwB;AAAA,QAC9C,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,MACzB;AAEA,iBAAW,KAAK,GAAG,aAAa;AAAA,IACjC,SAAS,OAAO;AACf,kBAAQ,mCAAqB,OAAuB,QAAW,CAAC;AAChE,UAAI,KAAK,eAAe,GAAG;AAC1B,mBAAW,KAAK,EAAE,MAAM,EAAE,SAAS,MAAM,SAAS,MAAM,EAAE,CAAC;AAC3D;AAAA,MACD;AACA,YAAM;AAAA,IACP;AAAA,EACD;AAEA,SAAO;AACR;", "names": []}