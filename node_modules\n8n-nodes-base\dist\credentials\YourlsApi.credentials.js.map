{"version": 3, "sources": ["../../credentials/YourlsApi.credentials.ts"], "sourcesContent": ["import type { ICredentialType, INodeProperties } from 'n8n-workflow';\n\nexport class YourlsApi implements ICredentialType {\n\tname = 'yourlsApi';\n\n\tdisplayName = 'Yourls API';\n\n\tdocumentationUrl = 'yourls';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Signature',\n\t\t\tname: 'signature',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'URL',\n\t\t\tname: 'url',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\tplaceholder: 'http://localhost:8080',\n\t\t},\n\t];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,UAAqC;AAAA,EAA3C;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA;AACD;", "names": []}