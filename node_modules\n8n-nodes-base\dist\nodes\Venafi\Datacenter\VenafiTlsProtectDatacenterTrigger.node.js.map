{"version": 3, "sources": ["../../../../nodes/Venafi/Datacenter/VenafiTlsProtectDatacenterTrigger.node.ts"], "sourcesContent": ["import moment from 'moment-timezone';\nimport {\n\ttype IPollFunctions,\n\ttype IDataObject,\n\ttype INodeExecutionData,\n\ttype INodeType,\n\ttype INodeTypeDescription,\n\tNodeConnectionTypes,\n} from 'n8n-workflow';\n\nimport { venafiApiRequest } from './GenericFunctions';\n\nexport class VenafiTlsProtectDatacenterTrigger implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Venafi TLS Protect Datacenter Trigger',\n\t\tname: 'venafiTlsProtectDatacenterTrigger',\n\t\ticon: 'file:../venafi.svg',\n\t\tgroup: ['trigger'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"triggerOn\"]}}',\n\t\tdescription: 'Starts the workflow when Venafi events occur',\n\t\tdefaults: {\n\t\t\tname: 'Venafi TLS Protect Datacenter​',\n\t\t},\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'venafiTlsProtectDatacenterApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tpolling: true,\n\t\tinputs: [],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Trigger On',\n\t\t\t\tname: 'triggerOn',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Certificate Expired',\n\t\t\t\t\t\tvalue: 'certificateExpired',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\trequired: true,\n\t\t\t\tdefault: 'certificateExpired',\n\t\t\t},\n\t\t],\n\t};\n\n\tasync poll(this: IPollFunctions): Promise<INodeExecutionData[][] | null> {\n\t\tconst webhookData = this.getWorkflowStaticData('node');\n\n\t\tconst qs: IDataObject = {};\n\n\t\tconst now = moment().format();\n\n\t\tqs.ValidToGreater = webhookData.lastTimeChecked || now;\n\n\t\tqs.ValidToLess = now;\n\n\t\tconst { Certificates: certificates } = await venafiApiRequest.call(\n\t\t\tthis,\n\t\t\t'GET',\n\t\t\t'/vedsdk/certificates',\n\t\t\t{},\n\t\t\tqs,\n\t\t);\n\n\t\twebhookData.lastTimeChecked = qs.ValidToLess;\n\n\t\tif (Array.isArray(certificates) && certificates.length !== 0) {\n\t\t\treturn [this.helpers.returnJsonArray(certificates)];\n\t\t}\n\n\t\treturn null;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6BAAmB;AACnB,0BAOO;AAEP,8BAAiC;AAE1B,MAAM,kCAAuD;AAAA,EAA7D;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,SAAS;AAAA,MACjB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,SAAS;AAAA,MACT,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,UAAU;AAAA,UACV,SAAS;AAAA,QACV;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,OAAmE;AACxE,UAAM,cAAc,KAAK,sBAAsB,MAAM;AAErD,UAAM,KAAkB,CAAC;AAEzB,UAAM,UAAM,uBAAAA,SAAO,EAAE,OAAO;AAE5B,OAAG,iBAAiB,YAAY,mBAAmB;AAEnD,OAAG,cAAc;AAEjB,UAAM,EAAE,cAAc,aAAa,IAAI,MAAM,yCAAiB;AAAA,MAC7D;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC;AAAA,MACD;AAAA,IACD;AAEA,gBAAY,kBAAkB,GAAG;AAEjC,QAAI,MAAM,QAAQ,YAAY,KAAK,aAAa,WAAW,GAAG;AAC7D,aAAO,CAAC,KAAK,QAAQ,gBAAgB,YAAY,CAAC;AAAA,IACnD;AAEA,WAAO;AAAA,EACR;AACD;", "names": ["moment"]}