{"version": 3, "sources": ["../../../../../../nodes/Google/Sheet/v2/actions/versionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport type { INodeProperties, INodeTypeDescription } from 'n8n-workflow';\nimport { NodeConnectionTypes } from 'n8n-workflow';\n\nimport * as sheet from './sheet/Sheet.resource';\nimport * as spreadsheet from './spreadsheet/SpreadSheet.resource';\n\nexport const authentication: INodeProperties = {\n\tdisplayName: 'Authentication',\n\tname: 'authentication',\n\ttype: 'options',\n\toptions: [\n\t\t{\n\t\t\tname: 'Service Account',\n\t\t\tvalue: 'serviceAccount',\n\t\t},\n\t\t{\n\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-miscased\n\t\t\tname: 'OAuth2 (recommended)',\n\t\t\tvalue: 'oAuth2',\n\t\t},\n\t],\n\tdefault: 'oAuth2',\n};\n\nexport const versionDescription: INodeTypeDescription = {\n\tdisplayName: 'Google Sheets',\n\tname: 'googleSheets',\n\ticon: 'file:googleSheets.svg',\n\tgroup: ['input', 'output'],\n\tversion: [3, 4, 4.1, 4.2, 4.3, 4.4, 4.5, 4.6],\n\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\tdescription: 'Read, update and write data to Google Sheets',\n\tdefaults: {\n\t\tname: 'Google Sheets',\n\t},\n\tinputs: [NodeConnectionTypes.Main],\n\toutputs: [NodeConnectionTypes.Main],\n\tusableAsTool: true,\n\thints: [\n\t\t{\n\t\t\tmessage:\n\t\t\t\t\"Use the 'Minimise API Calls' option for greater efficiency if your sheet is uniformly formatted without gaps between columns or rows\",\n\t\t\tdisplayCondition:\n\t\t\t\t'={{$parameter[\"operation\"] === \"append\" && !$parameter[\"options\"][\"useAppend\"]}}',\n\t\t\twhenToDisplay: 'beforeExecution',\n\t\t\tlocation: 'outputPane',\n\t\t},\n\t\t{\n\t\t\tmessage: 'No columns found in Google Sheet. All rows will be appended',\n\t\t\tdisplayCondition:\n\t\t\t\t'={{ [\"appendOrUpdate\", \"append\"].includes($parameter[\"operation\"]) && $parameter?.columns?.mappingMode === \"defineBelow\" && !$parameter?.columns?.schema?.length }}',\n\t\t\twhenToDisplay: 'beforeExecution',\n\t\t\tlocation: 'outputPane',\n\t\t},\n\t],\n\tcredentials: [\n\t\t{\n\t\t\tname: 'googleApi',\n\t\t\trequired: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tauthentication: ['serviceAccount'],\n\t\t\t\t},\n\t\t\t},\n\t\t\ttestedBy: 'googleApiCredentialTest',\n\t\t},\n\t\t{\n\t\t\tname: 'googleSheetsOAuth2Api',\n\t\t\trequired: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tauthentication: ['oAuth2'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t],\n\tproperties: [\n\t\tauthentication,\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Document',\n\t\t\t\t\tvalue: 'spreadsheet',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Sheet Within Document',\n\t\t\t\t\tvalue: 'sheet',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'sheet',\n\t\t},\n\t\t...sheet.descriptions,\n\t\t...spreadsheet.descriptions,\n\t],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,0BAAoC;AAEpC,YAAuB;AACvB,kBAA6B;AAEtB,MAAM,iBAAkC;AAAA,EAC9C,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,IACR;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA;AAAA,MAEC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,EACD;AAAA,EACA,SAAS;AACV;AAEO,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,SAAS,QAAQ;AAAA,EACzB,SAAS,CAAC,GAAG,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC5C,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,cAAc;AAAA,EACd,OAAO;AAAA,IACN;AAAA,MACC,SACC;AAAA,MACD,kBACC;AAAA,MACD,eAAe;AAAA,MACf,UAAU;AAAA,IACX;AAAA,IACA;AAAA,MACC,SAAS;AAAA,MACT,kBACC;AAAA,MACD,eAAe;AAAA,MACf,UAAU;AAAA,IACX;AAAA,EACD;AAAA,EACA,aAAa;AAAA,IACZ;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,gBAAgB,CAAC,gBAAgB;AAAA,QAClC;AAAA,MACD;AAAA,MACA,UAAU;AAAA,IACX;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,gBAAgB,CAAC,QAAQ;AAAA,QAC1B;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA,YAAY;AAAA,IACX;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA,GAAG,MAAM;AAAA,IACT,GAAG,YAAY;AAAA,EAChB;AACD;", "names": []}