{"version": 3, "sources": ["../../../../../../nodes/Webflow/V2/actions/Item/update.operation.ts"], "sourcesContent": ["import type {\n\tIDataObject,\n\tINodeExecutionData,\n\tINodeProperties,\n\tIExecuteFunctions,\n} from 'n8n-workflow';\n\nimport { updateDisplayOptions, wrapData } from '../../../../../utils/utilities';\nimport { webflowApiRequest } from '../../../GenericFunctions';\n\nconst properties: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Site Name or ID',\n\t\tname: 'siteId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getSites',\n\t\t},\n\t\tdefault: '',\n\t\tdescription:\n\t\t\t'ID of the site containing the collection whose items to add to. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'Collection Name or ID',\n\t\tname: 'collectionId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getCollections',\n\t\t\tloadOptionsDependsOn: ['siteId'],\n\t\t},\n\t\tdefault: '',\n\t\tdescription:\n\t\t\t'ID of the collection to add an item to. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'Item ID',\n\t\tname: 'itemId',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdescription: 'ID of the item to update',\n\t},\n\t{\n\t\tdisplayName: 'Live',\n\t\tname: 'live',\n\t\ttype: 'boolean',\n\t\trequired: true,\n\t\tdefault: false,\n\t\tdescription: 'Whether the item should be published on the live site',\n\t},\n\t{\n\t\tdisplayName: 'Fields',\n\t\tname: 'fieldsUi',\n\t\tplaceholder: 'Add Field',\n\t\ttype: 'fixedCollection',\n\t\ttypeOptions: {\n\t\t\tmultipleValues: true,\n\t\t},\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Field',\n\t\t\t\tname: 'fieldValues',\n\t\t\t\tvalues: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Field Name or ID',\n\t\t\t\t\t\tname: 'fieldId',\n\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\tloadOptionsMethod: 'getFields',\n\t\t\t\t\t\t\tloadOptionsDependsOn: ['collectionId'],\n\t\t\t\t\t\t},\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Field to set for the item to create. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Field Value',\n\t\t\t\t\t\tname: 'fieldValue',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription: 'Value to set for the item to create',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n];\n\nconst displayOptions = {\n\tshow: {\n\t\tresource: ['item'],\n\t\toperation: ['update'],\n\t},\n};\n\nexport const description = updateDisplayOptions(displayOptions, properties);\n\nexport async function execute(\n\tthis: IExecuteFunctions,\n\titems: INodeExecutionData[],\n): Promise<INodeExecutionData[]> {\n\tconst returnData: INodeExecutionData[] = [];\n\tlet responseData;\n\tfor (let i = 0; i < items.length; i++) {\n\t\ttry {\n\t\t\tconst collectionId = this.getNodeParameter('collectionId', i) as string;\n\t\t\tconst itemId = this.getNodeParameter('itemId', i) as string;\n\n\t\t\tconst uiFields = this.getNodeParameter('fieldsUi.fieldValues', i, []) as IDataObject[];\n\n\t\t\tconst live = this.getNodeParameter('live', i) as boolean;\n\n\t\t\tconst fieldData = {} as IDataObject;\n\n\t\t\tuiFields.forEach((data) => (fieldData[data.fieldId as string] = data.fieldValue));\n\n\t\t\tconst body: IDataObject = {\n\t\t\t\tfieldData,\n\t\t\t};\n\n\t\t\tresponseData = await webflowApiRequest.call(\n\t\t\t\tthis,\n\t\t\t\t'PATCH',\n\t\t\t\t`/collections/${collectionId}/items/${itemId}${live ? '/live' : ''}`,\n\t\t\t\tbody,\n\t\t\t);\n\n\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\twrapData(responseData.body as IDataObject[]),\n\t\t\t\t{ itemData: { item: i } },\n\t\t\t);\n\n\t\t\treturnData.push(...executionData);\n\t\t} catch (error) {\n\t\t\tif (this.continueOnFail()) {\n\t\t\t\treturnData.push({ json: { message: error.message, error } });\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tthrow error;\n\t\t}\n\t}\n\n\treturn returnData;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,uBAA+C;AAC/C,8BAAkC;AAElC,MAAM,aAAgC;AAAA,EACrC;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,IACT,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,MACnB,sBAAsB,CAAC,QAAQ;AAAA,IAChC;AAAA,IACA,SAAS;AAAA,IACT,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,gBAAgB;AAAA,IACjB;AAAA,IACA,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,QAAQ;AAAA,UACP;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,aAAa;AAAA,cACZ,mBAAmB;AAAA,cACnB,sBAAsB,CAAC,cAAc;AAAA,YACtC;AAAA,YACA,SAAS;AAAA,YACT,aACC;AAAA,UACF;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,aAAa;AAAA,UACd;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AAEA,MAAM,iBAAiB;AAAA,EACtB,MAAM;AAAA,IACL,UAAU,CAAC,MAAM;AAAA,IACjB,WAAW,CAAC,QAAQ;AAAA,EACrB;AACD;AAEO,MAAM,kBAAc,uCAAqB,gBAAgB,UAAU;AAE1E,eAAsB,QAErB,OACgC;AAChC,QAAM,aAAmC,CAAC;AAC1C,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,QAAI;AACH,YAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,YAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,YAAM,WAAW,KAAK,iBAAiB,wBAAwB,GAAG,CAAC,CAAC;AAEpE,YAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAE5C,YAAM,YAAY,CAAC;AAEnB,eAAS,QAAQ,CAAC,SAAU,UAAU,KAAK,OAAiB,IAAI,KAAK,UAAW;AAEhF,YAAM,OAAoB;AAAA,QACzB;AAAA,MACD;AAEA,qBAAe,MAAM,0CAAkB;AAAA,QACtC;AAAA,QACA;AAAA,QACA,gBAAgB,YAAY,UAAU,MAAM,GAAG,OAAO,UAAU,EAAE;AAAA,QAClE;AAAA,MACD;AAEA,YAAM,gBAAgB,KAAK,QAAQ;AAAA,YAClC,2BAAS,aAAa,IAAqB;AAAA,QAC3C,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,MACzB;AAEA,iBAAW,KAAK,GAAG,aAAa;AAAA,IACjC,SAAS,OAAO;AACf,UAAI,KAAK,eAAe,GAAG;AAC1B,mBAAW,KAAK,EAAE,MAAM,EAAE,SAAS,MAAM,SAAS,MAAM,EAAE,CAAC;AAC3D;AAAA,MACD;AACA,YAAM;AAAA,IACP;AAAA,EACD;AAEA,SAAO;AACR;", "names": []}