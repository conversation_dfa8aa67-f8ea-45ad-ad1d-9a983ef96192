{"version": 3, "sources": ["../../../../../../nodes/Google/Analytics/v2/actions/versionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport { NodeConnectionTypes, type INodeTypeDescription } from 'n8n-workflow';\n\nimport * as report from './report/Report.resource';\nimport * as userActivity from './userActivity/UserActivity.resource';\n\nexport const versionDescription: INodeTypeDescription = {\n\tdisplayName: 'Google Analytics',\n\tname: 'googleAnalytics',\n\ticon: 'file:analytics.svg',\n\tgroup: ['transform'],\n\tversion: 2,\n\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\tdescription: 'Use the Google Analytics API',\n\tdefaults: {\n\t\tname: 'Google Analytics',\n\t},\n\tinputs: [NodeConnectionTypes.Main],\n\toutputs: [NodeConnectionTypes.Main],\n\tcredentials: [\n\t\t{\n\t\t\tname: 'googleAnalyticsOAuth2',\n\t\t\trequired: true,\n\t\t},\n\t],\n\tproperties: [\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Report',\n\t\t\t\t\tvalue: 'report',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'User Activity',\n\t\t\t\t\tvalue: 'userActivity',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'report',\n\t\t},\n\t\t...report.description,\n\t\t...userActivity.description,\n\t],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAA+D;AAE/D,aAAwB;AACxB,mBAA8B;AAEvB,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,WAAW;AAAA,EACnB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,aAAa;AAAA,IACZ;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA,EACD;AAAA,EACA,YAAY;AAAA,IACX;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA,GAAG,OAAO;AAAA,IACV,GAAG,aAAa;AAAA,EACjB;AACD;", "names": []}