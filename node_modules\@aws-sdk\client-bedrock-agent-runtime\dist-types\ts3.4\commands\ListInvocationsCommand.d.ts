import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  BedrockAgentRuntimeClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../BedrockAgentRuntimeClient";
import {
  ListInvocationsRequest,
  ListInvocationsResponse,
} from "../models/models_1";
export { __MetadataBearer };
export { $Command };
export interface ListInvocationsCommandInput extends ListInvocationsRequest {}
export interface ListInvocationsCommandOutput
  extends ListInvocationsResponse,
    __MetadataBearer {}
declare const ListInvocationsCommand_base: {
  new (
    input: ListInvocationsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListInvocationsCommandInput,
    ListInvocationsCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListInvocationsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListInvocationsCommandInput,
    ListInvocationsCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListInvocationsCommand extends ListInvocationsCommand_base {
  protected static __types: {
    api: {
      input: ListInvocationsRequest;
      output: ListInvocationsResponse;
    };
    sdk: {
      input: ListInvocationsCommandInput;
      output: ListInvocationsCommandOutput;
    };
  };
}
