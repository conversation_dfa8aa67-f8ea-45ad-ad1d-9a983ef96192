{"version": 3, "sources": ["../../../../nodes/Slack/V1/UserDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const userOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Info',\n\t\t\t\tvalue: 'info',\n\t\t\t\tdescription: 'Get information about a user',\n\t\t\t\taction: 'Get information about a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Get a list of many users',\n\t\t\t\taction: 'Get many users',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Presence',\n\t\t\t\tvalue: 'getPresence',\n\t\t\t\tdescription: 'Get online status of a user',\n\t\t\t\taction: \"Get a user's presence status\",\n\t\t\t},\n\t\t],\n\t\tdefault: 'info',\n\t},\n];\n\nexport const userFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                                user:info                                   */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'User ID',\n\t\tname: 'user',\n\t\ttype: 'string',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getUsers',\n\t\t},\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['info'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\trequired: true,\n\t\tdescription: 'The ID of the user to get information about',\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 user:getAll                                */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 100,\n\t\t},\n\t\tdefault: 50,\n\t\tdescription: 'Max number of results to return',\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                user:getPresence                            */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'User ID',\n\t\tname: 'user',\n\t\ttype: 'string',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getUsers',\n\t\t},\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getPresence'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\trequired: true,\n\t\tdescription: 'The ID of the user to get the online status of',\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,iBAAoC;AAAA,EAChD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,aAAgC;AAAA;AAAA;AAAA;AAAA,EAI5C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,MAAM;AAAA,QAClB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,UAAU;AAAA,IACV,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,QACpB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,aAAa;AAAA,QACzB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,UAAU;AAAA,IACV,aAAa;AAAA,EACd;AACD;", "names": []}