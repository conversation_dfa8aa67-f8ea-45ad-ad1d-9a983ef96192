{"version": 3, "sources": ["../../../nodes/Cloudflare/ZoneCertificateDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const zoneCertificateOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['zoneCertificate'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'delete',\n\t\t\t\tdescription: 'Delete a certificate',\n\t\t\t\taction: 'Delete a certificate',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\tdescription: 'Get a certificate',\n\t\t\t\taction: 'Get a certificate',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getMany',\n\t\t\t\tdescription: 'Get many certificates',\n\t\t\t\taction: 'Get many certificates',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Upload',\n\t\t\t\tvalue: 'upload',\n\t\t\t\tdescription: 'Upload a certificate',\n\t\t\t\taction: 'Upload a certificate',\n\t\t\t},\n\t\t],\n\t\tdefault: 'upload',\n\t},\n];\n\nexport const zoneCertificateFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                          certificate:upload                                */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Zone Name or ID',\n\t\tname: 'zoneId',\n\t\ttype: 'options',\n\t\tdescription:\n\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getZones',\n\t\t},\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['zoneCertificate'],\n\t\t\t\toperation: ['upload', 'getMany', 'get', 'delete'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Certificate Content',\n\t\tname: 'certificate',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['zoneCertificate'],\n\t\t\t\toperation: ['upload'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription: \"The zone's leaf certificate\",\n\t},\n\t{\n\t\tdisplayName: 'Private Key',\n\t\tname: 'privateKey',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['zoneCertificate'],\n\t\t\t\toperation: ['upload'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                          certificate:getMany                               */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t\tdefault: false,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['zoneCertificate'],\n\t\t\t\toperation: ['getMany'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdefault: 25,\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 50,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['zoneCertificate'],\n\t\t\t\toperation: ['getMany'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\tdescription: 'Max number of results to return',\n\t},\n\t{\n\t\tdisplayName: 'Filters',\n\t\tname: 'filters',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['zoneCertificate'],\n\t\t\t\toperation: ['getMany'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Status',\n\t\t\t\tname: 'status',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Active',\n\t\t\t\t\t\tvalue: 'active',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Expired',\n\t\t\t\t\t\tvalue: 'expired',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Deleted',\n\t\t\t\t\t\tvalue: 'deleted',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Pending',\n\t\t\t\t\t\tvalue: 'pending',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"Status of the zone's custom SSL\",\n\t\t\t},\n\t\t],\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                          certificate:get                                   */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Certificate ID',\n\t\tname: 'certificateId',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['zoneCertificate'],\n\t\t\t\toperation: ['get', 'delete'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,4BAA+C;AAAA,EAC3D;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,iBAAiB;AAAA,MAC7B;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,wBAA2C;AAAA;AAAA;AAAA;AAAA,EAIvD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aACC;AAAA,IACD,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,iBAAiB;AAAA,QAC5B,WAAW,CAAC,UAAU,WAAW,OAAO,QAAQ;AAAA,MACjD;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,iBAAiB;AAAA,QAC5B,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,iBAAiB;AAAA,QAC5B,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,iBAAiB;AAAA,QAC5B,WAAW,CAAC,SAAS;AAAA,MACtB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,iBAAiB;AAAA,QAC5B,WAAW,CAAC,SAAS;AAAA,QACrB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,iBAAiB;AAAA,QAC5B,WAAW,CAAC,SAAS;AAAA,MACtB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,iBAAiB;AAAA,QAC5B,WAAW,CAAC,OAAO,QAAQ;AAAA,MAC5B;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;", "names": []}