{"version": 3, "sources": ["../../../../../../../nodes/Microsoft/Excel/v2/actions/workbook/Workbook.resource.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nimport * as addWorksheet from './addWorksheet.operation';\nimport * as deleteWorkbook from './deleteWorkbook.operation';\nimport * as getAll from './getAll.operation';\n\nexport { addWorksheet, deleteWorkbook, getAll };\n\nexport const description: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workbook'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Add Sheet',\n\t\t\t\tvalue: 'addWorksheet',\n\t\t\t\tdescription: 'Add a new sheet to the workbook',\n\t\t\t\taction: 'Add a sheet to a workbook',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'deleteWorkbook',\n\t\t\t\tdescription: 'Delete workbook',\n\t\t\t\taction: 'Delete workbook',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Get workbooks',\n\t\t\t\taction: 'Get workbooks',\n\t\t\t},\n\t\t],\n\t\tdefault: 'getAll',\n\t},\n\t...addWorksheet.description,\n\t...deleteWorkbook.description,\n\t...getAll.description,\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,mBAA8B;AAC9B,qBAAgC;AAChC,aAAwB;AAIjB,MAAM,cAAiC;AAAA,EAC7C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,MACtB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA,GAAG,aAAa;AAAA,EAChB,GAAG,eAAe;AAAA,EAClB,GAAG,OAAO;AACX;", "names": []}