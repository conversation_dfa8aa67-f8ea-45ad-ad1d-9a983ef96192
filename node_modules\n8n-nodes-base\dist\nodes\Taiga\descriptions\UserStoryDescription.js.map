{"version": 3, "sources": ["../../../../nodes/Taiga/descriptions/UserStoryDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const userStoryOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userStory'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Create',\n\t\t\t\tvalue: 'create',\n\t\t\t\tdescription: 'Create a user story',\n\t\t\t\taction: 'Create a user story',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'delete',\n\t\t\t\tdescription: 'Delete a user story',\n\t\t\t\taction: 'Delete a user story',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\tdescription: 'Get a user story',\n\t\t\t\taction: 'Get a user story',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Get many user stories',\n\t\t\t\taction: 'Get many user stories',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Update',\n\t\t\t\tvalue: 'update',\n\t\t\t\tdescription: 'Update a user story',\n\t\t\t\taction: 'Update a user story',\n\t\t\t},\n\t\t],\n\t\tdefault: 'create',\n\t},\n];\n\nexport const userStoryFields: INodeProperties[] = [\n\t// ----------------------------------------\n\t//            userStory: create\n\t// ----------------------------------------\n\t{\n\t\tdisplayName: 'Project Name or ID',\n\t\tname: 'projectId',\n\t\tdescription:\n\t\t\t'ID of the project to which the user story belongs. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\ttype: 'options',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getProjects',\n\t\t},\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userStory'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Subject',\n\t\tname: 'subject',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userStory'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userStory'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Assignee Name or ID',\n\t\t\t\tname: 'assigned_to',\n\t\t\t\ttype: 'options',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsDependsOn: ['projectId'],\n\t\t\t\t\tloadOptionsMethod: 'getUsers',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'ID of the user to whom the user story is assigned. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Backlog Order',\n\t\t\t\tname: 'backlog_order',\n\t\t\t\ttype: 'number',\n\t\t\t\tdefault: 1,\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tminValue: 1,\n\t\t\t\t},\n\t\t\t\tdescription: 'Order of the user story in the backlog',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Blocked Note',\n\t\t\t\tname: 'blocked_note',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'Reason why the user story is blocked. Requires \"Is Blocked\" toggle to be enabled.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Description',\n\t\t\t\tname: 'description',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Is Blocked',\n\t\t\t\tname: 'is_blocked',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user story is blocked',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Kanban Order',\n\t\t\t\tname: 'kanban_order',\n\t\t\t\ttype: 'number',\n\t\t\t\tdefault: 1,\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tminValue: 1,\n\t\t\t\t},\n\t\t\t\tdescription: 'Order of the user story in the kanban',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Milestone (Sprint) Name or ID',\n\t\t\t\tname: 'milestone',\n\t\t\t\ttype: 'options',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsDependsOn: ['projectId'],\n\t\t\t\t\tloadOptionsMethod: 'getMilestones',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'ID of the milestone of the user story. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Sprint Order',\n\t\t\t\tname: 'sprint_order',\n\t\t\t\ttype: 'number',\n\t\t\t\tdefault: 1,\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tminValue: 1,\n\t\t\t\t},\n\t\t\t\tdescription: 'Order of the user story in the milestone',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Status Name or ID',\n\t\t\t\tname: 'status',\n\t\t\t\ttype: 'options',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsDependsOn: ['projectId'],\n\t\t\t\t\tloadOptionsMethod: 'getUserStoryStatuses',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'ID of the status of the user story. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Tag Names or IDs',\n\t\t\t\tname: 'tags',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\tdescription:\n\t\t\t\t\t'Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsDependsOn: ['projectId'],\n\t\t\t\t\tloadOptionsMethod: 'getTags',\n\t\t\t\t},\n\t\t\t\tdefault: [],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Type Name or ID',\n\t\t\t\tname: 'type',\n\t\t\t\ttype: 'options',\n\t\t\t\tdescription:\n\t\t\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsDependsOn: ['projectId'],\n\t\t\t\t\tloadOptionsMethod: 'getTypes',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t],\n\t},\n\n\t// ----------------------------------------\n\t//            userStory: delete\n\t// ----------------------------------------\n\t{\n\t\tdisplayName: 'User Story ID',\n\t\tname: 'userStoryId',\n\t\tdescription: 'ID of the user story to delete',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userStory'],\n\t\t\t\toperation: ['delete'],\n\t\t\t},\n\t\t},\n\t},\n\n\t// ----------------------------------------\n\t//              userStory: get\n\t// ----------------------------------------\n\t{\n\t\tdisplayName: 'User Story ID',\n\t\tname: 'userStoryId',\n\t\tdescription: 'ID of the user story to retrieve',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userStory'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t},\n\n\t// ----------------------------------------\n\t//            userStory: getAll\n\t// ----------------------------------------\n\t{\n\t\tdisplayName: 'Project Name or ID',\n\t\tname: 'projectId',\n\t\tdescription:\n\t\t\t'ID of the project to which the user story belongs. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\ttype: 'options',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getProjects',\n\t\t},\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userStory'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userStory'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdefault: 50,\n\t\tdescription: 'Max number of results to return',\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userStory'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Filters',\n\t\tname: 'filters',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Filter',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userStory'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Assignee Name or ID',\n\t\t\t\tname: 'assigned_to',\n\t\t\t\tdescription:\n\t\t\t\t\t'ID of the user whom the user story is assigned to. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\ttype: 'options',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsDependsOn: ['projectId'],\n\t\t\t\t\tloadOptionsMethod: 'getUsers',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Epic Name or ID',\n\t\t\t\tname: 'epic',\n\t\t\t\tdescription:\n\t\t\t\t\t'ID of the epic to which the user story belongs. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\ttype: 'options',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsDependsOn: ['projectId'],\n\t\t\t\t\tloadOptionsMethod: 'getEpics',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Is Closed',\n\t\t\t\tname: 'statusIsClosed',\n\t\t\t\tdescription: 'Whether the user story is closed',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Is Archived',\n\t\t\t\tname: 'statusIsArchived',\n\t\t\t\tdescription: 'Whether the user story has been archived',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Milestone (Sprint) Name or ID',\n\t\t\t\tname: 'milestone',\n\t\t\t\ttype: 'options',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsDependsOn: ['projectId'],\n\t\t\t\t\tloadOptionsMethod: 'getMilestones',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'ID of the milestone of the user story. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Role Name or ID',\n\t\t\t\tname: 'role',\n\t\t\t\ttype: 'options',\n\t\t\t\tdescription:\n\t\t\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsDependsOn: ['projectId'],\n\t\t\t\t\tloadOptionsMethod: 'getRoles',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Status Name or ID',\n\t\t\t\tname: 'status',\n\t\t\t\tdescription:\n\t\t\t\t\t'ID of the status of the user story. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\ttype: 'options',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsDependsOn: ['projectId'],\n\t\t\t\t\tloadOptionsMethod: 'getUserStoryStatuses',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Tag Names or IDs',\n\t\t\t\tname: 'tags',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\tdescription:\n\t\t\t\t\t'Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsDependsOn: ['projectId'],\n\t\t\t\t\tloadOptionsMethod: 'getTags',\n\t\t\t\t},\n\t\t\t\tdefault: [],\n\t\t\t},\n\t\t],\n\t},\n\n\t// ----------------------------------------\n\t//            userStory: update\n\t// ----------------------------------------\n\t{\n\t\tdisplayName: 'Project Name or ID',\n\t\tname: 'projectId',\n\t\ttype: 'options',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getProjects',\n\t\t},\n\t\tdefault: '',\n\t\tdescription:\n\t\t\t'ID of the project to set the user story to. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userStory'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'User Story ID',\n\t\tname: 'userStoryId',\n\t\tdescription: 'ID of the user story to update',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userStory'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Update Fields',\n\t\tname: 'updateFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userStory'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Assignee Name or ID',\n\t\t\t\tname: 'assigned_to',\n\t\t\t\ttype: 'options',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsDependsOn: ['projectId'],\n\t\t\t\t\tloadOptionsMethod: 'getUsers',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'ID of the user to assign the the user story to. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Backlog Order',\n\t\t\t\tname: 'backlog_order',\n\t\t\t\ttype: 'number',\n\t\t\t\tdefault: 1,\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tminValue: 1,\n\t\t\t\t},\n\t\t\t\tdescription: 'Order of the user story in the backlog',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Blocked Note',\n\t\t\t\tname: 'blocked_note',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'Reason why the user story is blocked. Requires \"Is Blocked\" toggle to be enabled.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Description',\n\t\t\t\tname: 'description',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Is Blocked',\n\t\t\t\tname: 'is_blocked',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user story is blocked',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Kanban Order',\n\t\t\t\tname: 'kanban_order',\n\t\t\t\ttype: 'number',\n\t\t\t\tdefault: 1,\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tminValue: 1,\n\t\t\t\t},\n\t\t\t\tdescription: 'Order of the user story in the kanban',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Milestone (Sprint) Name or ID',\n\t\t\t\tname: 'milestone',\n\t\t\t\ttype: 'options',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsDependsOn: ['projectId'],\n\t\t\t\t\tloadOptionsMethod: 'getMilestones',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'ID of the milestone of the user story. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Subject',\n\t\t\t\tname: 'subject',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Sprint Order',\n\t\t\t\tname: 'sprint_order',\n\t\t\t\ttype: 'number',\n\t\t\t\tdefault: 1,\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tminValue: 1,\n\t\t\t\t},\n\t\t\t\tdescription: 'Order of the user story in the milestone',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Status Name or ID',\n\t\t\t\tname: 'status',\n\t\t\t\ttype: 'options',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsDependsOn: ['projectId'],\n\t\t\t\t\tloadOptionsMethod: 'getUserStoryStatuses',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'ID of the status of the user story. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Tag Names or IDs',\n\t\t\t\tname: 'tags',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\tdescription:\n\t\t\t\t\t'Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsDependsOn: ['projectId'],\n\t\t\t\t\tloadOptionsMethod: 'getTags',\n\t\t\t\t},\n\t\t\t\tdefault: [],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Type Name or ID',\n\t\t\t\tname: 'type',\n\t\t\t\ttype: 'options',\n\t\t\t\tdescription:\n\t\t\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsDependsOn: ['projectId'],\n\t\t\t\t\tloadOptionsMethod: 'getTypes',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,sBAAyC;AAAA,EACrD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,MACvB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,kBAAqC;AAAA;AAAA;AAAA;AAAA,EAIjD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aACC;AAAA,IACD,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,sBAAsB,CAAC,WAAW;AAAA,UAClC,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,sBAAsB,CAAC,WAAW;AAAA,UAClC,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,sBAAsB,CAAC,WAAW;AAAA,UAClC,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aACC;AAAA,QACD,aAAa;AAAA,UACZ,sBAAsB,CAAC,WAAW;AAAA,UAClC,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS,CAAC;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aACC;AAAA,QACD,aAAa;AAAA,UACZ,sBAAsB,CAAC,WAAW;AAAA,UAClC,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,MACV;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aACC;AAAA,IACD,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,MACZ,UAAU;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,QAAQ;AAAA,QACpB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aACC;AAAA,QACD,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,sBAAsB,CAAC,WAAW;AAAA,UAClC,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aACC;AAAA,QACD,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,sBAAsB,CAAC,WAAW;AAAA,UAClC,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,sBAAsB,CAAC,WAAW;AAAA,UAClC,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aACC;AAAA,QACD,aAAa;AAAA,UACZ,sBAAsB,CAAC,WAAW;AAAA,UAClC,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aACC;AAAA,QACD,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,sBAAsB,CAAC,WAAW;AAAA,UAClC,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aACC;AAAA,QACD,aAAa;AAAA,UACZ,sBAAsB,CAAC,WAAW;AAAA,UAClC,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS,CAAC;AAAA,MACX;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,IACT,aACC;AAAA,IACD,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,sBAAsB,CAAC,WAAW;AAAA,UAClC,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,sBAAsB,CAAC,WAAW;AAAA,UAClC,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,sBAAsB,CAAC,WAAW;AAAA,UAClC,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aACC;AAAA,QACD,aAAa;AAAA,UACZ,sBAAsB,CAAC,WAAW;AAAA,UAClC,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS,CAAC;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aACC;AAAA,QACD,aAAa;AAAA,UACZ,sBAAsB,CAAC,WAAW;AAAA,UAClC,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,MACV;AAAA,IACD;AAAA,EACD;AACD;", "names": []}