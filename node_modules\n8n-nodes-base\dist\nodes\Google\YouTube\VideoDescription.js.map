{"version": 3, "sources": ["../../../../nodes/Google/YouTube/VideoDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const videoOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'delete',\n\t\t\t\tdescription: 'Delete a video',\n\t\t\t\taction: 'Delete a video',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\tdescription: 'Get a video',\n\t\t\t\taction: 'Get a video',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Retrieve many videos',\n\t\t\t\taction: 'Get many videos',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Rate',\n\t\t\t\tvalue: 'rate',\n\t\t\t\tdescription: 'Rate a video',\n\t\t\t\taction: 'Rate a video',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Update',\n\t\t\t\tvalue: 'update',\n\t\t\t\tdescription: 'Update a video',\n\t\t\t\taction: 'Update a video',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Upload',\n\t\t\t\tvalue: 'upload',\n\t\t\t\tdescription: 'Upload a video',\n\t\t\t\taction: 'Upload a video',\n\t\t\t},\n\t\t],\n\t\tdefault: 'getAll',\n\t},\n];\n\nexport const videoFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 video:upload                               */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Title',\n\t\tname: 'title',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['upload'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-wrong-for-dynamic-options\n\t\tdisplayName: 'Region Code',\n\t\tname: 'regionCode',\n\t\ttype: 'options',\n\t\tdescription:\n\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getCountriesCodes',\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['upload'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Category Name or ID',\n\t\tname: 'categoryId',\n\t\ttype: 'options',\n\t\tdescription:\n\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getVideoCategories',\n\t\t\tloadOptionsDependsOn: ['regionCode'],\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['upload'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Input Binary Field',\n\t\tname: 'binaryProperty',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\thint: 'The name of the input binary field containing the file to be uploaded',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['upload'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\tdefault: 'data',\n\t},\n\t{\n\t\tdisplayName: 'Options',\n\t\tname: 'options',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add option',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['upload'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Default Language Name or ID',\n\t\t\t\tname: 'defaultLanguage',\n\t\t\t\ttype: 'options',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getLanguages',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'The language of the text in the playlist resource\\'s title and description properties. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Description',\n\t\t\t\tname: 'description',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The playlist's description\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Embeddable',\n\t\t\t\tname: 'embeddable',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the video can be embedded on another website',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'License',\n\t\t\t\tname: 'license',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Creative Common',\n\t\t\t\t\t\tvalue: 'creativeCommon',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Youtube',\n\t\t\t\t\t\tvalue: 'youtube',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The video's license\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Notify Subscribers',\n\t\t\t\tname: 'notifySubscribers',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription:\n\t\t\t\t\t\"Whether YouTube should send a notification about the new video to users who subscribe to the video's channel\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Privacy Status',\n\t\t\t\tname: 'privacyStatus',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Private',\n\t\t\t\t\t\tvalue: 'private',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Public',\n\t\t\t\t\t\tvalue: 'public',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Unlisted',\n\t\t\t\t\t\tvalue: 'unlisted',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The playlist's privacy status\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Public Stats Viewable',\n\t\t\t\tname: 'publicStatsViewable',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: true,\n\t\t\t\tdescription:\n\t\t\t\t\t\"Whether the extended video statistics on the video's watch page are publicly viewable\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Publish At',\n\t\t\t\tname: 'publishAt',\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'If you set a value for this property, you must also set the status.privacyStatus property to private',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Recording Date',\n\t\t\t\tname: 'recordingDate',\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The date and time when the video was recorded',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Self Declared Made For Kids',\n\t\t\t\tname: 'selfDeclaredMadeForKids',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription:\n\t\t\t\t\t'Whether the video is designated as child-directed, and it contains the current \"made for kids\" status of the video',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Tags',\n\t\t\t\tname: 'tags',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'Keyword tags associated with the playlist. Mulplie can be defined separated by comma.',\n\t\t\t},\n\t\t],\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 video:delete                               */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Video ID',\n\t\tname: 'videoId',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['delete'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'ID of the video',\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Options',\n\t\tname: 'options',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add option',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['delete'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'On Behalf Of Content Owner',\n\t\t\t\tname: 'onBehalfOfContentOwner',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t\"The onBehalfOfContentOwner parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value\",\n\t\t\t},\n\t\t],\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 video:get                                  */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Video ID',\n\t\tname: 'videoId',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['get'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Fields',\n\t\tname: 'part',\n\t\ttype: 'multiOptions',\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: '*',\n\t\t\t\tvalue: '*',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Content Details',\n\t\t\t\tvalue: 'contentDetails',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'ID',\n\t\t\t\tvalue: 'id',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Live Streaming Details',\n\t\t\t\tvalue: 'liveStreamingDetails',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Localizations',\n\t\t\t\tvalue: 'localizations',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Player',\n\t\t\t\tvalue: 'player',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Recording Details',\n\t\t\t\tvalue: 'recordingDetails',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Snippet',\n\t\t\t\tvalue: 'snippet',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Statistics',\n\t\t\t\tvalue: 'statistics',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Status',\n\t\t\t\tvalue: 'status',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Topic Details',\n\t\t\t\tvalue: 'topicDetails',\n\t\t\t},\n\t\t],\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['get'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'The fields parameter specifies a comma-separated list of one or more video resource properties that the API response will include',\n\t\tdefault: ['*'],\n\t},\n\t{\n\t\tdisplayName: 'Options',\n\t\tname: 'options',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add option',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['get'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'On Behalf Of Content Owner',\n\t\t\t\tname: 'onBehalfOfContentOwner',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t\"The onBehalfOfContentOwner parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value\",\n\t\t\t},\n\t\t],\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 video:getAll                               */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['video'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 50,\n\t\t},\n\t\tdefault: 25,\n\t\tdescription: 'Max number of results to return',\n\t},\n\t{\n\t\tdisplayName: 'Filters',\n\t\tname: 'filters',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add option',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Channel ID',\n\t\t\t\tname: 'channelId',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'The channelId parameter indicates that the API response should only contain resources created by the channel',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'For Developer',\n\t\t\t\tname: 'forDeveloper',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription:\n\t\t\t\t\t\"Whether to restrict the search to only retrieve videos uploaded via the developer's application or website\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Published After',\n\t\t\t\tname: 'publishedAfter',\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'The publishedAfter parameter indicates that the API response should only contain resources created at or after the specified time',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Published Before',\n\t\t\t\tname: 'publishedBefore',\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'The publishedBefore parameter indicates that the API response should only contain resources created before or at the specified time',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Query',\n\t\t\t\tname: 'q',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The q parameter specifies the query term to search for',\n\t\t\t},\n\t\t\t{\n\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-wrong-for-dynamic-options\n\t\t\t\tdisplayName: 'Region Code',\n\t\t\t\tname: 'regionCode',\n\t\t\t\ttype: 'options',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getCountriesCodes',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'The regionCode parameter instructs the API to select a video chart available in the specified region. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Related To Video ID',\n\t\t\t\tname: 'relatedToVideoId',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'The relatedToVideoId parameter retrieves a list of videos that are related to the video that the parameter value identifies',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Video Category ID',\n\t\t\t\tname: 'videoCategoryId',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'The videoCategoryId parameter identifies the video category for which the chart should be retrieved',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Video Syndicated',\n\t\t\t\tname: 'videoSyndicated',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription:\n\t\t\t\t\t'Whether to restrict a search to only videos that can be played outside youtube.com',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Video Type',\n\t\t\t\tname: 'videoType',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Any',\n\t\t\t\t\t\tvalue: 'any',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Episode',\n\t\t\t\t\t\tvalue: 'episode',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Movie',\n\t\t\t\t\t\tvalue: 'movie',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'The videoType parameter lets you restrict a search to a particular type of videos',\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Options',\n\t\tname: 'options',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add option',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Order',\n\t\t\t\tname: 'order',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Date',\n\t\t\t\t\t\tvalue: 'date',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Relevance',\n\t\t\t\t\t\tvalue: 'relevance',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'relevance',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Safe Search',\n\t\t\t\tname: 'safeSearch',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Moderate',\n\t\t\t\t\t\tvalue: 'moderate',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'YouTube will filter some content from search results and, at the least, will filter content that is restricted in your locale',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'None',\n\t\t\t\t\t\tvalue: 'none',\n\t\t\t\t\t\tdescription: 'YouTube will not filter the search result set',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Strict',\n\t\t\t\t\t\tvalue: 'strict',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'YouTube will try to exclude all restricted content from the search result set',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t],\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 video:rate                                 */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Video ID',\n\t\tname: 'videoId',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['rate'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Rating',\n\t\tname: 'rating',\n\t\ttype: 'options',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['rate'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Dislike',\n\t\t\t\tvalue: 'dislike',\n\t\t\t\tdescription: 'Records that the authenticated user disliked the video',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Like',\n\t\t\t\tvalue: 'like',\n\t\t\t\tdescription: 'Records that the authenticated user liked the video',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'None',\n\t\t\t\tvalue: 'none',\n\t\t\t\tdescription:\n\t\t\t\t\t'Removes any rating that the authenticated user had previously set for the video',\n\t\t\t},\n\t\t],\n\t\tdefault: '',\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 video:update                               */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Video ID',\n\t\tname: 'videoId',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['update'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Title',\n\t\tname: 'title',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['update'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-wrong-for-dynamic-options\n\t\tdisplayName: 'Region Code',\n\t\tname: 'regionCode',\n\t\ttype: 'options',\n\t\tdescription:\n\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getCountriesCodes',\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['update'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Category Name or ID',\n\t\tname: 'categoryId',\n\t\ttype: 'options',\n\t\tdescription:\n\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getVideoCategories',\n\t\t\tloadOptionsDependsOn: ['regionCode'],\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['update'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Update Fields',\n\t\tname: 'updateFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add option',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['update'],\n\t\t\t\tresource: ['video'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Default Language Name or ID',\n\t\t\t\tname: 'defaultLanguage',\n\t\t\t\ttype: 'options',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getLanguages',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'The language of the text in the playlist resource\\'s title and description properties. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Description',\n\t\t\t\tname: 'description',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The playlist's description\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Embeddable',\n\t\t\t\tname: 'embeddable',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the video can be embedded on another website',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'License',\n\t\t\t\tname: 'license',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Creative Common',\n\t\t\t\t\t\tvalue: 'creativeCommon',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Youtube',\n\t\t\t\t\t\tvalue: 'youtube',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The video's license\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Notify Subscribers',\n\t\t\t\tname: 'notifySubscribers',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription:\n\t\t\t\t\t\"Whether YouTube should send a notification about the new video to users who subscribe to the video's channel\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Privacy Status',\n\t\t\t\tname: 'privacyStatus',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Private',\n\t\t\t\t\t\tvalue: 'private',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Public',\n\t\t\t\t\t\tvalue: 'public',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Unlisted',\n\t\t\t\t\t\tvalue: 'unlistef',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The playlist's privacy status\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Public Stats Viewable',\n\t\t\t\tname: 'publicStatsViewable',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: true,\n\t\t\t\tdescription:\n\t\t\t\t\t\"Whether the extended video statistics on the video's watch page are publicly viewable\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Publish At',\n\t\t\t\tname: 'publishAt',\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'If you set a value for this property, you must also set the status.privacyStatus property to private',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Recording Date',\n\t\t\t\tname: 'recordingDate',\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The date and time when the video was recorded',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Self Declared Made For Kids',\n\t\t\t\tname: 'selfDeclaredMadeForKids',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription:\n\t\t\t\t\t'Whether the video is designated as child-directed, and it contains the current \"made for kids\" status of the video',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Tags',\n\t\t\t\tname: 'tags',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'Keyword tags associated with the playlist. Mulplie can be defined separated by comma.',\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,kBAAqC;AAAA,EACjD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,cAAiC;AAAA;AAAA;AAAA;AAAA,EAI7C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA;AAAA,IAEC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aACC;AAAA,IACD,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aACC;AAAA,IACD,aAAa;AAAA,MACZ,mBAAmB;AAAA,MACnB,sBAAsB,CAAC,YAAY;AAAA,IACpC;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,IACb,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,KAAK;AAAA,QACjB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,KAAK;AAAA,QACjB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,aACC;AAAA,IACD,SAAS,CAAC,GAAG;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,KAAK;AAAA,QACjB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,OAAO;AAAA,QAClB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA;AAAA,QAEC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,YACP,aACC;AAAA,UACF;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,YACP,aAAa;AAAA,UACd;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,YACP,aACC;AAAA,UACF;AAAA,QACD;AAAA,QACA,SAAS;AAAA,MACV;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,MAAM;AAAA,QAClB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,MAAM;AAAA,QAClB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aACC;AAAA,MACF;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA;AAAA,IAEC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aACC;AAAA,IACD,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aACC;AAAA,IACD,aAAa;AAAA,MACZ,mBAAmB;AAAA,MACnB,sBAAsB,CAAC,YAAY;AAAA,IACpC;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AACD;", "names": []}