{"version": 3, "sources": ["../../../../nodes/Kitemaker/descriptions/WorkItemDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const workItemOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdefault: 'get',\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Create',\n\t\t\t\tvalue: 'create',\n\t\t\t\taction: 'Create a work item',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\taction: 'Get a work item',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\taction: 'Get many work items',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Update',\n\t\t\t\tvalue: 'update',\n\t\t\t\taction: 'Update a work item',\n\t\t\t},\n\t\t],\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workItem'],\n\t\t\t},\n\t\t},\n\t},\n];\n\nexport const workItemFields: INodeProperties[] = [\n\t// ----------------------------------\n\t//         workItem: create\n\t// ----------------------------------\n\t{\n\t\tdisplayName: 'Title',\n\t\tname: 'title',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdescription: 'Title of the work item to create',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workItem'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Space Name or ID',\n\t\tname: 'spaceId',\n\t\ttype: 'options',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getSpaces',\n\t\t},\n\t\tdefault: [],\n\t\trequired: true,\n\t\tdescription:\n\t\t\t'ID of the space to retrieve the work items from. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workItem'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Status Name or ID',\n\t\tname: 'statusId',\n\t\ttype: 'options',\n\t\ttypeOptions: {\n\t\t\tloadOptionsDependsOn: ['spaceId'],\n\t\t\tloadOptionsMethod: 'getStatuses',\n\t\t},\n\t\tdefault: [],\n\t\trequired: true,\n\t\tdescription:\n\t\t\t'ID of the status to set on the item to create. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workItem'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workItem'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Description',\n\t\t\t\tname: 'description',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Description of the item to create. Markdown supported.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Effort',\n\t\t\t\tname: 'effort',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'SMALL',\n\t\t\t\tdescription: 'Effort to set for the item to create',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Small',\n\t\t\t\t\t\tvalue: 'SMALL',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Medium',\n\t\t\t\t\t\tvalue: 'MEDIUM',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Large',\n\t\t\t\t\t\tvalue: 'LARGE',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Impact',\n\t\t\t\tname: 'impact',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'SMALL',\n\t\t\t\tdescription: 'Impact to set for the item to create',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Small',\n\t\t\t\t\t\tvalue: 'SMALL',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Medium',\n\t\t\t\t\t\tvalue: 'MEDIUM',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Large',\n\t\t\t\t\t\tvalue: 'LARGE',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Label Names or IDs',\n\t\t\t\tname: 'labelIds',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getLabels',\n\t\t\t\t},\n\t\t\t\tdefault: [],\n\t\t\t\tdescription:\n\t\t\t\t\t'ID of the label to set on the item to create. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Member Names or IDs',\n\t\t\t\tname: 'memberIds',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getUsers',\n\t\t\t\t},\n\t\t\t\tdefault: [],\n\t\t\t\tdescription:\n\t\t\t\t\t'ID of the user to assign to the item to create. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t],\n\t},\n\n\t// ----------------------------------\n\t//         workItem: get\n\t// ----------------------------------\n\t{\n\t\tdisplayName: 'Work Item ID',\n\t\tname: 'workItemId',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdescription: 'ID of the work item to retrieve',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workItem'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t},\n\n\t// ----------------------------------\n\t//         workItem: getAll\n\t// ----------------------------------\n\t{\n\t\tdisplayName: 'Space Name or ID',\n\t\tname: 'spaceId',\n\t\ttype: 'options',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getSpaces',\n\t\t},\n\t\tdefault: [],\n\t\trequired: true,\n\t\tdescription:\n\t\t\t'ID of the space to retrieve the work items from. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workItem'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workItem'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdefault: 5,\n\t\tdescription: 'Max number of results to return',\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 1000,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workItem'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t},\n\n\t// ----------------------------------\n\t//         workItem: update\n\t// ----------------------------------\n\t{\n\t\tdisplayName: 'Work Item ID',\n\t\tname: 'workItemId',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdescription: 'ID of the work item to update',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workItem'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Update Fields',\n\t\tname: 'updateFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workItem'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Description',\n\t\t\t\tname: 'description',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Description of the item to update. Markdown supported.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Effort',\n\t\t\t\tname: 'effort',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'SMALL',\n\t\t\t\tdescription: 'Effort to set for the item to update',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Small',\n\t\t\t\t\t\tvalue: 'SMALL',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Medium',\n\t\t\t\t\t\tvalue: 'MEDIUM',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Large',\n\t\t\t\t\t\tvalue: 'LARGE',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Impact',\n\t\t\t\tname: 'impact',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'SMALL',\n\t\t\t\tdescription: 'Impact to set for the item to update',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Small',\n\t\t\t\t\t\tvalue: 'SMALL',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Medium',\n\t\t\t\t\t\tvalue: 'MEDIUM',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Large',\n\t\t\t\t\t\tvalue: 'LARGE',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Status Name or ID',\n\t\t\t\tname: 'statusId',\n\t\t\t\ttype: 'options',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getStatuses',\n\t\t\t\t},\n\t\t\t\tdefault: [],\n\t\t\t\tdescription:\n\t\t\t\t\t'ID of the status to set on the item to update. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Title',\n\t\t\t\tname: 'title',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Title to set for the work item to update',\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,qBAAwC;AAAA,EACpD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,MACtB;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,iBAAoC;AAAA;AAAA;AAAA;AAAA,EAIhD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS,CAAC;AAAA,IACV,UAAU;AAAA,IACV,aACC;AAAA,IACD,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,sBAAsB,CAAC,SAAS;AAAA,MAChC,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS,CAAC;AAAA,IACV,UAAU;AAAA,IACV,aACC;AAAA,IACD,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS,CAAC;AAAA,QACV,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS,CAAC;AAAA,QACV,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS,CAAC;AAAA,IACV,UAAU;AAAA,IACV,aACC;AAAA,IACD,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,QACpB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS,CAAC;AAAA,QACV,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AACD;", "names": []}