{"version": 3, "sources": ["../../../nodes/Workable/WorkableTrigger.node.ts"], "sourcesContent": ["import { snakeCase } from 'change-case';\nimport type {\n\tIHookFunctions,\n\tIWebhookFunctions,\n\tIDataObject,\n\tILoadOptionsFunctions,\n\tINodePropertyOptions,\n\tINodeType,\n\tINodeTypeDescription,\n\tIWebhookResponseData,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes } from 'n8n-workflow';\n\nimport { workableApiRequest } from './GenericFunctions';\n\nexport class WorkableTrigger implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Workable Trigger',\n\t\tname: 'workableTrigger',\n\t\t// eslint-disable-next-line n8n-nodes-base/node-class-description-icon-not-svg\n\t\ticon: 'file:workable.png',\n\t\tgroup: ['trigger'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"triggerOn\"]}}',\n\t\tdescription: 'Starts the workflow when Workable events occur',\n\t\tdefaults: {\n\t\t\tname: 'Workable Trigger',\n\t\t},\n\t\tinputs: [],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'workableApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\twebhooks: [\n\t\t\t{\n\t\t\t\tname: 'default',\n\t\t\t\thttpMethod: 'POST',\n\t\t\t\tresponseMode: 'onReceived',\n\t\t\t\tpath: 'webhook',\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Trigger On',\n\t\t\t\tname: 'triggerOn',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Candidate Created',\n\t\t\t\t\t\tvalue: 'candidateCreated',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Candidate Moved',\n\t\t\t\t\t\tvalue: 'candidateMoved',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: '',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Filters',\n\t\t\t\tname: 'filters',\n\t\t\t\ttype: 'collection',\n\t\t\t\tplaceholder: 'Add Filter',\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Job Name or ID',\n\t\t\t\t\t\tname: 'job',\n\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\tloadOptionsMethod: 'getJobs',\n\t\t\t\t\t\t},\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Get notifications only for one job. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Stage Name or ID',\n\t\t\t\t\t\tname: 'stage',\n\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\tloadOptionsMethod: 'getStages',\n\t\t\t\t\t\t},\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Get notifications for specific stages. e.g. \\'hired\\'. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\tasync getJobs(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst { jobs } = await workableApiRequest.call(this, 'GET', '/jobs');\n\t\t\t\tfor (const job of jobs) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: job.full_title,\n\t\t\t\t\t\tvalue: job.shortcode,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\tasync getStages(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst { stages } = await workableApiRequest.call(this, 'GET', '/stages');\n\t\t\t\tfor (const stage of stages) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: stage.name,\n\t\t\t\t\t\tvalue: stage.slug,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t},\n\t};\n\n\twebhookMethods = {\n\t\tdefault: {\n\t\t\tasync checkExists(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\t// Check all the webhooks which exist already if it is identical to the\n\t\t\t\t// one that is supposed to get created.\n\t\t\t\tconst { subscriptions } = await workableApiRequest.call(this, 'GET', '/subscriptions');\n\t\t\t\tfor (const subscription of subscriptions) {\n\t\t\t\t\tif (subscription.target === webhookUrl) {\n\t\t\t\t\t\twebhookData.webhookId = subscription.id as string;\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tasync create(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst credentials = await this.getCredentials<{\n\t\t\t\t\taccessToken: string;\n\t\t\t\t\tsubdomain: string;\n\t\t\t\t}>('workableApi');\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\t\t\t\tconst triggerOn = this.getNodeParameter('triggerOn') as string;\n\t\t\t\tconst { stage, job } = this.getNodeParameter('filters') as IDataObject;\n\t\t\t\tconst endpoint = '/subscriptions';\n\n\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\tevent: snakeCase(triggerOn).toLowerCase(),\n\t\t\t\t\targs: {\n\t\t\t\t\t\taccount_id: credentials.subdomain,\n\t\t\t\t\t\t...(job && { job_shortcode: job }),\n\t\t\t\t\t\t...(stage && { stage_slug: stage }),\n\t\t\t\t\t},\n\t\t\t\t\ttarget: webhookUrl,\n\t\t\t\t};\n\n\t\t\t\tconst responseData = await workableApiRequest.call(this, 'POST', endpoint, body);\n\n\t\t\t\tif (responseData.id === undefined) {\n\t\t\t\t\t// Required data is missing so was not successful\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\twebhookData.webhookId = responseData.id as string;\n\t\t\t\treturn true;\n\t\t\t},\n\t\t\tasync delete(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\tif (webhookData.webhookId !== undefined) {\n\t\t\t\t\tconst endpoint = `/subscriptions/${webhookData.webhookId}`;\n\t\t\t\t\ttry {\n\t\t\t\t\t\tawait workableApiRequest.call(this, 'DELETE', endpoint);\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\t// Remove from the static workflow data so that it is clear\n\t\t\t\t\t// that no webhooks are registered anymore\n\t\t\t\t\tdelete webhookData.webhookId;\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync webhook(this: IWebhookFunctions): Promise<IWebhookResponseData> {\n\t\tconst bodyData = this.getBodyData();\n\t\treturn {\n\t\t\tworkflowData: [this.helpers.returnJsonArray(bodyData)],\n\t\t};\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAA0B;AAW1B,0BAAoC;AAEpC,8BAAmC;AAE5B,MAAM,gBAAqC;AAAA,EAA3C;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA;AAAA,MAEN,MAAM;AAAA,MACN,OAAO,CAAC,SAAS;AAAA,MACjB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,UAAU;AAAA,QACT;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,MAAM;AAAA,QACP;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,UACT,UAAU;AAAA,QACX;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,SAAS,CAAC;AAAA,UACV,SAAS;AAAA,YACR;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,aAAa;AAAA,gBACZ,mBAAmB;AAAA,cACpB;AAAA,cACA,SAAS;AAAA,cACT,aACC;AAAA,YACF;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,aAAa;AAAA,gBACZ,mBAAmB;AAAA,cACpB;AAAA,cACA,SAAS;AAAA,cACT,aACC;AAAA,YACF;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA,QACZ,MAAM,UAAsE;AAC3E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,EAAE,KAAK,IAAI,MAAM,2CAAmB,KAAK,MAAM,OAAO,OAAO;AACnE,qBAAW,OAAO,MAAM;AACvB,uBAAW,KAAK;AAAA,cACf,MAAM,IAAI;AAAA,cACV,OAAO,IAAI;AAAA,YACZ,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,YAAwE;AAC7E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,EAAE,OAAO,IAAI,MAAM,2CAAmB,KAAK,MAAM,OAAO,SAAS;AACvE,qBAAW,SAAS,QAAQ;AAC3B,uBAAW,KAAK;AAAA,cACf,MAAM,MAAM;AAAA,cACZ,OAAO,MAAM;AAAA,YACd,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAEA,0BAAiB;AAAA,MAChB,SAAS;AAAA,QACR,MAAM,cAAoD;AACzD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AACnD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AAGrD,gBAAM,EAAE,cAAc,IAAI,MAAM,2CAAmB,KAAK,MAAM,OAAO,gBAAgB;AACrF,qBAAW,gBAAgB,eAAe;AACzC,gBAAI,aAAa,WAAW,YAAY;AACvC,0BAAY,YAAY,aAAa;AACrC,qBAAO;AAAA,YACR;AAAA,UACD;AACA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,cAAc,MAAM,KAAK,eAG5B,aAAa;AAChB,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AACnD,gBAAM,YAAY,KAAK,iBAAiB,WAAW;AACnD,gBAAM,EAAE,OAAO,IAAI,IAAI,KAAK,iBAAiB,SAAS;AACtD,gBAAM,WAAW;AAEjB,gBAAM,OAAoB;AAAA,YACzB,WAAO,8BAAU,SAAS,EAAE,YAAY;AAAA,YACxC,MAAM;AAAA,cACL,YAAY,YAAY;AAAA,cACxB,GAAI,OAAO,EAAE,eAAe,IAAI;AAAA,cAChC,GAAI,SAAS,EAAE,YAAY,MAAM;AAAA,YAClC;AAAA,YACA,QAAQ;AAAA,UACT;AAEA,gBAAM,eAAe,MAAM,2CAAmB,KAAK,MAAM,QAAQ,UAAU,IAAI;AAE/E,cAAI,aAAa,OAAO,QAAW;AAElC,mBAAO;AAAA,UACR;AAEA,sBAAY,YAAY,aAAa;AACrC,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,cAAI,YAAY,cAAc,QAAW;AACxC,kBAAM,WAAW,kBAAkB,YAAY,SAAS;AACxD,gBAAI;AACH,oBAAM,2CAAmB,KAAK,MAAM,UAAU,QAAQ;AAAA,YACvD,SAAS,OAAO;AACf,qBAAO;AAAA,YACR;AAGA,mBAAO,YAAY;AAAA,UACpB;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAgE;AACrE,UAAM,WAAW,KAAK,YAAY;AAClC,WAAO;AAAA,MACN,cAAc,CAAC,KAAK,QAAQ,gBAAgB,QAAQ,CAAC;AAAA,IACtD;AAAA,EACD;AACD;", "names": []}