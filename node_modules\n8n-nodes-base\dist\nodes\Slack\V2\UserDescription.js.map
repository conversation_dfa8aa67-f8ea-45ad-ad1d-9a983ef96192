{"version": 3, "sources": ["../../../../nodes/Slack/V2/UserDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const userOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'info',\n\t\t\t\tdescription: 'Get information about a user',\n\t\t\t\taction: 'Get information about a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Get a list of many users',\n\t\t\t\taction: 'Get many users',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: \"Get User's Profile\",\n\t\t\t\tvalue: 'getProfile',\n\t\t\t\tdescription: \"Get a user's profile\",\n\t\t\t\taction: \"Get a user's profile\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: \"Get User's Status\",\n\t\t\t\tvalue: 'getPresence',\n\t\t\t\tdescription: 'Get online status of a user',\n\t\t\t\taction: \"Get a user's presence status\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: \"Update User's Profile\",\n\t\t\t\tvalue: 'updateProfile',\n\t\t\t\tdescription: \"Update a user's profile\",\n\t\t\t\taction: \"Update a user's profile\",\n\t\t\t},\n\t\t],\n\t\tdefault: 'info',\n\t},\n];\n\nexport const userFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                                user:info                                   */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'User',\n\t\tname: 'user',\n\t\ttype: 'resourceLocator',\n\t\tdefault: { mode: 'list', value: '' },\n\t\tplaceholder: 'Select a user...',\n\t\tdescription: 'The ID of the user to get information about',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['info', 'getProfile'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\tmodes: [\n\t\t\t{\n\t\t\t\tdisplayName: 'From List',\n\t\t\t\tname: 'list',\n\t\t\t\ttype: 'list',\n\t\t\t\tplaceholder: 'Select a user...',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tsearchListMethod: 'getUsers',\n\t\t\t\t\tsearchable: true,\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'By ID',\n\t\t\t\tname: 'id',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidation: [\n\t\t\t\t\t{\n\t\t\t\t\t\ttype: 'regex',\n\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\tregex: '[a-zA-Z0-9]{2,}',\n\t\t\t\t\t\t\terrorMessage: 'Not a valid Slack User ID',\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tplaceholder: 'U123AB45JGM',\n\t\t\t},\n\t\t],\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 user:getAll                                */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 100,\n\t\t},\n\t\tdefault: 50,\n\t\tdescription: 'Max number of results to return',\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                user:getPresence                            */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'User',\n\t\tname: 'user',\n\t\ttype: 'resourceLocator',\n\t\tdefault: { mode: 'list', value: '' },\n\t\tplaceholder: 'Select a user...',\n\t\tdescription: 'The ID of the user to get the online status of',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getPresence'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\tmodes: [\n\t\t\t{\n\t\t\t\tdisplayName: 'From List',\n\t\t\t\tname: 'list',\n\t\t\t\ttype: 'list',\n\t\t\t\tplaceholder: 'Select a user...',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tsearchListMethod: 'getUsers',\n\t\t\t\t\tsearchable: true,\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'By ID',\n\t\t\t\tname: 'id',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidation: [\n\t\t\t\t\t{\n\t\t\t\t\t\ttype: 'regex',\n\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\tregex: '[a-zA-Z0-9]{2,}',\n\t\t\t\t\t\t\terrorMessage: 'Not a valid Slack User ID',\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tplaceholder: 'U123AB45JGM',\n\t\t\t},\n\t\t],\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                user:update user profile                     */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Options',\n\t\tname: 'options',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['updateProfile'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Custom Fields',\n\t\t\t\tname: 'customFieldUi',\n\t\t\t\tplaceholder: 'Add Custom Fields',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'customFieldValues',\n\t\t\t\t\t\tdisplayName: 'Custom Field',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Field Name or ID',\n\t\t\t\t\t\t\t\tname: 'id',\n\t\t\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\t\t\tloadOptionsMethod: 'getTeamFields',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t\t'ID of the field to set. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Field Value',\n\t\t\t\t\t\t\t\tname: 'value',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription: 'Value of the field to set',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Alt',\n\t\t\t\t\t\t\t\tname: 'alt',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Email',\n\t\t\t\tname: 'email',\n\t\t\t\ttype: 'string',\n\t\t\t\tplaceholder: '<EMAIL>',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'This field can only be changed by admins for users on paid teams',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'First Name',\n\t\t\t\tname: 'first_name',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Last Name',\n\t\t\t\tname: 'last_name',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Set Status',\n\t\t\t\tname: 'status',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tdefault: {},\n\t\t\t\tplaceholder: 'Set Status',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Set Status',\n\t\t\t\t\t\tname: 'set_status',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Status Emoji',\n\t\t\t\t\t\t\t\tname: 'status_emoji',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t\t'Is a string referencing an emoji enabled for the Slack team, such as :mountain_railway:',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Status Expiration',\n\t\t\t\t\t\t\t\tname: 'status_expiration',\n\t\t\t\t\t\t\t\ttype: 'dateTime',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t\t'The number of minutes to wait until this status expires and is cleared. Optional.',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Status Text',\n\t\t\t\t\t\t\t\tname: 'status_text',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription: 'Allows up to 100 characters, though we strongly encourage brevity',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'User ID',\n\t\t\t\tname: 'user',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'ID of user to change. This argument may only be specified by team admins on paid teams.',\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,iBAAoC;AAAA,EAChD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,aAAgC;AAAA;AAAA;AAAA;AAAA,EAI5C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,EAAE,MAAM,QAAQ,OAAO,GAAG;AAAA,IACnC,aAAa;AAAA,IACb,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ,YAAY;AAAA,QAChC,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,OAAO;AAAA,MACN;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,aAAa;AAAA,UACZ,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACb;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,YAAY;AAAA,UACX;AAAA,YACC,MAAM;AAAA,YACN,YAAY;AAAA,cACX,OAAO;AAAA,cACP,cAAc;AAAA,YACf;AAAA,UACD;AAAA,QACD;AAAA,QACA,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,QACpB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,EAAE,MAAM,QAAQ,OAAO,GAAG;AAAA,IACnC,aAAa;AAAA,IACb,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,aAAa;AAAA,QACzB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,OAAO;AAAA,MACN;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,aAAa;AAAA,UACZ,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACb;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,YAAY;AAAA,UACX;AAAA,YACC,MAAM;AAAA,YACN,YAAY;AAAA,cACX,OAAO;AAAA,cACP,cAAc;AAAA,YACf;AAAA,UACD;AAAA,QACD;AAAA,QACA,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,eAAe;AAAA,MAC5B;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,SAAS,CAAC;AAAA,QACV,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,aAAa;AAAA,kBACZ,mBAAmB;AAAA,gBACpB;AAAA,gBACA,SAAS;AAAA,gBACT,aACC;AAAA,cACF;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,cACV;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aACC;AAAA,cACF;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aACC;AAAA,cACF;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AACD;", "names": []}