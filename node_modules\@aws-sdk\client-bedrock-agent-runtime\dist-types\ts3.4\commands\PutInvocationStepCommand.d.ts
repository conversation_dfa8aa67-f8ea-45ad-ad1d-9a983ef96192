import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  BedrockAgentRuntimeClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../BedrockAgentRuntimeClient";
import {
  PutInvocationStepRequest,
  PutInvocationStepResponse,
} from "../models/models_1";
export { __MetadataBearer };
export { $Command };
export interface PutInvocationStepCommandInput
  extends PutInvocationStepRequest {}
export interface PutInvocationStepCommandOutput
  extends PutInvocationStepResponse,
    __MetadataBearer {}
declare const PutInvocationStepCommand_base: {
  new (
    input: PutInvocationStepCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutInvocationStepCommandInput,
    PutInvocationStepCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutInvocationStepCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutInvocationStepCommandInput,
    PutInvocationStepCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutInvocationStepCommand extends PutInvocationStepCommand_base {
  protected static __types: {
    api: {
      input: PutInvocationStepRequest;
      output: PutInvocationStepResponse;
    };
    sdk: {
      input: PutInvocationStepCommandInput;
      output: PutInvocationStepCommandOutput;
    };
  };
}
