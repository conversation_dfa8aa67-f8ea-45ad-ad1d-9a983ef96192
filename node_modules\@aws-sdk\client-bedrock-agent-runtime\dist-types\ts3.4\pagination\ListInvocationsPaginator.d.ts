import { Paginator } from "@smithy/types";
import {
  ListInvocationsCommandInput,
  ListInvocationsCommandOutput,
} from "../commands/ListInvocationsCommand";
import { BedrockAgentRuntimePaginationConfiguration } from "./Interfaces";
export declare const paginateListInvocations: (
  config: BedrockAgentRuntimePaginationConfiguration,
  input: ListInvocationsCommandInput,
  ...rest: any[]
) => Paginator<ListInvocationsCommandOutput>;
