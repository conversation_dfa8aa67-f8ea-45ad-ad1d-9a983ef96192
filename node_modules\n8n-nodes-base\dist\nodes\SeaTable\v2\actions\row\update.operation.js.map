{"version": 3, "sources": ["../../../../../../nodes/SeaTable/v2/actions/row/update.operation.ts"], "sourcesContent": ["import {\n\ttype IDataObject,\n\ttype INodeExecutionData,\n\ttype INodeProperties,\n\ttype IExecuteFunctions,\n\tupdateDisplayOptions,\n} from 'n8n-workflow';\n\nimport {\n\tseaTableApiRequest,\n\tgetTableColumns,\n\tsplit,\n\trowExport,\n\tupdateAble,\n\tsplitStringColumnsToArrays,\n} from '../../GenericFunctions';\nimport type { TColumnsUiValues, TColumnValue } from '../../types';\nimport type { IRowObject } from '../Interfaces';\n\nexport const properties: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Data to Send',\n\t\tname: 'fieldsToSend',\n\t\ttype: 'options',\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Auto-Map Input Data to Columns',\n\t\t\t\tvalue: 'autoMapInputData',\n\t\t\t\tdescription: 'Use when node input properties match destination column names',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Define Below for Each Column',\n\t\t\t\tvalue: 'defineBelow',\n\t\t\t\tdescription: 'Set the value for each destination column',\n\t\t\t},\n\t\t],\n\t\tdefault: 'defineBelow',\n\t\tdescription: 'Whether to insert the input data this node receives in the new row',\n\t},\n\t{\n\t\tdisplayName: 'Inputs to Ignore',\n\t\tname: 'inputsToIgnore',\n\t\ttype: 'string',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['row'],\n\t\t\t\toperation: ['update'],\n\t\t\t\tfieldsToSend: ['autoMapInputData'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription:\n\t\t\t'List of input properties to avoid sending, separated by commas. Leave empty to send all properties.',\n\t\tplaceholder: 'Enter properties...',\n\t},\n\t{\n\t\tdisplayName: 'Columns to Send',\n\t\tname: 'columnsUi',\n\t\tplaceholder: 'Add Column',\n\t\ttype: 'fixedCollection',\n\t\ttypeOptions: {\n\t\t\tmultipleValueButtonText: 'Add Column to Send',\n\t\t\tmultipleValues: true,\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Column',\n\t\t\t\tname: 'columnValues',\n\t\t\t\tvalues: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Column Name or ID',\n\t\t\t\t\t\tname: 'columnName',\n\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\tloadOptionsDependsOn: ['tableName'],\n\t\t\t\t\t\t\tloadOptionsMethod: 'getTableUpdateAbleColumns',\n\t\t\t\t\t\t},\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Column Value',\n\t\t\t\t\t\tname: 'columnValue',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['row'],\n\t\t\t\toperation: ['update'],\n\t\t\t\tfieldsToSend: ['defineBelow'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\tdescription:\n\t\t\t'Add destination column with its value. Provide the value in this way:Date: YYYY-MM-DD or YYYY-MM-DD hh:mmDuration: time in secondsCheckbox: true, on or 1Multi-Select: comma-separated list.',\n\t},\n\t{\n\t\tdisplayName: 'Hint: Link, files, images or digital signatures have to be added separately.',\n\t\tname: 'notice',\n\t\ttype: 'notice',\n\t\tdefault: '',\n\t},\n];\n\nconst displayOptions = {\n\tshow: {\n\t\tresource: ['row'],\n\t\toperation: ['update'],\n\t},\n};\n\nexport const description = updateDisplayOptions(displayOptions, properties);\n\nexport async function execute(\n\tthis: IExecuteFunctions,\n\tindex: number,\n): Promise<INodeExecutionData[]> {\n\tconst tableName = this.getNodeParameter('tableName', index) as string;\n\tconst tableColumns = await getTableColumns.call(this, tableName);\n\tconst fieldsToSend = this.getNodeParameter('fieldsToSend', index) as\n\t\t| 'defineBelow'\n\t\t| 'autoMapInputData';\n\tconst rowId = this.getNodeParameter('rowId', index) as string;\n\n\tlet rowInput = {} as IRowObject;\n\n\t// get rowInput, an object of key:value pairs like { Name: 'Promo Action 1', Status: \"Draft\" }.\n\tif (fieldsToSend === 'autoMapInputData') {\n\t\tconst items = this.getInputData();\n\t\tconst incomingKeys = Object.keys(items[index].json);\n\t\tconst inputDataToIgnore = split(this.getNodeParameter('inputsToIgnore', index, '') as string);\n\t\tfor (const key of incomingKeys) {\n\t\t\tif (inputDataToIgnore.includes(key)) continue;\n\t\t\trowInput[key] = items[index].json[key] as TColumnValue;\n\t\t}\n\t} else {\n\t\tconst columns = this.getNodeParameter('columnsUi.columnValues', index, []) as TColumnsUiValues;\n\t\tfor (const column of columns) {\n\t\t\trowInput[column.columnName] = column.columnValue;\n\t\t}\n\t}\n\n\t// only keep key:value pairs for columns that are allowed to update.\n\trowInput = rowExport(rowInput, updateAble(tableColumns));\n\n\t// string to array: multi-select and collaborators\n\trowInput = splitStringColumnsToArrays(rowInput, tableColumns);\n\n\tconst body = {\n\t\ttable_name: tableName,\n\t\tupdates: [\n\t\t\t{\n\t\t\t\trow_id: rowId,\n\t\t\t\trow: rowInput,\n\t\t\t},\n\t\t],\n\t} as IDataObject;\n\n\tconst responseData = await seaTableApiRequest.call(\n\t\tthis,\n\t\t{},\n\t\t'PUT',\n\t\t'/api-gateway/api/v2/dtables/{{dtable_uuid}}/rows/',\n\t\tbody,\n\t);\n\n\treturn this.helpers.returnJsonArray(responseData as IDataObject[]);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAMO;AAEP,8BAOO;AAIA,MAAM,aAAgC;AAAA,EAC5C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,KAAK;AAAA,QAChB,WAAW,CAAC,QAAQ;AAAA,QACpB,cAAc,CAAC,kBAAkB;AAAA,MAClC;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aACC;AAAA,IACD,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,yBAAyB;AAAA,MACzB,gBAAgB;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,QAAQ;AAAA,UACP;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,aACC;AAAA,YACD,aAAa;AAAA,cACZ,sBAAsB,CAAC,WAAW;AAAA,cAClC,mBAAmB;AAAA,YACpB;AAAA,YACA,SAAS;AAAA,UACV;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,KAAK;AAAA,QAChB,WAAW,CAAC,QAAQ;AAAA,QACpB,cAAc,CAAC,aAAa;AAAA,MAC7B;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACV;AACD;AAEA,MAAM,iBAAiB;AAAA,EACtB,MAAM;AAAA,IACL,UAAU,CAAC,KAAK;AAAA,IAChB,WAAW,CAAC,QAAQ;AAAA,EACrB;AACD;AAEO,MAAM,kBAAc,0CAAqB,gBAAgB,UAAU;AAE1E,eAAsB,QAErB,OACgC;AAChC,QAAM,YAAY,KAAK,iBAAiB,aAAa,KAAK;AAC1D,QAAM,eAAe,MAAM,wCAAgB,KAAK,MAAM,SAAS;AAC/D,QAAM,eAAe,KAAK,iBAAiB,gBAAgB,KAAK;AAGhE,QAAM,QAAQ,KAAK,iBAAiB,SAAS,KAAK;AAElD,MAAI,WAAW,CAAC;AAGhB,MAAI,iBAAiB,oBAAoB;AACxC,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,eAAe,OAAO,KAAK,MAAM,KAAK,EAAE,IAAI;AAClD,UAAM,wBAAoB,+BAAM,KAAK,iBAAiB,kBAAkB,OAAO,EAAE,CAAW;AAC5F,eAAW,OAAO,cAAc;AAC/B,UAAI,kBAAkB,SAAS,GAAG,EAAG;AACrC,eAAS,GAAG,IAAI,MAAM,KAAK,EAAE,KAAK,GAAG;AAAA,IACtC;AAAA,EACD,OAAO;AACN,UAAM,UAAU,KAAK,iBAAiB,0BAA0B,OAAO,CAAC,CAAC;AACzE,eAAW,UAAU,SAAS;AAC7B,eAAS,OAAO,UAAU,IAAI,OAAO;AAAA,IACtC;AAAA,EACD;AAGA,iBAAW,mCAAU,cAAU,oCAAW,YAAY,CAAC;AAGvD,iBAAW,oDAA2B,UAAU,YAAY;AAE5D,QAAM,OAAO;AAAA,IACZ,YAAY;AAAA,IACZ,SAAS;AAAA,MACR;AAAA,QACC,QAAQ;AAAA,QACR,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AAEA,QAAM,eAAe,MAAM,2CAAmB;AAAA,IAC7C;AAAA,IACA,CAAC;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAEA,SAAO,KAAK,QAAQ,gBAAgB,YAA6B;AAClE;", "names": []}