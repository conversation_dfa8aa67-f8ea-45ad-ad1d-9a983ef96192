{"version": 3, "sources": ["../../../../nodes/TheHiveProject/helpers/utils.ts"], "sourcesContent": ["import get from 'lodash/get';\nimport set from 'lodash/set';\nimport { ApplicationError, type IDataObject } from 'n8n-workflow';\n\nexport function splitAndTrim(str: string | string[]) {\n\tif (typeof str === 'string') {\n\t\treturn str\n\t\t\t.split(',')\n\t\t\t.map((tag) => tag.trim())\n\t\t\t.filter((tag) => tag);\n\t}\n\treturn str;\n}\n\nexport function fixFieldType(fields: IDataObject) {\n\tconst returnData: IDataObject = {};\n\n\tfor (const key of Object.keys(fields)) {\n\t\tif (\n\t\t\t[\n\t\t\t\t'date',\n\t\t\t\t'lastSyncDate',\n\t\t\t\t'startDate',\n\t\t\t\t'endDate',\n\t\t\t\t'dueDate',\n\t\t\t\t'includeInTimeline',\n\t\t\t\t'sightedAt',\n\t\t\t].includes(key)\n\t\t) {\n\t\t\treturnData[key] = Date.parse(fields[key] as string);\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (['tags', 'addTags', 'removeTags'].includes(key)) {\n\t\t\treturnData[key] = splitAndTrim(fields[key] as string);\n\t\t\tcontinue;\n\t\t}\n\n\t\treturnData[key] = fields[key];\n\t}\n\n\treturn returnData;\n}\n\nexport function prepareInputItem(item: IDataObject, schema: IDataObject[], i: number) {\n\tconst returnData: IDataObject = {};\n\n\tfor (const entry of schema) {\n\t\tconst id = entry.id as string;\n\t\tconst value = get(item, id);\n\n\t\tif (value !== undefined) {\n\t\t\tset(returnData, id, value);\n\t\t} else {\n\t\t\tif (entry.required) {\n\t\t\t\tthrow new ApplicationError(`Required field \"${id}\" is missing in item ${i}`, {\n\t\t\t\t\tlevel: 'warning',\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\treturn returnData;\n}\n\nexport function constructFilter(entry: IDataObject) {\n\tconst { field, value } = entry;\n\tlet { operator } = entry;\n\n\tif (operator === undefined) {\n\t\toperator = '_eq';\n\t}\n\n\tif (operator === '_between') {\n\t\tconst { from, to } = entry;\n\t\treturn {\n\t\t\t_between: {\n\t\t\t\t_field: field,\n\t\t\t\t_from: from,\n\t\t\t\t_to: to,\n\t\t\t},\n\t\t};\n\t}\n\n\tif (operator === '_in') {\n\t\tconst { values } = entry;\n\t\treturn {\n\t\t\t_in: {\n\t\t\t\t_field: field,\n\t\t\t\t_values: typeof values === 'string' ? splitAndTrim(values) : values,\n\t\t\t},\n\t\t};\n\t}\n\n\treturn {\n\t\t[operator as string]: {\n\t\t\t_field: field,\n\t\t\t_value: value,\n\t\t},\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAgB;AAChB,iBAAgB;AAChB,0BAAmD;AAE5C,SAAS,aAAa,KAAwB;AACpD,MAAI,OAAO,QAAQ,UAAU;AAC5B,WAAO,IACL,MAAM,GAAG,EACT,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,EACvB,OAAO,CAAC,QAAQ,GAAG;AAAA,EACtB;AACA,SAAO;AACR;AAEO,SAAS,aAAa,QAAqB;AACjD,QAAM,aAA0B,CAAC;AAEjC,aAAW,OAAO,OAAO,KAAK,MAAM,GAAG;AACtC,QACC;AAAA,MACC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD,EAAE,SAAS,GAAG,GACb;AACD,iBAAW,GAAG,IAAI,KAAK,MAAM,OAAO,GAAG,CAAW;AAClD;AAAA,IACD;AAEA,QAAI,CAAC,QAAQ,WAAW,YAAY,EAAE,SAAS,GAAG,GAAG;AACpD,iBAAW,GAAG,IAAI,aAAa,OAAO,GAAG,CAAW;AACpD;AAAA,IACD;AAEA,eAAW,GAAG,IAAI,OAAO,GAAG;AAAA,EAC7B;AAEA,SAAO;AACR;AAEO,SAAS,iBAAiB,MAAmB,QAAuB,GAAW;AACrF,QAAM,aAA0B,CAAC;AAEjC,aAAW,SAAS,QAAQ;AAC3B,UAAM,KAAK,MAAM;AACjB,UAAM,YAAQ,WAAAA,SAAI,MAAM,EAAE;AAE1B,QAAI,UAAU,QAAW;AACxB,qBAAAC,SAAI,YAAY,IAAI,KAAK;AAAA,IAC1B,OAAO;AACN,UAAI,MAAM,UAAU;AACnB,cAAM,IAAI,qCAAiB,mBAAmB,EAAE,wBAAwB,CAAC,IAAI;AAAA,UAC5E,OAAO;AAAA,QACR,CAAC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AAEO,SAAS,gBAAgB,OAAoB;AACnD,QAAM,EAAE,OAAO,MAAM,IAAI;AACzB,MAAI,EAAE,SAAS,IAAI;AAEnB,MAAI,aAAa,QAAW;AAC3B,eAAW;AAAA,EACZ;AAEA,MAAI,aAAa,YAAY;AAC5B,UAAM,EAAE,MAAM,GAAG,IAAI;AACrB,WAAO;AAAA,MACN,UAAU;AAAA,QACT,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AAEA,MAAI,aAAa,OAAO;AACvB,UAAM,EAAE,OAAO,IAAI;AACnB,WAAO;AAAA,MACN,KAAK;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS,OAAO,WAAW,WAAW,aAAa,MAAM,IAAI;AAAA,MAC9D;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AAAA,IACN,CAAC,QAAkB,GAAG;AAAA,MACrB,QAAQ;AAAA,MACR,QAAQ;AAAA,IACT;AAAA,EACD;AACD;", "names": ["get", "set"]}