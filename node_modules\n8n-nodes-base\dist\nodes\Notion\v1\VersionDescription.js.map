{"version": 3, "sources": ["../../../../nodes/Notion/v1/VersionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport { NodeConnectionTypes, type INodeTypeDescription } from 'n8n-workflow';\n\nimport { blockFields, blockOperations } from '../shared/descriptions/BlockDescription';\nimport { databaseFields, databaseOperations } from '../shared/descriptions/DatabaseDescription';\nimport {\n\tdatabasePageFields,\n\tdatabasePageOperations,\n} from '../shared/descriptions/DatabasePageDescription';\nimport { pageFields, pageOperations } from '../shared/descriptions/PageDescription';\nimport { userFields, userOperations } from '../shared/descriptions/UserDescription';\n\nexport const versionDescription: INodeTypeDescription = {\n\tdisplayName: 'Notion',\n\tname: 'notion',\n\ticon: 'file:notion.svg',\n\tgroup: ['output'],\n\tversion: 1,\n\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\tdescription: 'Consume Notion API',\n\tdefaults: {\n\t\tname: 'Notion',\n\t},\n\tinputs: [NodeConnectionTypes.Main],\n\toutputs: [NodeConnectionTypes.Main],\n\tcredentials: [\n\t\t{\n\t\t\tname: 'notionApi',\n\t\t\trequired: true,\n\t\t\t// displayOptions: {\n\t\t\t// \tshow: {\n\t\t\t// \t\tauthentication: [\n\t\t\t// \t\t\t'apiKey',\n\t\t\t// \t\t],\n\t\t\t// \t},\n\t\t\t// },\n\t\t},\n\t\t// {\n\t\t// \tname: 'notionOAuth2Api',\n\t\t// \trequired: true,\n\t\t// \tdisplayOptions: {\n\t\t// \t\tshow: {\n\t\t// \t\t\tauthentication: [\n\t\t// \t\t\t\t'oAuth2',\n\t\t// \t\t\t],\n\t\t// \t\t},\n\t\t// \t},\n\t\t// },\n\t],\n\tproperties: [\n\t\t// {\n\t\t// \tdisplayName: 'Authentication',\n\t\t// \tname: 'authentication',\n\t\t// \ttype: 'options',\n\t\t// \toptions: [\n\t\t// \t\t{\n\t\t// \t\t\tname: 'API Key',\n\t\t// \t\t\tvalue: 'apiKey',\n\t\t// \t\t},\n\t\t// \t\t{\n\t\t// \t\t\tname: 'OAuth2',\n\t\t// \t\t\tvalue: 'oAuth2',\n\t\t// \t\t},\n\t\t// \t],\n\t\t// \tdefault: 'apiKey',\n\t\t// \tdescription: 'The resource to operate on.',\n\t\t// },\n\t\t{\n\t\t\tdisplayName:\n\t\t\t\t'In Notion, make sure to <a href=\"https://www.notion.so/help/add-and-manage-connections-with-the-api\" target=\"_blank\">add your connection</a> to the pages you want to access.',\n\t\t\tname: 'notionNotice',\n\t\t\ttype: 'notice',\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Block',\n\t\t\t\t\tvalue: 'block',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Database',\n\t\t\t\t\tvalue: 'database',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Database Page',\n\t\t\t\t\tvalue: 'databasePage',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Page',\n\t\t\t\t\tvalue: 'page',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'User',\n\t\t\t\t\tvalue: 'user',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'page',\n\t\t},\n\t\t...blockOperations,\n\t\t...blockFields,\n\t\t...databaseOperations,\n\t\t...databaseFields,\n\t\t...databasePageOperations,\n\t\t...databasePageFields,\n\t\t...pageOperations,\n\t\t...pageFields,\n\t\t...userOperations,\n\t\t...userFields,\n\t],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAA+D;AAE/D,8BAA6C;AAC7C,iCAAmD;AACnD,qCAGO;AACP,6BAA2C;AAC3C,6BAA2C;AAEpC,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,QAAQ;AAAA,EAChB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,aAAa;AAAA,IACZ;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYD;AAAA,EACA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAkBX;AAAA,MACC,aACC;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACJ;AACD;", "names": []}