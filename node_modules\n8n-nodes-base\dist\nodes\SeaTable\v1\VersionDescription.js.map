{"version": 3, "sources": ["../../../../nodes/SeaTable/v1/VersionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport { NodeConnectionTypes, type INodeTypeDescription } from 'n8n-workflow';\n\nimport { rowFields, rowOperations } from './RowDescription';\n\nexport const versionDescription: INodeTypeDescription = {\n\tdisplayName: 'SeaTable',\n\tname: 'seaTable',\n\ticon: 'file:seaTable.svg',\n\tgroup: ['input'],\n\tversion: 1,\n\tsubtitle: '={{$parameter[\"resource\"] + \": \" + $parameter[\"operation\"]}}',\n\tdescription: 'Consume the SeaTable API',\n\tdefaults: {\n\t\tname: 'SeaTable',\n\t},\n\tinputs: [NodeConnectionTypes.Main],\n\toutputs: [NodeConnectionTypes.Main],\n\tcredentials: [\n\t\t{\n\t\t\tname: 'seaTableApi',\n\t\t\trequired: true,\n\t\t},\n\t],\n\tproperties: [\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Row',\n\t\t\t\t\tvalue: 'row',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'row',\n\t\t},\n\t\t...rowOperations,\n\t\t...rowFields,\n\t],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAA+D;AAE/D,4BAAyC;AAElC,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,OAAO;AAAA,EACf,SAAS;AAAA,EACT,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,aAAa;AAAA,IACZ;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA,EACD;AAAA,EACA,YAAY;AAAA,IACX;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA,GAAG;AAAA,IACH,GAAG;AAAA,EACJ;AACD;", "names": []}