{"version": 3, "sources": ["../../../nodes/N8n/WorkflowDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nimport {\n\tgetCursorPaginator,\n\tparseAndSetBodyJson,\n\tprepareWorkflowCreateBody,\n\tprepareWorkflowUpdateBody,\n} from './GenericFunctions';\nimport { workflowIdLocator } from './WorkflowLocator';\n\nexport const workflowOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdefault: 'getAll',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workflow'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Activate',\n\t\t\t\tvalue: 'activate',\n\t\t\t\taction: 'Activate a workflow',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Create',\n\t\t\t\tvalue: 'create',\n\t\t\t\taction: 'Create a workflow',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\turl: '/workflows',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Deactivate',\n\t\t\t\tvalue: 'deactivate',\n\t\t\t\taction: 'Deactivate a workflow',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'delete',\n\t\t\t\taction: 'Delete a workflow',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\taction: 'Get a workflow',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\taction: 'Get many workflows',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\t\turl: '/workflows',\n\t\t\t\t\t},\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tpaginate: true,\n\t\t\t\t\t},\n\t\t\t\t\toperations: {\n\t\t\t\t\t\tpagination: getCursorPaginator(),\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Update',\n\t\t\t\tvalue: 'update',\n\t\t\t\taction: 'Update a workflow',\n\t\t\t},\n\t\t],\n\t},\n];\n\nconst activateOperation: INodeProperties[] = [\n\t{\n\t\t...workflowIdLocator,\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workflow'],\n\t\t\t\toperation: ['activate'],\n\t\t\t},\n\t\t},\n\t\t// The routing for resourceLocator-enabled properties currently needs to\n\t\t// happen in the property block where the property itself is defined, or\n\t\t// extractValue won't work when used with $parameter in routing.request.url.\n\t\trouting: {\n\t\t\trequest: {\n\t\t\t\tmethod: 'POST',\n\t\t\t\turl: '=/workflows/{{ $value }}/activate',\n\t\t\t},\n\t\t},\n\t},\n];\n\nconst createOperation: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Workflow Object',\n\t\tname: 'workflowObject',\n\t\ttype: 'json',\n\t\tdefault: '{ \"name\": \"My workflow\", \"nodes\": [], \"connections\": {}, \"settings\": {} }',\n\t\tplaceholder:\n\t\t\t'{\\n  \"name\": \"My workflow\",\\n  \"nodes\": [],\\n  \"connections\": {},\\n  \"settings\": {}\\n}',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\talwaysOpenEditWindow: true,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workflow'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tpreSend: [parseAndSetBodyJson('workflowObject'), prepareWorkflowCreateBody],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t\"A valid JSON object with required fields: 'name', 'nodes', 'connections' and 'settings'. More information can be found in the <a href=\\\"https://docs.n8n.io/api/api-reference/#tag/Workflow/paths/~1workflows/post\\\">documentation</a>.\",\n\t},\n];\n\nconst deactivateOperation: INodeProperties[] = [\n\t{\n\t\t...workflowIdLocator,\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workflow'],\n\t\t\t\toperation: ['deactivate'],\n\t\t\t},\n\t\t},\n\t\trouting: {\n\t\t\trequest: {\n\t\t\t\tmethod: 'POST',\n\t\t\t\turl: '=/workflows/{{ $value }}/deactivate',\n\t\t\t},\n\t\t},\n\t},\n];\n\nconst deleteOperation: INodeProperties[] = [\n\t{\n\t\t...workflowIdLocator,\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workflow'],\n\t\t\t\toperation: ['delete'],\n\t\t\t},\n\t\t},\n\t\trouting: {\n\t\t\trequest: {\n\t\t\t\tmethod: 'DELETE',\n\t\t\t\turl: '=/workflows/{{ $value }}',\n\t\t\t},\n\t\t},\n\t},\n];\n\nconst getAllOperation: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdefault: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workflow'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdefault: 100,\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 250,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workflow'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\trouting: {\n\t\t\trequest: {\n\t\t\t\tqs: {\n\t\t\t\t\tlimit: '={{ $value }}',\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\tdescription: 'Max number of results to return',\n\t},\n\t{\n\t\tdisplayName: 'Filters',\n\t\tname: 'filters',\n\t\ttype: 'collection',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workflow'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Return Only Active Workflows',\n\t\t\t\tname: 'activeWorkflows',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: true,\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tqs: {\n\t\t\t\t\t\t\tactive: '={{ $value }}',\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Tags',\n\t\t\t\tname: 'tags',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\trouting: {\n\t\t\t\t\t// Only include the 'tags' query parameter if it's non-empty\n\t\t\t\t\tsend: {\n\t\t\t\t\t\ttype: 'query',\n\t\t\t\t\t\tproperty: 'tags',\n\t\t\t\t\t\tvalue: '={{ $value !== \"\" ? $value : undefined }}',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdescription: 'Include only workflows with these tags',\n\t\t\t\thint: 'Comma separated list of tags (empty value is ignored)',\n\t\t\t},\n\t\t],\n\t},\n];\n\nconst getOperation: INodeProperties[] = [\n\t{\n\t\t...workflowIdLocator,\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workflow'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t\trouting: {\n\t\t\trequest: {\n\t\t\t\tmethod: 'GET',\n\t\t\t\turl: '=/workflows/{{ $value }}',\n\t\t\t},\n\t\t},\n\t},\n];\n\nconst updateOperation: INodeProperties[] = [\n\t{\n\t\t...workflowIdLocator,\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workflow'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t\trouting: {\n\t\t\trequest: {\n\t\t\t\tmethod: 'PUT',\n\t\t\t\turl: '=/workflows/{{ $value }}',\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Workflow Object',\n\t\tname: 'workflowObject',\n\t\ttype: 'json',\n\t\tdefault: '',\n\t\tplaceholder:\n\t\t\t'{\\n  \"name\": \"My workflow\",\\n  \"nodes\": [],\\n  \"connections\": {},\\n  \"settings\": {}\\n}',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\talwaysOpenEditWindow: true,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['workflow'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tpreSend: [parseAndSetBodyJson('workflowObject'), prepareWorkflowUpdateBody],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t\"A valid JSON object with required fields: 'name', 'nodes', 'connections' and 'settings'. More information can be found in the <a href=\\\"https://docs.n8n.io/api/api-reference/#tag/Workflow/paths/~1workflows~1%7Bid%7D/put\\\">documentation</a>.\",\n\t},\n];\n\nexport const workflowFields: INodeProperties[] = [\n\t...activateOperation,\n\t...createOperation,\n\t...deactivateOperation,\n\t...deleteOperation,\n\t...getAllOperation,\n\t...getOperation,\n\t...updateOperation,\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,8BAKO;AACP,6BAAkC;AAE3B,MAAM,qBAAwC;AAAA,EACpD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,MACtB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,UACN;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,UACN;AAAA,UACA,MAAM;AAAA,YACL,UAAU;AAAA,UACX;AAAA,UACA,YAAY;AAAA,YACX,gBAAY,4CAAmB;AAAA,UAChC;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,EACD;AACD;AAEA,MAAM,oBAAuC;AAAA,EAC5C;AAAA,IACC,GAAG;AAAA,IACH,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,UAAU;AAAA,MACvB;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAIA,SAAS;AAAA,MACR,SAAS;AAAA,QACR,QAAQ;AAAA,QACR,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAEA,MAAM,kBAAqC;AAAA,EAC1C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aACC;AAAA,IACD,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,sBAAsB;AAAA,IACvB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR,MAAM;AAAA,QACL,SAAS,KAAC,6CAAoB,gBAAgB,GAAG,iDAAyB;AAAA,MAC3E;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AACD;AAEA,MAAM,sBAAyC;AAAA,EAC9C;AAAA,IACC,GAAG;AAAA,IACH,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,YAAY;AAAA,MACzB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR,SAAS;AAAA,QACR,QAAQ;AAAA,QACR,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAEA,MAAM,kBAAqC;AAAA,EAC1C;AAAA,IACC,GAAG;AAAA,IACH,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR,SAAS;AAAA,QACR,QAAQ;AAAA,QACR,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAEA,MAAM,kBAAqC;AAAA,EAC1C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,QACpB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR,SAAS;AAAA,QACR,IAAI;AAAA,UACH,OAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,UACR,SAAS;AAAA,YACR,IAAI;AAAA,cACH,QAAQ;AAAA,YACT;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA;AAAA,UAER,MAAM;AAAA,YACL,MAAM;AAAA,YACN,UAAU;AAAA,YACV,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,aAAa;AAAA,QACb,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AACD;AAEA,MAAM,eAAkC;AAAA,EACvC;AAAA,IACC,GAAG;AAAA,IACH,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR,SAAS;AAAA,QACR,QAAQ;AAAA,QACR,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAEA,MAAM,kBAAqC;AAAA,EAC1C;AAAA,IACC,GAAG;AAAA,IACH,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR,SAAS;AAAA,QACR,QAAQ;AAAA,QACR,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aACC;AAAA,IACD,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,sBAAsB;AAAA,IACvB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR,MAAM;AAAA,QACL,SAAS,KAAC,6CAAoB,gBAAgB,GAAG,iDAAyB;AAAA,MAC3E;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AACD;AAEO,MAAM,iBAAoC;AAAA,EAChD,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACJ;", "names": []}