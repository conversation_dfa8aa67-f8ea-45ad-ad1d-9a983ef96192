{"version": 3, "sources": ["../../credentials/WhatsAppTriggerApi.credentials.ts"], "sourcesContent": ["import type { ICredentialTestRequest, ICredentialType, INodeProperties } from 'n8n-workflow';\n\nexport class WhatsAppTriggerApi implements ICredentialType {\n\tname = 'whatsAppTriggerApi';\n\n\tdisplayName = 'WhatsApp OAuth API';\n\n\tdocumentationUrl = 'whatsApp';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Client ID',\n\t\t\tname: 'clientId',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Client Secret',\n\t\t\tname: 'clientSecret',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: {\n\t\t\t\tpassword: true,\n\t\t\t},\n\t\t\tdefault: '',\n\t\t\trequired: true,\n\t\t},\n\t];\n\n\ttest: ICredentialTestRequest = {\n\t\trequest: {\n\t\t\tmethod: 'POST',\n\t\t\tbaseURL: 'https://graph.facebook.com/v19.0/oauth/access_token',\n\t\t\tbody: {\n\t\t\t\tclient_id: '={{$credentials.clientId}}',\n\t\t\t\tclient_secret: '={{$credentials.clientSecret}}',\n\t\t\t\tgrant_type: 'client_credentials',\n\t\t\t},\n\t\t},\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,mBAA8C;AAAA,EAApD;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,SAAS;AAAA,QACT,UAAU;AAAA,MACX;AAAA,IACD;AAEA,gBAA+B;AAAA,MAC9B,SAAS;AAAA,QACR,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,UACL,WAAW;AAAA,UACX,eAAe;AAAA,UACf,YAAY;AAAA,QACb;AAAA,MACD;AAAA,IACD;AAAA;AACD;", "names": []}