{"version": 3, "sources": ["../../../nodes/QuickBooks/types.ts"], "sourcesContent": ["import type { IDataObject } from 'n8n-workflow';\n\nexport type QuickBooksOAuth2Credentials = {\n\tenvironment: 'production' | 'sandbox';\n\toauthTokenData: {\n\t\tcallbackQueryString: {\n\t\t\trealmId: string;\n\t\t};\n\t};\n};\n\nexport type DateFieldsUi = Partial<{\n\tdateRangeCustom: DateFieldUi;\n\tdateRangeDueCustom: DateFieldUi;\n\tdateRangeModificationCustom: DateFieldUi;\n\tdateRangeCreationCustom: DateFieldUi;\n}>;\n\ntype DateFieldUi = {\n\t[key: string]: {\n\t\t[key: string]: string;\n\t};\n};\n\nexport type TransactionFields = Partial<{\n\tcolumns: string[];\n\tmemo: string[];\n\tterm: string[];\n\tcustomer: string[];\n\tvendor: string[];\n}> &\n\tDateFieldsUi &\n\tIDataObject;\n\nexport type Option = { name: string; value: string };\n\nexport type TransactionReport = {\n\tColumns: {\n\t\tColumn: Array<{\n\t\t\tColTitle: string;\n\t\t\tColType: string;\n\t\t}>;\n\t};\n\tRows: {\n\t\tRow: Array<{\n\t\t\tColData: Array<{ value: string }>;\n\t\t}>;\n\t};\n};\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}