{"version": 3, "sources": ["../../../nodes/ServiceNow/UserRoleDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const userRoleOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userRole'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\taction: 'Get many user roles',\n\t\t\t},\n\t\t],\n\t\tdefault: 'getAll',\n\t},\n];\n\nexport const userRoleFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                                userRole:getAll                      */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['userRole'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['userRole'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 500,\n\t\t},\n\t\tdefault: 50,\n\t\tdescription: 'Max number of results to return',\n\t},\n\t{\n\t\tdisplayName: 'Options',\n\t\tname: 'options',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userRole'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Exclude Reference Link',\n\t\t\t\tname: 'sysparm_exclude_reference_link',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether to exclude Table API links for reference fields',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Field Names or IDs',\n\t\t\t\tname: 'sysparm_fields',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getColumns',\n\t\t\t\t},\n\t\t\t\tdefault: [],\n\t\t\t\tdescription:\n\t\t\t\t\t'A list of fields to return. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\thint: 'String of comma separated values or an array of strings can be set in an expression',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Filter',\n\t\t\t\tname: 'sysparm_query',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'An encoded query string used to filter the results. <a href=\"https://developer.servicenow.com/dev.do#!/learn/learning-plans/quebec/servicenow_application_developer/app_store_learnv2_rest_quebec_more_about_query_parameters\">More info</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Return Values',\n\t\t\t\tname: 'sysparm_display_value',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Actual Values',\n\t\t\t\t\t\tvalue: 'false',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Both',\n\t\t\t\t\t\tvalue: 'all',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Display Values',\n\t\t\t\t\t\tvalue: 'true',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'false',\n\t\t\t\tdescription: 'Choose which values to return',\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,qBAAwC;AAAA,EACpD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,MACtB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,iBAAoC;AAAA;AAAA;AAAA;AAAA,EAIhD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,UAAU;AAAA,MACtB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS,CAAC;AAAA,QACV,aACC;AAAA,QACD,MAAM;AAAA,MACP;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AACD;", "names": []}