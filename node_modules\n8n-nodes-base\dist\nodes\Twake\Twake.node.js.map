{"version": 3, "sources": ["../../../nodes/Twake/Twake.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tIDataObject,\n\tILoadOptionsFunctions,\n\tINodeExecutionData,\n\tINodePropertyOptions,\n\tINodeType,\n\tINodeTypeDescription,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes, NodeOperationError } from 'n8n-workflow';\n\nimport { twakeApiRequest } from './GenericFunctions';\n\nexport class Twake implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Twake',\n\t\tname: 'twake',\n\t\tgroup: ['transform'],\n\t\tversion: 1,\n\t\t// eslint-disable-next-line n8n-nodes-base/node-class-description-icon-not-svg\n\t\ticon: 'file:twake.png',\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume Twake API',\n\t\tdefaults: {\n\t\t\tname: 'Twake',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'twakeCloudApi',\n\t\t\t\trequired: true,\n\t\t\t\t// displayOptions: {\n\t\t\t\t// \tshow: {\n\t\t\t\t// \t\ttwakeVersion: [\n\t\t\t\t// \t\t\t'cloud',\n\t\t\t\t// \t\t],\n\t\t\t\t// \t},\n\t\t\t\t// },\n\t\t\t},\n\t\t\t// {\n\t\t\t// \tname: 'twakeServerApi',\n\t\t\t// \trequired: true,\n\t\t\t// \tdisplayOptions: {\n\t\t\t// \t\tshow: {\n\t\t\t// \t\t\ttwakeVersion: [\n\t\t\t// \t\t\t\t'server',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t},\n\t\t\t// \t},\n\t\t\t// },\n\t\t],\n\t\tproperties: [\n\t\t\t// {\n\t\t\t// \tdisplayName: 'Twake Version',\n\t\t\t// \tname: 'twakeVersion',\n\t\t\t// \ttype: 'options',\n\t\t\t// \toptions: [\n\t\t\t// \t\t{\n\t\t\t// \t\t\tname: 'Cloud',\n\t\t\t// \t\t\tvalue: 'cloud',\n\t\t\t// \t\t},\n\t\t\t// \t\t{\n\t\t\t// \t\t\tname: 'Server (Self Hosted)',\n\t\t\t// \t\t\tvalue: 'server',\n\t\t\t// \t\t},\n\t\t\t// \t],\n\t\t\t// \tdefault: 'cloud',\n\t\t\t// },\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Message',\n\t\t\t\t\t\tvalue: 'message',\n\t\t\t\t\t\tdescription: 'Send data to the message app',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'message',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Operation',\n\t\t\t\tname: 'operation',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresource: ['message'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Send',\n\t\t\t\t\t\tvalue: 'send',\n\t\t\t\t\t\tdescription: 'Send a message',\n\t\t\t\t\t\taction: 'Send a message',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'send',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Channel Name or ID',\n\t\t\t\tname: 'channelId',\n\t\t\t\ttype: 'options',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getChannels',\n\t\t\t\t},\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\toperation: ['send'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'Channel\\'s ID. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Content',\n\t\t\t\tname: 'content',\n\t\t\t\ttype: 'string',\n\t\t\t\trequired: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\toperation: ['send'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Message content',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Additional Fields',\n\t\t\t\tname: 'additionalFields',\n\t\t\t\ttype: 'collection',\n\t\t\t\tplaceholder: 'Add Field',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\toperation: ['send'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Sender Icon',\n\t\t\t\t\t\tname: 'senderIcon',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription: 'URL of the image/icon',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Sender Name',\n\t\t\t\t\t\tname: 'senderName',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\tasync getChannels(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst responseData = await twakeApiRequest.call(this, 'POST', '/channel', {});\n\t\t\t\tif (responseData === undefined) {\n\t\t\t\t\tthrow new NodeOperationError(this.getNode(), 'No data got returned');\n\t\t\t\t}\n\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tfor (const channel of responseData) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: channel.name,\n\t\t\t\t\t\tvalue: channel.id,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: IDataObject[] = [];\n\t\tconst length = items.length;\n\t\tlet responseData;\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tif (resource === 'message') {\n\t\t\t\tif (operation === 'send') {\n\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\tconst message: IDataObject = {\n\t\t\t\t\t\tchannel_id: this.getNodeParameter('channelId', i),\n\t\t\t\t\t\tcontent: {\n\t\t\t\t\t\t\tformatted: this.getNodeParameter('content', i) as string,\n\t\t\t\t\t\t},\n\t\t\t\t\t\thidden_data: {\n\t\t\t\t\t\t\tallow_delete: 'everyone',\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\n\t\t\t\t\tif (additionalFields.senderName) {\n\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\tmessage.hidden_data!.custom_title = additionalFields.senderName as string;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (additionalFields.senderIcon) {\n\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\tmessage.hidden_data!.custom_icon = additionalFields.senderIcon as string;\n\t\t\t\t\t}\n\n\t\t\t\t\tconst body = {\n\t\t\t\t\t\tobject: message,\n\t\t\t\t\t};\n\n\t\t\t\t\tconst endpoint = '/actions/message/save';\n\n\t\t\t\t\tresponseData = await twakeApiRequest.call(this, 'POST', endpoint, body);\n\n\t\t\t\t\tresponseData = responseData.object;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (Array.isArray(responseData)) {\n\t\t\treturnData.push.apply(returnData, responseData as IDataObject[]);\n\t\t} else if (responseData !== undefined) {\n\t\t\treturnData.push(responseData as IDataObject);\n\t\t}\n\t\treturn [this.helpers.returnJsonArray(returnData)];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,0BAAwD;AAExD,8BAAgC;AAEzB,MAAM,MAA2B;AAAA,EAAjC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO,CAAC,WAAW;AAAA,MACnB,SAAS;AAAA;AAAA,MAET,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAYD;AAAA,MACA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAiBX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,UAAU,CAAC,SAAS;AAAA,YACrB;AAAA,UACD;AAAA,UACA,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,YACT;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,YACZ,mBAAmB;AAAA,UACpB;AAAA,UACA,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,WAAW,CAAC,MAAM;AAAA,YACnB;AAAA,UACD;AAAA,UACA,SAAS;AAAA,UACT,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,WAAW,CAAC,MAAM;AAAA,YACnB;AAAA,UACD;AAAA,UACA,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,WAAW,CAAC,MAAM;AAAA,YACnB;AAAA,UACD;AAAA,UACA,SAAS,CAAC;AAAA,UACV,SAAS;AAAA,YACR;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,YACV;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA,QACZ,MAAM,cAA0E;AAC/E,gBAAM,eAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,YAAY,CAAC,CAAC;AAC5E,cAAI,iBAAiB,QAAW;AAC/B,kBAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,sBAAsB;AAAA,UACpE;AAEA,gBAAM,aAAqC,CAAC;AAC5C,qBAAW,WAAW,cAAc;AACnC,uBAAW,KAAK;AAAA,cACf,MAAM,QAAQ;AAAA,cACd,OAAO,QAAQ;AAAA,YAChB,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAA4B,CAAC;AACnC,UAAM,SAAS,MAAM;AACrB,QAAI;AACJ,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI,aAAa,WAAW;AAC3B,YAAI,cAAc,QAAQ;AACzB,gBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAM,UAAuB;AAAA,YAC5B,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAAA,YAChD,SAAS;AAAA,cACR,WAAW,KAAK,iBAAiB,WAAW,CAAC;AAAA,YAC9C;AAAA,YACA,aAAa;AAAA,cACZ,cAAc;AAAA,YACf;AAAA,UACD;AAEA,cAAI,iBAAiB,YAAY;AAEhC,oBAAQ,YAAa,eAAe,iBAAiB;AAAA,UACtD;AAEA,cAAI,iBAAiB,YAAY;AAEhC,oBAAQ,YAAa,cAAc,iBAAiB;AAAA,UACrD;AAEA,gBAAM,OAAO;AAAA,YACZ,QAAQ;AAAA,UACT;AAEA,gBAAM,WAAW;AAEjB,yBAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,UAAU,IAAI;AAEtE,yBAAe,aAAa;AAAA,QAC7B;AAAA,MACD;AAAA,IACD;AACA,QAAI,MAAM,QAAQ,YAAY,GAAG;AAChC,iBAAW,KAAK,MAAM,YAAY,YAA6B;AAAA,IAChE,WAAW,iBAAiB,QAAW;AACtC,iBAAW,KAAK,YAA2B;AAAA,IAC5C;AACA,WAAO,CAAC,KAAK,QAAQ,gBAAgB,UAAU,CAAC;AAAA,EACjD;AACD;", "names": []}