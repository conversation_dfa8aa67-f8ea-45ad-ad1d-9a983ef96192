{"version": 3, "sources": ["../../../nodes/Reddit/UserDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const userOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\taction: 'Get a user',\n\t\t\t},\n\t\t],\n\t\tdefault: 'get',\n\t},\n];\n\nexport const userFields: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Username',\n\t\tname: 'username',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdescription: 'Reddit ID of the user to retrieve',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Details',\n\t\tname: 'details',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\tdefault: 'about',\n\t\tdescription: 'Details of the user to retrieve',\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'About',\n\t\t\t\tvalue: 'about',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Comments',\n\t\t\t\tvalue: 'comments',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Gilded',\n\t\t\t\tvalue: 'gilded',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Overview',\n\t\t\t\tvalue: 'overview',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Submitted',\n\t\t\t\tvalue: 'submitted',\n\t\t\t},\n\t\t],\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['get'],\n\t\t\t\tdetails: ['overview', 'submitted', 'comments', 'gilded'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdefault: 100,\n\t\tdescription: 'Max number of results to return',\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 100,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['get'],\n\t\t\t\tdetails: ['comments', 'gilded', 'overview', 'submitted'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,iBAAoC;AAAA,EAChD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,aAAgC;AAAA,EAC5C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,IACb,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,KAAK;AAAA,QACjB,SAAS,CAAC,YAAY,aAAa,YAAY,QAAQ;AAAA,MACxD;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,KAAK;AAAA,QACjB,SAAS,CAAC,YAAY,UAAU,YAAY,WAAW;AAAA,QACvD,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AACD;", "names": []}