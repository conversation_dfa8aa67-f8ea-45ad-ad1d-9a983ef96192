{"version": 3, "sources": ["../../../nodes/Coda/ViewDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const viewOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Delete Row',\n\t\t\t\tvalue: 'deleteViewRow',\n\t\t\t\tdescription: 'Delete view row',\n\t\t\t\taction: 'Delete a view row',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\tdescription: 'Get a view',\n\t\t\t\taction: 'Get a view',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Columns',\n\t\t\t\tvalue: 'getAllViewColumns',\n\t\t\t\tdescription: 'Get all views columns',\n\t\t\t\taction: 'Get all view columns',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Get many views',\n\t\t\t\taction: 'Get many views',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Rows',\n\t\t\t\tvalue: 'getAllViewRows',\n\t\t\t\tdescription: 'Get all views rows',\n\t\t\t\taction: 'Get a view row',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Push Button',\n\t\t\t\tvalue: 'pushViewButton',\n\t\t\t\tdescription: 'Push view button',\n\t\t\t\taction: 'Push a view button',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Update Row',\n\t\t\t\tvalue: 'updateViewRow',\n\t\t\t\taction: 'Update a view row',\n\t\t\t},\n\t\t],\n\t\tdefault: 'get',\n\t},\n];\n\nexport const viewFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                                   view:get                                 */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Doc Name or ID',\n\t\tname: 'docId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getDocs',\n\t\t},\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'ID of the doc. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'View ID',\n\t\tname: 'viewId',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'The view to get the row from',\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                   view:getAll                              */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Doc Name or ID',\n\t\tname: 'docId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getDocs',\n\t\t},\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'ID of the doc. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 100,\n\t\t},\n\t\tdefault: 50,\n\t\tdescription: 'Max number of results to return',\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                   view:getAllViewRows                      */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Doc Name or ID',\n\t\tname: 'docId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getDocs',\n\t\t},\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['getAllViewRows'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'ID of the doc. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'View Name or ID',\n\t\tname: 'viewId',\n\t\ttype: 'options',\n\t\ttypeOptions: {\n\t\t\tloadOptionsDependsOn: ['docId'],\n\t\t\tloadOptionsMethod: 'getViews',\n\t\t},\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['getAllViewRows'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'The table to get the rows from. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['getAllViewRows'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['getAllViewRows'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 100,\n\t\t},\n\t\tdefault: 50,\n\t\tdescription: 'Max number of results to return',\n\t},\n\t{\n\t\tdisplayName: 'Options',\n\t\tname: 'options',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add option',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['getAllViewRows'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Query',\n\t\t\t\tname: 'query',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'Query used to filter returned rows, specified as &lt;column_id_or_name&gt;:&lt;value&gt;. If you\\'d like to use a column name instead of an ID, you must quote it (e.g., \"My Column\":123). Also note that value is a JSON value; if you\\'d like to use a string, you must surround it in quotes (e.g., \"groceries\").',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Use Column Names',\n\t\t\t\tname: 'useColumnNames',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription:\n\t\t\t\t\t'Whether to use column names instead of column IDs in the returned output. This is generally discouraged as it is fragile. If columns are renamed, code using original names may throw errors.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'ValueFormat',\n\t\t\t\tname: 'valueFormat',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: '',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Simple',\n\t\t\t\t\t\tvalue: 'simple',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Simple With Arrays',\n\t\t\t\t\t\tvalue: 'simpleWithArrays',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Rich',\n\t\t\t\t\t\tvalue: 'rich',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdescription: 'The format that cell values are returned as',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'RAW Data',\n\t\t\t\tname: 'rawData',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether to return the data exactly in the way it got received from the API',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Sort By',\n\t\t\t\tname: 'sortBy',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: '',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Created At',\n\t\t\t\t\t\tvalue: 'createdAt',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Natural',\n\t\t\t\t\t\tvalue: 'natural',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdescription:\n\t\t\t\t\t'Specifies the sort order of the rows returned. If left unspecified, rows are returned by creation time ascending.',\n\t\t\t},\n\t\t],\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                   view:getAllViewColumns                   */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Doc Name or ID',\n\t\tname: 'docId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getDocs',\n\t\t},\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['getAllViewColumns'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'ID of the doc. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'View Name or ID',\n\t\tname: 'viewId',\n\t\ttype: 'options',\n\t\ttypeOptions: {\n\t\t\tloadOptionsDependsOn: ['docId'],\n\t\t\tloadOptionsMethod: 'getViews',\n\t\t},\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['getAllViewColumns'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'The table to get the rows from. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['getAllViewColumns'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['getAllViewColumns'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 100,\n\t\t},\n\t\tdefault: 50,\n\t\tdescription: 'Max number of results to return',\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                   view:deleteViewRow                       */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Doc Name or ID',\n\t\tname: 'docId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getDocs',\n\t\t},\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['deleteViewRow'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'ID of the doc. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'View Name or ID',\n\t\tname: 'viewId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\tdefault: '',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getViews',\n\t\t\tloadOptionsDependsOn: ['docId'],\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['deleteViewRow'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'The view to get the row from. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'Row Name or ID',\n\t\tname: 'rowId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\tdefault: '',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getViewRows',\n\t\t\tloadOptionsDependsOn: ['viewId'],\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['deleteViewRow'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'The view to get the row from. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                   view:pushViewButton                      */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Doc Name or ID',\n\t\tname: 'docId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getDocs',\n\t\t},\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['pushViewButton'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'ID of the doc. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'View Name or ID',\n\t\tname: 'viewId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\tdefault: '',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getViews',\n\t\t\tloadOptionsDependsOn: ['docId'],\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['pushViewButton'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'The view to get the row from. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'Row Name or ID',\n\t\tname: 'rowId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\tdefault: '',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getViewRows',\n\t\t\tloadOptionsDependsOn: ['viewId'],\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['pushViewButton'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'The view to get the row from. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'Column Name or ID',\n\t\tname: 'columnId',\n\t\ttype: 'options',\n\t\tdescription:\n\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getViewColumns',\n\t\t\tloadOptionsDependsOn: ['docId', 'viewId'],\n\t\t},\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['pushViewButton'],\n\t\t\t},\n\t\t},\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                   view:updateViewRow                       */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Doc Name or ID',\n\t\tname: 'docId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getDocs',\n\t\t},\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['updateViewRow'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'ID of the doc. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'View Name or ID',\n\t\tname: 'viewId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\tdefault: '',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getViews',\n\t\t\tloadOptionsDependsOn: ['docId'],\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['updateViewRow'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'The view to get the row from. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'Row Name or ID',\n\t\tname: 'rowId',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\tdefault: '',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getViewRows',\n\t\t\tloadOptionsDependsOn: ['viewId'],\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['updateViewRow'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'The view to get the row from. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'Key Name',\n\t\tname: 'keyName',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: 'columns',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['updateViewRow'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'The view to get the row from',\n\t},\n\t{\n\t\tdisplayName: 'Options',\n\t\tname: 'options',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add option',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['view'],\n\t\t\t\toperation: ['updateViewRow'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Disable Parsing',\n\t\t\t\tname: 'disableParsing',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the API will not attempt to parse the data in any way',\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,iBAAoC;AAAA,EAChD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,aAAgC;AAAA;AAAA;AAAA;AAAA,EAI5C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,QACpB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,gBAAgB;AAAA,MAC7B;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,sBAAsB,CAAC,OAAO;AAAA,MAC9B,mBAAmB;AAAA,IACpB;AAAA,IACA,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,gBAAgB;AAAA,MAC7B;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,gBAAgB;AAAA,MAC7B;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,gBAAgB;AAAA,QAC5B,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,gBAAgB;AAAA,MAC7B;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,mBAAmB;AAAA,MAChC;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,sBAAsB,CAAC,OAAO;AAAA,MAC9B,mBAAmB;AAAA,IACpB;AAAA,IACA,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,mBAAmB;AAAA,MAChC;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,mBAAmB;AAAA,MAChC;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,mBAAmB;AAAA,QAC/B,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,eAAe;AAAA,MAC5B;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,MACZ,mBAAmB;AAAA,MACnB,sBAAsB,CAAC,OAAO;AAAA,IAC/B;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,eAAe;AAAA,MAC5B;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,MACZ,mBAAmB;AAAA,MACnB,sBAAsB,CAAC,QAAQ;AAAA,IAChC;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,eAAe;AAAA,MAC5B;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,gBAAgB;AAAA,MAC7B;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,MACZ,mBAAmB;AAAA,MACnB,sBAAsB,CAAC,OAAO;AAAA,IAC/B;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,gBAAgB;AAAA,MAC7B;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,MACZ,mBAAmB;AAAA,MACnB,sBAAsB,CAAC,QAAQ;AAAA,IAChC;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,gBAAgB;AAAA,MAC7B;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aACC;AAAA,IACD,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,MACnB,sBAAsB,CAAC,SAAS,QAAQ;AAAA,IACzC;AAAA,IACA,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,gBAAgB;AAAA,MAC7B;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,eAAe;AAAA,MAC5B;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,MACZ,mBAAmB;AAAA,MACnB,sBAAsB,CAAC,OAAO;AAAA,IAC/B;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,eAAe;AAAA,MAC5B;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,MACZ,mBAAmB;AAAA,MACnB,sBAAsB,CAAC,QAAQ;AAAA,IAChC;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,eAAe;AAAA,MAC5B;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,eAAe;AAAA,MAC5B;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,eAAe;AAAA,MAC5B;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AACD;", "names": []}