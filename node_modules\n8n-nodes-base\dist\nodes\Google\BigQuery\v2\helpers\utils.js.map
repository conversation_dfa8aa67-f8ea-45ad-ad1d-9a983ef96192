{"version": 3, "sources": ["../../../../../../nodes/Google/BigQuery/v2/helpers/utils.ts"], "sourcesContent": ["import { DateTime } from 'luxon';\nimport type { IDataObject, IExecuteFunctions, INodeExecutionData } from 'n8n-workflow';\nimport { jsonParse, NodeOperationError } from 'n8n-workflow';\n\nimport type { SchemaField, TableRawData, TableSchema } from './interfaces';\n\nfunction getFieldValue(schemaField: SchemaField, field: IDataObject, parseTimestamps = false) {\n\tif (schemaField.type === 'RECORD') {\n\t\t// eslint-disable-next-line @typescript-eslint/no-use-before-define\n\t\treturn simplify([field.v as TableRawData], schemaField.fields as unknown as SchemaField[]);\n\t} else {\n\t\tlet value = field.v;\n\t\tif (schemaField.type === 'JSON') {\n\t\t\ttry {\n\t\t\t\tvalue = jsonParse(value as string);\n\t\t\t} catch (error) {}\n\t\t} else if (schemaField.type === 'TIMESTAMP' && parseTimestamps) {\n\t\t\tconst dt = DateTime.fromSeconds(Number(value));\n\t\t\tvalue = dt.isValid ? dt.toISO() : value;\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function wrapData(data: IDataObject | IDataObject[]): INodeExecutionData[] {\n\tif (!Array.isArray(data)) {\n\t\treturn [{ json: data }];\n\t}\n\treturn data.map((item) => ({\n\t\tjson: item,\n\t}));\n}\n\nexport function simplify(\n\tdata: TableRawData[],\n\tschema: SchemaField[],\n\tincludeSchema = false,\n\tparseTimestamps = false,\n) {\n\tconst returnData: IDataObject[] = [];\n\tfor (const entry of data) {\n\t\tconst record: IDataObject = {};\n\n\t\tfor (const [index, field] of entry.f.entries()) {\n\t\t\tif (schema[index].mode !== 'REPEATED') {\n\t\t\t\trecord[schema[index].name] = getFieldValue(schema[index], field, parseTimestamps);\n\t\t\t} else {\n\t\t\t\trecord[schema[index].name] = (field.v as unknown as IDataObject[]).flatMap(\n\t\t\t\t\t(repeatedField) => {\n\t\t\t\t\t\treturn getFieldValue(\n\t\t\t\t\t\t\tschema[index],\n\t\t\t\t\t\t\trepeatedField as unknown as IDataObject,\n\t\t\t\t\t\t\tparseTimestamps,\n\t\t\t\t\t\t);\n\t\t\t\t\t},\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tif (includeSchema) {\n\t\t\trecord._schema = schema;\n\t\t}\n\n\t\treturnData.push(record);\n\t}\n\n\treturn returnData;\n}\n\nexport function prepareOutput(\n\tthis: IExecuteFunctions,\n\tresponse: IDataObject,\n\titemIndex: number,\n\trawOutput: boolean,\n\tincludeSchema = false,\n) {\n\tlet responseData;\n\n\tif (response === undefined) return [];\n\n\tif (rawOutput) {\n\t\tresponseData = response;\n\t} else {\n\t\tconst { rows, schema } = response;\n\t\tconst parseTimestamps = this.getNode().typeVersion >= 2.1;\n\n\t\tif (rows !== undefined && schema !== undefined) {\n\t\t\tconst fields = (schema as TableSchema).fields;\n\t\t\tresponseData = rows;\n\n\t\t\tresponseData = simplify(\n\t\t\t\tresponseData as TableRawData[],\n\t\t\t\tfields,\n\t\t\t\tincludeSchema,\n\t\t\t\tparseTimestamps,\n\t\t\t);\n\t\t} else if (schema && includeSchema) {\n\t\t\tresponseData = { success: true, _schema: schema };\n\t\t} else {\n\t\t\tresponseData = { success: true };\n\t\t}\n\t}\n\n\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\twrapData(responseData as IDataObject[]),\n\t\t{\n\t\t\titemData: { item: itemIndex },\n\t\t},\n\t);\n\n\treturn executionData;\n}\n\nexport function checkSchema(\n\tthis: IExecuteFunctions,\n\tschema: TableSchema,\n\trecord: IDataObject,\n\ti: number,\n) {\n\tconst returnData = { ...record };\n\n\tschema.fields.forEach(({ name, mode, type, fields }) => {\n\t\tif (mode === 'REQUIRED' && returnData[name] === undefined) {\n\t\t\tthrow new NodeOperationError(\n\t\t\t\tthis.getNode(),\n\t\t\t\t`The property '${name}' is required, please define it in the 'Fields to Send'`,\n\t\t\t\t{ itemIndex: i },\n\t\t\t);\n\t\t}\n\t\tif (type !== 'STRING' && returnData[name] === '') {\n\t\t\treturnData[name] = null;\n\t\t}\n\t\tif (type === 'JSON') {\n\t\t\tlet value = returnData[name];\n\t\t\tif (typeof value === 'object') {\n\t\t\t\tvalue = JSON.stringify(value);\n\t\t\t}\n\t\t\treturnData[name] = value;\n\t\t}\n\t\tif (type === 'RECORD' && typeof returnData[name] !== 'object') {\n\t\t\tlet parsedField;\n\t\t\ttry {\n\t\t\t\tparsedField = jsonParse(returnData[name] as string);\n\t\t\t} catch (error) {\n\t\t\t\tconst recordField = fields ? `Field Schema:\\n ${JSON.stringify(fields)}` : '';\n\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t`The property '${name}' is a RECORD type, but the value is nor an object nor a valid JSON string`,\n\t\t\t\t\t{ itemIndex: i, description: recordField },\n\t\t\t\t);\n\t\t\t}\n\t\t\treturnData[name] = parsedField as IDataObject;\n\t\t}\n\t});\n\n\treturn returnData;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAyB;AAEzB,0BAA8C;AAI9C,SAAS,cAAc,aAA0B,OAAoB,kBAAkB,OAAO;AAC7F,MAAI,YAAY,SAAS,UAAU;AAElC,WAAO,SAAS,CAAC,MAAM,CAAiB,GAAG,YAAY,MAAkC;AAAA,EAC1F,OAAO;AACN,QAAI,QAAQ,MAAM;AAClB,QAAI,YAAY,SAAS,QAAQ;AAChC,UAAI;AACH,oBAAQ,+BAAU,KAAe;AAAA,MAClC,SAAS,OAAO;AAAA,MAAC;AAAA,IAClB,WAAW,YAAY,SAAS,eAAe,iBAAiB;AAC/D,YAAM,KAAK,sBAAS,YAAY,OAAO,KAAK,CAAC;AAC7C,cAAQ,GAAG,UAAU,GAAG,MAAM,IAAI;AAAA,IACnC;AACA,WAAO;AAAA,EACR;AACD;AAEO,SAAS,SAAS,MAAyD;AACjF,MAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACzB,WAAO,CAAC,EAAE,MAAM,KAAK,CAAC;AAAA,EACvB;AACA,SAAO,KAAK,IAAI,CAAC,UAAU;AAAA,IAC1B,MAAM;AAAA,EACP,EAAE;AACH;AAEO,SAAS,SACf,MACA,QACA,gBAAgB,OAChB,kBAAkB,OACjB;AACD,QAAM,aAA4B,CAAC;AACnC,aAAW,SAAS,MAAM;AACzB,UAAM,SAAsB,CAAC;AAE7B,eAAW,CAAC,OAAO,KAAK,KAAK,MAAM,EAAE,QAAQ,GAAG;AAC/C,UAAI,OAAO,KAAK,EAAE,SAAS,YAAY;AACtC,eAAO,OAAO,KAAK,EAAE,IAAI,IAAI,cAAc,OAAO,KAAK,GAAG,OAAO,eAAe;AAAA,MACjF,OAAO;AACN,eAAO,OAAO,KAAK,EAAE,IAAI,IAAK,MAAM,EAA+B;AAAA,UAClE,CAAC,kBAAkB;AAClB,mBAAO;AAAA,cACN,OAAO,KAAK;AAAA,cACZ;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,QAAI,eAAe;AAClB,aAAO,UAAU;AAAA,IAClB;AAEA,eAAW,KAAK,MAAM;AAAA,EACvB;AAEA,SAAO;AACR;AAEO,SAAS,cAEf,UACA,WACA,WACA,gBAAgB,OACf;AACD,MAAI;AAEJ,MAAI,aAAa,OAAW,QAAO,CAAC;AAEpC,MAAI,WAAW;AACd,mBAAe;AAAA,EAChB,OAAO;AACN,UAAM,EAAE,MAAM,OAAO,IAAI;AACzB,UAAM,kBAAkB,KAAK,QAAQ,EAAE,eAAe;AAEtD,QAAI,SAAS,UAAa,WAAW,QAAW;AAC/C,YAAM,SAAU,OAAuB;AACvC,qBAAe;AAEf,qBAAe;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACD,WAAW,UAAU,eAAe;AACnC,qBAAe,EAAE,SAAS,MAAM,SAAS,OAAO;AAAA,IACjD,OAAO;AACN,qBAAe,EAAE,SAAS,KAAK;AAAA,IAChC;AAAA,EACD;AAEA,QAAM,gBAAgB,KAAK,QAAQ;AAAA,IAClC,SAAS,YAA6B;AAAA,IACtC;AAAA,MACC,UAAU,EAAE,MAAM,UAAU;AAAA,IAC7B;AAAA,EACD;AAEA,SAAO;AACR;AAEO,SAAS,YAEf,QACA,QACA,GACC;AACD,QAAM,aAAa,EAAE,GAAG,OAAO;AAE/B,SAAO,OAAO,QAAQ,CAAC,EAAE,MAAM,MAAM,MAAM,OAAO,MAAM;AACvD,QAAI,SAAS,cAAc,WAAW,IAAI,MAAM,QAAW;AAC1D,YAAM,IAAI;AAAA,QACT,KAAK,QAAQ;AAAA,QACb,iBAAiB,IAAI;AAAA,QACrB,EAAE,WAAW,EAAE;AAAA,MAChB;AAAA,IACD;AACA,QAAI,SAAS,YAAY,WAAW,IAAI,MAAM,IAAI;AACjD,iBAAW,IAAI,IAAI;AAAA,IACpB;AACA,QAAI,SAAS,QAAQ;AACpB,UAAI,QAAQ,WAAW,IAAI;AAC3B,UAAI,OAAO,UAAU,UAAU;AAC9B,gBAAQ,KAAK,UAAU,KAAK;AAAA,MAC7B;AACA,iBAAW,IAAI,IAAI;AAAA,IACpB;AACA,QAAI,SAAS,YAAY,OAAO,WAAW,IAAI,MAAM,UAAU;AAC9D,UAAI;AACJ,UAAI;AACH,0BAAc,+BAAU,WAAW,IAAI,CAAW;AAAA,MACnD,SAAS,OAAO;AACf,cAAM,cAAc,SAAS;AAAA,GAAmB,KAAK,UAAU,MAAM,CAAC,KAAK;AAC3E,cAAM,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,iBAAiB,IAAI;AAAA,UACrB,EAAE,WAAW,GAAG,aAAa,YAAY;AAAA,QAC1C;AAAA,MACD;AACA,iBAAW,IAAI,IAAI;AAAA,IACpB;AAAA,EACD,CAAC;AAED,SAAO;AACR;", "names": []}