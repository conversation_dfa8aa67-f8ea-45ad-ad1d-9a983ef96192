{"version": 3, "sources": ["../../credentials/VirusTotalApi.credentials.ts"], "sourcesContent": ["import type {\n\tIAuthenticateGeneric,\n\tICredentialTestRequest,\n\tICredentialType,\n\tINodeProperties,\n\tIcon,\n} from 'n8n-workflow';\n\nexport class VirusTotalApi implements ICredentialType {\n\tname = 'virusTotalApi';\n\n\tdisplayName = 'VirusTotal API';\n\n\tdocumentationUrl = 'virustotal';\n\n\ticon: Icon = 'file:icons/VirusTotal.svg';\n\n\thttpRequestNode = {\n\t\tname: 'VirusTotal',\n\t\tdocsUrl: 'https://developers.virustotal.com/reference/overview',\n\t\tapiBaseUrl: 'https://www.virustotal.com/api/v3/',\n\t};\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'API Token',\n\t\t\tname: 'accessToken',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\trequired: true,\n\t\t\tdefault: '',\n\t\t},\n\t];\n\n\tauthenticate: IAuthenticateGeneric = {\n\t\ttype: 'generic',\n\t\tproperties: {\n\t\t\theaders: {\n\t\t\t\t'x-apikey': '={{$credentials.accessToken}}',\n\t\t\t},\n\t\t},\n\t};\n\n\ttest: ICredentialTestRequest = {\n\t\trequest: {\n\t\t\tbaseURL: 'https://www.virustotal.com/api/v3',\n\t\t\turl: '/popular_threat_categories',\n\t\t},\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAQO,MAAM,cAAyC;AAAA,EAA/C;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,gBAAa;AAEb,2BAAkB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAEA,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,UAAU;AAAA,QACV,SAAS;AAAA,MACV;AAAA,IACD;AAEA,wBAAqC;AAAA,MACpC,MAAM;AAAA,MACN,YAAY;AAAA,QACX,SAAS;AAAA,UACR,YAAY;AAAA,QACb;AAAA,MACD;AAAA,IACD;AAEA,gBAA+B;AAAA,MAC9B,SAAS;AAAA,QACR,SAAS;AAAA,QACT,KAAK;AAAA,MACN;AAAA,IACD;AAAA;AACD;", "names": []}