{"version": 3, "sources": ["../../../nodes/Zulip/Zulip.node.ts"], "sourcesContent": ["import { snakeCase } from 'change-case';\nimport type {\n\tIDataObject,\n\tIExecuteFunctions,\n\tILoadOptionsFunctions,\n\tINodeExecutionData,\n\tINodePropertyOptions,\n\tINodeType,\n\tINodeTypeDescription,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes, NodeOperationError } from 'n8n-workflow';\n\nimport { validateJSON, zulipApiRequest } from './GenericFunctions';\nimport { messageFields, messageOperations } from './MessageDescription';\nimport type { IMessage } from './MessageInterface';\nimport { streamFields, streamOperations } from './StreamDescription';\nimport type { IPrincipal, IStream } from './StreamInterface';\nimport { userFields, userOperations } from './UserDescription';\nimport type { IUser } from './UserInterface';\n\nexport class Zulip implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Zulip',\n\t\tname: 'zulip',\n\t\ticon: 'file:zulip.svg',\n\t\tgroup: ['output'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume Zulip API',\n\t\tdefaults: {\n\t\t\tname: 'Zulip',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'zulipApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Message',\n\t\t\t\t\t\tvalue: 'message',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Stream',\n\t\t\t\t\t\tvalue: 'stream',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'User',\n\t\t\t\t\t\tvalue: 'user',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'message',\n\t\t\t},\n\t\t\t// MESSAGE\n\t\t\t...messageOperations,\n\t\t\t...messageFields,\n\n\t\t\t// STREAM\n\t\t\t...streamOperations,\n\t\t\t...streamFields,\n\n\t\t\t// USER\n\t\t\t...userOperations,\n\t\t\t...userFields,\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\t// Get all the available streams to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getStreams(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst { streams } = await zulipApiRequest.call(this, 'GET', '/streams');\n\t\t\t\tfor (const stream of streams) {\n\t\t\t\t\tconst streamName = stream.name;\n\t\t\t\t\tconst streamId = stream.stream_id;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: streamName,\n\t\t\t\t\t\tvalue: streamId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the available topics to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getTopics(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst streamId = this.getCurrentNodeParameter('stream') as string;\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst { topics } = await zulipApiRequest.call(this, 'GET', `/users/me/${streamId}/topics`);\n\t\t\t\tfor (const topic of topics) {\n\t\t\t\t\tconst topicName = topic.name;\n\t\t\t\t\tconst topicId = topic.name;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: topicName,\n\t\t\t\t\t\tvalue: topicId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the available users to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getUsers(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst { members } = await zulipApiRequest.call(this, 'GET', '/users');\n\t\t\t\tfor (const member of members) {\n\t\t\t\t\tconst memberName = member.full_name;\n\t\t\t\t\tconst memberId = member.email;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: memberName,\n\t\t\t\t\t\tvalue: memberId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: INodeExecutionData[] = [];\n\t\tconst length = items.length;\n\t\tlet responseData;\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'message') {\n\t\t\t\t\t//https://zulipchat.com/api/send-message\n\t\t\t\t\tif (operation === 'sendPrivate') {\n\t\t\t\t\t\tconst to = (this.getNodeParameter('to', i) as string[]).join(',');\n\t\t\t\t\t\tconst content = this.getNodeParameter('content', i) as string;\n\t\t\t\t\t\tconst body: IMessage = {\n\t\t\t\t\t\t\ttype: 'private',\n\t\t\t\t\t\t\tto,\n\t\t\t\t\t\t\tcontent,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tresponseData = await zulipApiRequest.call(this, 'POST', '/messages', body);\n\t\t\t\t\t}\n\t\t\t\t\t//https://zulipchat.com/api/send-message\n\t\t\t\t\tif (operation === 'sendStream') {\n\t\t\t\t\t\tconst stream = this.getNodeParameter('stream', i) as string;\n\t\t\t\t\t\tconst topic = this.getNodeParameter('topic', i) as string;\n\t\t\t\t\t\tconst content = this.getNodeParameter('content', i) as string;\n\t\t\t\t\t\tconst body: IMessage = {\n\t\t\t\t\t\t\ttype: 'stream',\n\t\t\t\t\t\t\tto: stream,\n\t\t\t\t\t\t\ttopic,\n\t\t\t\t\t\t\tcontent,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tresponseData = await zulipApiRequest.call(this, 'POST', '/messages', body);\n\t\t\t\t\t}\n\t\t\t\t\t//https://zulipchat.com/api/update-message\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst messageId = this.getNodeParameter('messageId', i) as string;\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\t\tconst body: IMessage = {};\n\t\t\t\t\t\tif (updateFields.content) {\n\t\t\t\t\t\t\tbody.content = updateFields.content as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.propagateMode) {\n\t\t\t\t\t\t\tbody.propagate_mode = snakeCase(updateFields.propagateMode as string);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.topic) {\n\t\t\t\t\t\t\tbody.topic = updateFields.topic as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await zulipApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'PATCH',\n\t\t\t\t\t\t\t`/messages/${messageId}`,\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t//https://zulipchat.com/api/get-raw-message\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tconst messageId = this.getNodeParameter('messageId', i) as string;\n\t\t\t\t\t\tresponseData = await zulipApiRequest.call(this, 'GET', `/messages/${messageId}`);\n\t\t\t\t\t}\n\t\t\t\t\t//https://zulipchat.com/api/delete-message\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tconst messageId = this.getNodeParameter('messageId', i) as string;\n\t\t\t\t\t\tresponseData = await zulipApiRequest.call(this, 'DELETE', `/messages/${messageId}`);\n\t\t\t\t\t}\n\t\t\t\t\t//https://zulipchat.com/api/upload-file\n\t\t\t\t\tif (operation === 'updateFile') {\n\t\t\t\t\t\tconst credentials = await this.getCredentials('zulipApi');\n\t\t\t\t\t\tconst dataBinaryProperty = this.getNodeParameter('dataBinaryProperty', i);\n\n\t\t\t\t\t\tconst binaryData = this.helpers.assertBinaryData(i, dataBinaryProperty);\n\t\t\t\t\t\tconst binaryDataBuffer = await this.helpers.getBinaryDataBuffer(i, dataBinaryProperty);\n\t\t\t\t\t\tconst formData = {\n\t\t\t\t\t\t\tfile: {\n\t\t\t\t\t\t\t\tvalue: binaryDataBuffer,\n\t\t\t\t\t\t\t\toptions: {\n\t\t\t\t\t\t\t\t\tfilename: binaryData.fileName,\n\t\t\t\t\t\t\t\t\tcontentType: binaryData.mimeType,\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t};\n\t\t\t\t\t\tresponseData = await zulipApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/user_uploads',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tundefined,\n\t\t\t\t\t\t\t{ formData },\n\t\t\t\t\t\t);\n\t\t\t\t\t\tresponseData.uri = `${credentials.url}${responseData.uri}`;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (resource === 'stream') {\n\t\t\t\t\tconst body: IStream = {};\n\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (additionalFields.includePublic) {\n\t\t\t\t\t\t\tbody.include_public = additionalFields.includePublic as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.includeSubscribed) {\n\t\t\t\t\t\t\tbody.include_subscribed = additionalFields.includeSubscribed as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.includeAllActive) {\n\t\t\t\t\t\t\tbody.include_all_active = additionalFields.includeAllActive as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.includeDefault) {\n\t\t\t\t\t\t\tbody.include_default = additionalFields.includeDefault as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.includeOwnersubscribed) {\n\t\t\t\t\t\t\tbody.include_owner_subscribed = additionalFields.includeOwnersubscribed as boolean;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zulipApiRequest.call(this, 'GET', '/streams', body);\n\t\t\t\t\t\tresponseData = responseData.streams;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (operation === 'getSubscribed') {\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (additionalFields.includeSubscribers) {\n\t\t\t\t\t\t\tbody.include_subscribers = additionalFields.includeSubscribers as boolean;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zulipApiRequest.call(this, 'GET', '/users/me/subscriptions', body);\n\t\t\t\t\t\tresponseData = responseData.subscriptions;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst jsonParameters = this.getNodeParameter('jsonParameters', i);\n\t\t\t\t\t\tconst subscriptions = this.getNodeParameter('subscriptions', i) as IDataObject;\n\n\t\t\t\t\t\tbody.subscriptions = JSON.stringify(subscriptions.properties);\n\n\t\t\t\t\t\tif (jsonParameters) {\n\t\t\t\t\t\t\tconst additionalFieldsJson = this.getNodeParameter(\n\t\t\t\t\t\t\t\t'additionalFieldsJson',\n\t\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t) as string;\n\n\t\t\t\t\t\t\tif (additionalFieldsJson !== '') {\n\t\t\t\t\t\t\t\tif (validateJSON(additionalFieldsJson) !== undefined) {\n\t\t\t\t\t\t\t\t\tObject.assign(body, JSON.parse(additionalFieldsJson));\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t\t\t\t'Additional fields must be a valid JSON',\n\t\t\t\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\t\tbody.subscriptions = JSON.stringify(subscriptions.properties);\n\n\t\t\t\t\t\t\tif (additionalFields.inviteOnly) {\n\t\t\t\t\t\t\t\tbody.invite_only = additionalFields.inviteOnly as boolean;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.principals) {\n\t\t\t\t\t\t\t\tconst principals: string[] = [];\n\t\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\t\tadditionalFields.principals.properties.map((principal: IPrincipal) => {\n\t\t\t\t\t\t\t\t\tprincipals.push(principal.email);\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tbody.principals = JSON.stringify(principals);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.authorizationErrorsFatal) {\n\t\t\t\t\t\t\t\tbody.authorization_errors_fatal =\n\t\t\t\t\t\t\t\t\tadditionalFields.authorizationErrorsFatal as boolean;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.historyPublicToSubscribers) {\n\t\t\t\t\t\t\t\tbody.history_public_to_subscribers =\n\t\t\t\t\t\t\t\t\tadditionalFields.historyPublicToSubscribers as boolean;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.streamPostPolicy) {\n\t\t\t\t\t\t\t\tbody.stream_post_policy = additionalFields.streamPostPolicy as number;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.announce) {\n\t\t\t\t\t\t\t\tbody.announce = additionalFields.announce as boolean;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zulipApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/users/me/subscriptions',\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tconst streamId = this.getNodeParameter('streamId', i) as string;\n\n\t\t\t\t\t\tresponseData = await zulipApiRequest.call(this, 'DELETE', `/streams/${streamId}`, {});\n\t\t\t\t\t}\n\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst streamId = this.getNodeParameter('streamId', i) as string;\n\n\t\t\t\t\t\tconst jsonParameters = this.getNodeParameter('jsonParameters', i);\n\n\t\t\t\t\t\tif (jsonParameters) {\n\t\t\t\t\t\t\tconst additionalFieldsJson = this.getNodeParameter(\n\t\t\t\t\t\t\t\t'additionalFieldsJson',\n\t\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t) as string;\n\n\t\t\t\t\t\t\tif (additionalFieldsJson !== '') {\n\t\t\t\t\t\t\t\tif (validateJSON(additionalFieldsJson) !== undefined) {\n\t\t\t\t\t\t\t\t\tObject.assign(body, JSON.parse(additionalFieldsJson));\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t\t\t\t'Additional fields must be a valid JSON',\n\t\t\t\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\t\tif (additionalFields.description) {\n\t\t\t\t\t\t\t\tbody.description = JSON.stringify(additionalFields.description as string);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.newName) {\n\t\t\t\t\t\t\t\tbody.new_name = JSON.stringify(additionalFields.newName as string);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.isPrivate) {\n\t\t\t\t\t\t\t\tbody.is_private = additionalFields.isPrivate as boolean;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.isAnnouncementOnly) {\n\t\t\t\t\t\t\t\tbody.is_announcement_only = additionalFields.isAnnouncementOnly as boolean;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.streamPostPolicy) {\n\t\t\t\t\t\t\t\tbody.stream_post_policy = additionalFields.streamPostPolicy as number;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.historyPublicToSubscribers) {\n\t\t\t\t\t\t\t\tbody.history_public_to_subscribers =\n\t\t\t\t\t\t\t\t\tadditionalFields.historyPublicToSubscribers as boolean;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tresponseData = await zulipApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'PATCH',\n\t\t\t\t\t\t\t\t`/streams/${streamId}`,\n\t\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (resource === 'user') {\n\t\t\t\t\tconst body: IUser = {};\n\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tconst userId = this.getNodeParameter('userId', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (additionalFields.clientGravatar) {\n\t\t\t\t\t\t\tbody.client_gravatar = additionalFields.client_gravatar as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.includeCustomProfileFields) {\n\t\t\t\t\t\t\tbody.include_custom_profile_fields =\n\t\t\t\t\t\t\t\tadditionalFields.includeCustomProfileFields as boolean;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zulipApiRequest.call(this, 'GET', `/users/${userId}`, body);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (additionalFields.clientGravatar) {\n\t\t\t\t\t\t\tbody.client_gravatar = additionalFields.client_gravatar as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.includeCustomProfileFields) {\n\t\t\t\t\t\t\tbody.include_custom_profile_fields =\n\t\t\t\t\t\t\t\tadditionalFields.includeCustomProfileFields as boolean;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zulipApiRequest.call(this, 'GET', '/users', body);\n\t\t\t\t\t\tresponseData = responseData.members;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tbody.email = this.getNodeParameter('email', i) as string;\n\t\t\t\t\t\tbody.password = this.getNodeParameter('password', i) as string;\n\t\t\t\t\t\tbody.full_name = this.getNodeParameter('fullName', i) as string;\n\t\t\t\t\t\tbody.short_name = this.getNodeParameter('shortName', i) as string;\n\n\t\t\t\t\t\tresponseData = await zulipApiRequest.call(this, 'POST', '/users', body);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst userId = this.getNodeParameter('userId', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (additionalFields.fullName) {\n\t\t\t\t\t\t\tbody.full_name = JSON.stringify(additionalFields.fullName as string);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.isAdmin) {\n\t\t\t\t\t\t\tbody.is_admin = additionalFields.isAdmin as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.isGuest) {\n\t\t\t\t\t\t\tbody.is_guest = additionalFields.isGuest as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.role) {\n\t\t\t\t\t\t\tbody.role = additionalFields.role as number;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.profileData) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.profile_data = additionalFields.profileData.properties as [IDataObject];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zulipApiRequest.call(this, 'PATCH', `/users/${userId}`, body);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (operation === 'deactivate') {\n\t\t\t\t\t\tconst userId = this.getNodeParameter('userId', i) as string;\n\n\t\t\t\t\t\tresponseData = await zulipApiRequest.call(this, 'DELETE', `/users/${userId}`, body);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\tthis.helpers.returnJsonArray(responseData as IDataObject[]),\n\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t);\n\t\t\t\treturnData.push(...executionData);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\t\tthis.helpers.returnJsonArray({ error: error.message }),\n\t\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t\t);\n\t\t\t\t\treturnData.push(...executionData);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAA0B;AAU1B,0BAAwD;AAExD,8BAA8C;AAC9C,gCAAiD;AAEjD,+BAA+C;AAE/C,6BAA2C;AAGpC,MAAM,MAA2B;AAAA,EAAjC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,QAAQ;AAAA,MAChB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA;AAAA,QAEA,GAAG;AAAA,QACH,GAAG;AAAA;AAAA,QAGH,GAAG;AAAA,QACH,GAAG;AAAA;AAAA,QAGH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA;AAAA;AAAA,QAGZ,MAAM,aAAyE;AAC9E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,EAAE,QAAQ,IAAI,MAAM,wCAAgB,KAAK,MAAM,OAAO,UAAU;AACtE,qBAAW,UAAU,SAAS;AAC7B,kBAAM,aAAa,OAAO;AAC1B,kBAAM,WAAW,OAAO;AACxB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,YAAwE;AAC7E,gBAAM,WAAW,KAAK,wBAAwB,QAAQ;AACtD,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,EAAE,OAAO,IAAI,MAAM,wCAAgB,KAAK,MAAM,OAAO,aAAa,QAAQ,SAAS;AACzF,qBAAW,SAAS,QAAQ;AAC3B,kBAAM,YAAY,MAAM;AACxB,kBAAM,UAAU,MAAM;AACtB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,WAAuE;AAC5E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,EAAE,QAAQ,IAAI,MAAM,wCAAgB,KAAK,MAAM,OAAO,QAAQ;AACpE,qBAAW,UAAU,SAAS;AAC7B,kBAAM,aAAa,OAAO;AAC1B,kBAAM,WAAW,OAAO;AACxB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAAmC,CAAC;AAC1C,UAAM,SAAS,MAAM;AACrB,QAAI;AACJ,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,YAAI,aAAa,WAAW;AAE3B,cAAI,cAAc,eAAe;AAChC,kBAAM,KAAM,KAAK,iBAAiB,MAAM,CAAC,EAAe,KAAK,GAAG;AAChE,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,OAAiB;AAAA,cACtB,MAAM;AAAA,cACN;AAAA,cACA;AAAA,YACD;AACA,2BAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,aAAa,IAAI;AAAA,UAC1E;AAEA,cAAI,cAAc,cAAc;AAC/B,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,OAAiB;AAAA,cACtB,MAAM;AAAA,cACN,IAAI;AAAA,cACJ;AAAA,cACA;AAAA,YACD;AACA,2BAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,aAAa,IAAI;AAAA,UAC1E;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,kBAAM,OAAiB,CAAC;AACxB,gBAAI,aAAa,SAAS;AACzB,mBAAK,UAAU,aAAa;AAAA,YAC7B;AACA,gBAAI,aAAa,eAAe;AAC/B,mBAAK,qBAAiB,8BAAU,aAAa,aAAuB;AAAA,YACrE;AACA,gBAAI,aAAa,OAAO;AACvB,mBAAK,QAAQ,aAAa;AAAA,YAC3B;AACA,2BAAe,MAAM,wCAAgB;AAAA,cACpC;AAAA,cACA;AAAA,cACA,aAAa,SAAS;AAAA,cACtB;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,OAAO;AACxB,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,2BAAe,MAAM,wCAAgB,KAAK,MAAM,OAAO,aAAa,SAAS,EAAE;AAAA,UAChF;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,2BAAe,MAAM,wCAAgB,KAAK,MAAM,UAAU,aAAa,SAAS,EAAE;AAAA,UACnF;AAEA,cAAI,cAAc,cAAc;AAC/B,kBAAM,cAAc,MAAM,KAAK,eAAe,UAAU;AACxD,kBAAM,qBAAqB,KAAK,iBAAiB,sBAAsB,CAAC;AAExE,kBAAM,aAAa,KAAK,QAAQ,iBAAiB,GAAG,kBAAkB;AACtE,kBAAM,mBAAmB,MAAM,KAAK,QAAQ,oBAAoB,GAAG,kBAAkB;AACrF,kBAAM,WAAW;AAAA,cAChB,MAAM;AAAA,gBACL,OAAO;AAAA,gBACP,SAAS;AAAA,kBACR,UAAU,WAAW;AAAA,kBACrB,aAAa,WAAW;AAAA,gBACzB;AAAA,cACD;AAAA,YACD;AACA,2BAAe,MAAM,wCAAgB;AAAA,cACpC;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD,CAAC;AAAA,cACD;AAAA,cACA,EAAE,SAAS;AAAA,YACZ;AACA,yBAAa,MAAM,GAAG,YAAY,GAAG,GAAG,aAAa,GAAG;AAAA,UACzD;AAAA,QACD;AAEA,YAAI,aAAa,UAAU;AAC1B,gBAAM,OAAgB,CAAC;AAEvB,cAAI,cAAc,UAAU;AAC3B,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,iBAAiB,eAAe;AACnC,mBAAK,iBAAiB,iBAAiB;AAAA,YACxC;AACA,gBAAI,iBAAiB,mBAAmB;AACvC,mBAAK,qBAAqB,iBAAiB;AAAA,YAC5C;AACA,gBAAI,iBAAiB,kBAAkB;AACtC,mBAAK,qBAAqB,iBAAiB;AAAA,YAC5C;AACA,gBAAI,iBAAiB,gBAAgB;AACpC,mBAAK,kBAAkB,iBAAiB;AAAA,YACzC;AACA,gBAAI,iBAAiB,wBAAwB;AAC5C,mBAAK,2BAA2B,iBAAiB;AAAA,YAClD;AAEA,2BAAe,MAAM,wCAAgB,KAAK,MAAM,OAAO,YAAY,IAAI;AACvE,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,iBAAiB;AAClC,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,iBAAiB,oBAAoB;AACxC,mBAAK,sBAAsB,iBAAiB;AAAA,YAC7C;AAEA,2BAAe,MAAM,wCAAgB,KAAK,MAAM,OAAO,2BAA2B,IAAI;AACtF,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,CAAC;AAE9D,iBAAK,gBAAgB,KAAK,UAAU,cAAc,UAAU;AAE5D,gBAAI,gBAAgB;AACnB,oBAAM,uBAAuB,KAAK;AAAA,gBACjC;AAAA,gBACA;AAAA,cACD;AAEA,kBAAI,yBAAyB,IAAI;AAChC,wBAAI,sCAAa,oBAAoB,MAAM,QAAW;AACrD,yBAAO,OAAO,MAAM,KAAK,MAAM,oBAAoB,CAAC;AAAA,gBACrD,OAAO;AACN,wBAAM,IAAI;AAAA,oBACT,KAAK,QAAQ;AAAA,oBACb;AAAA,oBACA,EAAE,WAAW,EAAE;AAAA,kBAChB;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,oBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,mBAAK,gBAAgB,KAAK,UAAU,cAAc,UAAU;AAE5D,kBAAI,iBAAiB,YAAY;AAChC,qBAAK,cAAc,iBAAiB;AAAA,cACrC;AACA,kBAAI,iBAAiB,YAAY;AAChC,sBAAM,aAAuB,CAAC;AAE9B,iCAAiB,WAAW,WAAW,IAAI,CAAC,cAA0B;AACrE,6BAAW,KAAK,UAAU,KAAK;AAAA,gBAChC,CAAC;AACD,qBAAK,aAAa,KAAK,UAAU,UAAU;AAAA,cAC5C;AACA,kBAAI,iBAAiB,0BAA0B;AAC9C,qBAAK,6BACJ,iBAAiB;AAAA,cACnB;AACA,kBAAI,iBAAiB,4BAA4B;AAChD,qBAAK,gCACJ,iBAAiB;AAAA,cACnB;AACA,kBAAI,iBAAiB,kBAAkB;AACtC,qBAAK,qBAAqB,iBAAiB;AAAA,cAC5C;AACA,kBAAI,iBAAiB,UAAU;AAC9B,qBAAK,WAAW,iBAAiB;AAAA,cAClC;AAAA,YACD;AAEA,2BAAe,MAAM,wCAAgB;AAAA,cACpC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAEpD,2BAAe,MAAM,wCAAgB,KAAK,MAAM,UAAU,YAAY,QAAQ,IAAI,CAAC,CAAC;AAAA,UACrF;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAEpD,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAEhE,gBAAI,gBAAgB;AACnB,oBAAM,uBAAuB,KAAK;AAAA,gBACjC;AAAA,gBACA;AAAA,cACD;AAEA,kBAAI,yBAAyB,IAAI;AAChC,wBAAI,sCAAa,oBAAoB,MAAM,QAAW;AACrD,yBAAO,OAAO,MAAM,KAAK,MAAM,oBAAoB,CAAC;AAAA,gBACrD,OAAO;AACN,wBAAM,IAAI;AAAA,oBACT,KAAK,QAAQ;AAAA,oBACb;AAAA,oBACA,EAAE,WAAW,EAAE;AAAA,kBAChB;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,oBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,kBAAI,iBAAiB,aAAa;AACjC,qBAAK,cAAc,KAAK,UAAU,iBAAiB,WAAqB;AAAA,cACzE;AACA,kBAAI,iBAAiB,SAAS;AAC7B,qBAAK,WAAW,KAAK,UAAU,iBAAiB,OAAiB;AAAA,cAClE;AACA,kBAAI,iBAAiB,WAAW;AAC/B,qBAAK,aAAa,iBAAiB;AAAA,cACpC;AACA,kBAAI,iBAAiB,oBAAoB;AACxC,qBAAK,uBAAuB,iBAAiB;AAAA,cAC9C;AACA,kBAAI,iBAAiB,kBAAkB;AACtC,qBAAK,qBAAqB,iBAAiB;AAAA,cAC5C;AACA,kBAAI,iBAAiB,4BAA4B;AAChD,qBAAK,gCACJ,iBAAiB;AAAA,cACnB;AAEA,6BAAe,MAAM,wCAAgB;AAAA,gBACpC;AAAA,gBACA;AAAA,gBACA,YAAY,QAAQ;AAAA,gBACpB;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAEA,YAAI,aAAa,QAAQ;AACxB,gBAAM,OAAc,CAAC;AAErB,cAAI,cAAc,OAAO;AACxB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,iBAAiB,gBAAgB;AACpC,mBAAK,kBAAkB,iBAAiB;AAAA,YACzC;AACA,gBAAI,iBAAiB,4BAA4B;AAChD,mBAAK,gCACJ,iBAAiB;AAAA,YACnB;AAEA,2BAAe,MAAM,wCAAgB,KAAK,MAAM,OAAO,UAAU,MAAM,IAAI,IAAI;AAAA,UAChF;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,iBAAiB,gBAAgB;AACpC,mBAAK,kBAAkB,iBAAiB;AAAA,YACzC;AACA,gBAAI,iBAAiB,4BAA4B;AAChD,mBAAK,gCACJ,iBAAiB;AAAA,YACnB;AAEA,2BAAe,MAAM,wCAAgB,KAAK,MAAM,OAAO,UAAU,IAAI;AACrE,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,UAAU;AAC3B,iBAAK,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC7C,iBAAK,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACnD,iBAAK,YAAY,KAAK,iBAAiB,YAAY,CAAC;AACpD,iBAAK,aAAa,KAAK,iBAAiB,aAAa,CAAC;AAEtD,2BAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,UAAU,IAAI;AAAA,UACvE;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,iBAAiB,UAAU;AAC9B,mBAAK,YAAY,KAAK,UAAU,iBAAiB,QAAkB;AAAA,YACpE;AACA,gBAAI,iBAAiB,SAAS;AAC7B,mBAAK,WAAW,iBAAiB;AAAA,YAClC;AACA,gBAAI,iBAAiB,SAAS;AAC7B,mBAAK,WAAW,iBAAiB;AAAA,YAClC;AACA,gBAAI,iBAAiB,MAAM;AAC1B,mBAAK,OAAO,iBAAiB;AAAA,YAC9B;AACA,gBAAI,iBAAiB,aAAa;AAEjC,mBAAK,eAAe,iBAAiB,YAAY;AAAA,YAClD;AAEA,2BAAe,MAAM,wCAAgB,KAAK,MAAM,SAAS,UAAU,MAAM,IAAI,IAAI;AAAA,UAClF;AAEA,cAAI,cAAc,cAAc;AAC/B,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,2BAAe,MAAM,wCAAgB,KAAK,MAAM,UAAU,UAAU,MAAM,IAAI,IAAI;AAAA,UACnF;AAAA,QACD;AACA,cAAM,gBAAgB,KAAK,QAAQ;AAAA,UAClC,KAAK,QAAQ,gBAAgB,YAA6B;AAAA,UAC1D,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,QACzB;AACA,mBAAW,KAAK,GAAG,aAAa;AAAA,MACjC,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,gBAAM,gBAAgB,KAAK,QAAQ;AAAA,YAClC,KAAK,QAAQ,gBAAgB,EAAE,OAAO,MAAM,QAAQ,CAAC;AAAA,YACrD,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,UACzB;AACA,qBAAW,KAAK,GAAG,aAAa;AAChC;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AACA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": []}