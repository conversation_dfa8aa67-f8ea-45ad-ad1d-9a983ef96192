{"version": 3, "sources": ["../../../nodes/QuickChart/types.ts"], "sourcesContent": ["import type { IDataObject } from 'n8n-workflow';\n\nexport interface IDataset {\n\tlabel?: string;\n\tdata: string | IDataObject;\n\tbackgroundColor?: string;\n\tborderColor?: string;\n\tcolor?: string;\n\ttype?: string;\n\tfill?: boolean;\n\tpointStyle?: string;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}