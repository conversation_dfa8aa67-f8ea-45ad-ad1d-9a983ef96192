{"version": 3, "sources": ["../../../../nodes/Google/GSuiteAdmin/UserDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nconst rolesOptions = [\n\t{\n\t\tname: 'Directory Sync Admin',\n\t\tvalue: 'directorySyncAdmin',\n\t\tdescription: 'Whether to assign the Directory Sync Admin role',\n\t},\n\t{\n\t\tname: 'Groups Admin',\n\t\tvalue: 'groupsAdmin',\n\t\tdescription: 'Whether to assign the Groups Admin role',\n\t},\n\t{\n\t\tname: 'Groups Editor',\n\t\tvalue: 'groupsEditor',\n\t\tdescription: 'Whether to assign the Groups Editor role',\n\t},\n\t{\n\t\tname: 'Groups Reader',\n\t\tvalue: 'groupsReader',\n\t\tdescription: 'Whether to assign the Groups Reader role',\n\t},\n\t{\n\t\tname: 'Help Desk Admin',\n\t\tvalue: 'helpDeskAdmin',\n\t\tdescription: 'Whether to assign the Help Desk Admin role',\n\t},\n\t{\n\t\tname: 'Inventory Reporting Admin',\n\t\tvalue: 'inventoryReportingAdmin',\n\t\tdescription: 'Whether to assign the Inventory Reporting Admin role',\n\t},\n\t{\n\t\tname: 'Mobile Admin',\n\t\tvalue: 'mobileAdmin',\n\t\tdescription: 'Whether to assign the Mobile Admin role',\n\t},\n\t{\n\t\tname: 'Services Admin',\n\t\tvalue: 'servicesAdmin',\n\t\tdescription: 'Whether to assign the Services Admin role',\n\t},\n\t{\n\t\tname: 'Storage Admin',\n\t\tvalue: 'storageAdmin',\n\t\tdescription: 'Whether to assign the Storage Admin role',\n\t},\n\t{\n\t\tname: 'Super Admin',\n\t\tvalue: 'superAdmin',\n\t\tdescription: 'Whether to assign the Super Admin role',\n\t},\n\t{\n\t\tname: 'User Management',\n\t\tvalue: 'userManagement',\n\t\tdescription: 'Whether to assign the User Management role',\n\t},\n];\n\nexport const userOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Add to Group',\n\t\t\t\tvalue: 'addToGroup',\n\t\t\t\tdescription: 'Add an existing user to a group',\n\t\t\t\taction: 'Add user to group',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Create',\n\t\t\t\tvalue: 'create',\n\t\t\t\tdescription: 'Create a user',\n\t\t\t\taction: 'Create a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'delete',\n\t\t\t\tdescription: 'Delete a user',\n\t\t\t\taction: 'Delete a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\tdescription: 'Get a user',\n\t\t\t\taction: 'Get a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Get many users',\n\t\t\t\taction: 'Get many users',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Remove From Group',\n\t\t\t\tvalue: 'removeFromGroup',\n\t\t\t\tdescription: 'Remove a user from a group',\n\t\t\t\taction: 'Remove user from group',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Update',\n\t\t\t\tvalue: 'update',\n\t\t\t\tdescription: 'Update a user',\n\t\t\t\taction: 'Update a user',\n\t\t\t},\n\t\t],\n\t\tdefault: 'create',\n\t},\n];\n\nexport const userFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 user                                       */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'User',\n\t\tname: 'userId',\n\t\trequired: true,\n\t\ttype: 'resourceLocator',\n\t\tdefault: {\n\t\t\tmode: 'list',\n\t\t\tvalue: '',\n\t\t},\n\t\tdescription: 'Select the user to perform the operation on',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['addToGroup', 'delete', 'get', 'removeFromGroup', 'update'],\n\t\t\t},\n\t\t},\n\t\tmodes: [\n\t\t\t{\n\t\t\t\tdisplayName: 'From list',\n\t\t\t\tname: 'list',\n\t\t\t\ttype: 'list',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tsearchListMethod: 'searchUsers',\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'By Email',\n\t\t\t\tname: 'userEmail',\n\t\t\t\ttype: 'string',\n\t\t\t\thint: 'Enter the user email',\n\t\t\t\tplaceholder: 'e.g. <EMAIL>',\n\t\t\t\tvalidation: [\n\t\t\t\t\t{\n\t\t\t\t\t\ttype: 'regex',\n\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\tregex: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$',\n\t\t\t\t\t\t\terrorMessage: 'Please enter a valid email address.',\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'By ID',\n\t\t\t\tname: 'userId',\n\t\t\t\ttype: 'string',\n\t\t\t\thint: 'Enter the user id',\n\t\t\t\tplaceholder: 'e.g. 123456789879230471055',\n\t\t\t},\n\t\t],\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 user:addToGroup                            */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Group',\n\t\tname: 'groupId',\n\t\trequired: true,\n\t\ttype: 'resourceLocator',\n\t\tdefault: {\n\t\t\tmode: 'list',\n\t\t\tvalue: '',\n\t\t},\n\t\tdescription: 'Select the group to perform the operation on',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['addToGroup', 'removeFromGroup'],\n\t\t\t},\n\t\t},\n\t\tmodes: [\n\t\t\t{\n\t\t\t\tdisplayName: 'From list',\n\t\t\t\tname: 'list',\n\t\t\t\ttype: 'list',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tsearchListMethod: 'searchGroups',\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'By ID',\n\t\t\t\tname: 'groupId',\n\t\t\t\ttype: 'string',\n\t\t\t\tplaceholder: 'e.g. 0123kx3o1habcdf',\n\t\t\t},\n\t\t],\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 user:create                                */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'First Name',\n\t\tname: 'firstName',\n\t\tplaceholder: 'e.g. Nathan',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['create'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Last Name',\n\t\tname: 'lastName',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tplaceholder: 'e.g. Smith',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['create'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Password',\n\t\tname: 'password',\n\t\ttype: 'string',\n\t\ttypeOptions: {\n\t\t\tpassword: true,\n\t\t},\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['create'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription:\n\t\t\t'Stores the password for the user account. A minimum of 8 characters is required. The maximum length is 100 characters.',\n\t},\n\t{\n\t\tdisplayName: 'Username',\n\t\tname: 'username',\n\t\ttype: 'string',\n\t\tplaceholder: 'e.g. n.smith',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['create'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription:\n\t\t\t\"The username that will be set to the user. Example: If you domain is example.com and you set the username to n.smith then the user's final email address <NAME_EMAIL>.\",\n\t},\n\t{\n\t\tdisplayName: 'Domain Name or ID',\n\t\tname: 'domain',\n\t\ttype: 'options',\n\t\tdescription:\n\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getDomains',\n\t\t},\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['create'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['create'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Change Password at Next Login',\n\t\t\t\tname: 'changePasswordAtNextLogin',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user is forced to change their password at next login',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Phones',\n\t\t\t\tname: 'phoneUi',\n\t\t\t\tplaceholder: 'Add Phone',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'phoneValues',\n\t\t\t\t\t\tdisplayName: 'Phone',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Type',\n\t\t\t\t\t\t\t\tname: 'type',\n\t\t\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Assistant',\n\t\t\t\t\t\t\t\t\t\tvalue: 'assistant',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Callback',\n\t\t\t\t\t\t\t\t\t\tvalue: 'callback',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Car',\n\t\t\t\t\t\t\t\t\t\tvalue: 'car',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Company Main',\n\t\t\t\t\t\t\t\t\t\tvalue: 'company_main',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Custom',\n\t\t\t\t\t\t\t\t\t\tvalue: 'custom',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Grand Central',\n\t\t\t\t\t\t\t\t\t\tvalue: 'grand_central',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Home',\n\t\t\t\t\t\t\t\t\t\tvalue: 'home',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Home Fax',\n\t\t\t\t\t\t\t\t\t\tvalue: 'home_fax',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'ISDN',\n\t\t\t\t\t\t\t\t\t\tvalue: 'isdn',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Main',\n\t\t\t\t\t\t\t\t\t\tvalue: 'main',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Mobile',\n\t\t\t\t\t\t\t\t\t\tvalue: 'mobile',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Other',\n\t\t\t\t\t\t\t\t\t\tvalue: 'other',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Other Fax',\n\t\t\t\t\t\t\t\t\t\tvalue: 'other_fax',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Pager',\n\t\t\t\t\t\t\t\t\t\tvalue: 'pager',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Radio',\n\t\t\t\t\t\t\t\t\t\tvalue: 'radio',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Telex',\n\t\t\t\t\t\t\t\t\t\tvalue: 'telex',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'TTY TDD',\n\t\t\t\t\t\t\t\t\t\tvalue: 'tty_tdd',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Work',\n\t\t\t\t\t\t\t\t\t\tvalue: 'work',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Work Fax',\n\t\t\t\t\t\t\t\t\t\tvalue: 'work_fax',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Work Mobile',\n\t\t\t\t\t\t\t\t\t\tvalue: 'work_mobile',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Work Pager',\n\t\t\t\t\t\t\t\t\t\tvalue: 'work_pager',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t\tdefault: 'work',\n\t\t\t\t\t\t\t\tdescription: 'The type of phone number',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Phone Number',\n\t\t\t\t\t\t\t\tname: 'value',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Primary',\n\t\t\t\t\t\t\t\tname: 'primary',\n\t\t\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\t\t\tdescription: \"Whether this is the user's primary phone number\",\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Secondary Emails',\n\t\t\t\tname: 'emailUi',\n\t\t\t\tplaceholder: 'Add Email',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'emailValues',\n\t\t\t\t\t\tdisplayName: 'Email',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Type',\n\t\t\t\t\t\t\t\tname: 'type',\n\t\t\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Home',\n\t\t\t\t\t\t\t\t\t\tvalue: 'home',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Work',\n\t\t\t\t\t\t\t\t\t\tvalue: 'work',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Other',\n\t\t\t\t\t\t\t\t\t\tvalue: 'other',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t\tdefault: 'work',\n\t\t\t\t\t\t\t\tdescription: 'The type of the email account',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Email',\n\t\t\t\t\t\t\t\tname: 'address',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Roles',\n\t\t\t\tname: 'roles',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'Select the roles you want to assign to the user',\n\t\t\t\toptions: rolesOptions,\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Custom Fields',\n\t\t\t\tname: 'customFields',\n\t\t\t\tplaceholder: 'Add or Edit Custom Fields',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tdefault: {},\n\t\t\t\tdescription: 'Allows editing and adding of custom fields',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'fieldValues',\n\t\t\t\t\t\tdisplayName: 'Field',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Schema Name or ID',\n\t\t\t\t\t\t\t\tname: 'schemaName',\n\t\t\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\t\t\tloadOptionsMethod: 'getSchemas',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t\t'Select the schema to use for custom fields. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Field Name or ID',\n\t\t\t\t\t\t\t\tname: 'fieldName',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\t\tdescription: 'Enter a field name from the selected schema',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Value',\n\t\t\t\t\t\t\t\tname: 'value',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\t\tdescription: 'Provide a value for the selected field',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 user:get                                   */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Output',\n\t\tname: 'output',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\tdefault: 'simplified',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['get'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Simplified',\n\t\t\t\tvalue: 'simplified',\n\t\t\t\tdescription:\n\t\t\t\t\t'Only return specific fields: kind, ID, primaryEmail, name (with subfields), isAdmin, lastLoginTime, creationTime, and suspended',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Raw',\n\t\t\t\tvalue: 'raw',\n\t\t\t\tdescription: 'Return all fields from the API response',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Select Included Fields',\n\t\t\t\tvalue: 'select',\n\t\t\t\tdescription: 'Choose specific fields to include',\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Fields',\n\t\tname: 'fields',\n\t\ttype: 'multiOptions',\n\t\tdefault: [],\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toutput: ['select'],\n\t\t\t\toperation: ['get'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Creation Time',\n\t\t\t\tvalue: 'creationTime',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Is Admin',\n\t\t\t\tvalue: 'isAdmin',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Kind',\n\t\t\t\tvalue: 'kind',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Last Login Time',\n\t\t\t\tvalue: 'lastLoginTime',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Name',\n\t\t\t\tvalue: 'name',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Primary Email',\n\t\t\t\tvalue: 'primaryEmail',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Suspended',\n\t\t\t\tvalue: 'suspended',\n\t\t\t},\n\t\t],\n\t\tdescription: 'Fields to include in the response when \"Select Included Fields\" is chosen',\n\t},\n\t{\n\t\tdisplayName: 'Custom Fields',\n\t\tname: 'projection',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: \"Don't Include\",\n\t\t\t\tvalue: 'basic',\n\t\t\t\tdescription: 'Do not include any custom fields for the user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Custom',\n\t\t\t\tvalue: 'custom',\n\t\t\t\tdescription: 'Include custom fields from schemas requested in Custom Schema Names or IDs',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Include All',\n\t\t\t\tvalue: 'full',\n\t\t\t\tdescription: 'Include all fields associated with this user',\n\t\t\t},\n\t\t],\n\t\tdefault: 'basic',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['get'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'What subset of fields to fetch for this user',\n\t},\n\t{\n\t\tdisplayName: 'Custom Schema Names or IDs',\n\t\tname: 'customFieldMask',\n\t\ttype: 'multiOptions',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['get'],\n\t\t\t\tresource: ['user'],\n\t\t\t\t'/projection': ['custom'],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getSchemas',\n\t\t},\n\t\tdefault: [],\n\t\tdescription:\n\t\t\t'A comma-separated list of schema names. All fields from these schemas are fetched. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 user:getAll                                */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['user'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 500,\n\t\t},\n\t\tdefault: 100,\n\t\tdescription: 'Max number of results to return',\n\t},\n\t{\n\t\tdisplayName: 'Output',\n\t\tname: 'output',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\tdefault: 'simplified',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Simplified',\n\t\t\t\tvalue: 'simplified',\n\t\t\t\tdescription:\n\t\t\t\t\t'Only return specific fields: kind, ID, primaryEmail, name (with subfields), isAdmin, lastLoginTime, creationTime, and suspended',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Raw',\n\t\t\t\tvalue: 'raw',\n\t\t\t\tdescription: 'Return all fields from the API response',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Select Included Fields',\n\t\t\t\tvalue: 'select',\n\t\t\t\tdescription: 'Choose specific fields to include',\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Fields',\n\t\tname: 'fields',\n\t\ttype: 'multiOptions',\n\t\tdefault: [],\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toutput: ['select'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Creation Time',\n\t\t\t\tvalue: 'creationTime',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Is Admin',\n\t\t\t\tvalue: 'isAdmin',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Kind',\n\t\t\t\tvalue: 'kind',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Last Login Time',\n\t\t\t\tvalue: 'lastLoginTime',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Name',\n\t\t\t\tvalue: 'name',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Primary Email',\n\t\t\t\tvalue: 'primaryEmail',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Suspended',\n\t\t\t\tvalue: 'suspended',\n\t\t\t},\n\t\t],\n\t\tdescription: 'Fields to include in the response when \"Select Included Fields\" is chosen',\n\t},\n\t{\n\t\tdisplayName: 'Custom Fields',\n\t\tname: 'projection',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: \"Don't Include\",\n\t\t\t\tvalue: 'basic',\n\t\t\t\tdescription: 'Do not include any custom fields for the user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Custom',\n\t\t\t\tvalue: 'custom',\n\t\t\t\tdescription: 'Include custom fields from schemas requested in Custom Schema Names or IDs',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Include All',\n\t\t\t\tvalue: 'full',\n\t\t\t\tdescription: 'Include all fields associated with this user',\n\t\t\t},\n\t\t],\n\t\tdefault: 'basic',\n\t\tdescription: 'What subset of fields to fetch for this user',\n\t},\n\t{\n\t\tdisplayName: 'Custom Schema Names or IDs',\n\t\tname: 'customFieldMask',\n\t\ttype: 'multiOptions',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['user'],\n\t\t\t\t'/projection': ['custom'],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getSchemas',\n\t\t},\n\t\tdefault: [],\n\t\tdescription:\n\t\t\t'A comma-separated list of schema names. All fields from these schemas are fetched. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'Filter',\n\t\tname: 'filter',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Filter',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Customer',\n\t\t\t\tname: 'customer',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"The unique ID for the customer's Google Workspace account\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Domain',\n\t\t\t\tname: 'domain',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The domain name. Use this field to get groups from a specific domain.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Query',\n\t\t\t\tname: 'query',\n\t\t\t\ttype: 'string',\n\t\t\t\tplaceholder: 'e.g. name:contact* email:contact*',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'Query string to filter the results. Follow Google Admin SDK documentation. <a href=\"https://developers.google.com/admin-sdk/directory/v1/guides/search-users#examples\" target=\"_blank\">More info</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Show Deleted',\n\t\t\t\tname: 'showDeleted',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether retrieve the list of deleted users',\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Sort',\n\t\tname: 'sort',\n\t\ttype: 'fixedCollection',\n\t\tplaceholder: 'Add Sort Rule',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'sortRules',\n\t\t\t\tdisplayName: 'Sort Rules',\n\t\t\t\tvalues: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Order By',\n\t\t\t\t\t\tname: 'orderBy',\n\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'Email',\n\t\t\t\t\t\t\t\tvalue: 'email',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'Family Name',\n\t\t\t\t\t\t\t\tvalue: 'familyName',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'Given Name',\n\t\t\t\t\t\t\t\tvalue: 'givenName',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription: 'Field to sort the results by',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Sort Order',\n\t\t\t\t\t\tname: 'sortOrder',\n\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'Ascending',\n\t\t\t\t\t\t\t\tvalue: 'ASCENDING',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'Descending',\n\t\t\t\t\t\t\t\tvalue: 'DESCENDING',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t\tdefault: 'ASCENDING',\n\t\t\t\t\t\tdescription: 'Sort order direction',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t\tdescription: 'Define sorting rules for the results',\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 user:update                                */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Update Fields',\n\t\tname: 'updateFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['update'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Archived',\n\t\t\t\tname: 'archived',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether user is archived',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Suspend',\n\t\t\t\tname: 'suspendUi',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription:\n\t\t\t\t\t'Whether to set the user as suspended. If set to OFF, the user will be reactivated. If not added, the status will remain unchanged.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Change Password at Next Login',\n\t\t\t\tname: 'changePasswordAtNextLogin',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user is forced to change their password at next login',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'First Name',\n\t\t\t\tname: 'firstName',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: 'e.g. John',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Last Name',\n\t\t\t\tname: 'lastName',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: 'e.g. Doe',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Password',\n\t\t\t\tname: 'password',\n\t\t\t\ttype: 'string',\n\t\t\t\ttypeOptions: { password: true },\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: 'e.g. MyStrongP@ssword123',\n\t\t\t\tdescription:\n\t\t\t\t\t'Stores the password for the user account. A minimum of 8 characters is required. The maximum length is 100 characters.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Phones',\n\t\t\t\tname: 'phoneUi',\n\t\t\t\tplaceholder: 'Add Phone',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'phoneValues',\n\t\t\t\t\t\tdisplayName: 'Phone',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Type',\n\t\t\t\t\t\t\t\tname: 'type',\n\t\t\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Assistant',\n\t\t\t\t\t\t\t\t\t\tvalue: 'assistant',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Callback',\n\t\t\t\t\t\t\t\t\t\tvalue: 'callback',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Car',\n\t\t\t\t\t\t\t\t\t\tvalue: 'car',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Company Main',\n\t\t\t\t\t\t\t\t\t\tvalue: 'company_main',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Custom',\n\t\t\t\t\t\t\t\t\t\tvalue: 'custom',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Grand Central',\n\t\t\t\t\t\t\t\t\t\tvalue: 'grand_central',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Home',\n\t\t\t\t\t\t\t\t\t\tvalue: 'home',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Home Fax',\n\t\t\t\t\t\t\t\t\t\tvalue: 'home_fax',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'ISDN',\n\t\t\t\t\t\t\t\t\t\tvalue: 'isdn',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Main',\n\t\t\t\t\t\t\t\t\t\tvalue: 'main',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Mobile',\n\t\t\t\t\t\t\t\t\t\tvalue: 'mobile',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Other',\n\t\t\t\t\t\t\t\t\t\tvalue: 'other',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Other Fax',\n\t\t\t\t\t\t\t\t\t\tvalue: 'other_fax',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Pager',\n\t\t\t\t\t\t\t\t\t\tvalue: 'pager',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Radio',\n\t\t\t\t\t\t\t\t\t\tvalue: 'radio',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Telex',\n\t\t\t\t\t\t\t\t\t\tvalue: 'telex',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'TTY TDD',\n\t\t\t\t\t\t\t\t\t\tvalue: 'tty_tdd',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Work',\n\t\t\t\t\t\t\t\t\t\tvalue: 'work',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Work Fax',\n\t\t\t\t\t\t\t\t\t\tvalue: 'work_fax',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Work Mobile',\n\t\t\t\t\t\t\t\t\t\tvalue: 'work_mobile',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Work Pager',\n\t\t\t\t\t\t\t\t\t\tvalue: 'work_pager',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t\tdefault: 'work',\n\t\t\t\t\t\t\t\tdescription: 'The type of phone number',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Phone Number',\n\t\t\t\t\t\t\t\tname: 'value',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tplaceholder: 'e.g. +1234567890',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Primary',\n\t\t\t\t\t\t\t\tname: 'primary',\n\t\t\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t\t\"Whether this is the user's primary phone number. A user may only have one primary phone number.\",\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Primary Email',\n\t\t\t\tname: 'primaryEmail',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: 'e.g. <EMAIL>',\n\t\t\t\tdescription:\n\t\t\t\t\t\"The user's primary email address. This property is required in a request to create a user account. The primaryEmail must be unique and cannot be an alias of another user.\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Secondary Emails',\n\t\t\t\tname: 'emailUi',\n\t\t\t\tplaceholder: 'Add Email',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'emailValues',\n\t\t\t\t\t\tdisplayName: 'Email',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Type',\n\t\t\t\t\t\t\t\tname: 'type',\n\t\t\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Home',\n\t\t\t\t\t\t\t\t\t\tvalue: 'home',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Work',\n\t\t\t\t\t\t\t\t\t\tvalue: 'work',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Other',\n\t\t\t\t\t\t\t\t\t\tvalue: 'other',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t\tdefault: 'work',\n\t\t\t\t\t\t\t\tdescription: 'The type of the email account',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Email',\n\t\t\t\t\t\t\t\tname: 'address',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tplaceholder: 'e.g. <EMAIL>',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Roles',\n\t\t\t\tname: 'roles',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'Select the roles you want to assign to the user',\n\t\t\t\toptions: rolesOptions,\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Custom Fields',\n\t\t\t\tname: 'customFields',\n\t\t\t\tplaceholder: 'Add or Edit Custom Fields',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tdefault: {},\n\t\t\t\tdescription: 'Allows editing and adding of custom fields',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'fieldValues',\n\t\t\t\t\t\tdisplayName: 'Field',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Schema Name or ID',\n\t\t\t\t\t\t\t\tname: 'schemaName',\n\t\t\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\t\t\tloadOptionsMethod: 'getSchemas',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t\t'Select the schema to use for custom fields. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Field Name or ID',\n\t\t\t\t\t\t\t\tname: 'fieldName',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\t\tdescription: 'Enter a field name from the selected schema',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Value',\n\t\t\t\t\t\t\t\tname: 'value',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\t\tdescription: 'Provide a value for the selected field',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,MAAM,eAAe;AAAA,EACpB;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AACD;AAEO,MAAM,iBAAoC;AAAA,EAChD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,aAAgC;AAAA;AAAA;AAAA;AAAA,EAI5C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,cAAc,UAAU,OAAO,mBAAmB,QAAQ;AAAA,MACvE;AAAA,IACD;AAAA,IACA,OAAO;AAAA,MACN;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,kBAAkB;AAAA,QACnB;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,YAAY;AAAA,UACX;AAAA,YACC,MAAM;AAAA,YACN,YAAY;AAAA,cACX,OAAO;AAAA,cACP,cAAc;AAAA,YACf;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,cAAc,iBAAiB;AAAA,MAC5C;AAAA,IACD;AAAA,IACA,OAAO;AAAA,MACN;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,kBAAkB;AAAA,QACnB;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,UAAU;AAAA,IACX;AAAA,IACA,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aACC;AAAA,IACD,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,SAAS,CAAC;AAAA,QACV,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,kBACR;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,gBACD;AAAA,gBACA,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,cACV;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,SAAS,CAAC;AAAA,QACV,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,kBACR;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,gBACD;AAAA,gBACA,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,cACV;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,aAAa;AAAA,kBACZ,mBAAmB;AAAA,gBACpB;AAAA,gBACA,SAAS;AAAA,gBACT,aACC;AAAA,cACF;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,UAAU;AAAA,gBACV,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,UAAU;AAAA,gBACV,aAAa;AAAA,cACd;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,KAAK;AAAA,QACjB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,QAAQ,CAAC,QAAQ;AAAA,QACjB,WAAW,CAAC,KAAK;AAAA,QACjB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,KAAK;AAAA,QACjB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,KAAK;AAAA,QACjB,UAAU,CAAC,MAAM;AAAA,QACjB,eAAe,CAAC,QAAQ;AAAA,MACzB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS,CAAC;AAAA,IACV,aACC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,QAAQ,CAAC,QAAQ;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,QACjB,eAAe,CAAC,QAAQ;AAAA,MACzB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS,CAAC;AAAA,IACV,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,UACP;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,cACR;AAAA,gBACC,MAAM;AAAA,gBACN,OAAO;AAAA,cACR;AAAA,cACA;AAAA,gBACC,MAAM;AAAA,gBACN,OAAO;AAAA,cACR;AAAA,cACA;AAAA,gBACC,MAAM;AAAA,gBACN,OAAO;AAAA,cACR;AAAA,YACD;AAAA,YACA,SAAS;AAAA,YACT,aAAa;AAAA,UACd;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,cACR;AAAA,gBACC,MAAM;AAAA,gBACN,OAAO;AAAA,cACR;AAAA,cACA;AAAA,gBACC,MAAM;AAAA,gBACN,OAAO;AAAA,cACR;AAAA,YACD;AAAA,YACA,SAAS;AAAA,YACT,aAAa;AAAA,UACd;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,SAAS,CAAC;AAAA,QACV,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,kBACR;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,gBACD;AAAA,gBACA,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aACC;AAAA,cACF;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,SAAS,CAAC;AAAA,QACV,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,kBACR;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,gBACD;AAAA,gBACA,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,aAAa;AAAA,kBACZ,mBAAmB;AAAA,gBACpB;AAAA,gBACA,SAAS;AAAA,gBACT,aACC;AAAA,cACF;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,UAAU;AAAA,gBACV,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,UAAU;AAAA,gBACV,aAAa;AAAA,cACd;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;", "names": []}