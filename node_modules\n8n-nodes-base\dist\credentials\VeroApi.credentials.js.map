{"version": 3, "sources": ["../../credentials/VeroApi.credentials.ts"], "sourcesContent": ["import type { ICredentialType, INodeProperties } from 'n8n-workflow';\n\nexport class VeroApi implements ICredentialType {\n\tname = 'veroApi';\n\n\tdisplayName = 'Vero API';\n\n\tdocumentationUrl = 'vero';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Auth Token',\n\t\t\tname: 'authToken',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\tdefault: '',\n\t\t},\n\t];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,QAAmC;AAAA,EAAzC;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,MACV;AAAA,IACD;AAAA;AACD;", "names": []}