{"version": 3, "sources": ["../../../nodes/Discourse/UserGroupDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const userGroupOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdescription: 'Choose an operation',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userGroup'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Add',\n\t\t\t\tvalue: 'add',\n\t\t\t\tdescription: 'Create a user to group',\n\t\t\t\taction: 'Add a user to a group',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Remove',\n\t\t\t\tvalue: 'remove',\n\t\t\t\tdescription: 'Remove user from group',\n\t\t\t\taction: 'Remove a user from a group',\n\t\t\t},\n\t\t],\n\t\tdefault: 'add',\n\t},\n];\n\nexport const userGroupFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                                userGroup:add                               */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Usernames',\n\t\tname: 'usernames',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userGroup'],\n\t\t\t\toperation: ['add'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription: 'Usernames to add to group. Multiples can be defined separated by comma.',\n\t},\n\t{\n\t\tdisplayName: 'Group ID',\n\t\tname: 'groupId',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userGroup'],\n\t\t\t\toperation: ['add'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription: 'ID of the group',\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                userGroup:remove                            */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Usernames',\n\t\tname: 'usernames',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userGroup'],\n\t\t\t\toperation: ['remove'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription: 'Usernames to remove from group. Multiples can be defined separated by comma.',\n\t},\n\t{\n\t\tdisplayName: 'Group ID',\n\t\tname: 'groupId',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userGroup'],\n\t\t\t\toperation: ['remove'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription: 'ID of the group to remove',\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,sBAAyC;AAAA,EACrD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,MACvB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,kBAAqC;AAAA;AAAA;AAAA;AAAA,EAIjD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,WAAW;AAAA,QACtB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AACD;", "names": []}