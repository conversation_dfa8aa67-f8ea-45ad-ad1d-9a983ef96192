{"version": 3, "sources": ["../../../../nodes/Twitter/V1/TwitterV1.node.ts"], "sourcesContent": ["import ISO6391 from 'iso-639-1';\nimport type {\n\tIDataObject,\n\tIExecuteFunctions,\n\tILoadOptionsFunctions,\n\tINodeExecutionData,\n\tINodePropertyOptions,\n\tINodeType,\n\tINodeTypeBaseDescription,\n\tINodeTypeDescription,\n\tJsonObject,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes } from 'n8n-workflow';\n\nimport { directMessageFields, directMessageOperations } from './DirectMessageDescription';\nimport {\n\ttwitterApiRequest,\n\ttwitterApiRequestAllItems,\n\tuploadAttachments,\n} from './GenericFunctions';\nimport { tweetFields, tweetOperations } from './TweetDescription';\nimport type { ITweet, ITweetCreate } from './TweetInterface';\n\nexport class TwitterV1 implements INodeType {\n\tdescription: INodeTypeDescription;\n\n\tconstructor(baseDecription: INodeTypeBaseDescription) {\n\t\tthis.description = {\n\t\t\t...baseDecription,\n\t\t\tversion: 1,\n\t\t\tdescription: 'Consume Twitter API',\n\t\t\tsubtitle: '={{$parameter[\"operation\"] + \":\" + $parameter[\"resource\"]}}',\n\t\t\tdefaults: {\n\t\t\t\tname: 'Twitter',\n\t\t\t},\n\t\t\tinputs: [NodeConnectionTypes.Main],\n\t\t\toutputs: [NodeConnectionTypes.Main],\n\t\t\tcredentials: [\n\t\t\t\t{\n\t\t\t\t\tname: 'twitterOAuth1Api',\n\t\t\t\t\trequired: true,\n\t\t\t\t},\n\t\t\t],\n\t\t\tproperties: [\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Resource',\n\t\t\t\t\tname: 'resource',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\tnoDataExpression: true,\n\t\t\t\t\toptions: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Direct Message',\n\t\t\t\t\t\t\tvalue: 'directMessage',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Tweet',\n\t\t\t\t\t\t\tvalue: 'tweet',\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t\tdefault: 'tweet',\n\t\t\t\t},\n\t\t\t\t// DIRECT MESSAGE\n\t\t\t\t...directMessageOperations,\n\t\t\t\t...directMessageFields,\n\t\t\t\t// TWEET\n\t\t\t\t...tweetOperations,\n\t\t\t\t...tweetFields,\n\t\t\t],\n\t\t};\n\t}\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\t// Get all the available languages to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getLanguages(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst languages = ISO6391.getAllNames();\n\t\t\t\tfor (const language of languages) {\n\t\t\t\t\tconst languageName = language;\n\t\t\t\t\tconst languageId = ISO6391.getCode(language);\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: languageName,\n\t\t\t\t\t\tvalue: languageId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: INodeExecutionData[] = [];\n\t\tconst length = items.length;\n\t\tlet responseData;\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'directMessage') {\n\t\t\t\t\t//https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/sending-and-receiving/api-reference/new-event\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst userId = this.getNodeParameter('userId', i) as string;\n\t\t\t\t\t\tconst text = this.getNodeParameter('text', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tconst body: ITweetCreate = {\n\t\t\t\t\t\t\ttype: 'message_create',\n\t\t\t\t\t\t\tmessage_create: {\n\t\t\t\t\t\t\t\ttarget: {\n\t\t\t\t\t\t\t\t\trecipient_id: userId,\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tmessage_data: {\n\t\t\t\t\t\t\t\t\ttext,\n\t\t\t\t\t\t\t\t\tattachment: {},\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (additionalFields.attachment) {\n\t\t\t\t\t\t\tconst attachment = additionalFields.attachment as string;\n\n\t\t\t\t\t\t\tconst attachmentProperties: string[] = attachment.split(',').map((propertyName) => {\n\t\t\t\t\t\t\t\treturn propertyName.trim();\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\tconst medias = await uploadAttachments.call(this, attachmentProperties, i);\n\t\t\t\t\t\t\tbody.message_create.message_data.attachment = {\n\t\t\t\t\t\t\t\ttype: 'media',\n\t\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\t\tmedia: { id: medias[0].media_id_string },\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tdelete body.message_create.message_data.attachment;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await twitterApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/direct_messages/events/new.json',\n\t\t\t\t\t\t\t{ event: body },\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tresponseData = responseData.event;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (resource === 'tweet') {\n\t\t\t\t\t// https://developer.twitter.com/en/docs/tweets/post-and-engage/api-reference/post-statuses-update\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst text = this.getNodeParameter('text', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tconst body: ITweet = {\n\t\t\t\t\t\t\tstatus: text,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (additionalFields.inReplyToStatusId) {\n\t\t\t\t\t\t\tbody.in_reply_to_status_id = additionalFields.inReplyToStatusId as string;\n\t\t\t\t\t\t\tbody.auto_populate_reply_metadata = true;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.attachments) {\n\t\t\t\t\t\t\tconst attachments = additionalFields.attachments as string;\n\n\t\t\t\t\t\t\tconst attachmentProperties: string[] = attachments.split(',').map((propertyName) => {\n\t\t\t\t\t\t\t\treturn propertyName.trim();\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\tconst medias = await uploadAttachments.call(this, attachmentProperties, i);\n\n\t\t\t\t\t\t\tbody.media_ids = (medias as IDataObject[])\n\t\t\t\t\t\t\t\t.map((media: IDataObject) => media.media_id_string)\n\t\t\t\t\t\t\t\t.join(',');\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.possiblySensitive) {\n\t\t\t\t\t\t\tbody.possibly_sensitive = additionalFields.possiblySensitive as boolean;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.displayCoordinates) {\n\t\t\t\t\t\t\tbody.display_coordinates = additionalFields.displayCoordinates as boolean;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.locationFieldsUi) {\n\t\t\t\t\t\t\tconst locationUi = additionalFields.locationFieldsUi as IDataObject;\n\t\t\t\t\t\t\tif (locationUi.locationFieldsValues) {\n\t\t\t\t\t\t\t\tconst values = locationUi.locationFieldsValues as IDataObject;\n\t\t\t\t\t\t\t\tbody.lat = parseFloat(values.latitude as string);\n\t\t\t\t\t\t\t\tbody.long = parseFloat(values.longitude as string);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await twitterApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/statuses/update.json',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tbody as unknown as IDataObject,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t// https://developer.twitter.com/en/docs/twitter-api/v1/tweets/post-and-engage/api-reference/post-statuses-destroy-id\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tconst tweetId = this.getNodeParameter('tweetId', i) as string;\n\n\t\t\t\t\t\tresponseData = await twitterApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t`/statuses/destroy/${tweetId}.json`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t// https://developer.twitter.com/en/docs/tweets/search/api-reference/get-search-tweets\n\t\t\t\t\tif (operation === 'search') {\n\t\t\t\t\t\tconst q = this.getNodeParameter('searchText', i) as string;\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tconst qs: IDataObject = {\n\t\t\t\t\t\t\tq,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (additionalFields.includeEntities) {\n\t\t\t\t\t\t\tqs.include_entities = additionalFields.includeEntities as boolean;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.resultType) {\n\t\t\t\t\t\t\tqs.response_type = additionalFields.resultType as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.until) {\n\t\t\t\t\t\t\tqs.until = additionalFields.until as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.lang) {\n\t\t\t\t\t\t\tqs.lang = additionalFields.lang as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.locationFieldsUi) {\n\t\t\t\t\t\t\tconst locationUi = additionalFields.locationFieldsUi as IDataObject;\n\t\t\t\t\t\t\tif (locationUi.locationFieldsValues) {\n\t\t\t\t\t\t\t\tconst values = locationUi.locationFieldsValues as IDataObject;\n\t\t\t\t\t\t\t\tqs.geocode = `${values.latitude as string},${values.longitude as string},${\n\t\t\t\t\t\t\t\t\tvalues.distance\n\t\t\t\t\t\t\t\t}${values.radius}`;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tqs.tweet_mode = additionalFields.tweetMode || 'compat';\n\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await twitterApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'statuses',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/search/tweets.json',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tqs.count = this.getNodeParameter('limit', 0);\n\t\t\t\t\t\t\tresponseData = await twitterApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/search/tweets.json',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tresponseData = responseData.statuses;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twitter.com/en/docs/twitter-api/v1/tweets/post-and-engage/api-reference/post-favorites-create\n\t\t\t\t\tif (operation === 'like') {\n\t\t\t\t\t\tconst tweetId = this.getNodeParameter('tweetId', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tconst qs: IDataObject = {\n\t\t\t\t\t\t\tid: tweetId,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (additionalFields.includeEntities) {\n\t\t\t\t\t\t\tqs.include_entities = additionalFields.includeEntities as boolean;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await twitterApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/favorites/create.json',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twitter.com/en/docs/twitter-api/v1/tweets/post-and-engage/api-reference/post-statuses-retweet-id\n\t\t\t\t\tif (operation === 'retweet') {\n\t\t\t\t\t\tconst tweetId = this.getNodeParameter('tweetId', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tconst qs: IDataObject = {\n\t\t\t\t\t\t\tid: tweetId,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (additionalFields.trimUser) {\n\t\t\t\t\t\t\tqs.trim_user = additionalFields.trimUser as boolean;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await twitterApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t`/statuses/retweet/${tweetId}.json`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\tthis.helpers.returnJsonArray(responseData as IDataObject[]),\n\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t);\n\t\t\t\treturnData.push(...executionData);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\tconst executionErrorData = {\n\t\t\t\t\t\tjson: {\n\t\t\t\t\t\t\terror: (error as JsonObject).message,\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\t\t\t\t\treturnData.push(executionErrorData);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAAoB;AAYpB,0BAAoC;AAEpC,sCAA6D;AAC7D,8BAIO;AACP,8BAA6C;AAGtC,MAAM,UAA+B;AAAA,EAG3C,YAAY,gBAA0C;AA6CtD,mBAAU;AAAA,MACT,aAAa;AAAA;AAAA;AAAA,QAGZ,MAAM,eAA2E;AAChF,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,YAAY,iBAAAA,QAAQ,YAAY;AACtC,qBAAW,YAAY,WAAW;AACjC,kBAAM,eAAe;AACrB,kBAAM,aAAa,iBAAAA,QAAQ,QAAQ,QAAQ;AAC3C,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AA9DC,SAAK,cAAc;AAAA,MAClB,GAAG;AAAA,MACH,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA;AAAA,QAEA,GAAG;AAAA,QACH,GAAG;AAAA;AAAA,QAEH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAAA,EACD;AAAA,EAsBA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAAmC,CAAC;AAC1C,UAAM,SAAS,MAAM;AACrB,QAAI;AACJ,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,YAAI,aAAa,iBAAiB;AAEjC,cAAI,cAAc,UAAU;AAC3B,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC5C,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,kBAAM,OAAqB;AAAA,cAC1B,MAAM;AAAA,cACN,gBAAgB;AAAA,gBACf,QAAQ;AAAA,kBACP,cAAc;AAAA,gBACf;AAAA,gBACA,cAAc;AAAA,kBACb;AAAA,kBACA,YAAY,CAAC;AAAA,gBACd;AAAA,cACD;AAAA,YACD;AAEA,gBAAI,iBAAiB,YAAY;AAChC,oBAAM,aAAa,iBAAiB;AAEpC,oBAAM,uBAAiC,WAAW,MAAM,GAAG,EAAE,IAAI,CAAC,iBAAiB;AAClF,uBAAO,aAAa,KAAK;AAAA,cAC1B,CAAC;AAED,oBAAM,SAAS,MAAM,0CAAkB,KAAK,MAAM,sBAAsB,CAAC;AACzE,mBAAK,eAAe,aAAa,aAAa;AAAA,gBAC7C,MAAM;AAAA;AAAA,gBAEN,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,gBAAgB;AAAA,cACxC;AAAA,YACD,OAAO;AACN,qBAAO,KAAK,eAAe,aAAa;AAAA,YACzC;AAEA,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA;AAAA,cACA,EAAE,OAAO,KAAK;AAAA,YACf;AAEA,2BAAe,aAAa;AAAA,UAC7B;AAAA,QACD;AACA,YAAI,aAAa,SAAS;AAEzB,cAAI,cAAc,UAAU;AAC3B,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC5C,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,kBAAM,OAAe;AAAA,cACpB,QAAQ;AAAA,YACT;AAEA,gBAAI,iBAAiB,mBAAmB;AACvC,mBAAK,wBAAwB,iBAAiB;AAC9C,mBAAK,+BAA+B;AAAA,YACrC;AAEA,gBAAI,iBAAiB,aAAa;AACjC,oBAAM,cAAc,iBAAiB;AAErC,oBAAM,uBAAiC,YAAY,MAAM,GAAG,EAAE,IAAI,CAAC,iBAAiB;AACnF,uBAAO,aAAa,KAAK;AAAA,cAC1B,CAAC;AAED,oBAAM,SAAS,MAAM,0CAAkB,KAAK,MAAM,sBAAsB,CAAC;AAEzE,mBAAK,YAAa,OAChB,IAAI,CAAC,UAAuB,MAAM,eAAe,EACjD,KAAK,GAAG;AAAA,YACX;AAEA,gBAAI,iBAAiB,mBAAmB;AACvC,mBAAK,qBAAqB,iBAAiB;AAAA,YAC5C;AAEA,gBAAI,iBAAiB,oBAAoB;AACxC,mBAAK,sBAAsB,iBAAiB;AAAA,YAC7C;AAEA,gBAAI,iBAAiB,kBAAkB;AACtC,oBAAM,aAAa,iBAAiB;AACpC,kBAAI,WAAW,sBAAsB;AACpC,sBAAM,SAAS,WAAW;AAC1B,qBAAK,MAAM,WAAW,OAAO,QAAkB;AAC/C,qBAAK,OAAO,WAAW,OAAO,SAAmB;AAAA,cAClD;AAAA,YACD;AAEA,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA,qBAAqB,OAAO;AAAA,cAC5B,CAAC;AAAA,cACD,CAAC;AAAA,YACF;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,IAAI,KAAK,iBAAiB,cAAc,CAAC;AAC/C,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,kBAAM,KAAkB;AAAA,cACvB;AAAA,YACD;AAEA,gBAAI,iBAAiB,iBAAiB;AACrC,iBAAG,mBAAmB,iBAAiB;AAAA,YACxC;AAEA,gBAAI,iBAAiB,YAAY;AAChC,iBAAG,gBAAgB,iBAAiB;AAAA,YACrC;AAEA,gBAAI,iBAAiB,OAAO;AAC3B,iBAAG,QAAQ,iBAAiB;AAAA,YAC7B;AAEA,gBAAI,iBAAiB,MAAM;AAC1B,iBAAG,OAAO,iBAAiB;AAAA,YAC5B;AAEA,gBAAI,iBAAiB,kBAAkB;AACtC,oBAAM,aAAa,iBAAiB;AACpC,kBAAI,WAAW,sBAAsB;AACpC,sBAAM,SAAS,WAAW;AAC1B,mBAAG,UAAU,GAAG,OAAO,QAAkB,IAAI,OAAO,SAAmB,IACtE,OAAO,QACR,GAAG,OAAO,MAAM;AAAA,cACjB;AAAA,YACD;AAEA,eAAG,aAAa,iBAAiB,aAAa;AAE9C,gBAAI,WAAW;AACd,6BAAe,MAAM,kDAA0B;AAAA,gBAC9C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,iBAAG,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC3C,6BAAe,MAAM,0CAAkB;AAAA,gBACtC;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AACA,6BAAe,aAAa;AAAA,YAC7B;AAAA,UACD;AAEA,cAAI,cAAc,QAAQ;AACzB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,kBAAM,KAAkB;AAAA,cACvB,IAAI;AAAA,YACL;AAEA,gBAAI,iBAAiB,iBAAiB;AACrC,iBAAG,mBAAmB,iBAAiB;AAAA,YACxC;AAEA,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,WAAW;AAC5B,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,kBAAM,KAAkB;AAAA,cACvB,IAAI;AAAA,YACL;AAEA,gBAAI,iBAAiB,UAAU;AAC9B,iBAAG,YAAY,iBAAiB;AAAA,YACjC;AAEA,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA,qBAAqB,OAAO;AAAA,cAC5B,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AACA,cAAM,gBAAgB,KAAK,QAAQ;AAAA,UAClC,KAAK,QAAQ,gBAAgB,YAA6B;AAAA,UAC1D,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,QACzB;AACA,mBAAW,KAAK,GAAG,aAAa;AAAA,MACjC,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,gBAAM,qBAAqB;AAAA,YAC1B,MAAM;AAAA,cACL,OAAQ,MAAqB;AAAA,YAC9B;AAAA,UACD;AACA,qBAAW,KAAK,kBAAkB;AAClC;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AAEA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": ["ISO6391"]}