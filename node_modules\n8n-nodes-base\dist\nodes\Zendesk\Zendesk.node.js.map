{"version": 3, "sources": ["../../../nodes/Zendesk/Zendesk.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tIDataObject,\n\tILoadOptionsFunctions,\n\tINodeExecutionData,\n\tINodePropertyOptions,\n\tINodeType,\n\tINodeTypeDescription,\n\tJsonObject,\n} from 'n8n-workflow';\nimport { NodeApiError, NodeOperationError, NodeConnectionTypes } from 'n8n-workflow';\n\nimport { validateJSON, zendeskApiRequest, zendeskApiRequestAllItems } from './GenericFunctions';\nimport { organizationFields, organizationOperations } from './OrganizationDescription';\nimport { ticketFields, ticketOperations } from './TicketDescription';\nimport { ticketFieldFields, ticketFieldOperations } from './TicketFieldDescription';\nimport type { IComment, ITicket } from './TicketInterface';\nimport { userFields, userOperations } from './UserDescription';\n\nexport class Zendesk implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Zendesk',\n\t\tname: 'zendesk',\n\t\ticon: 'file:zendesk.svg',\n\t\tgroup: ['output'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume Zendesk API',\n\t\tdefaults: {\n\t\t\tname: 'Zendesk',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'zendeskApi',\n\t\t\t\trequired: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tauthentication: ['apiToken'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'zendeskOAuth2Api',\n\t\t\t\trequired: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tauthentication: ['oAuth2'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Authentication',\n\t\t\t\tname: 'authentication',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'API Token',\n\t\t\t\t\t\tvalue: 'apiToken',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'OAuth2',\n\t\t\t\t\t\tvalue: 'oAuth2',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'apiToken',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Ticket',\n\t\t\t\t\t\tvalue: 'ticket',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Tickets are the means through which your end users (customers) communicate with agents in Zendesk Support',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Ticket Field',\n\t\t\t\t\t\tvalue: 'ticketField',\n\t\t\t\t\t\tdescription: 'Manage system and custom ticket fields',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'User',\n\t\t\t\t\t\tvalue: 'user',\n\t\t\t\t\t\tdescription: 'Manage users',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Organization',\n\t\t\t\t\t\tvalue: 'organization',\n\t\t\t\t\t\tdescription: 'Manage organizations',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'ticket',\n\t\t\t},\n\t\t\t// TICKET\n\t\t\t...ticketOperations,\n\t\t\t...ticketFields,\n\t\t\t// TICKET FIELD\n\t\t\t...ticketFieldOperations,\n\t\t\t...ticketFieldFields,\n\t\t\t// USER\n\t\t\t...userOperations,\n\t\t\t...userFields,\n\t\t\t// ORGANIZATION\n\t\t\t...organizationOperations,\n\t\t\t...organizationFields,\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\t// Get all the custom fields to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getCustomFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst customFields = [\n\t\t\t\t\t'text',\n\t\t\t\t\t'textarea',\n\t\t\t\t\t'date',\n\t\t\t\t\t'integer',\n\t\t\t\t\t'decimal',\n\t\t\t\t\t'regexp',\n\t\t\t\t\t'multiselect',\n\t\t\t\t\t'tagger',\n\t\t\t\t];\n\t\t\t\tconst fields = await zendeskApiRequestAllItems.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'ticket_fields',\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/ticket_fields',\n\t\t\t\t);\n\t\t\t\tfor (const field of fields) {\n\t\t\t\t\tif (customFields.includes(field.type as string)) {\n\t\t\t\t\t\tconst fieldName = field.title;\n\t\t\t\t\t\tconst fieldId = field.id;\n\t\t\t\t\t\treturnData.push({\n\t\t\t\t\t\t\tname: fieldName,\n\t\t\t\t\t\t\tvalue: fieldId,\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the groups to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getGroups(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst groups = await zendeskApiRequestAllItems.call(this, 'groups', 'GET', '/groups');\n\t\t\t\tfor (const group of groups) {\n\t\t\t\t\tconst groupName = group.name;\n\t\t\t\t\tconst groupId = group.id;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: groupName,\n\t\t\t\t\t\tvalue: groupId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the tags to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getTags(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst tags = await zendeskApiRequestAllItems.call(this, 'tags', 'GET', '/tags');\n\t\t\t\tfor (const tag of tags) {\n\t\t\t\t\tconst tagName = tag.name;\n\t\t\t\t\tconst tagId = tag.name;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: tagName,\n\t\t\t\t\t\tvalue: tagId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\n\t\t\t// Get all the locales to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getLocales(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst locales = await zendeskApiRequestAllItems.call(this, 'locales', 'GET', '/locales');\n\t\t\t\tfor (const locale of locales) {\n\t\t\t\t\tconst localeName = `${locale.locale} - ${locale.name}`;\n\t\t\t\t\tconst localeId = locale.locale;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: localeName,\n\t\t\t\t\t\tvalue: localeId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\n\t\t\t// Get all the user fields to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getUserFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst fields = await zendeskApiRequestAllItems.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'user_fields',\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/user_fields',\n\t\t\t\t);\n\t\t\t\tfor (const field of fields) {\n\t\t\t\t\tconst fieldName = field.title;\n\t\t\t\t\tconst fieldId = field.key;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: fieldName,\n\t\t\t\t\t\tvalue: fieldId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\n\t\t\t// Get all the organization fields to display them to the user for easy selection\n\t\t\tasync getOrganizationFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst fields = await zendeskApiRequestAllItems.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'organization_fields',\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/organization_fields',\n\t\t\t\t);\n\t\t\t\tfor (const field of fields) {\n\t\t\t\t\tconst fieldName = field.title;\n\t\t\t\t\tconst fieldId = field.key;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: fieldName,\n\t\t\t\t\t\tvalue: fieldId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\n\t\t\tasync getOrganizations(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst fields = await zendeskApiRequestAllItems.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'organizations',\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/organizations',\n\t\t\t\t\t{},\n\t\t\t\t\t{},\n\t\t\t\t);\n\t\t\t\tfor (const field of fields) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: field.name,\n\t\t\t\t\t\tvalue: field.id,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: INodeExecutionData[] = [];\n\t\tconst length = items.length;\n\t\tconst qs: IDataObject = {};\n\t\tlet responseData;\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\t\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/introduction/\n\t\t\t\tif (resource === 'ticket') {\n\t\t\t\t\t//https://developer.zendesk.com/rest_api/docs/support/tickets\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst description = this.getNodeParameter('description', i) as string;\n\t\t\t\t\t\tconst jsonParameters = this.getNodeParameter('jsonParameters', i);\n\t\t\t\t\t\tconst comment: IComment = {\n\t\t\t\t\t\t\tbody: description,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tconst body: ITicket = {\n\t\t\t\t\t\t\tcomment,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tif (jsonParameters) {\n\t\t\t\t\t\t\tconst additionalFieldsJson = this.getNodeParameter(\n\t\t\t\t\t\t\t\t'additionalFieldsJson',\n\t\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t) as string;\n\n\t\t\t\t\t\t\tif (additionalFieldsJson !== '') {\n\t\t\t\t\t\t\t\tif (validateJSON(additionalFieldsJson) !== undefined) {\n\t\t\t\t\t\t\t\t\tObject.assign(body, JSON.parse(additionalFieldsJson));\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t\t\t\t'Additional fields must be a valid JSON',\n\t\t\t\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\t\tif (additionalFields.type) {\n\t\t\t\t\t\t\t\tbody.type = additionalFields.type as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.externalId) {\n\t\t\t\t\t\t\t\tbody.external_id = additionalFields.externalId as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.subject) {\n\t\t\t\t\t\t\t\tbody.subject = additionalFields.subject as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.status) {\n\t\t\t\t\t\t\t\tbody.status = additionalFields.status as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.recipient) {\n\t\t\t\t\t\t\t\tbody.recipient = additionalFields.recipient as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.group) {\n\t\t\t\t\t\t\t\tbody.group_id = additionalFields.group as number;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.tags) {\n\t\t\t\t\t\t\t\tbody.tags = additionalFields.tags as string[];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (additionalFields.customFieldsUi) {\n\t\t\t\t\t\t\t\tbody.custom_fields = (additionalFields.customFieldsUi as IDataObject)\n\t\t\t\t\t\t\t\t\t.customFieldsValues as IDataObject[];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(this, 'POST', '/tickets', { ticket: body });\n\t\t\t\t\t\tresponseData = responseData.ticket;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/rest_api/docs/support/tickets#update-ticket\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst ticketId = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tconst jsonParameters = this.getNodeParameter('jsonParameters', i);\n\t\t\t\t\t\tconst body: ITicket = {};\n\n\t\t\t\t\t\tif (jsonParameters) {\n\t\t\t\t\t\t\tconst updateFieldsJson = this.getNodeParameter('updateFieldsJson', i) as string;\n\n\t\t\t\t\t\t\tif (updateFieldsJson !== '') {\n\t\t\t\t\t\t\t\tif (validateJSON(updateFieldsJson) !== undefined) {\n\t\t\t\t\t\t\t\t\tObject.assign(body, JSON.parse(updateFieldsJson));\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t\t\t\t'Additional fields must be a valid JSON',\n\t\t\t\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\t\tif (updateFields.type) {\n\t\t\t\t\t\t\t\tbody.type = updateFields.type as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (updateFields.externalId) {\n\t\t\t\t\t\t\t\tbody.external_id = updateFields.externalId as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (updateFields.subject) {\n\t\t\t\t\t\t\t\tbody.subject = updateFields.subject as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (updateFields.status) {\n\t\t\t\t\t\t\t\tbody.status = updateFields.status as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (updateFields.recipient) {\n\t\t\t\t\t\t\t\tbody.recipient = updateFields.recipient as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (updateFields.group) {\n\t\t\t\t\t\t\t\tbody.group_id = updateFields.group as number;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (updateFields.tags) {\n\t\t\t\t\t\t\t\tbody.tags = updateFields.tags as string[];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (updateFields.customFieldsUi) {\n\t\t\t\t\t\t\t\tbody.custom_fields = (updateFields.customFieldsUi as IDataObject)\n\t\t\t\t\t\t\t\t\t.customFieldsValues as IDataObject[];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (updateFields.assigneeEmail) {\n\t\t\t\t\t\t\t\tbody.assignee_email = updateFields.assigneeEmail as string;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (updateFields.internalNote) {\n\t\t\t\t\t\t\t\tconst comment: IComment = {\n\t\t\t\t\t\t\t\t\thtml_body: updateFields.internalNote as string,\n\t\t\t\t\t\t\t\t\tpublic: false,\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\tbody.comment = comment;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (updateFields.publicReply) {\n\t\t\t\t\t\t\t\tconst comment: IComment = {\n\t\t\t\t\t\t\t\t\tbody: updateFields.publicReply as string,\n\t\t\t\t\t\t\t\t\tpublic: true,\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\tbody.comment = comment;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(this, 'PUT', `/tickets/${ticketId}`, {\n\t\t\t\t\t\t\tticket: body,\n\t\t\t\t\t\t});\n\t\t\t\t\t\tresponseData = responseData.ticket;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/rest_api/docs/support/tickets#show-ticket\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/tickets/suspended_tickets/#show-suspended-ticket\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tconst ticketType = this.getNodeParameter('ticketType', i) as string;\n\t\t\t\t\t\tconst ticketId = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tconst endpoint =\n\t\t\t\t\t\t\tticketType === 'regular' ? `/tickets/${ticketId}` : `/suspended_tickets/${ticketId}`;\n\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(this, 'GET', endpoint, {});\n\t\t\t\t\t\tresponseData = responseData.ticket || responseData.suspended_ticket;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/rest_api/docs/support/search#list-search-results\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/tickets/suspended_tickets/#list-suspended-tickets\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst ticketType = this.getNodeParameter('ticketType', i) as string;\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tqs.query = 'type:ticket';\n\t\t\t\t\t\tif (options.query) {\n\t\t\t\t\t\t\tqs.query += ` ${options.query}`;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.status) {\n\t\t\t\t\t\t\tqs.query += ` status:${options.status}`;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.group) {\n\t\t\t\t\t\t\tqs.query += ` group:${options.group}`;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (options.sortBy) {\n\t\t\t\t\t\t\tqs.sort_by = options.sortBy;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.sortOrder) {\n\t\t\t\t\t\t\tqs.sort_order = options.sortOrder;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconst endpoint = ticketType === 'regular' ? '/search' : '/suspended_tickets';\n\t\t\t\t\t\tconst property = ticketType === 'regular' ? 'results' : 'suspended_tickets';\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await zendeskApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\tproperty,\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\tendpoint,\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tqs.per_page = limit;\n\t\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(this, 'GET', endpoint, {}, qs);\n\t\t\t\t\t\t\tresponseData = responseData.results || responseData.suspended_tickets;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/rest_api/docs/support/tickets#delete-ticket\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/tickets/suspended_tickets/#delete-suspended-ticket\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tconst ticketType = this.getNodeParameter('ticketType', i) as string;\n\t\t\t\t\t\tconst ticketId = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tconst endpoint =\n\t\t\t\t\t\t\tticketType === 'regular' ? `/tickets/${ticketId}` : `/suspended_tickets/${ticketId}`;\n\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(this, 'DELETE', endpoint, {});\n\t\t\t\t\t\tresponseData = { success: true };\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/tickets/suspended_tickets/#recover-suspended-ticket\n\t\t\t\t\tif (operation === 'recover') {\n\t\t\t\t\t\tconst ticketId = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'PUT',\n\t\t\t\t\t\t\t\t`/suspended_tickets/${ticketId}/recover`,\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tresponseData = responseData.ticket;\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tthrow new NodeApiError(this.getNode(), error as JsonObject);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/tickets/ticket_fields/\n\t\t\t\tif (resource === 'ticketField') {\n\t\t\t\t\t//https://developer.zendesk.com/rest_api/docs/support/tickets#show-ticket\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tconst ticketFieldId = this.getNodeParameter('ticketFieldId', i) as string;\n\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t`/ticket_fields/${ticketFieldId}`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t);\n\t\t\t\t\t\tresponseData = responseData.ticket_field;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/rest_api/docs/support/ticket_fields#list-ticket-fields\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await zendeskApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'ticket_fields',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/ticket_fields',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tqs.limit = limit;\n\t\t\t\t\t\t\tresponseData = await zendeskApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'ticket_fields',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/ticket_fields',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tresponseData = responseData.slice(0, limit);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/users/users/\n\t\t\t\tif (resource === 'user') {\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/users/users/#create-user\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst name = this.getNodeParameter('name', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tname,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\n\t\t\t\t\t\tif (body.userFieldsUi) {\n\t\t\t\t\t\t\tconst userFieldsUI = (body.userFieldsUi as IDataObject)\n\t\t\t\t\t\t\t\t.userFieldValues as IDataObject[];\n\t\t\t\t\t\t\tif (userFieldsUI) {\n\t\t\t\t\t\t\t\tbody.user_fields = {};\n\t\t\t\t\t\t\t\tfor (const userField of userFieldsUI) {\n\t\t\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\t\t\tbody.user_fields[userField.field] = userField.value;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tdelete body.userFieldsUi;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(this, 'POST', '/users', { user: body });\n\t\t\t\t\t\tresponseData = responseData.user;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/users/users/#update-user\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst userId = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\tconst body: IDataObject = {};\n\n\t\t\t\t\t\tObject.assign(body, updateFields);\n\n\t\t\t\t\t\tif (body.userFieldsUi) {\n\t\t\t\t\t\t\tconst userFieldsUI = (body.userFieldsUi as IDataObject)\n\t\t\t\t\t\t\t\t.userFieldValues as IDataObject[];\n\t\t\t\t\t\t\tif (userFieldsUI) {\n\t\t\t\t\t\t\t\tbody.user_fields = {};\n\t\t\t\t\t\t\t\tfor (const userField of userFieldsUI) {\n\t\t\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\t\t\tbody.user_fields[userField.field] = userField.value;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tdelete body.userFieldsUi;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(this, 'PUT', `/users/${userId}`, {\n\t\t\t\t\t\t\tuser: body,\n\t\t\t\t\t\t});\n\t\t\t\t\t\tresponseData = responseData.user;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/users/users/#show-user\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tconst userId = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(this, 'GET', `/users/${userId}`, {});\n\t\t\t\t\t\tresponseData = responseData.user;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/users/users/#list-users\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tconst options = this.getNodeParameter('filters', i);\n\n\t\t\t\t\t\tObject.assign(qs, options);\n\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await zendeskApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'users',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/users',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tqs.per_page = limit;\n\t\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(this, 'GET', '/users', {}, qs);\n\t\t\t\t\t\t\tresponseData = responseData.users;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/organizations/organizations/#list-organizations\n\t\t\t\t\tif (operation === 'getOrganizations') {\n\t\t\t\t\t\tconst userId = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t`/users/${userId}/organizations`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t);\n\t\t\t\t\t\tresponseData = responseData.organizations;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/users/users/#search-users\n\t\t\t\t\tif (operation === 'search') {\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tconst options = this.getNodeParameter('filters', i);\n\n\t\t\t\t\t\tObject.assign(qs, options);\n\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await zendeskApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'users',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/users/search',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tqs.per_page = limit;\n\t\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(this, 'GET', '/users/search', {}, qs);\n\t\t\t\t\t\t\tresponseData = responseData.users;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/users/users/#delete-user\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tconst userId = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(this, 'DELETE', `/users/${userId}`, {});\n\t\t\t\t\t\tresponseData = responseData.user;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/users/users/#show-user-related-information\n\t\t\t\t\tif (operation === 'getRelatedData') {\n\t\t\t\t\t\tconst userId = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t`/users/${userId}/related`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t);\n\t\t\t\t\t\tresponseData = responseData.user_related;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/organizations/organizations/\n\t\t\t\tif (resource === 'organization') {\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/organizations/organizations/#create-organization\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst name = this.getNodeParameter('name', i) as string;\n\n\t\t\t\t\t\tconst body: IDataObject & {\n\t\t\t\t\t\t\tname: string;\n\t\t\t\t\t\t\torganization_fields?: { [key: string]: object | string };\n\t\t\t\t\t\t} = {\n\t\t\t\t\t\t\tname,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst { organizationFieldsUi, ...rest } = this.getNodeParameter(\n\t\t\t\t\t\t\t'additionalFields',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t) as IDataObject & {\n\t\t\t\t\t\t\torganizationFieldsUi?: {\n\t\t\t\t\t\t\t\torganizationFieldValues: Array<{ field: string; value: string }>;\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tObject.assign(body, rest);\n\n\t\t\t\t\t\tif (organizationFieldsUi?.organizationFieldValues.length) {\n\t\t\t\t\t\t\tconst organizationFieldsUI = organizationFieldsUi.organizationFieldValues;\n\t\t\t\t\t\t\tif (organizationFieldsUI.length) {\n\t\t\t\t\t\t\t\tbody.organization_fields = {};\n\t\t\t\t\t\t\t\tfor (const organizationField of organizationFieldsUI) {\n\t\t\t\t\t\t\t\t\tbody.organization_fields[organizationField.field] = organizationField.value;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(this, 'POST', '/organizations', {\n\t\t\t\t\t\t\torganization: body,\n\t\t\t\t\t\t});\n\t\t\t\t\t\tresponseData = responseData.organization;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/organizations/organizations/#delete-organization\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tconst organizationId = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tawait zendeskApiRequest.call(this, 'DELETE', `/organizations/${organizationId}`, {});\n\t\t\t\t\t\tresponseData = { success: true };\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/organizations/organizations/#count-organizations\n\t\t\t\t\tif (operation === 'count') {\n\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(this, 'GET', '/organizations/count', {});\n\t\t\t\t\t\tresponseData = responseData.count;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/organizations/organizations/#show-organization\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tconst organizationId = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t`/organizations/${organizationId}`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t);\n\t\t\t\t\t\tresponseData = responseData.organization;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/organizations/organizations/#list-organizations\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await zendeskApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'organizations',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/organizations',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tqs.per_page = limit;\n\t\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(this, 'GET', '/organizations', {}, qs);\n\t\t\t\t\t\t\tresponseData = responseData.organizations;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/organizations/organizations/#show-organizations-related-information\n\t\t\t\t\tif (operation === 'getRelatedData') {\n\t\t\t\t\t\tconst organizationId = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t`/organizations/${organizationId}/related`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t);\n\t\t\t\t\t\tresponseData = responseData.organization_related;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.zendesk.com/api-reference/ticketing/organizations/organizations/#update-organization\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst organizationId = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tconst body: IDataObject & { organization_fields?: { [key: string]: object | string } } =\n\t\t\t\t\t\t\t{};\n\n\t\t\t\t\t\tconst { organizationFieldsUi, ...rest } = this.getNodeParameter(\n\t\t\t\t\t\t\t'updateFields',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t) as IDataObject & {\n\t\t\t\t\t\t\torganizationFieldsUi?: {\n\t\t\t\t\t\t\t\torganizationFieldValues: Array<{ field: string; value: string }>;\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tObject.assign(body, rest);\n\n\t\t\t\t\t\tif (organizationFieldsUi?.organizationFieldValues.length) {\n\t\t\t\t\t\t\tconst organizationFieldsUI = organizationFieldsUi.organizationFieldValues;\n\t\t\t\t\t\t\tif (organizationFieldsUI.length) {\n\t\t\t\t\t\t\t\tbody.organization_fields = {};\n\t\t\t\t\t\t\t\tfor (const organizationField of organizationFieldsUI) {\n\t\t\t\t\t\t\t\t\tbody.organization_fields[organizationField.field] = organizationField.value;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zendeskApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'PUT',\n\t\t\t\t\t\t\t`/organizations/${organizationId}`,\n\t\t\t\t\t\t\t{ organization: body },\n\t\t\t\t\t\t);\n\t\t\t\t\t\tresponseData = responseData.organization;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\tthis.helpers.returnJsonArray(responseData as IDataObject),\n\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t);\n\t\t\t\treturnData.push(...executionData);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ json: { error: error.message } });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,0BAAsE;AAEtE,8BAA2E;AAC3E,qCAA2D;AAC3D,+BAA+C;AAC/C,oCAAyD;AAEzD,6BAA2C;AAEpC,MAAM,QAA6B;AAAA,EAAnC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,QAAQ;AAAA,MAChB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,gBAAgB,CAAC,UAAU;AAAA,YAC5B;AAAA,UACD;AAAA,QACD;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,gBAAgB,CAAC,QAAQ;AAAA,YAC1B;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aACC;AAAA,YACF;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA;AAAA,QAEA,GAAG;AAAA,QACH,GAAG;AAAA;AAAA,QAEH,GAAG;AAAA,QACH,GAAG;AAAA;AAAA,QAEH,GAAG;AAAA,QACH,GAAG;AAAA;AAAA,QAEH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA;AAAA;AAAA,QAGZ,MAAM,kBAA8E;AACnF,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,eAAe;AAAA,YACpB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AACA,gBAAM,SAAS,MAAM,kDAA0B;AAAA,YAC9C;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AACA,qBAAW,SAAS,QAAQ;AAC3B,gBAAI,aAAa,SAAS,MAAM,IAAc,GAAG;AAChD,oBAAM,YAAY,MAAM;AACxB,oBAAM,UAAU,MAAM;AACtB,yBAAW,KAAK;AAAA,gBACf,MAAM;AAAA,gBACN,OAAO;AAAA,cACR,CAAC;AAAA,YACF;AAAA,UACD;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,YAAwE;AAC7E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,SAAS,MAAM,kDAA0B,KAAK,MAAM,UAAU,OAAO,SAAS;AACpF,qBAAW,SAAS,QAAQ;AAC3B,kBAAM,YAAY,MAAM;AACxB,kBAAM,UAAU,MAAM;AACtB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,UAAsE;AAC3E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,OAAO,MAAM,kDAA0B,KAAK,MAAM,QAAQ,OAAO,OAAO;AAC9E,qBAAW,OAAO,MAAM;AACvB,kBAAM,UAAU,IAAI;AACpB,kBAAM,QAAQ,IAAI;AAClB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAIA,MAAM,aAAyE;AAC9E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,UAAU,MAAM,kDAA0B,KAAK,MAAM,WAAW,OAAO,UAAU;AACvF,qBAAW,UAAU,SAAS;AAC7B,kBAAM,aAAa,GAAG,OAAO,MAAM,MAAM,OAAO,IAAI;AACpD,kBAAM,WAAW,OAAO;AACxB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAIA,MAAM,gBAA4E;AACjF,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,SAAS,MAAM,kDAA0B;AAAA,YAC9C;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AACA,qBAAW,SAAS,QAAQ;AAC3B,kBAAM,YAAY,MAAM;AACxB,kBAAM,UAAU,MAAM;AACtB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA,QAGA,MAAM,wBAAoF;AACzF,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,SAAS,MAAM,kDAA0B;AAAA,YAC9C;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AACA,qBAAW,SAAS,QAAQ;AAC3B,kBAAM,YAAY,MAAM;AACxB,kBAAM,UAAU,MAAM;AACtB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,QAEA,MAAM,mBAA+E;AACpF,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,SAAS,MAAM,kDAA0B;AAAA,YAC9C;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,CAAC;AAAA,YACD,CAAC;AAAA,UACF;AACA,qBAAW,SAAS,QAAQ;AAC3B,uBAAW,KAAK;AAAA,cACf,MAAM,MAAM;AAAA,cACZ,OAAO,MAAM;AAAA,YACd,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAAmC,CAAC;AAC1C,UAAM,SAAS,MAAM;AACrB,UAAM,KAAkB,CAAC;AACzB,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,cAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,cAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,YAAI,aAAa,UAAU;AAE1B,cAAI,cAAc,UAAU;AAC3B,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAC1D,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,UAAoB;AAAA,cACzB,MAAM;AAAA,YACP;AACA,kBAAM,OAAgB;AAAA,cACrB;AAAA,YACD;AACA,gBAAI,gBAAgB;AACnB,oBAAM,uBAAuB,KAAK;AAAA,gBACjC;AAAA,gBACA;AAAA,cACD;AAEA,kBAAI,yBAAyB,IAAI;AAChC,wBAAI,sCAAa,oBAAoB,MAAM,QAAW;AACrD,yBAAO,OAAO,MAAM,KAAK,MAAM,oBAAoB,CAAC;AAAA,gBACrD,OAAO;AACN,wBAAM,IAAI;AAAA,oBACT,KAAK,QAAQ;AAAA,oBACb;AAAA,oBACA,EAAE,WAAW,EAAE;AAAA,kBAChB;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,oBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,kBAAI,iBAAiB,MAAM;AAC1B,qBAAK,OAAO,iBAAiB;AAAA,cAC9B;AACA,kBAAI,iBAAiB,YAAY;AAChC,qBAAK,cAAc,iBAAiB;AAAA,cACrC;AACA,kBAAI,iBAAiB,SAAS;AAC7B,qBAAK,UAAU,iBAAiB;AAAA,cACjC;AACA,kBAAI,iBAAiB,QAAQ;AAC5B,qBAAK,SAAS,iBAAiB;AAAA,cAChC;AACA,kBAAI,iBAAiB,WAAW;AAC/B,qBAAK,YAAY,iBAAiB;AAAA,cACnC;AACA,kBAAI,iBAAiB,OAAO;AAC3B,qBAAK,WAAW,iBAAiB;AAAA,cAClC;AACA,kBAAI,iBAAiB,MAAM;AAC1B,qBAAK,OAAO,iBAAiB;AAAA,cAC9B;AACA,kBAAI,iBAAiB,gBAAgB;AACpC,qBAAK,gBAAiB,iBAAiB,eACrC;AAAA,cACH;AAAA,YACD;AACA,2BAAe,MAAM,0CAAkB,KAAK,MAAM,QAAQ,YAAY,EAAE,QAAQ,KAAK,CAAC;AACtF,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,WAAW,KAAK,iBAAiB,MAAM,CAAC;AAC9C,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,OAAgB,CAAC;AAEvB,gBAAI,gBAAgB;AACnB,oBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,kBAAI,qBAAqB,IAAI;AAC5B,wBAAI,sCAAa,gBAAgB,MAAM,QAAW;AACjD,yBAAO,OAAO,MAAM,KAAK,MAAM,gBAAgB,CAAC;AAAA,gBACjD,OAAO;AACN,wBAAM,IAAI;AAAA,oBACT,KAAK,QAAQ;AAAA,oBACb;AAAA,oBACA,EAAE,WAAW,EAAE;AAAA,kBAChB;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,oBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,kBAAI,aAAa,MAAM;AACtB,qBAAK,OAAO,aAAa;AAAA,cAC1B;AACA,kBAAI,aAAa,YAAY;AAC5B,qBAAK,cAAc,aAAa;AAAA,cACjC;AACA,kBAAI,aAAa,SAAS;AACzB,qBAAK,UAAU,aAAa;AAAA,cAC7B;AACA,kBAAI,aAAa,QAAQ;AACxB,qBAAK,SAAS,aAAa;AAAA,cAC5B;AACA,kBAAI,aAAa,WAAW;AAC3B,qBAAK,YAAY,aAAa;AAAA,cAC/B;AACA,kBAAI,aAAa,OAAO;AACvB,qBAAK,WAAW,aAAa;AAAA,cAC9B;AACA,kBAAI,aAAa,MAAM;AACtB,qBAAK,OAAO,aAAa;AAAA,cAC1B;AACA,kBAAI,aAAa,gBAAgB;AAChC,qBAAK,gBAAiB,aAAa,eACjC;AAAA,cACH;AACA,kBAAI,aAAa,eAAe;AAC/B,qBAAK,iBAAiB,aAAa;AAAA,cACpC;AACA,kBAAI,aAAa,cAAc;AAC9B,sBAAM,UAAoB;AAAA,kBACzB,WAAW,aAAa;AAAA,kBACxB,QAAQ;AAAA,gBACT;AACA,qBAAK,UAAU;AAAA,cAChB;AAEA,kBAAI,aAAa,aAAa;AAC7B,sBAAM,UAAoB;AAAA,kBACzB,MAAM,aAAa;AAAA,kBACnB,QAAQ;AAAA,gBACT;AACA,qBAAK,UAAU;AAAA,cAChB;AAAA,YACD;AACA,2BAAe,MAAM,0CAAkB,KAAK,MAAM,OAAO,YAAY,QAAQ,IAAI;AAAA,cAChF,QAAQ;AAAA,YACT,CAAC;AACD,2BAAe,aAAa;AAAA,UAC7B;AAGA,cAAI,cAAc,OAAO;AACxB,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AACxD,kBAAM,WAAW,KAAK,iBAAiB,MAAM,CAAC;AAC9C,kBAAM,WACL,eAAe,YAAY,YAAY,QAAQ,KAAK,sBAAsB,QAAQ;AACnF,2BAAe,MAAM,0CAAkB,KAAK,MAAM,OAAO,UAAU,CAAC,CAAC;AACrE,2BAAe,aAAa,UAAU,aAAa;AAAA,UACpD;AAGA,cAAI,cAAc,UAAU;AAC3B,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AACxD,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,eAAG,QAAQ;AACX,gBAAI,QAAQ,OAAO;AAClB,iBAAG,SAAS,IAAI,QAAQ,KAAK;AAAA,YAC9B;AACA,gBAAI,QAAQ,QAAQ;AACnB,iBAAG,SAAS,WAAW,QAAQ,MAAM;AAAA,YACtC;AACA,gBAAI,QAAQ,OAAO;AAClB,iBAAG,SAAS,UAAU,QAAQ,KAAK;AAAA,YACpC;AAEA,gBAAI,QAAQ,QAAQ;AACnB,iBAAG,UAAU,QAAQ;AAAA,YACtB;AACA,gBAAI,QAAQ,WAAW;AACtB,iBAAG,aAAa,QAAQ;AAAA,YACzB;AACA,kBAAM,WAAW,eAAe,YAAY,YAAY;AACxD,kBAAM,WAAW,eAAe,YAAY,YAAY;AACxD,gBAAI,WAAW;AACd,6BAAe,MAAM,kDAA0B;AAAA,gBAC9C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,oBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,iBAAG,WAAW;AACd,6BAAe,MAAM,0CAAkB,KAAK,MAAM,OAAO,UAAU,CAAC,GAAG,EAAE;AACzE,6BAAe,aAAa,WAAW,aAAa;AAAA,YACrD;AAAA,UACD;AAGA,cAAI,cAAc,UAAU;AAC3B,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AACxD,kBAAM,WAAW,KAAK,iBAAiB,MAAM,CAAC;AAC9C,kBAAM,WACL,eAAe,YAAY,YAAY,QAAQ,KAAK,sBAAsB,QAAQ;AACnF,2BAAe,MAAM,0CAAkB,KAAK,MAAM,UAAU,UAAU,CAAC,CAAC;AACxE,2BAAe,EAAE,SAAS,KAAK;AAAA,UAChC;AAEA,cAAI,cAAc,WAAW;AAC5B,kBAAM,WAAW,KAAK,iBAAiB,MAAM,CAAC;AAC9C,gBAAI;AACH,6BAAe,MAAM,0CAAkB;AAAA,gBACtC;AAAA,gBACA;AAAA,gBACA,sBAAsB,QAAQ;AAAA,gBAC9B,CAAC;AAAA,cACF;AACA,6BAAe,aAAa;AAAA,YAC7B,SAAS,OAAO;AACf,oBAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,KAAmB;AAAA,YAC3D;AAAA,UACD;AAAA,QACD;AAEA,YAAI,aAAa,eAAe;AAE/B,cAAI,cAAc,OAAO;AACxB,kBAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,CAAC;AAC9D,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA,kBAAkB,aAAa;AAAA,cAC/B,CAAC;AAAA,YACF;AACA,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,gBAAI,WAAW;AACd,6BAAe,MAAM,kDAA0B;AAAA,gBAC9C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,oBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,iBAAG,QAAQ;AACX,6BAAe,MAAM,kDAA0B;AAAA,gBAC9C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AACA,6BAAe,aAAa,MAAM,GAAG,KAAK;AAAA,YAC3C;AAAA,UACD;AAAA,QACD;AAEA,YAAI,aAAa,QAAQ;AAExB,cAAI,cAAc,UAAU;AAC3B,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC5C,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,kBAAM,OAAoB;AAAA,cACzB;AAAA,YACD;AAEA,mBAAO,OAAO,MAAM,gBAAgB;AAEpC,gBAAI,KAAK,cAAc;AACtB,oBAAM,eAAgB,KAAK,aACzB;AACF,kBAAI,cAAc;AACjB,qBAAK,cAAc,CAAC;AACpB,2BAAW,aAAa,cAAc;AAErC,uBAAK,YAAY,UAAU,KAAK,IAAI,UAAU;AAAA,gBAC/C;AACA,uBAAO,KAAK;AAAA,cACb;AAAA,YACD;AACA,2BAAe,MAAM,0CAAkB,KAAK,MAAM,QAAQ,UAAU,EAAE,MAAM,KAAK,CAAC;AAClF,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,SAAS,KAAK,iBAAiB,MAAM,CAAC;AAC5C,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,kBAAM,OAAoB,CAAC;AAE3B,mBAAO,OAAO,MAAM,YAAY;AAEhC,gBAAI,KAAK,cAAc;AACtB,oBAAM,eAAgB,KAAK,aACzB;AACF,kBAAI,cAAc;AACjB,qBAAK,cAAc,CAAC;AACpB,2BAAW,aAAa,cAAc;AAErC,uBAAK,YAAY,UAAU,KAAK,IAAI,UAAU;AAAA,gBAC/C;AACA,uBAAO,KAAK;AAAA,cACb;AAAA,YACD;AAEA,2BAAe,MAAM,0CAAkB,KAAK,MAAM,OAAO,UAAU,MAAM,IAAI;AAAA,cAC5E,MAAM;AAAA,YACP,CAAC;AACD,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,OAAO;AACxB,kBAAM,SAAS,KAAK,iBAAiB,MAAM,CAAC;AAC5C,2BAAe,MAAM,0CAAkB,KAAK,MAAM,OAAO,UAAU,MAAM,IAAI,CAAC,CAAC;AAC/E,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,mBAAO,OAAO,IAAI,OAAO;AAEzB,gBAAI,WAAW;AACd,6BAAe,MAAM,kDAA0B;AAAA,gBAC9C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,oBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,iBAAG,WAAW;AACd,6BAAe,MAAM,0CAAkB,KAAK,MAAM,OAAO,UAAU,CAAC,GAAG,EAAE;AACzE,6BAAe,aAAa;AAAA,YAC7B;AAAA,UACD;AAEA,cAAI,cAAc,oBAAoB;AACrC,kBAAM,SAAS,KAAK,iBAAiB,MAAM,CAAC;AAC5C,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA,UAAU,MAAM;AAAA,cAChB,CAAC;AAAA,YACF;AACA,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,mBAAO,OAAO,IAAI,OAAO;AAEzB,gBAAI,WAAW;AACd,6BAAe,MAAM,kDAA0B;AAAA,gBAC9C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,oBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,iBAAG,WAAW;AACd,6BAAe,MAAM,0CAAkB,KAAK,MAAM,OAAO,iBAAiB,CAAC,GAAG,EAAE;AAChF,6BAAe,aAAa;AAAA,YAC7B;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,SAAS,KAAK,iBAAiB,MAAM,CAAC;AAC5C,2BAAe,MAAM,0CAAkB,KAAK,MAAM,UAAU,UAAU,MAAM,IAAI,CAAC,CAAC;AAClF,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,kBAAkB;AACnC,kBAAM,SAAS,KAAK,iBAAiB,MAAM,CAAC;AAC5C,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA,UAAU,MAAM;AAAA,cAChB,CAAC;AAAA,YACF;AACA,2BAAe,aAAa;AAAA,UAC7B;AAAA,QACD;AAEA,YAAI,aAAa,gBAAgB;AAEhC,cAAI,cAAc,UAAU;AAC3B,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAE5C,kBAAM,OAGF;AAAA,cACH;AAAA,YACD;AAEA,kBAAM,EAAE,sBAAsB,GAAG,KAAK,IAAI,KAAK;AAAA,cAC9C;AAAA,cACA;AAAA,YACD;AAMA,mBAAO,OAAO,MAAM,IAAI;AAExB,gBAAI,sBAAsB,wBAAwB,QAAQ;AACzD,oBAAM,uBAAuB,qBAAqB;AAClD,kBAAI,qBAAqB,QAAQ;AAChC,qBAAK,sBAAsB,CAAC;AAC5B,2BAAW,qBAAqB,sBAAsB;AACrD,uBAAK,oBAAoB,kBAAkB,KAAK,IAAI,kBAAkB;AAAA,gBACvE;AAAA,cACD;AAAA,YACD;AAEA,2BAAe,MAAM,0CAAkB,KAAK,MAAM,QAAQ,kBAAkB;AAAA,cAC3E,cAAc;AAAA,YACf,CAAC;AACD,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,iBAAiB,KAAK,iBAAiB,MAAM,CAAC;AACpD,kBAAM,0CAAkB,KAAK,MAAM,UAAU,kBAAkB,cAAc,IAAI,CAAC,CAAC;AACnF,2BAAe,EAAE,SAAS,KAAK;AAAA,UAChC;AAEA,cAAI,cAAc,SAAS;AAC1B,2BAAe,MAAM,0CAAkB,KAAK,MAAM,OAAO,wBAAwB,CAAC,CAAC;AACnF,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,OAAO;AACxB,kBAAM,iBAAiB,KAAK,iBAAiB,MAAM,CAAC;AACpD,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA,kBAAkB,cAAc;AAAA,cAChC,CAAC;AAAA,YACF;AACA,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,gBAAI,WAAW;AACd,6BAAe,MAAM,kDAA0B;AAAA,gBAC9C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,oBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,iBAAG,WAAW;AACd,6BAAe,MAAM,0CAAkB,KAAK,MAAM,OAAO,kBAAkB,CAAC,GAAG,EAAE;AACjF,6BAAe,aAAa;AAAA,YAC7B;AAAA,UACD;AAEA,cAAI,cAAc,kBAAkB;AACnC,kBAAM,iBAAiB,KAAK,iBAAiB,MAAM,CAAC;AACpD,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA,kBAAkB,cAAc;AAAA,cAChC,CAAC;AAAA,YACF;AACA,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,iBAAiB,KAAK,iBAAiB,MAAM,CAAC;AAEpD,kBAAM,OACL,CAAC;AAEF,kBAAM,EAAE,sBAAsB,GAAG,KAAK,IAAI,KAAK;AAAA,cAC9C;AAAA,cACA;AAAA,YACD;AAMA,mBAAO,OAAO,MAAM,IAAI;AAExB,gBAAI,sBAAsB,wBAAwB,QAAQ;AACzD,oBAAM,uBAAuB,qBAAqB;AAClD,kBAAI,qBAAqB,QAAQ;AAChC,qBAAK,sBAAsB,CAAC;AAC5B,2BAAW,qBAAqB,sBAAsB;AACrD,uBAAK,oBAAoB,kBAAkB,KAAK,IAAI,kBAAkB;AAAA,gBACvE;AAAA,cACD;AAAA,YACD;AAEA,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA,kBAAkB,cAAc;AAAA,cAChC,EAAE,cAAc,KAAK;AAAA,YACtB;AACA,2BAAe,aAAa;AAAA,UAC7B;AAAA,QACD;AACA,cAAM,gBAAgB,KAAK,QAAQ;AAAA,UAClC,KAAK,QAAQ,gBAAgB,YAA2B;AAAA,UACxD,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,QACzB;AACA,mBAAW,KAAK,GAAG,aAAa;AAAA,MACjC,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,MAAM,EAAE,OAAO,MAAM,QAAQ,EAAE,CAAC;AAClD;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AACA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": []}