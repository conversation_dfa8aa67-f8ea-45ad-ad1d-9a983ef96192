{"version": 3, "sources": ["../../../../nodes/Todoist/v2/TodoistV2.node.ts"], "sourcesContent": ["import {\n\ttype IExecuteFunctions,\n\ttype IDataObject,\n\ttype ILoadOptionsFunctions,\n\ttype INodeExecutionData,\n\ttype INodeListSearchResult,\n\ttype INodePropertyOptions,\n\ttype INodeType,\n\ttype INodeTypeBaseDescription,\n\ttype INodeTypeDescription,\n\tNodeConnectionTypes,\n} from 'n8n-workflow';\n\nimport type { OperationType, TodoistProjectType } from './Service';\nimport { TodoistService } from './Service';\nimport { todoistApiRequest } from '../GenericFunctions';\n\n// interface IBodyCreateTask {\n// \tcontent?: string;\n// \tdescription?: string;\n// \tproject_id?: number;\n// \tsection_id?: number;\n// \tparent_id?: number;\n// \torder?: number;\n// \tlabel_ids?: number[];\n// \tpriority?: number;\n// \tdue_string?: string;\n// \tdue_datetime?: string;\n// \tdue_date?: string;\n// \tdue_lang?: string;\n// }\n\nconst versionDescription: INodeTypeDescription = {\n\tdisplayName: 'Todoist',\n\tname: 'todoist',\n\ticon: 'file:todoist.svg',\n\tgroup: ['output'],\n\tversion: [2, 2.1],\n\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\tdescription: 'Consume Todoist API',\n\tdefaults: {\n\t\tname: 'Todoist',\n\t},\n\tusableAsTool: true,\n\tinputs: [NodeConnectionTypes.Main],\n\toutputs: [NodeConnectionTypes.Main],\n\tcredentials: [\n\t\t{\n\t\t\tname: 'todoistApi',\n\t\t\trequired: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tauthentication: ['apiKey'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\t{\n\t\t\tname: 'todoistOAuth2Api',\n\t\t\trequired: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tauthentication: ['oAuth2'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t],\n\tproperties: [\n\t\t{\n\t\t\tdisplayName: 'Authentication',\n\t\t\tname: 'authentication',\n\t\t\ttype: 'options',\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'API Key',\n\t\t\t\t\tvalue: 'apiKey',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'OAuth2',\n\t\t\t\t\tvalue: 'oAuth2',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'apiKey',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Task',\n\t\t\t\t\tvalue: 'task',\n\t\t\t\t\tdescription: 'Task resource',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'task',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Operation',\n\t\t\tname: 'operation',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\trequired: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['task'],\n\t\t\t\t},\n\t\t\t},\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Close',\n\t\t\t\t\tvalue: 'close',\n\t\t\t\t\tdescription: 'Close a task',\n\t\t\t\t\taction: 'Close a task',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Create',\n\t\t\t\t\tvalue: 'create',\n\t\t\t\t\tdescription: 'Create a new task',\n\t\t\t\t\taction: 'Create a task',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Delete',\n\t\t\t\t\tvalue: 'delete',\n\t\t\t\t\tdescription: 'Delete a task',\n\t\t\t\t\taction: 'Delete a task',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Get',\n\t\t\t\t\tvalue: 'get',\n\t\t\t\t\tdescription: 'Get a task',\n\t\t\t\t\taction: 'Get a task',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Get Many',\n\t\t\t\t\tvalue: 'getAll',\n\t\t\t\t\tdescription: 'Get many tasks',\n\t\t\t\t\taction: 'Get many tasks',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Move',\n\t\t\t\t\tvalue: 'move',\n\t\t\t\t\tdescription: 'Move a task',\n\t\t\t\t\taction: 'Move a task',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Reopen',\n\t\t\t\t\tvalue: 'reopen',\n\t\t\t\t\tdescription: 'Reopen a task',\n\t\t\t\t\taction: 'Reopen a task',\n\t\t\t\t},\n\t\t\t\t// {\n\t\t\t\t// \tname: 'Sync',\n\t\t\t\t// \tvalue: 'sync',\n\t\t\t\t// \tdescription: 'Sync a project',\n\t\t\t\t// },\n\t\t\t\t{\n\t\t\t\t\tname: 'Update',\n\t\t\t\t\tvalue: 'update',\n\t\t\t\t\tdescription: 'Update a task',\n\t\t\t\t\taction: 'Update a task',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'create',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Task ID',\n\t\t\tname: 'taskId',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\trequired: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['task'],\n\t\t\t\t\toperation: ['delete', 'close', 'get', 'reopen', 'update', 'move'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Project Name or ID',\n\t\t\tname: 'project',\n\t\t\ttype: 'resourceLocator',\n\t\t\tdefault: { mode: 'list', value: '' },\n\t\t\trequired: true,\n\t\t\tmodes: [\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'From List',\n\t\t\t\t\tname: 'list',\n\t\t\t\t\ttype: 'list',\n\t\t\t\t\tplaceholder: 'Select a project...',\n\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\tsearchListMethod: 'searchProjects',\n\t\t\t\t\t\tsearchable: true,\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'ID',\n\t\t\t\t\tname: 'id',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tplaceholder: '2302163813',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['task'],\n\t\t\t\t\toperation: ['create', 'move', 'sync'],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription: 'The destination project. Choose from the list, or specify an ID.',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Section Name or ID',\n\t\t\tname: 'section',\n\t\t\ttype: 'options',\n\t\t\ttypeOptions: {\n\t\t\t\tloadOptionsMethod: 'getSections',\n\t\t\t\tloadOptionsDependsOn: ['project.value'],\n\t\t\t},\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['task'],\n\t\t\t\t\toperation: ['move'],\n\t\t\t\t},\n\t\t\t\thide: {\n\t\t\t\t\t'@version': [{ _cnd: { gte: 2.1 } }],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdefault: '',\n\t\t\tdescription:\n\t\t\t\t'Section to which you want move the task. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Additional Fields',\n\t\t\tname: 'options',\n\t\t\ttype: 'collection',\n\t\t\tplaceholder: 'Add option',\n\t\t\tdefault: {},\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['task'],\n\t\t\t\t\toperation: ['move'],\n\t\t\t\t\t'@version': [{ _cnd: { gte: 2.1 } }],\n\t\t\t\t},\n\t\t\t},\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Section Name or ID',\n\t\t\t\t\tname: 'section',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\tloadOptionsMethod: 'getSections',\n\t\t\t\t\t\tloadOptionsDependsOn: ['project', 'options.parent'],\n\t\t\t\t\t},\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'The destination section. The task becomes the last root task of the section. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Parent Name or ID',\n\t\t\t\t\tname: 'parent',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\tloadOptionsMethod: 'getItems',\n\t\t\t\t\t\tloadOptionsDependsOn: ['project', 'options.section'],\n\t\t\t\t\t},\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'The destination parent task. The task becomes the last child task of the parent task. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t},\n\t\t\t],\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Label Names or IDs',\n\t\t\tname: 'labels',\n\t\t\ttype: 'multiOptions',\n\t\t\ttypeOptions: {\n\t\t\t\tloadOptionsMethod: 'getLabels',\n\t\t\t},\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['task'],\n\t\t\t\t\toperation: ['create'],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdefault: [],\n\t\t\tdescription:\n\t\t\t\t'Optional labels that will be assigned to a created task. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Content',\n\t\t\tname: 'content',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: {\n\t\t\t\trows: 5,\n\t\t\t},\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['task'],\n\t\t\t\t\toperation: ['create'],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdefault: '',\n\t\t\trequired: true,\n\t\t\tdescription: 'Task content',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Sync Commands',\n\t\t\tname: 'commands',\n\t\t\ttype: 'string',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['task'],\n\t\t\t\t\toperation: ['sync'],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdefault: '[]',\n\t\t\thint: 'See docs for possible commands: https://developer.todoist.com/sync/v8/#sync',\n\t\t\tdescription: 'Sync body',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Additional Fields',\n\t\t\tname: 'options',\n\t\t\ttype: 'collection',\n\t\t\tplaceholder: 'Add option',\n\t\t\tdefault: {},\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['task'],\n\t\t\t\t\toperation: ['create'],\n\t\t\t\t},\n\t\t\t},\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Description',\n\t\t\t\t\tname: 'description',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription: 'A description for the task',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Due Date Time',\n\t\t\t\t\tname: 'dueDateTime',\n\t\t\t\t\ttype: 'dateTime',\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription: 'Specific date and time in RFC3339 format in UTC',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Due String Locale',\n\t\t\t\t\tname: 'dueLang',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'2-letter code specifying language in case due_string is not written in English',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Due String',\n\t\t\t\t\tname: 'dueString',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'Human defined task due date (ex.: “next Monday”, “Tomorrow”). Value is set using local (not UTC) time.',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Parent Name or ID',\n\t\t\t\t\tname: 'parentId',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\tloadOptionsMethod: 'getItems',\n\t\t\t\t\t\tloadOptionsDependsOn: ['project.value', 'options.section'],\n\t\t\t\t\t},\n\t\t\t\t\tdefault: {},\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'The parent task you want to operate on. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Priority',\n\t\t\t\t\tname: 'priority',\n\t\t\t\t\ttype: 'number',\n\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\tmaxValue: 4,\n\t\t\t\t\t\tminValue: 1,\n\t\t\t\t\t},\n\t\t\t\t\tdefault: 1,\n\t\t\t\t\tdescription: 'Task priority from 1 (normal) to 4 (urgent)',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Section Name or ID',\n\t\t\t\t\tname: 'section',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\tloadOptionsMethod: 'getSections',\n\t\t\t\t\t\tloadOptionsDependsOn: ['project.value'],\n\t\t\t\t\t},\n\t\t\t\t\tdefault: {},\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'The section you want to operate on. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t},\n\t\t\t],\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Return All',\n\t\t\tname: 'returnAll',\n\t\t\ttype: 'boolean',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\toperation: ['getAll'],\n\t\t\t\t\tresource: ['task'],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdefault: false,\n\t\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Limit',\n\t\t\tname: 'limit',\n\t\t\ttype: 'number',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\toperation: ['getAll'],\n\t\t\t\t\tresource: ['task'],\n\t\t\t\t\treturnAll: [false],\n\t\t\t\t},\n\t\t\t},\n\t\t\ttypeOptions: {\n\t\t\t\tminValue: 1,\n\t\t\t\tmaxValue: 500,\n\t\t\t},\n\t\t\tdefault: 50,\n\t\t\tdescription: 'Max number of results to return',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Filters',\n\t\t\tname: 'filters',\n\t\t\ttype: 'collection',\n\t\t\tplaceholder: 'Add option',\n\t\t\tdefault: {},\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['task'],\n\t\t\t\t\toperation: ['getAll'],\n\t\t\t\t},\n\t\t\t},\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Filter',\n\t\t\t\t\tname: 'filter',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'Filter by any <a href=\"https://get.todoist.help/hc/en-us/articles/205248842\">supported filter.</a>',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'IDs',\n\t\t\t\t\tname: 'ids',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription: 'A list of the task IDs to retrieve, this should be a comma-separated list',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Label Name or ID',\n\t\t\t\t\tname: 'labelId',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\tloadOptionsMethod: 'getLabels',\n\t\t\t\t\t},\n\t\t\t\t\tdefault: {},\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'Filter tasks by label. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Lang',\n\t\t\t\t\tname: 'lang',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'IETF language tag defining what language filter is written in, if differs from default English',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Parent Name or ID',\n\t\t\t\t\tname: 'parentId',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\tloadOptionsMethod: 'getItems',\n\t\t\t\t\t\tloadOptionsDependsOn: ['filters.projectId', 'filters.sectionId'],\n\t\t\t\t\t},\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'Filter tasks by parent task ID. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Project Name or ID',\n\t\t\t\t\tname: 'projectId',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\tloadOptionsMethod: 'getProjects',\n\t\t\t\t\t},\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'Filter tasks by project ID. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Section Name or ID',\n\t\t\t\t\tname: 'sectionId',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\tloadOptionsMethod: 'getSections',\n\t\t\t\t\t\tloadOptionsDependsOn: ['filters.projectId'],\n\t\t\t\t\t},\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'Filter tasks by section ID. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t},\n\t\t\t],\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Update Fields',\n\t\t\tname: 'updateFields',\n\t\t\ttype: 'collection',\n\t\t\tplaceholder: 'Add Field',\n\t\t\tdefault: {},\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['task'],\n\t\t\t\t\toperation: ['update'],\n\t\t\t\t},\n\t\t\t},\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Content',\n\t\t\t\t\tname: 'content',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription: 'Task content',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Description',\n\t\t\t\t\tname: 'description',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription: 'A description for the task',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Due Date Time',\n\t\t\t\t\tname: 'dueDateTime',\n\t\t\t\t\ttype: 'dateTime',\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription: 'Specific date and time in RFC3339 format in UTC',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Due String Locale',\n\t\t\t\t\tname: 'dueLang',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'2-letter code specifying language in case due_string is not written in English',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Due String',\n\t\t\t\t\tname: 'dueString',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'Human defined task due date (ex.: “next Monday”, “Tomorrow”). Value is set using local (not UTC) time.',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Due String Locale',\n\t\t\t\t\tname: 'dueLang',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'2-letter code specifying language in case due_string is not written in English',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Label Names or IDs',\n\t\t\t\t\tname: 'labels',\n\t\t\t\t\ttype: 'multiOptions',\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\tloadOptionsMethod: 'getLabels',\n\t\t\t\t\t},\n\t\t\t\t\tdefault: [],\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Priority',\n\t\t\t\t\tname: 'priority',\n\t\t\t\t\ttype: 'number',\n\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\tmaxValue: 4,\n\t\t\t\t\t\tminValue: 1,\n\t\t\t\t\t},\n\t\t\t\t\tdefault: 1,\n\t\t\t\t\tdescription: 'Task priority from 1 (normal) to 4 (urgent)',\n\t\t\t\t},\n\t\t\t],\n\t\t},\n\t],\n};\n\nexport class TodoistV2 implements INodeType {\n\tdescription: INodeTypeDescription;\n\n\tconstructor(baseDescription: INodeTypeBaseDescription) {\n\t\tthis.description = {\n\t\t\t...baseDescription,\n\t\t\t...versionDescription,\n\t\t};\n\t}\n\n\tmethods = {\n\t\tlistSearch: {\n\t\t\tasync searchProjects(\n\t\t\t\tthis: ILoadOptionsFunctions,\n\t\t\t\tfilter?: string,\n\t\t\t): Promise<INodeListSearchResult> {\n\t\t\t\tconst projects: TodoistProjectType[] = await todoistApiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/projects',\n\t\t\t\t);\n\t\t\t\treturn {\n\t\t\t\t\tresults: projects\n\t\t\t\t\t\t.filter(\n\t\t\t\t\t\t\t(project) => !filter || project.name.toLowerCase().includes(filter.toLowerCase()),\n\t\t\t\t\t\t)\n\t\t\t\t\t\t.map((project) => ({\n\t\t\t\t\t\t\tname: project.name,\n\t\t\t\t\t\t\tvalue: project.id,\n\t\t\t\t\t\t})),\n\t\t\t\t};\n\t\t\t},\n\t\t},\n\t\tloadOptions: {\n\t\t\t// Get all the available projects to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getProjects(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst projects = await todoistApiRequest.call(this, 'GET', '/projects');\n\t\t\t\tfor (const project of projects) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: project.name,\n\t\t\t\t\t\tvalue: project.id,\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\treturn returnData;\n\t\t\t},\n\n\t\t\t// Get all the available sections in the selected project, to display them\n\t\t\t// to user so that they can select one easily\n\t\t\tasync getSections(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\n\t\t\t\tconst options = Object.assign(\n\t\t\t\t\t{},\n\t\t\t\t\tthis.getNodeParameter('options', {}),\n\t\t\t\t\tthis.getNodeParameter('filters', {}),\n\t\t\t\t) as IDataObject;\n\n\t\t\t\tconst projectId =\n\t\t\t\t\t(options.projectId as number) ??\n\t\t\t\t\t(this.getCurrentNodeParameter('project', { extractValue: true }) as number);\n\t\t\t\tif (projectId) {\n\t\t\t\t\tconst qs: IDataObject = { project_id: projectId };\n\t\t\t\t\tconst sections = await todoistApiRequest.call(this, 'GET', '/sections', {}, qs);\n\t\t\t\t\tfor (const section of sections) {\n\t\t\t\t\t\treturnData.push({\n\t\t\t\t\t\t\tname: section.name,\n\t\t\t\t\t\t\tvalue: section.id,\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn returnData;\n\t\t\t},\n\n\t\t\t// Get all the available parents in the selected project and section,\n\t\t\t// to display them to user so that they can select one easily\n\t\t\tasync getItems(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\n\t\t\t\tconst options = Object.assign(\n\t\t\t\t\t{},\n\t\t\t\t\tthis.getNodeParameter('options', {}),\n\t\t\t\t\tthis.getNodeParameter('filters', {}),\n\t\t\t\t) as IDataObject;\n\n\t\t\t\tconst projectId =\n\t\t\t\t\t(options.projectId as number) ??\n\t\t\t\t\t(this.getCurrentNodeParameter('project', { extractValue: true }) as number);\n\n\t\t\t\tconst sectionId =\n\t\t\t\t\t(options.sectionId as number) ||\n\t\t\t\t\t(options.section as number) ||\n\t\t\t\t\t(this.getCurrentNodeParameter('sectionId') as number);\n\n\t\t\t\tif (projectId) {\n\t\t\t\t\tconst qs: IDataObject = sectionId\n\t\t\t\t\t\t? { project_id: projectId, section_id: sectionId }\n\t\t\t\t\t\t: { project_id: projectId };\n\n\t\t\t\t\tconst items = await todoistApiRequest.call(this, 'GET', '/tasks', {}, qs);\n\t\t\t\t\tfor (const item of items) {\n\t\t\t\t\t\treturnData.push({\n\t\t\t\t\t\t\tname: item.content,\n\t\t\t\t\t\t\tvalue: item.id,\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn returnData;\n\t\t\t},\n\n\t\t\t// Get all the available labels to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getLabels(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst labels = await todoistApiRequest.call(this, 'GET', '/labels');\n\n\t\t\t\tfor (const label of labels) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: label.name,\n\t\t\t\t\t\tvalue: label.name,\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: INodeExecutionData[] = [];\n\t\tconst length = items.length;\n\t\tconst service = new TodoistService();\n\t\tlet responseData;\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0) as OperationType;\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'task') {\n\t\t\t\t\tresponseData = await service.execute(this, operation, i);\n\t\t\t\t}\n\n\t\t\t\tif (responseData !== undefined && Array.isArray(responseData?.data)) {\n\t\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\t\tthis.helpers.returnJsonArray(responseData.data as IDataObject[]),\n\t\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t\t);\n\t\t\t\t\treturnData.push(...executionData);\n\t\t\t\t} else {\n\t\t\t\t\tif (responseData?.hasOwnProperty('success')) {\n\t\t\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\t\t\tthis.helpers.returnJsonArray({ success: responseData.success }),\n\t\t\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t\t\t);\n\t\t\t\t\t\treturnData.push(...executionData);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\t\t\tthis.helpers.returnJsonArray(responseData?.data as IDataObject),\n\t\t\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t\t\t);\n\t\t\t\t\t\treturnData.push(...executionData);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\t\tthis.helpers.returnJsonArray({ error: error.message }),\n\t\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t\t);\n\t\t\t\t\treturnData.push(...executionData);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAWO;AAGP,qBAA+B;AAC/B,8BAAkC;AAiBlC,MAAM,qBAA2C;AAAA,EAChD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,QAAQ;AAAA,EAChB,SAAS,CAAC,GAAG,GAAG;AAAA,EAChB,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,cAAc;AAAA,EACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,aAAa;AAAA,IACZ;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,gBAAgB,CAAC,QAAQ;AAAA,QAC1B;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,gBAAgB,CAAC,QAAQ;AAAA,QAC1B;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA,YAAY;AAAA,IACX;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,MACD;AAAA,MACA,SAAS;AAAA,MACT,UAAU;AAAA,IACX;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,UAAU;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,MAAM;AAAA,QAClB;AAAA,MACD;AAAA,MACA,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,MAAM;AAAA,UACjB,WAAW,CAAC,UAAU,SAAS,OAAO,UAAU,UAAU,MAAM;AAAA,QACjE;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,EAAE,MAAM,QAAQ,OAAO,GAAG;AAAA,MACnC,UAAU;AAAA,MACV,OAAO;AAAA,QACN;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,aAAa;AAAA,YACZ,kBAAkB;AAAA,YAClB,YAAY;AAAA,UACb;AAAA,QACD;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,QACd;AAAA,MACD;AAAA,MACA,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,MAAM;AAAA,UACjB,WAAW,CAAC,UAAU,QAAQ,MAAM;AAAA,QACrC;AAAA,MACD;AAAA,MACA,aAAa;AAAA,IACd;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,QACZ,mBAAmB;AAAA,QACnB,sBAAsB,CAAC,eAAe;AAAA,MACvC;AAAA,MACA,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,MAAM;AAAA,UACjB,WAAW,CAAC,MAAM;AAAA,QACnB;AAAA,QACA,MAAM;AAAA,UACL,YAAY,CAAC,EAAE,MAAM,EAAE,KAAK,IAAI,EAAE,CAAC;AAAA,QACpC;AAAA,MACD;AAAA,MACA,SAAS;AAAA,MACT,aACC;AAAA,IACF;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS,CAAC;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,MAAM;AAAA,UACjB,WAAW,CAAC,MAAM;AAAA,UAClB,YAAY,CAAC,EAAE,MAAM,EAAE,KAAK,IAAI,EAAE,CAAC;AAAA,QACpC;AAAA,MACD;AAAA,MACA,SAAS;AAAA,QACR;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,YACZ,mBAAmB;AAAA,YACnB,sBAAsB,CAAC,WAAW,gBAAgB;AAAA,UACnD;AAAA,UACA,SAAS;AAAA,UACT,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,YACZ,mBAAmB;AAAA,YACnB,sBAAsB,CAAC,WAAW,iBAAiB;AAAA,UACpD;AAAA,UACA,SAAS;AAAA,UACT,aACC;AAAA,QACF;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,QACZ,mBAAmB;AAAA,MACpB;AAAA,MACA,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,MAAM;AAAA,UACjB,WAAW,CAAC,QAAQ;AAAA,QACrB;AAAA,MACD;AAAA,MACA,SAAS,CAAC;AAAA,MACV,aACC;AAAA,IACF;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,QACZ,MAAM;AAAA,MACP;AAAA,MACA,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,MAAM;AAAA,UACjB,WAAW,CAAC,QAAQ;AAAA,QACrB;AAAA,MACD;AAAA,MACA,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,IACd;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,MAAM;AAAA,UACjB,WAAW,CAAC,MAAM;AAAA,QACnB;AAAA,MACD;AAAA,MACA,SAAS;AAAA,MACT,MAAM;AAAA,MACN,aAAa;AAAA,IACd;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS,CAAC;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,MAAM;AAAA,UACjB,WAAW,CAAC,QAAQ;AAAA,QACrB;AAAA,MACD;AAAA,MACA,SAAS;AAAA,QACR;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,YACZ,mBAAmB;AAAA,YACnB,sBAAsB,CAAC,iBAAiB,iBAAiB;AAAA,UAC1D;AAAA,UACA,SAAS,CAAC;AAAA,UACV,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,YACZ,UAAU;AAAA,YACV,UAAU;AAAA,UACX;AAAA,UACA,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,YACZ,mBAAmB;AAAA,YACnB,sBAAsB,CAAC,eAAe;AAAA,UACvC;AAAA,UACA,SAAS,CAAC;AAAA,UACV,aACC;AAAA,QACF;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,WAAW,CAAC,QAAQ;AAAA,UACpB,UAAU,CAAC,MAAM;AAAA,QAClB;AAAA,MACD;AAAA,MACA,SAAS;AAAA,MACT,aAAa;AAAA,IACd;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,WAAW,CAAC,QAAQ;AAAA,UACpB,UAAU,CAAC,MAAM;AAAA,UACjB,WAAW,CAAC,KAAK;AAAA,QAClB;AAAA,MACD;AAAA,MACA,aAAa;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,MACX;AAAA,MACA,SAAS;AAAA,MACT,aAAa;AAAA,IACd;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS,CAAC;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,MAAM;AAAA,UACjB,WAAW,CAAC,QAAQ;AAAA,QACrB;AAAA,MACD;AAAA,MACA,SAAS;AAAA,QACR;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,YACZ,mBAAmB;AAAA,UACpB;AAAA,UACA,SAAS,CAAC;AAAA,UACV,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,YACZ,mBAAmB;AAAA,YACnB,sBAAsB,CAAC,qBAAqB,mBAAmB;AAAA,UAChE;AAAA,UACA,SAAS;AAAA,UACT,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,YACZ,mBAAmB;AAAA,UACpB;AAAA,UACA,SAAS;AAAA,UACT,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,YACZ,mBAAmB;AAAA,YACnB,sBAAsB,CAAC,mBAAmB;AAAA,UAC3C;AAAA,UACA,SAAS;AAAA,UACT,aACC;AAAA,QACF;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS,CAAC;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,MAAM;AAAA,UACjB,WAAW,CAAC,QAAQ;AAAA,QACrB;AAAA,MACD;AAAA,MACA,SAAS;AAAA,QACR;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aACC;AAAA,UACD,aAAa;AAAA,YACZ,mBAAmB;AAAA,UACpB;AAAA,UACA,SAAS,CAAC;AAAA,QACX;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,YACZ,UAAU;AAAA,YACV,UAAU;AAAA,UACX;AAAA,UACA,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,UAA+B;AAAA,EAG3C,YAAY,iBAA2C;AAOvD,mBAAU;AAAA,MACT,YAAY;AAAA,QACX,MAAM,eAEL,QACiC;AACjC,gBAAM,WAAiC,MAAM,0CAAkB;AAAA,YAC9D;AAAA,YACA;AAAA,YACA;AAAA,UACD;AACA,iBAAO;AAAA,YACN,SAAS,SACP;AAAA,cACA,CAAC,YAAY,CAAC,UAAU,QAAQ,KAAK,YAAY,EAAE,SAAS,OAAO,YAAY,CAAC;AAAA,YACjF,EACC,IAAI,CAAC,aAAa;AAAA,cAClB,MAAM,QAAQ;AAAA,cACd,OAAO,QAAQ;AAAA,YAChB,EAAE;AAAA,UACJ;AAAA,QACD;AAAA,MACD;AAAA,MACA,aAAa;AAAA;AAAA;AAAA,QAGZ,MAAM,cAA0E;AAC/E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,WAAW,MAAM,0CAAkB,KAAK,MAAM,OAAO,WAAW;AACtE,qBAAW,WAAW,UAAU;AAC/B,uBAAW,KAAK;AAAA,cACf,MAAM,QAAQ;AAAA,cACd,OAAO,QAAQ;AAAA,YAChB,CAAC;AAAA,UACF;AAEA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAIA,MAAM,cAA0E;AAC/E,gBAAM,aAAqC,CAAC;AAE5C,gBAAM,UAAU,OAAO;AAAA,YACtB,CAAC;AAAA,YACD,KAAK,iBAAiB,WAAW,CAAC,CAAC;AAAA,YACnC,KAAK,iBAAiB,WAAW,CAAC,CAAC;AAAA,UACpC;AAEA,gBAAM,YACJ,QAAQ,aACR,KAAK,wBAAwB,WAAW,EAAE,cAAc,KAAK,CAAC;AAChE,cAAI,WAAW;AACd,kBAAM,KAAkB,EAAE,YAAY,UAAU;AAChD,kBAAM,WAAW,MAAM,0CAAkB,KAAK,MAAM,OAAO,aAAa,CAAC,GAAG,EAAE;AAC9E,uBAAW,WAAW,UAAU;AAC/B,yBAAW,KAAK;AAAA,gBACf,MAAM,QAAQ;AAAA,gBACd,OAAO,QAAQ;AAAA,cAChB,CAAC;AAAA,YACF;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAIA,MAAM,WAAuE;AAC5E,gBAAM,aAAqC,CAAC;AAE5C,gBAAM,UAAU,OAAO;AAAA,YACtB,CAAC;AAAA,YACD,KAAK,iBAAiB,WAAW,CAAC,CAAC;AAAA,YACnC,KAAK,iBAAiB,WAAW,CAAC,CAAC;AAAA,UACpC;AAEA,gBAAM,YACJ,QAAQ,aACR,KAAK,wBAAwB,WAAW,EAAE,cAAc,KAAK,CAAC;AAEhE,gBAAM,YACJ,QAAQ,aACR,QAAQ,WACR,KAAK,wBAAwB,WAAW;AAE1C,cAAI,WAAW;AACd,kBAAM,KAAkB,YACrB,EAAE,YAAY,WAAW,YAAY,UAAU,IAC/C,EAAE,YAAY,UAAU;AAE3B,kBAAM,QAAQ,MAAM,0CAAkB,KAAK,MAAM,OAAO,UAAU,CAAC,GAAG,EAAE;AACxE,uBAAW,QAAQ,OAAO;AACzB,yBAAW,KAAK;AAAA,gBACf,MAAM,KAAK;AAAA,gBACX,OAAO,KAAK;AAAA,cACb,CAAC;AAAA,YACF;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAIA,MAAM,YAAwE;AAC7E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,SAAS,MAAM,0CAAkB,KAAK,MAAM,OAAO,SAAS;AAElE,qBAAW,SAAS,QAAQ;AAC3B,uBAAW,KAAK;AAAA,cACf,MAAM,MAAM;AAAA,cACZ,OAAO,MAAM;AAAA,YACd,CAAC;AAAA,UACF;AAEA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AA9HC,SAAK,cAAc;AAAA,MAClB,GAAG;AAAA,MACH,GAAG;AAAA,IACJ;AAAA,EACD;AAAA,EA4HA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAAmC,CAAC;AAC1C,UAAM,SAAS,MAAM;AACrB,UAAM,UAAU,IAAI,8BAAe;AACnC,QAAI;AACJ,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,YAAI,aAAa,QAAQ;AACxB,yBAAe,MAAM,QAAQ,QAAQ,MAAM,WAAW,CAAC;AAAA,QACxD;AAEA,YAAI,iBAAiB,UAAa,MAAM,QAAQ,cAAc,IAAI,GAAG;AACpE,gBAAM,gBAAgB,KAAK,QAAQ;AAAA,YAClC,KAAK,QAAQ,gBAAgB,aAAa,IAAqB;AAAA,YAC/D,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,UACzB;AACA,qBAAW,KAAK,GAAG,aAAa;AAAA,QACjC,OAAO;AACN,cAAI,cAAc,eAAe,SAAS,GAAG;AAC5C,kBAAM,gBAAgB,KAAK,QAAQ;AAAA,cAClC,KAAK,QAAQ,gBAAgB,EAAE,SAAS,aAAa,QAAQ,CAAC;AAAA,cAC9D,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,YACzB;AACA,uBAAW,KAAK,GAAG,aAAa;AAAA,UACjC,OAAO;AACN,kBAAM,gBAAgB,KAAK,QAAQ;AAAA,cAClC,KAAK,QAAQ,gBAAgB,cAAc,IAAmB;AAAA,cAC9D,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,YACzB;AACA,uBAAW,KAAK,GAAG,aAAa;AAAA,UACjC;AAAA,QACD;AAAA,MACD,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,gBAAM,gBAAgB,KAAK,QAAQ;AAAA,YAClC,KAAK,QAAQ,gBAAgB,EAAE,OAAO,MAAM,QAAQ,CAAC;AAAA,YACrD,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,UACzB;AACA,qBAAW,KAAK,GAAG,aAAa;AAChC;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AACA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": []}