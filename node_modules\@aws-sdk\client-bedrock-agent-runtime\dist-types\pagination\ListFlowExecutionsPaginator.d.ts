import { Paginator } from "@smithy/types";
import { ListFlowExecutionsCommandInput, ListFlowExecutionsCommandOutput } from "../commands/ListFlowExecutionsCommand";
import { BedrockAgentRuntimePaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListFlowExecutions: (config: BedrockAgentRuntimePaginationConfiguration, input: ListFlowExecutionsCommandInput, ...rest: any[]) => Paginator<ListFlowExecutionsCommandOutput>;
