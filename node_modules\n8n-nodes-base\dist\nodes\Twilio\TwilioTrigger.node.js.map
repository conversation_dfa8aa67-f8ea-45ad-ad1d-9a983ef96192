{"version": 3, "sources": ["../../../nodes/Twilio/TwilioTrigger.node.ts"], "sourcesContent": ["import {\n\ttype IHookFunctions,\n\ttype IWebhookFunctions,\n\ttype INodeType,\n\ttype INodeTypeDescription,\n\ttype IWebhookResponseData,\n\tNodeConnectionTypes,\n} from 'n8n-workflow';\n\nimport { twilioTriggerApiRequest } from './GenericFunctions';\n\nexport class TwilioTrigger implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Twilio Trigger',\n\t\tname: 'twilioTrigger',\n\t\ticon: 'file:twilio.svg',\n\t\tgroup: ['trigger'],\n\t\tversion: [1],\n\t\tdefaultVersion: 1,\n\t\tsubtitle: '=Updates: {{$parameter[\"updates\"].join(\", \")}}',\n\t\tdescription: 'Starts the workflow on a Twilio update',\n\t\tdefaults: {\n\t\t\tname: 'Twilio Trigger',\n\t\t},\n\t\tinputs: [],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'twilio<PERSON><PERSON>',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\twebhooks: [\n\t\t\t{\n\t\t\t\tname: 'default',\n\t\t\t\thttpMethod: 'POST',\n\t\t\t\tresponseMode: 'onReceived',\n\t\t\t\tpath: 'webhook',\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Trigger On',\n\t\t\t\tname: 'updates',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'New SMS',\n\t\t\t\t\t\tvalue: 'com.twilio.messaging.inbound-message.received',\n\t\t\t\t\t\tdescription: 'When an SMS message is received',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'New Call',\n\t\t\t\t\t\tvalue: 'com.twilio.voice.insights.call-summary.complete',\n\t\t\t\t\t\tdescription: 'When a call is received',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\trequired: true,\n\t\t\t\tdefault: [],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: \"The 'New Call' event may take up to thirty minutes to be triggered\",\n\t\t\t\tname: 'callTriggerNotice',\n\t\t\t\ttype: 'notice',\n\t\t\t\tdefault: '',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tupdates: ['com.twilio.voice.insights.call-summary.complete'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t],\n\t};\n\n\twebhookMethods = {\n\t\tdefault: {\n\t\t\tasync checkExists(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\n\t\t\t\tconst { sinks } = (await twilioTriggerApiRequest.call(this, 'GET', 'Sinks')) || {};\n\n\t\t\t\tconst sink = sinks.find(\n\t\t\t\t\t(entry: { sink_configuration: { destination: string | undefined } }) =>\n\t\t\t\t\t\tentry.sink_configuration.destination === webhookUrl,\n\t\t\t\t);\n\n\t\t\t\tif (sink) {\n\t\t\t\t\tconst { subscriptions } =\n\t\t\t\t\t\t(await twilioTriggerApiRequest.call(this, 'GET', 'Subscriptions')) || {};\n\n\t\t\t\t\tconst subscription = subscriptions.find(\n\t\t\t\t\t\t(entry: { sink_sid: any }) => entry.sink_sid === sink.sid,\n\t\t\t\t\t);\n\n\t\t\t\t\tif (subscription) {\n\t\t\t\t\t\tconst { types } =\n\t\t\t\t\t\t\t(await twilioTriggerApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t`Subscriptions/${subscription.sid}/SubscribedEvents`,\n\t\t\t\t\t\t\t)) || {};\n\n\t\t\t\t\t\tconst typesFound = types.map((type: { type: any }) => type.type);\n\n\t\t\t\t\t\tconst allowedUpdates = this.getNodeParameter('updates') as string[];\n\n\t\t\t\t\t\tif (typesFound.sort().join(',') === allowedUpdates.sort().join(',')) {\n\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\n\t\t\tasync create(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst workflowData = this.getWorkflowStaticData('node');\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\n\t\t\t\tconst allowedUpdates = this.getNodeParameter('updates') as string[];\n\n\t\t\t\tconst bodySink = {\n\t\t\t\t\tDescription: 'Sink created by n8n Twilio Trigger Node.',\n\t\t\t\t\tSinkConfiguration: `{\t\"destination\": \"${webhookUrl}\",\t\"method\": \"POST\"\t}`,\n\t\t\t\t\tSinkType: 'webhook',\n\t\t\t\t};\n\n\t\t\t\tconst sink = await twilioTriggerApiRequest.call(this, 'POST', 'Sinks', bodySink);\n\n\t\t\t\tworkflowData.sinkId = sink.sid;\n\n\t\t\t\tconst body = {\n\t\t\t\t\tDescription: 'Subscription created by n8n Twilio Trigger Node.',\n\t\t\t\t\tTypes: `{ \"type\": \"${allowedUpdates[0]}\" }`,\n\t\t\t\t\tSinkSid: sink.sid,\n\t\t\t\t};\n\n\t\t\t\tconst subscription = await twilioTriggerApiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'POST',\n\t\t\t\t\t'Subscriptions',\n\t\t\t\t\tbody,\n\t\t\t\t);\n\t\t\t\tworkflowData.subscriptionId = subscription.sid;\n\t\t\t\t// if there is more than one event type add the others on the existing subscription\n\t\t\t\tif (allowedUpdates.length > 1) {\n\t\t\t\t\tfor (let index = 1; index < allowedUpdates.length; index++) {\n\t\t\t\t\t\tawait twilioTriggerApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t`Subscriptions/${workflowData.subscriptionId}/SubscribedEvents`,\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tType: allowedUpdates[index],\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\t\t\t},\n\n\t\t\tasync delete(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst workflowData = this.getWorkflowStaticData('node');\n\t\t\t\tconst sinkId = workflowData.sinkId;\n\t\t\t\tconst subscriptionId = workflowData.subscriptionId;\n\n\t\t\t\ttry {\n\t\t\t\t\tif (sinkId) {\n\t\t\t\t\t\tawait twilioTriggerApiRequest.call(this, 'DELETE', `Sinks/${sinkId}`, {});\n\t\t\t\t\t\tworkflowData.sinkId = '';\n\t\t\t\t\t}\n\t\t\t\t\tif (subscriptionId) {\n\t\t\t\t\t\tawait twilioTriggerApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'DELETE',\n\t\t\t\t\t\t\t`Subscriptions/${subscriptionId}`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t);\n\t\t\t\t\t\tworkflowData.subscriptionId = '';\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync webhook(this: IWebhookFunctions): Promise<IWebhookResponseData> {\n\t\tconst bodyData = this.getBodyData();\n\n\t\treturn {\n\t\t\tworkflowData: [this.helpers.returnJsonArray(bodyData)],\n\t\t};\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAOO;AAEP,8BAAwC;AAEjC,MAAM,cAAmC;AAAA,EAAzC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,SAAS;AAAA,MACjB,SAAS,CAAC,CAAC;AAAA,MACX,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,UAAU;AAAA,QACT;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,MAAM;AAAA,QACP;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,UACD;AAAA,UACA,UAAU;AAAA,UACV,SAAS,CAAC;AAAA,QACX;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,SAAS,CAAC,iDAAiD;AAAA,YAC5D;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,0BAAiB;AAAA,MAChB,SAAS;AAAA,QACR,MAAM,cAAoD;AACzD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AAEnD,gBAAM,EAAE,MAAM,IAAK,MAAM,gDAAwB,KAAK,MAAM,OAAO,OAAO,KAAM,CAAC;AAEjF,gBAAM,OAAO,MAAM;AAAA,YAClB,CAAC,UACA,MAAM,mBAAmB,gBAAgB;AAAA,UAC3C;AAEA,cAAI,MAAM;AACT,kBAAM,EAAE,cAAc,IACpB,MAAM,gDAAwB,KAAK,MAAM,OAAO,eAAe,KAAM,CAAC;AAExE,kBAAM,eAAe,cAAc;AAAA,cAClC,CAAC,UAA6B,MAAM,aAAa,KAAK;AAAA,YACvD;AAEA,gBAAI,cAAc;AACjB,oBAAM,EAAE,MAAM,IACZ,MAAM,gDAAwB;AAAA,gBAC9B;AAAA,gBACA;AAAA,gBACA,iBAAiB,aAAa,GAAG;AAAA,cAClC,KAAM,CAAC;AAER,oBAAM,aAAa,MAAM,IAAI,CAAC,SAAwB,KAAK,IAAI;AAE/D,oBAAM,iBAAiB,KAAK,iBAAiB,SAAS;AAEtD,kBAAI,WAAW,KAAK,EAAE,KAAK,GAAG,MAAM,eAAe,KAAK,EAAE,KAAK,GAAG,GAAG;AACpE,uBAAO;AAAA,cACR,OAAO;AACN,uBAAO;AAAA,cACR;AAAA,YACD;AAAA,UACD;AACA,iBAAO;AAAA,QACR;AAAA,QAEA,MAAM,SAA+C;AACpD,gBAAM,eAAe,KAAK,sBAAsB,MAAM;AACtD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AAEnD,gBAAM,iBAAiB,KAAK,iBAAiB,SAAS;AAEtD,gBAAM,WAAW;AAAA,YAChB,aAAa;AAAA,YACb,mBAAmB,qBAAqB,UAAU;AAAA,YAClD,UAAU;AAAA,UACX;AAEA,gBAAM,OAAO,MAAM,gDAAwB,KAAK,MAAM,QAAQ,SAAS,QAAQ;AAE/E,uBAAa,SAAS,KAAK;AAE3B,gBAAM,OAAO;AAAA,YACZ,aAAa;AAAA,YACb,OAAO,cAAc,eAAe,CAAC,CAAC;AAAA,YACtC,SAAS,KAAK;AAAA,UACf;AAEA,gBAAM,eAAe,MAAM,gDAAwB;AAAA,YAClD;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AACA,uBAAa,iBAAiB,aAAa;AAE3C,cAAI,eAAe,SAAS,GAAG;AAC9B,qBAAS,QAAQ,GAAG,QAAQ,eAAe,QAAQ,SAAS;AAC3D,oBAAM,gDAAwB;AAAA,gBAC7B;AAAA,gBACA;AAAA,gBACA,iBAAiB,aAAa,cAAc;AAAA,gBAC5C;AAAA,kBACC,MAAM,eAAe,KAAK;AAAA,gBAC3B;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA,QAEA,MAAM,SAA+C;AACpD,gBAAM,eAAe,KAAK,sBAAsB,MAAM;AACtD,gBAAM,SAAS,aAAa;AAC5B,gBAAM,iBAAiB,aAAa;AAEpC,cAAI;AACH,gBAAI,QAAQ;AACX,oBAAM,gDAAwB,KAAK,MAAM,UAAU,SAAS,MAAM,IAAI,CAAC,CAAC;AACxE,2BAAa,SAAS;AAAA,YACvB;AACA,gBAAI,gBAAgB;AACnB,oBAAM,gDAAwB;AAAA,gBAC7B;AAAA,gBACA;AAAA,gBACA,iBAAiB,cAAc;AAAA,gBAC/B,CAAC;AAAA,cACF;AACA,2BAAa,iBAAiB;AAAA,YAC/B;AAAA,UACD,SAAS,OAAO;AACf,mBAAO;AAAA,UACR;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAgE;AACrE,UAAM,WAAW,KAAK,YAAY;AAElC,WAAO;AAAA,MACN,cAAc,CAAC,KAAK,QAAQ,gBAAgB,QAAQ,CAAC;AAAA,IACtD;AAAA,EACD;AACD;", "names": []}