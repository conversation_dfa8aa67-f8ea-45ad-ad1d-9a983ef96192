{"version": 3, "sources": ["../../../test/nodes/TriggerHelpers.ts"], "sourcesContent": ["import type * as express from 'express';\nimport { type IncomingHttpHeaders } from 'http';\nimport { mock } from 'jest-mock-extended';\nimport get from 'lodash/get';\nimport merge from 'lodash/merge';\nimport set from 'lodash/set';\nimport { PollContext, returnJsonArray } from 'n8n-core';\nimport type { InstanceSettings, ExecutionLifecycleHooks } from 'n8n-core';\nimport { ScheduledTaskManager } from 'n8n-core/dist/execution-engine/scheduled-task-manager';\nimport {\n\tcreateDeferredPromise,\n\ttype IBinaryData,\n\ttype ICredentialDataDecryptedObject,\n\ttype IDataObject,\n\ttype IHttpRequestOptions,\n\ttype INode,\n\ttype INodeType,\n\ttype INodeTypes,\n\ttype ITriggerFunctions,\n\ttype IWebhookFunctions,\n\ttype IWorkflowExecuteAdditionalData,\n\ttype NodeTypeAndVersion,\n\ttype VersionedNodeType,\n\ttype Workflow,\n} from 'n8n-workflow';\n\ntype MockDeepPartial<T> = Parameters<typeof mock<T>>[0];\n\ntype TestTriggerNodeOptions = {\n\tmode?: 'manual' | 'trigger';\n\tnode?: MockDeepPartial<INode>;\n\ttimezone?: string;\n\tworkflowStaticData?: IDataObject;\n\tcredential?: ICredentialDataDecryptedObject;\n\thelpers?: Partial<ITriggerFunctions['helpers']>;\n};\n\ntype TestWebhookTriggerNodeOptions = TestTriggerNodeOptions & {\n\twebhookName?: string;\n\trequest?: MockDeepPartial<express.Request>;\n\tbodyData?: IDataObject;\n\tchildNodes?: NodeTypeAndVersion[];\n\tworkflow?: Workflow;\n\theaderData?: IncomingHttpHeaders;\n};\n\ntype TestPollingTriggerNodeOptions = TestTriggerNodeOptions & {};\n\nfunction getNodeVersion(Trigger: new () => VersionedNodeType, version?: number) {\n\tconst instance = new Trigger();\n\treturn instance.nodeVersions[version ?? instance.currentVersion];\n}\n\nexport async function testTriggerNode(\n\tTrigger: (new () => INodeType) | INodeType,\n\toptions: TestTriggerNodeOptions = {},\n) {\n\tconst trigger = 'description' in Trigger ? Trigger : new Trigger();\n\tconst emit: jest.MockedFunction<ITriggerFunctions['emit']> = jest.fn();\n\n\tconst timezone = options.timezone ?? 'Europe/Berlin';\n\tconst version = trigger.description.version;\n\tconst node = merge(\n\t\t{\n\t\t\ttype: trigger.description.name,\n\t\t\tname: trigger.description.defaults.name ?? `Test Node (${trigger.description.name})`,\n\t\t\ttypeVersion: typeof version === 'number' ? version : version.at(-1),\n\t\t} satisfies Partial<INode>,\n\t\toptions.node,\n\t) as INode;\n\tconst workflow = mock<Workflow>({ timezone: options.timezone ?? 'Europe/Berlin' });\n\n\tconst scheduledTaskManager = new ScheduledTaskManager(mock<InstanceSettings>());\n\tconst helpers = mock<ITriggerFunctions['helpers']>({\n\t\tcreateDeferredPromise,\n\t\treturnJsonArray,\n\t\tregisterCron: (cronExpression, onTick) =>\n\t\t\tscheduledTaskManager.registerCron(workflow, cronExpression, onTick),\n\t});\n\n\tconst triggerFunctions = mock<ITriggerFunctions>({\n\t\thelpers,\n\t\temit,\n\t\tgetTimezone: () => timezone,\n\t\tgetNode: () => node,\n\t\tgetCredentials: async <T extends object = ICredentialDataDecryptedObject>() =>\n\t\t\t(options.credential ?? {}) as T,\n\t\tgetMode: () => options.mode ?? 'trigger',\n\t\tgetWorkflowStaticData: () => options.workflowStaticData ?? {},\n\t\tgetNodeParameter: (parameterName, fallback) => get(node.parameters, parameterName) ?? fallback,\n\t});\n\n\tconst response = await trigger.trigger?.call(triggerFunctions);\n\n\tif (options.mode === 'manual') {\n\t\texpect(response?.manualTriggerFunction).toBeInstanceOf(Function);\n\t\tawait response?.manualTriggerFunction?.();\n\t}\n\n\treturn {\n\t\tclose: jest.fn(response?.closeFunction),\n\t\temit,\n\t};\n}\n\nexport async function testVersionedWebhookTriggerNode(\n\tTrigger: new () => VersionedNodeType,\n\tversion?: number,\n\toptions: TestWebhookTriggerNodeOptions = {},\n) {\n\treturn await testWebhookTriggerNode(getNodeVersion(Trigger, version), options);\n}\n\nexport async function testWebhookTriggerNode(\n\tTrigger: (new () => INodeType) | INodeType,\n\toptions: TestWebhookTriggerNodeOptions = {},\n) {\n\tconst trigger = 'description' in Trigger ? Trigger : new Trigger();\n\n\tconst timezone = options.timezone ?? 'Europe/Berlin';\n\tconst version = trigger.description.version;\n\tconst node = merge(\n\t\t{\n\t\t\tid: options.node?.id ?? '1',\n\t\t\ttype: trigger.description.name,\n\t\t\tname: trigger.description.defaults.name ?? `Test Node (${trigger.description.name})`,\n\t\t\ttypeVersion: typeof version === 'number' ? version : version.at(-1),\n\t\t} satisfies Partial<INode>,\n\t\toptions.node,\n\t) as INode;\n\tconst workflow = mock<Workflow>({ timezone: options.timezone ?? 'Europe/Berlin' });\n\n\tconst scheduledTaskManager = new ScheduledTaskManager(mock<InstanceSettings>());\n\tconst helpers = mock<ITriggerFunctions['helpers']>({\n\t\treturnJsonArray,\n\t\tregisterCron: (cronExpression, onTick) =>\n\t\t\tscheduledTaskManager.registerCron(workflow, cronExpression, onTick),\n\t\tprepareBinaryData: options.helpers?.prepareBinaryData ?? jest.fn(),\n\t});\n\n\tconst request = mock<express.Request>({\n\t\tmethod: 'GET',\n\t\t...options.request,\n\t});\n\tconst response = mock<express.Response>({ status: jest.fn(() => mock<express.Response>()) });\n\tconst webhookFunctions = mock<IWebhookFunctions>({\n\t\thelpers,\n\t\tnodeHelpers: {\n\t\t\tcopyBinaryFile: jest.fn(async () => mock<IBinaryData>()),\n\t\t},\n\t\tgetTimezone: () => timezone,\n\t\tgetNode: () => node,\n\t\tgetMode: () => options.mode ?? 'trigger',\n\t\tgetInstanceId: () => 'instanceId',\n\t\tgetBodyData: () => options.bodyData ?? {},\n\t\tgetHeaderData: () => options.headerData ?? {},\n\t\tgetInputConnectionData: async () => ({}),\n\t\tgetNodeWebhookUrl: (name) => `/test-webhook-url/${name}`,\n\t\tgetParamsData: () => ({}),\n\t\tgetQueryData: () => ({}),\n\t\tgetRequestObject: () => request,\n\t\tgetResponseObject: () => response,\n\t\tgetWorkflow: () => options.workflow ?? mock<Workflow>(),\n\t\tgetWebhookName: () => options.webhookName ?? 'default',\n\t\tgetWorkflowStaticData: () => options.workflowStaticData ?? {},\n\t\tgetNodeParameter: (parameterName, fallback) => get(node.parameters, parameterName) ?? fallback,\n\t\tgetChildNodes: () => options.childNodes ?? [],\n\t\tgetCredentials: async <T extends object = ICredentialDataDecryptedObject>() =>\n\t\t\t(options.credential ?? {}) as T,\n\t});\n\n\tconst responseData = await trigger.webhook?.call(webhookFunctions);\n\n\treturn {\n\t\tresponseData,\n\t\tresponse: webhookFunctions.getResponseObject(),\n\t};\n}\n\nexport async function testPollingTriggerNode(\n\tTrigger: (new () => INodeType) | INodeType,\n\toptions: TestPollingTriggerNodeOptions = {},\n) {\n\tconst trigger = 'description' in Trigger ? Trigger : new Trigger();\n\n\tconst timezone = options.timezone ?? 'Europe/Berlin';\n\tconst version = trigger.description.version;\n\tconst node = merge(\n\t\t{\n\t\t\ttype: trigger.description.name,\n\t\t\tname: trigger.description.defaults.name ?? `Test Node (${trigger.description.name})`,\n\t\t\ttypeVersion: typeof version === 'number' ? version : version.at(-1),\n\t\t\tcredentials: {},\n\t\t} satisfies Partial<INode>,\n\t\toptions.node,\n\t) as INode;\n\tconst workflow = mock<Workflow>({\n\t\ttimezone,\n\t\tnodeTypes: mock<INodeTypes>({\n\t\t\tgetByNameAndVersion: () => mock<INodeType>({ description: trigger.description }),\n\t\t}),\n\t\tgetStaticData: () => options.workflowStaticData ?? {},\n\t});\n\tconst mode = options.mode ?? 'trigger';\n\n\tconst pollContext = new PollContext(\n\t\tworkflow,\n\t\tnode,\n\t\tmock<IWorkflowExecuteAdditionalData>({\n\t\t\tcurrentNodeParameters: node.parameters,\n\t\t\tcredentialsHelper: mock<IWorkflowExecuteAdditionalData['credentialsHelper']>({\n\t\t\t\tgetParentTypes: () => [],\n\t\t\t\tauthenticate: async (_creds, _type, options) => {\n\t\t\t\t\tset(options, 'headers.authorization', 'mockAuth');\n\t\t\t\t\treturn options as IHttpRequestOptions;\n\t\t\t\t},\n\t\t\t}),\n\t\t\thooks: mock<ExecutionLifecycleHooks>(),\n\t\t}),\n\t\tmode,\n\t\t'init',\n\t);\n\n\tpollContext.getNode = () => node;\n\tpollContext.getCredentials = async <T extends object = ICredentialDataDecryptedObject>() =>\n\t\t(options.credential ?? {}) as T;\n\tpollContext.getNodeParameter = (parameterName, fallback) =>\n\t\tget(node.parameters, parameterName) ?? fallback;\n\n\tconst response = await trigger.poll?.call(pollContext);\n\n\treturn {\n\t\tresponse,\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gCAAqB;AACrB,iBAAgB;AAChB,mBAAkB;AAClB,iBAAgB;AAChB,sBAA6C;AAE7C,oCAAqC;AACrC,0BAeO;AAwBP,SAAS,eAAe,SAAsC,SAAkB;AAC/E,QAAM,WAAW,IAAI,QAAQ;AAC7B,SAAO,SAAS,aAAa,WAAW,SAAS,cAAc;AAChE;AAEA,eAAsB,gBACrB,SACA,UAAkC,CAAC,GAClC;AACD,QAAM,UAAU,iBAAiB,UAAU,UAAU,IAAI,QAAQ;AACjE,QAAM,OAAuD,KAAK,GAAG;AAErE,QAAM,WAAW,QAAQ,YAAY;AACrC,QAAM,UAAU,QAAQ,YAAY;AACpC,QAAM,WAAO,aAAAA;AAAA,IACZ;AAAA,MACC,MAAM,QAAQ,YAAY;AAAA,MAC1B,MAAM,QAAQ,YAAY,SAAS,QAAQ,cAAc,QAAQ,YAAY,IAAI;AAAA,MACjF,aAAa,OAAO,YAAY,WAAW,UAAU,QAAQ,GAAG,EAAE;AAAA,IACnE;AAAA,IACA,QAAQ;AAAA,EACT;AACA,QAAM,eAAW,gCAAe,EAAE,UAAU,QAAQ,YAAY,gBAAgB,CAAC;AAEjF,QAAM,uBAAuB,IAAI,uDAAqB,gCAAuB,CAAC;AAC9E,QAAM,cAAU,gCAAmC;AAAA,IAClD;AAAA,IACA;AAAA,IACA,cAAc,CAAC,gBAAgB,WAC9B,qBAAqB,aAAa,UAAU,gBAAgB,MAAM;AAAA,EACpE,CAAC;AAED,QAAM,uBAAmB,gCAAwB;AAAA,IAChD;AAAA,IACA;AAAA,IACA,aAAa,MAAM;AAAA,IACnB,SAAS,MAAM;AAAA,IACf,gBAAgB,YACd,QAAQ,cAAc,CAAC;AAAA,IACzB,SAAS,MAAM,QAAQ,QAAQ;AAAA,IAC/B,uBAAuB,MAAM,QAAQ,sBAAsB,CAAC;AAAA,IAC5D,kBAAkB,CAAC,eAAe,iBAAa,WAAAC,SAAI,KAAK,YAAY,aAAa,KAAK;AAAA,EACvF,CAAC;AAED,QAAM,WAAW,MAAM,QAAQ,SAAS,KAAK,gBAAgB;AAE7D,MAAI,QAAQ,SAAS,UAAU;AAC9B,WAAO,UAAU,qBAAqB,EAAE,eAAe,QAAQ;AAC/D,UAAM,UAAU,wBAAwB;AAAA,EACzC;AAEA,SAAO;AAAA,IACN,OAAO,KAAK,GAAG,UAAU,aAAa;AAAA,IACtC;AAAA,EACD;AACD;AAEA,eAAsB,gCACrB,SACA,SACA,UAAyC,CAAC,GACzC;AACD,SAAO,MAAM,uBAAuB,eAAe,SAAS,OAAO,GAAG,OAAO;AAC9E;AAEA,eAAsB,uBACrB,SACA,UAAyC,CAAC,GACzC;AACD,QAAM,UAAU,iBAAiB,UAAU,UAAU,IAAI,QAAQ;AAEjE,QAAM,WAAW,QAAQ,YAAY;AACrC,QAAM,UAAU,QAAQ,YAAY;AACpC,QAAM,WAAO,aAAAD;AAAA,IACZ;AAAA,MACC,IAAI,QAAQ,MAAM,MAAM;AAAA,MACxB,MAAM,QAAQ,YAAY;AAAA,MAC1B,MAAM,QAAQ,YAAY,SAAS,QAAQ,cAAc,QAAQ,YAAY,IAAI;AAAA,MACjF,aAAa,OAAO,YAAY,WAAW,UAAU,QAAQ,GAAG,EAAE;AAAA,IACnE;AAAA,IACA,QAAQ;AAAA,EACT;AACA,QAAM,eAAW,gCAAe,EAAE,UAAU,QAAQ,YAAY,gBAAgB,CAAC;AAEjF,QAAM,uBAAuB,IAAI,uDAAqB,gCAAuB,CAAC;AAC9E,QAAM,cAAU,gCAAmC;AAAA,IAClD;AAAA,IACA,cAAc,CAAC,gBAAgB,WAC9B,qBAAqB,aAAa,UAAU,gBAAgB,MAAM;AAAA,IACnE,mBAAmB,QAAQ,SAAS,qBAAqB,KAAK,GAAG;AAAA,EAClE,CAAC;AAED,QAAM,cAAU,gCAAsB;AAAA,IACrC,QAAQ;AAAA,IACR,GAAG,QAAQ;AAAA,EACZ,CAAC;AACD,QAAM,eAAW,gCAAuB,EAAE,QAAQ,KAAK,GAAG,UAAM,gCAAuB,CAAC,EAAE,CAAC;AAC3F,QAAM,uBAAmB,gCAAwB;AAAA,IAChD;AAAA,IACA,aAAa;AAAA,MACZ,gBAAgB,KAAK,GAAG,gBAAY,gCAAkB,CAAC;AAAA,IACxD;AAAA,IACA,aAAa,MAAM;AAAA,IACnB,SAAS,MAAM;AAAA,IACf,SAAS,MAAM,QAAQ,QAAQ;AAAA,IAC/B,eAAe,MAAM;AAAA,IACrB,aAAa,MAAM,QAAQ,YAAY,CAAC;AAAA,IACxC,eAAe,MAAM,QAAQ,cAAc,CAAC;AAAA,IAC5C,wBAAwB,aAAa,CAAC;AAAA,IACtC,mBAAmB,CAAC,SAAS,qBAAqB,IAAI;AAAA,IACtD,eAAe,OAAO,CAAC;AAAA,IACvB,cAAc,OAAO,CAAC;AAAA,IACtB,kBAAkB,MAAM;AAAA,IACxB,mBAAmB,MAAM;AAAA,IACzB,aAAa,MAAM,QAAQ,gBAAY,gCAAe;AAAA,IACtD,gBAAgB,MAAM,QAAQ,eAAe;AAAA,IAC7C,uBAAuB,MAAM,QAAQ,sBAAsB,CAAC;AAAA,IAC5D,kBAAkB,CAAC,eAAe,iBAAa,WAAAC,SAAI,KAAK,YAAY,aAAa,KAAK;AAAA,IACtF,eAAe,MAAM,QAAQ,cAAc,CAAC;AAAA,IAC5C,gBAAgB,YACd,QAAQ,cAAc,CAAC;AAAA,EAC1B,CAAC;AAED,QAAM,eAAe,MAAM,QAAQ,SAAS,KAAK,gBAAgB;AAEjE,SAAO;AAAA,IACN;AAAA,IACA,UAAU,iBAAiB,kBAAkB;AAAA,EAC9C;AACD;AAEA,eAAsB,uBACrB,SACA,UAAyC,CAAC,GACzC;AACD,QAAM,UAAU,iBAAiB,UAAU,UAAU,IAAI,QAAQ;AAEjE,QAAM,WAAW,QAAQ,YAAY;AACrC,QAAM,UAAU,QAAQ,YAAY;AACpC,QAAM,WAAO,aAAAD;AAAA,IACZ;AAAA,MACC,MAAM,QAAQ,YAAY;AAAA,MAC1B,MAAM,QAAQ,YAAY,SAAS,QAAQ,cAAc,QAAQ,YAAY,IAAI;AAAA,MACjF,aAAa,OAAO,YAAY,WAAW,UAAU,QAAQ,GAAG,EAAE;AAAA,MAClE,aAAa,CAAC;AAAA,IACf;AAAA,IACA,QAAQ;AAAA,EACT;AACA,QAAM,eAAW,gCAAe;AAAA,IAC/B;AAAA,IACA,eAAW,gCAAiB;AAAA,MAC3B,qBAAqB,UAAM,gCAAgB,EAAE,aAAa,QAAQ,YAAY,CAAC;AAAA,IAChF,CAAC;AAAA,IACD,eAAe,MAAM,QAAQ,sBAAsB,CAAC;AAAA,EACrD,CAAC;AACD,QAAM,OAAO,QAAQ,QAAQ;AAE7B,QAAM,cAAc,IAAI;AAAA,IACvB;AAAA,IACA;AAAA,QACA,gCAAqC;AAAA,MACpC,uBAAuB,KAAK;AAAA,MAC5B,uBAAmB,gCAA0D;AAAA,QAC5E,gBAAgB,MAAM,CAAC;AAAA,QACvB,cAAc,OAAO,QAAQ,OAAOE,aAAY;AAC/C,yBAAAC,SAAID,UAAS,yBAAyB,UAAU;AAChD,iBAAOA;AAAA,QACR;AAAA,MACD,CAAC;AAAA,MACD,WAAO,gCAA8B;AAAA,IACtC,CAAC;AAAA,IACD;AAAA,IACA;AAAA,EACD;AAEA,cAAY,UAAU,MAAM;AAC5B,cAAY,iBAAiB,YAC3B,QAAQ,cAAc,CAAC;AACzB,cAAY,mBAAmB,CAAC,eAAe,iBAC9C,WAAAD,SAAI,KAAK,YAAY,aAAa,KAAK;AAExC,QAAM,WAAW,MAAM,QAAQ,MAAM,KAAK,WAAW;AAErD,SAAO;AAAA,IACN;AAAA,EACD;AACD;", "names": ["merge", "get", "options", "set"]}