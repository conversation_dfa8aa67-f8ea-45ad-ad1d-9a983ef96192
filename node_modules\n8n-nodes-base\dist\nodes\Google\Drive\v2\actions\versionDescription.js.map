{"version": 3, "sources": ["../../../../../../nodes/Google/Drive/v2/actions/versionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport { NodeConnectionTypes, type INodeTypeDescription } from 'n8n-workflow';\n\nimport * as drive from './drive/Drive.resource';\nimport * as file from './file/File.resource';\nimport * as fileFolder from './fileFolder/FileFolder.resource';\nimport * as folder from './folder/Folder.resource';\n\nexport const versionDescription: INodeTypeDescription = {\n\tdisplayName: 'Google Drive',\n\tname: 'googleDrive',\n\ticon: 'file:googleDrive.svg',\n\tgroup: ['input'],\n\tversion: 3,\n\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\tdescription: 'Access data on Google Drive',\n\tdefaults: {\n\t\tname: 'Google Drive',\n\t},\n\tinputs: [NodeConnectionTypes.Main],\n\toutputs: [NodeConnectionTypes.Main],\n\tusableAsTool: true,\n\tcredentials: [\n\t\t{\n\t\t\tname: 'google<PERSON><PERSON>',\n\t\t\trequired: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tauthentication: ['serviceAccount'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\t{\n\t\t\tname: 'googleDriveOAuth2Api',\n\t\t\trequired: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tauthentication: ['oAuth2'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t],\n\tproperties: [\n\t\t{\n\t\t\tdisplayName: 'Authentication',\n\t\t\tname: 'authentication',\n\t\t\ttype: 'options',\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-miscased\n\t\t\t\t\tname: 'OAuth2 (recommended)',\n\t\t\t\t\tvalue: 'oAuth2',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Service Account',\n\t\t\t\t\tvalue: 'serviceAccount',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'oAuth2',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'File',\n\t\t\t\t\tvalue: 'file',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'File/Folder',\n\t\t\t\t\tvalue: 'fileFolder',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Folder',\n\t\t\t\t\tvalue: 'folder',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Shared Drive',\n\t\t\t\t\tvalue: 'drive',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'file',\n\t\t},\n\t\t...drive.description,\n\t\t...file.description,\n\t\t...fileFolder.description,\n\t\t...folder.description,\n\t],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAA+D;AAE/D,YAAuB;AACvB,WAAsB;AACtB,iBAA4B;AAC5B,aAAwB;AAEjB,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,OAAO;AAAA,EACf,SAAS;AAAA,EACT,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,cAAc;AAAA,EACd,aAAa;AAAA,IACZ;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,gBAAgB,CAAC,gBAAgB;AAAA,QAClC;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,gBAAgB,CAAC,QAAQ;AAAA,QAC1B;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA,YAAY;AAAA,IACX;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,QACR;AAAA;AAAA,UAEC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA,GAAG,MAAM;AAAA,IACT,GAAG,KAAK;AAAA,IACR,GAAG,WAAW;AAAA,IACd,GAAG,OAAO;AAAA,EACX;AACD;", "names": []}