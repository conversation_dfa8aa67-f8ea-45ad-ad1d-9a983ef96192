{"version": 3, "sources": ["../../../../../../nodes/Microsoft/SharePoint/descriptions/item/upsert.operation.ts"], "sourcesContent": ["import { updateDisplayOptions, type INodeProperties } from 'n8n-workflow';\n\nimport { itemColumnsPreSend } from '../../helpers/utils';\nimport { listRLC, siteRLC, untilListSelected, untilSiteSelected } from '../common.descriptions';\n\nconst properties: INodeProperties[] = [\n\t{\n\t\t...siteRLC,\n\t\tdescription: 'Select the site to retrieve lists from',\n\t},\n\t{\n\t\t...listRLC,\n\t\tdescription: 'Select the list you want to create or update an item in',\n\t\tdisplayOptions: {\n\t\t\thide: {\n\t\t\t\t...untilSiteSelected,\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName:\n\t\t\t'Due to API restrictions, the following column types cannot be updated: Hyperlink, Location, Metadata',\n\t\tname: 'noticeUnsupportedFields',\n\t\tdisplayOptions: {\n\t\t\thide: {\n\t\t\t\t...untilSiteSelected,\n\t\t\t\t...untilListSelected,\n\t\t\t},\n\t\t},\n\t\ttype: 'notice',\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Columns',\n\t\tname: 'columns',\n\t\tdefault: {\n\t\t\tmappingMode: 'defineBelow',\n\t\t\tvalue: null,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\thide: {\n\t\t\t\t...untilSiteSelected,\n\t\t\t\t...untilListSelected,\n\t\t\t},\n\t\t},\n\t\tnoDataExpression: true,\n\t\trequired: true,\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tpreSend: [itemColumnsPreSend],\n\t\t\t},\n\t\t},\n\t\ttype: 'resourceMapper',\n\t\ttypeOptions: {\n\t\t\tloadOptionsDependsOn: ['site.value', 'list.value'],\n\t\t\tresourceMapper: {\n\t\t\t\tresourceMapperMethod: 'getMappingColumns',\n\t\t\t\tmode: 'upsert',\n\t\t\t\tfieldWords: {\n\t\t\t\t\tsingular: 'column',\n\t\t\t\t\tplural: 'columns',\n\t\t\t\t},\n\t\t\t\taddAllFields: true,\n\t\t\t\tmultiKeyMatch: false,\n\t\t\t},\n\t\t},\n\t},\n];\n\nconst displayOptions = {\n\tshow: {\n\t\tresource: ['item'],\n\t\toperation: ['upsert'],\n\t},\n};\n\nexport const description = updateDisplayOptions(displayOptions, properties);\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAA2D;AAE3D,mBAAmC;AACnC,oBAAuE;AAEvE,MAAM,aAAgC;AAAA,EACrC;AAAA,IACC,GAAG;AAAA,IACH,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,GAAG;AAAA,IACH,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,GAAG;AAAA,MACJ;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aACC;AAAA,IACD,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,MACR,aAAa;AAAA,MACb,OAAO;AAAA,IACR;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAAA,IACA,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,SAAS;AAAA,MACR,MAAM;AAAA,QACL,SAAS,CAAC,+BAAkB;AAAA,MAC7B;AAAA,IACD;AAAA,IACA,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,sBAAsB,CAAC,cAAc,YAAY;AAAA,MACjD,gBAAgB;AAAA,QACf,sBAAsB;AAAA,QACtB,MAAM;AAAA,QACN,YAAY;AAAA,UACX,UAAU;AAAA,UACV,QAAQ;AAAA,QACT;AAAA,QACA,cAAc;AAAA,QACd,eAAe;AAAA,MAChB;AAAA,IACD;AAAA,EACD;AACD;AAEA,MAAM,iBAAiB;AAAA,EACtB,MAAM;AAAA,IACL,UAAU,CAAC,MAAM;AAAA,IACjB,WAAW,CAAC,QAAQ;AAAA,EACrB;AACD;AAEO,MAAM,kBAAc,0CAAqB,gBAAgB,UAAU;", "names": []}