{"version": 3, "sources": ["../../../../../../nodes/Microsoft/SharePoint/descriptions/file/upload.operation.ts"], "sourcesContent": ["import { updateDisplayOptions, type INodeProperties } from 'n8n-workflow';\n\nimport { uploadFilePreSend } from '../../helpers/utils';\nimport { folderRLC, siteRLC, untilSiteSelected } from '../common.descriptions';\n\nconst properties: INodeProperties[] = [\n\t{\n\t\t...siteRLC,\n\t\tdescription: 'Select the site to retrieve folders from',\n\t},\n\t{\n\t\t...folderRLC,\n\t\tdescription: 'Select the folder to upload the file to',\n\t\tdisplayOptions: {\n\t\t\thide: {\n\t\t\t\t...untilSiteSelected,\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'File Name',\n\t\tname: 'fileName',\n\t\tdefault: '',\n\t\tdescription: 'The name of the file being uploaded',\n\t\tplaceholder: 'e.g. My New File',\n\t\trequired: true,\n\t\ttype: 'string',\n\t},\n\t{\n\t\tdisplayName: 'File Contents',\n\t\tname: 'fileContents',\n\t\tdefault: '',\n\t\tdescription:\n\t\t\t'Find the name of input field containing the binary data to upload the file in the Input panel on the left, in the Binary tab',\n\t\thint: 'The name of the input field containing the binary file data to update the file',\n\t\tplaceholder: 'data',\n\t\trequired: true,\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tpreSend: [uploadFilePreSend],\n\t\t\t},\n\t\t},\n\t\ttype: 'string',\n\t},\n];\n\nconst displayOptions = {\n\tshow: {\n\t\tresource: ['file'],\n\t\toperation: ['upload'],\n\t},\n};\n\nexport const description = updateDisplayOptions(displayOptions, properties);\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAA2D;AAE3D,mBAAkC;AAClC,oBAAsD;AAEtD,MAAM,aAAgC;AAAA,EACrC;AAAA,IACC,GAAG;AAAA,IACH,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,GAAG;AAAA,IACH,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,GAAG;AAAA,MACJ;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,IACb,UAAU;AAAA,IACV,MAAM;AAAA,EACP;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aACC;AAAA,IACD,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,IACV,SAAS;AAAA,MACR,MAAM;AAAA,QACL,SAAS,CAAC,8BAAiB;AAAA,MAC5B;AAAA,IACD;AAAA,IACA,MAAM;AAAA,EACP;AACD;AAEA,MAAM,iBAAiB;AAAA,EACtB,MAAM;AAAA,IACL,UAAU,CAAC,MAAM;AAAA,IACjB,WAAW,CAAC,QAAQ;AAAA,EACrB;AACD;AAEO,MAAM,kBAAc,0CAAqB,gBAAgB,UAAU;", "names": []}