{"version": 3, "sources": ["../../credentials/WiseApi.credentials.ts"], "sourcesContent": ["import type { ICredentialType, INodeProperties } from 'n8n-workflow';\n\nexport class WiseApi implements ICredentialType {\n\tname = 'wiseApi';\n\n\tdisplayName = 'Wise API';\n\n\tdocumentationUrl = 'wise';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'API Token',\n\t\t\tname: 'apiToken',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Environment',\n\t\t\tname: 'environment',\n\t\t\ttype: 'options',\n\t\t\tdefault: 'live',\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Live',\n\t\t\t\t\tvalue: 'live',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Test',\n\t\t\t\t\tvalue: 'test',\n\t\t\t\t},\n\t\t\t],\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Private Key (Optional)',\n\t\t\tname: 'privateKey',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\tdefault: '',\n\t\t\tdescription:\n\t\t\t\t'Optional private key used for Strong Customer Authentication (SCA). Only needed to retrieve statements, and execute transfers.',\n\t\t},\n\t];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,QAAmC;AAAA,EAAzC;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,IACD;AAAA;AACD;", "names": []}