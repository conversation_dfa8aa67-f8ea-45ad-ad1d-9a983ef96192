{"version": 3, "sources": ["../../../../../nodes/Mattermost/v1/actions/versionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport { NodeConnectionTypes, type INodeTypeDescription } from 'n8n-workflow';\n\nimport * as channel from './channel';\nimport * as message from './message';\nimport * as reaction from './reaction';\nimport * as user from './user';\n\nexport const versionDescription: INodeTypeDescription = {\n\tdisplayName: 'Mattermost',\n\tname: 'mattermost',\n\ticon: 'file:mattermost.svg',\n\tgroup: ['output'],\n\tversion: 1,\n\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\tdescription: 'Sends data to Mattermost',\n\tdefaults: {\n\t\tname: 'Mattermost',\n\t},\n\tinputs: [NodeConnectionTypes.Main],\n\toutputs: [NodeConnectionTypes.Main],\n\tcredentials: [\n\t\t{\n\t\t\tname: 'mattermostApi',\n\t\t\trequired: true,\n\t\t},\n\t],\n\tproperties: [\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Channel',\n\t\t\t\t\tvalue: 'channel',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Message',\n\t\t\t\t\tvalue: 'message',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Reaction',\n\t\t\t\t\tvalue: 'reaction',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'User',\n\t\t\t\t\tvalue: 'user',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'message',\n\t\t},\n\t\t...channel.descriptions,\n\t\t...message.descriptions,\n\t\t...reaction.descriptions,\n\t\t...user.descriptions,\n\t],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAA+D;AAE/D,cAAyB;AACzB,cAAyB;AACzB,eAA0B;AAC1B,WAAsB;AAEf,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,QAAQ;AAAA,EAChB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,aAAa;AAAA,IACZ;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA,EACD;AAAA,EACA,YAAY;AAAA,IACX;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,IACX,GAAG,SAAS;AAAA,IACZ,GAAG,KAAK;AAAA,EACT;AACD;", "names": []}