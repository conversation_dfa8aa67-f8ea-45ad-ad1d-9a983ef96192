{"version": 3, "sources": ["../../../../../nodes/Google/Analytics/v1/UserActivityDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const userActivityOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userActivity'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Search',\n\t\t\t\tvalue: 'search',\n\t\t\t\tdescription: 'Return user activity data',\n\t\t\t\taction: 'Search user activity data',\n\t\t\t},\n\t\t],\n\t\tdefault: 'search',\n\t},\n];\n\nexport const userActivityFields: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'View Name or ID',\n\t\tname: 'viewId',\n\t\ttype: 'options',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getViews',\n\t\t},\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userActivity'],\n\t\t\t\toperation: ['search'],\n\t\t\t},\n\t\t},\n\t\tplaceholder: '123456',\n\t\tdescription:\n\t\t\t'The View ID of Google Analytics. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'User ID',\n\t\tname: 'userId',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userActivity'],\n\t\t\t\toperation: ['search'],\n\t\t\t},\n\t\t},\n\t\tplaceholder: '123456',\n\t\tdescription: 'ID of a user',\n\t},\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['search'],\n\t\t\t\tresource: ['userActivity'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['search'],\n\t\t\t\tresource: ['userActivity'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 500,\n\t\t},\n\t\tdefault: 100,\n\t\tdescription: 'Max number of results to return',\n\t},\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['search'],\n\t\t\t\tresource: ['userActivity'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Activity Types',\n\t\t\t\tname: 'activityTypes',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Ecommerce',\n\t\t\t\t\t\tvalue: 'ECOMMERCE',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Event',\n\t\t\t\t\t\tvalue: 'EVENT',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Goal',\n\t\t\t\t\t\tvalue: 'GOAL',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Pageview',\n\t\t\t\t\t\tvalue: 'PAGEVIEW',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Screenview',\n\t\t\t\t\t\tvalue: 'SCREENVIEW',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdescription: 'Type of activites requested',\n\t\t\t\tdefault: [],\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,yBAA4C;AAAA,EACxD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,cAAc;AAAA,MAC1B;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,qBAAwC;AAAA,EACpD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,IACT,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,cAAc;AAAA,QACzB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,IACb,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,cAAc;AAAA,QACzB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,cAAc;AAAA,MAC1B;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,cAAc;AAAA,QACzB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,cAAc;AAAA,MAC1B;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,aAAa;AAAA,QACb,SAAS,CAAC;AAAA,MACX;AAAA,IACD;AAAA,EACD;AACD;", "names": []}