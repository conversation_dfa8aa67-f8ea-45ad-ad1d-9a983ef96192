{"version": 3, "sources": ["../../../../nodes/Transform/utils/utils.ts"], "sourcesContent": ["import { ApplicationError } from 'n8n-workflow';\n\nexport const prepareFieldsArray = (fields: string | string[], fieldName = 'Fields') => {\n\tif (typeof fields === 'string') {\n\t\treturn fields\n\t\t\t.split(',')\n\t\t\t.map((entry) => entry.trim())\n\t\t\t.filter((entry) => entry !== '');\n\t}\n\tif (Array.isArray(fields)) {\n\t\treturn fields;\n\t}\n\tthrow new ApplicationError(\n\t\t`The \\'${fieldName}\\' parameter must be a string of fields separated by commas or an array of strings.`,\n\t\t{ level: 'warning' },\n\t);\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAAiC;AAE1B,MAAM,qBAAqB,CAAC,QAA2B,YAAY,aAAa;AACtF,MAAI,OAAO,WAAW,UAAU;AAC/B,WAAO,OACL,MAAM,GAAG,EACT,IAAI,CAAC,UAAU,MAAM,KAAK,CAAC,EAC3B,OAAO,CAAC,UAAU,UAAU,EAAE;AAAA,EACjC;AACA,MAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,WAAO;AAAA,EACR;AACA,QAAM,IAAI;AAAA,IACT,QAAS,SAAS;AAAA,IAClB,EAAE,OAAO,UAAU;AAAA,EACpB;AACD;", "names": []}