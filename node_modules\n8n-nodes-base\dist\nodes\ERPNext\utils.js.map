{"version": 3, "sources": ["../../../nodes/ERPNext/utils.ts"], "sourcesContent": ["import flow from 'lodash/flow';\nimport sortBy from 'lodash/sortBy';\nimport uniqBy from 'lodash/uniqBy';\n\nexport type DocumentProperties = {\n\tcustomProperty: Array<{ field: string; value: string }>;\n};\n\ntype DocFields = Array<{ name: string; value: string }>;\n\nconst ensureName = (docFields: DocFields) => docFields.filter((o) => o.name);\nconst sortByName = (docFields: DocFields) => sortBy(docFields, ['name']);\nconst uniqueByName = (docFields: DocFields) => uniqBy(docFields, (o) => o.name);\n\nexport const processNames = flow(ensureName, sortByName, uniqueByName);\n\nexport const toSQL = (operator: string) => {\n\tconst operators: { [key: string]: string } = {\n\t\tis: '=',\n\t\tisNot: '!=',\n\t\tgreater: '>',\n\t\tless: '<',\n\t\tequalsGreater: '>=',\n\t\tequalsLess: '<=',\n\t};\n\n\treturn operators[operator];\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAiB;AACjB,oBAAmB;AACnB,oBAAmB;AAQnB,MAAM,aAAa,CAAC,cAAyB,UAAU,OAAO,CAAC,MAAM,EAAE,IAAI;AAC3E,MAAM,aAAa,CAAC,kBAAyB,cAAAA,SAAO,WAAW,CAAC,MAAM,CAAC;AACvE,MAAM,eAAe,CAAC,kBAAyB,cAAAC,SAAO,WAAW,CAAC,MAAM,EAAE,IAAI;AAEvE,MAAM,mBAAe,YAAAC,SAAK,YAAY,YAAY,YAAY;AAE9D,MAAM,QAAQ,CAAC,aAAqB;AAC1C,QAAM,YAAuC;AAAA,IAC5C,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,SAAS;AAAA,IACT,MAAM;AAAA,IACN,eAAe;AAAA,IACf,YAAY;AAAA,EACb;AAEA,SAAO,UAAU,QAAQ;AAC1B;", "names": ["sortBy", "uniqBy", "flow"]}