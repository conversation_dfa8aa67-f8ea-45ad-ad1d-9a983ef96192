{"version": 3, "sources": ["../../credentials/ZulipApi.credentials.ts"], "sourcesContent": ["import type { ICredentialType, INodeProperties } from 'n8n-workflow';\n\nexport class ZulipApi implements ICredentialType {\n\tname = 'zulip<PERSON><PERSON>';\n\n\tdisplayName = 'Zulip API';\n\n\tdocumentationUrl = 'zulip';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'URL',\n\t\t\tname: 'url',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\tplaceholder: 'https://yourZulipDomain.zulipchat.com',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Email',\n\t\t\tname: 'email',\n\t\t\ttype: 'string',\n\t\t\tplaceholder: '<EMAIL>',\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'API Key',\n\t\t\tname: 'api<PERSON><PERSON>',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\tdefault: '',\n\t\t},\n\t];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,SAAoC;AAAA,EAA1C;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,MACV;AAAA,IACD;AAAA;AACD;", "names": []}