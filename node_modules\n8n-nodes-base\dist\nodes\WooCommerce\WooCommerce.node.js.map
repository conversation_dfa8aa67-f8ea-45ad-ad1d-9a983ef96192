{"version": 3, "sources": ["../../../nodes/WooCommerce/WooCommerce.node.ts"], "sourcesContent": ["import {\n\ttype IExecuteFunctions,\n\ttype IDataObject,\n\ttype ILoadOptionsFunctions,\n\ttype INodeExecutionData,\n\ttype INodePropertyOptions,\n\ttype INodeType,\n\ttype INodeTypeDescription,\n\tNodeConnectionTypes,\n} from 'n8n-workflow';\n\nimport { customerFields, customerOperations } from './descriptions';\nimport {\n\tadjustMetadata,\n\tsetFields,\n\tsetMetadata,\n\ttoSnakeCase,\n\twoocommerceApiRequest,\n\twoocommerceApiRequestAllItems,\n} from './GenericFunctions';\nimport { orderFields, orderOperations } from './OrderDescription';\nimport type {\n\tIAddress,\n\tICouponLine,\n\tIFeeLine,\n\tILineItem,\n\tIOrder,\n\tIShoppingLine,\n} from './OrderInterface';\nimport { productFields, productOperations } from './ProductDescription';\nimport type { IDimension, IImage, IProduct } from './ProductInterface';\n\nexport class WooCommerce implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'WooCommerce',\n\t\tname: 'wooCommerce',\n\t\ticon: 'file:wooCommerce.svg',\n\t\tgroup: ['output'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume WooCommerce API',\n\t\tdefaults: {\n\t\t\tname: 'WooCommerce',\n\t\t},\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tusableAsTool: true,\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'wooCommerceApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Customer',\n\t\t\t\t\t\tvalue: 'customer',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Order',\n\t\t\t\t\t\tvalue: 'order',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Product',\n\t\t\t\t\t\tvalue: 'product',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'product',\n\t\t\t},\n\t\t\t...customerOperations,\n\t\t\t...customerFields,\n\t\t\t...productOperations,\n\t\t\t...productFields,\n\t\t\t...orderOperations,\n\t\t\t...orderFields,\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\t// Get all the available categories to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getCategories(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst categories = await woocommerceApiRequestAllItems.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/products/categories',\n\t\t\t\t\t{},\n\t\t\t\t);\n\t\t\t\tfor (const category of categories) {\n\t\t\t\t\tconst categoryName = category.name;\n\t\t\t\t\tconst categoryId = category.id;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: categoryName,\n\t\t\t\t\t\tvalue: categoryId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the available tags to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getTags(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst tags = await woocommerceApiRequestAllItems.call(this, 'GET', '/products/tags', {});\n\t\t\t\tfor (const tag of tags) {\n\t\t\t\t\tconst tagName = tag.name;\n\t\t\t\t\tconst tagId = tag.id;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: tagName,\n\t\t\t\t\t\tvalue: tagId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: INodeExecutionData[] = [];\n\t\tconst length = items.length;\n\t\tlet responseData;\n\t\tconst qs: IDataObject = {};\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tif (resource === 'customer') {\n\t\t\t\t// **********************************************************************\n\t\t\t\t//                                customer\n\t\t\t\t// **********************************************************************\n\n\t\t\t\t// https://woocommerce.github.io/woocommerce-rest-api-docs/?shell#customer-properties\n\n\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t//             customer: create\n\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t// https://woocommerce.github.io/woocommerce-rest-api-docs/?javascript#create-a-customer\n\n\t\t\t\t\tconst body = {\n\t\t\t\t\t\temail: this.getNodeParameter('email', i),\n\t\t\t\t\t} as IDataObject;\n\n\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\tObject.assign(body, adjustMetadata(additionalFields));\n\t\t\t\t\t}\n\n\t\t\t\t\tresponseData = await woocommerceApiRequest.call(this, 'POST', '/customers', body);\n\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t//             customer: delete\n\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t// https://woocommerce.github.io/woocommerce-rest-api-docs/?javascript#delete-a-customer\n\n\t\t\t\t\tconst customerId = this.getNodeParameter('customerId', i);\n\n\t\t\t\t\tqs.force = true; // required, customers do not support trashing\n\n\t\t\t\t\tconst endpoint = `/customers/${customerId}`;\n\t\t\t\t\tresponseData = await woocommerceApiRequest.call(this, 'DELETE', endpoint, {}, qs);\n\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t//              customer: get\n\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t// https://woocommerce.github.io/woocommerce-rest-api-docs/?javascript#retrieve-a-customer\n\n\t\t\t\t\tconst customerId = this.getNodeParameter('customerId', i);\n\n\t\t\t\t\tconst endpoint = `/customers/${customerId}`;\n\t\t\t\t\tresponseData = await woocommerceApiRequest.call(this, 'GET', endpoint);\n\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t//             customer: getAll\n\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t// https://woocommerce.github.io/woocommerce-rest-api-docs/?javascript#list-all-customers\n\n\t\t\t\t\tconst filters = this.getNodeParameter('filters', i);\n\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\tif (Object.keys(filters).length) {\n\t\t\t\t\t\tObject.assign(qs, filters);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\tresponseData = await woocommerceApiRequestAllItems.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t'/customers',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tqs.per_page = this.getNodeParameter('limit', i);\n\t\t\t\t\t\tresponseData = await woocommerceApiRequest.call(this, 'GET', '/customers', {}, qs);\n\t\t\t\t\t}\n\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t//             customer: update\n\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t// https://woocommerce.github.io/woocommerce-rest-api-docs/?javascript#update-a-customer\n\n\t\t\t\t\tconst body = {} as IDataObject;\n\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\tif (Object.keys(updateFields).length) {\n\t\t\t\t\t\tObject.assign(body, adjustMetadata(updateFields));\n\t\t\t\t\t}\n\n\t\t\t\t\tconst customerId = this.getNodeParameter('customerId', i);\n\n\t\t\t\t\tconst endpoint = `/customers/${customerId}`;\n\t\t\t\t\tresponseData = await woocommerceApiRequest.call(this, 'PUT', endpoint, body);\n\t\t\t\t}\n\t\t\t} else if (resource === 'product') {\n\t\t\t\t//https://woocommerce.github.io/woocommerce-rest-api-docs/#create-a-product\n\t\t\t\tif (operation === 'create') {\n\t\t\t\t\tconst name = this.getNodeParameter('name', i) as string;\n\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\tconst body: IProduct = {\n\t\t\t\t\t\tname,\n\t\t\t\t\t};\n\n\t\t\t\t\tsetFields(additionalFields, body);\n\n\t\t\t\t\tif (additionalFields.categories) {\n\t\t\t\t\t\tbody.categories = (additionalFields.categories as string[]).map((category) => ({\n\t\t\t\t\t\t\tid: parseInt(category, 10),\n\t\t\t\t\t\t})) as unknown as IDataObject[];\n\t\t\t\t\t}\n\n\t\t\t\t\tconst images = (this.getNodeParameter('imagesUi', i) as IDataObject)\n\t\t\t\t\t\t.imagesValues as IImage[];\n\t\t\t\t\tif (images) {\n\t\t\t\t\t\tbody.images = images;\n\t\t\t\t\t}\n\t\t\t\t\tconst dimension = (this.getNodeParameter('dimensionsUi', i) as IDataObject)\n\t\t\t\t\t\t.dimensionsValues as IDimension;\n\t\t\t\t\tif (dimension) {\n\t\t\t\t\t\tbody.dimensions = dimension;\n\t\t\t\t\t}\n\t\t\t\t\tconst metadata = (this.getNodeParameter('metadataUi', i) as IDataObject)\n\t\t\t\t\t\t.metadataValues as IDataObject[];\n\t\t\t\t\tif (metadata) {\n\t\t\t\t\t\tbody.meta_data = metadata;\n\t\t\t\t\t}\n\t\t\t\t\tresponseData = await woocommerceApiRequest.call(this, 'POST', '/products', body);\n\t\t\t\t}\n\t\t\t\t//https://woocommerce.github.io/woocommerce-rest-api-docs/#update-a-product\n\t\t\t\tif (operation === 'update') {\n\t\t\t\t\tconst productId = this.getNodeParameter('productId', i) as string;\n\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\tconst body: IProduct = {};\n\n\t\t\t\t\tsetFields(updateFields, body);\n\n\t\t\t\t\tconst images = (this.getNodeParameter('imagesUi', i) as IDataObject)\n\t\t\t\t\t\t.imagesValues as IImage[];\n\t\t\t\t\tif (images) {\n\t\t\t\t\t\tbody.images = images;\n\t\t\t\t\t}\n\t\t\t\t\tconst dimension = (this.getNodeParameter('dimensionsUi', i) as IDataObject)\n\t\t\t\t\t\t.dimensionsValues as IDimension;\n\t\t\t\t\tif (dimension) {\n\t\t\t\t\t\tbody.dimensions = dimension;\n\t\t\t\t\t}\n\t\t\t\t\tconst metadata = (this.getNodeParameter('metadataUi', i) as IDataObject)\n\t\t\t\t\t\t.metadataValues as IDataObject[];\n\t\t\t\t\tif (metadata) {\n\t\t\t\t\t\tbody.meta_data = metadata;\n\t\t\t\t\t}\n\t\t\t\t\tresponseData = await woocommerceApiRequest.call(\n\t\t\t\t\t\tthis,\n\t\t\t\t\t\t'PUT',\n\t\t\t\t\t\t`/products/${productId}`,\n\t\t\t\t\t\tbody,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\t//https://woocommerce.github.io/woocommerce-rest-api-docs/#retrieve-a-product\n\t\t\t\tif (operation === 'get') {\n\t\t\t\t\tconst productId = this.getNodeParameter('productId', i) as string;\n\t\t\t\t\tresponseData = await woocommerceApiRequest.call(\n\t\t\t\t\t\tthis,\n\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t`/products/${productId}`,\n\t\t\t\t\t\t{},\n\t\t\t\t\t\tqs,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\t//https://woocommerce.github.io/woocommerce-rest-api-docs/#list-all-products\n\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\tif (options.after) {\n\t\t\t\t\t\tqs.after = options.after as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.before) {\n\t\t\t\t\t\tqs.before = options.before as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.category) {\n\t\t\t\t\t\tqs.category = options.category as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.context) {\n\t\t\t\t\t\tqs.context = options.context as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.featured) {\n\t\t\t\t\t\tqs.featured = options.featured as boolean;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.maxPrice) {\n\t\t\t\t\t\tqs.max_price = options.maxPrice as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.minPrice) {\n\t\t\t\t\t\tqs.max_price = options.minPrice as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.order) {\n\t\t\t\t\t\tqs.order = options.order as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.orderBy) {\n\t\t\t\t\t\tqs.orderby = options.orderBy as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.search) {\n\t\t\t\t\t\tqs.search = options.search as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.sku) {\n\t\t\t\t\t\tqs.sku = options.sku as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.slug) {\n\t\t\t\t\t\tqs.slug = options.slug as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.status) {\n\t\t\t\t\t\tqs.status = options.status as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.stockStatus) {\n\t\t\t\t\t\tqs.stock_status = options.stockStatus as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.tag) {\n\t\t\t\t\t\tqs.tag = options.tag as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.taxClass) {\n\t\t\t\t\t\tqs.tax_class = options.taxClass as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.type) {\n\t\t\t\t\t\tqs.type = options.type as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\tresponseData = await woocommerceApiRequestAllItems.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t'/products',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tqs.per_page = this.getNodeParameter('limit', i);\n\t\t\t\t\t\tresponseData = await woocommerceApiRequest.call(this, 'GET', '/products', {}, qs);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t//https://woocommerce.github.io/woocommerce-rest-api-docs/#delete-a-product\n\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\tconst productId = this.getNodeParameter('productId', i) as string;\n\t\t\t\t\tresponseData = await woocommerceApiRequest.call(\n\t\t\t\t\t\tthis,\n\t\t\t\t\t\t'DELETE',\n\t\t\t\t\t\t`/products/${productId}`,\n\t\t\t\t\t\t{},\n\t\t\t\t\t\t{ force: true },\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (resource === 'order') {\n\t\t\t\t//https://woocommerce.github.io/woocommerce-rest-api-docs/#create-an-order\n\t\t\t\tif (operation === 'create') {\n\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\tconst body: IOrder = {};\n\n\t\t\t\t\tsetFields(additionalFields, body);\n\n\t\t\t\t\tconst billing = (this.getNodeParameter('billingUi', i) as IDataObject)\n\t\t\t\t\t\t.billingValues as IAddress;\n\t\t\t\t\tif (billing !== undefined) {\n\t\t\t\t\t\tbody.billing = billing;\n\t\t\t\t\t\ttoSnakeCase(billing as IDataObject);\n\t\t\t\t\t}\n\t\t\t\t\tconst shipping = (this.getNodeParameter('shippingUi', i) as IDataObject)\n\t\t\t\t\t\t.shippingValues as IAddress;\n\t\t\t\t\tif (shipping !== undefined) {\n\t\t\t\t\t\tbody.shipping = shipping;\n\t\t\t\t\t\ttoSnakeCase(shipping as IDataObject);\n\t\t\t\t\t}\n\t\t\t\t\tconst couponLines = (this.getNodeParameter('couponLinesUi', i) as IDataObject)\n\t\t\t\t\t\t.couponLinesValues as ICouponLine[];\n\t\t\t\t\tif (couponLines) {\n\t\t\t\t\t\tbody.coupon_lines = couponLines;\n\t\t\t\t\t\tsetMetadata(couponLines);\n\t\t\t\t\t\ttoSnakeCase(couponLines);\n\t\t\t\t\t}\n\t\t\t\t\tconst feeLines = (this.getNodeParameter('feeLinesUi', i) as IDataObject)\n\t\t\t\t\t\t.feeLinesValues as IFeeLine[];\n\t\t\t\t\tif (feeLines) {\n\t\t\t\t\t\tbody.fee_lines = feeLines;\n\t\t\t\t\t\tsetMetadata(feeLines);\n\t\t\t\t\t\ttoSnakeCase(feeLines);\n\t\t\t\t\t}\n\t\t\t\t\tconst lineItems = (this.getNodeParameter('lineItemsUi', i) as IDataObject)\n\t\t\t\t\t\t.lineItemsValues as ILineItem[];\n\t\t\t\t\tif (lineItems) {\n\t\t\t\t\t\tbody.line_items = lineItems;\n\t\t\t\t\t\tsetMetadata(lineItems);\n\t\t\t\t\t\ttoSnakeCase(lineItems);\n\t\t\t\t\t}\n\t\t\t\t\tconst metadata = (this.getNodeParameter('metadataUi', i) as IDataObject)\n\t\t\t\t\t\t.metadataValues as IDataObject[];\n\t\t\t\t\tif (metadata) {\n\t\t\t\t\t\tbody.meta_data = metadata;\n\t\t\t\t\t}\n\t\t\t\t\tconst shippingLines = (this.getNodeParameter('shippingLinesUi', i) as IDataObject)\n\t\t\t\t\t\t.shippingLinesValues as IShoppingLine[];\n\t\t\t\t\tif (shippingLines) {\n\t\t\t\t\t\tbody.shipping_lines = shippingLines;\n\t\t\t\t\t\tsetMetadata(shippingLines);\n\t\t\t\t\t\ttoSnakeCase(shippingLines);\n\t\t\t\t\t}\n\t\t\t\t\tresponseData = await woocommerceApiRequest.call(this, 'POST', '/orders', body);\n\t\t\t\t}\n\t\t\t\t//https://woocommerce.github.io/woocommerce-rest-api-docs/#update-an-order\n\t\t\t\tif (operation === 'update') {\n\t\t\t\t\tconst orderId = this.getNodeParameter('orderId', i) as string;\n\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\tconst body: IOrder = {};\n\n\t\t\t\t\tif (updateFields.currency) {\n\t\t\t\t\t\tbody.currency = updateFields.currency as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (updateFields.customerId) {\n\t\t\t\t\t\tbody.customer_id = parseInt(updateFields.customerId as string, 10);\n\t\t\t\t\t}\n\t\t\t\t\tif (updateFields.customerNote) {\n\t\t\t\t\t\tbody.customer_note = updateFields.customerNote as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (updateFields.parentId) {\n\t\t\t\t\t\tbody.parent_id = parseInt(updateFields.parentId as string, 10);\n\t\t\t\t\t}\n\t\t\t\t\tif (updateFields.paymentMethodId) {\n\t\t\t\t\t\tbody.payment_method = updateFields.paymentMethodId as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (updateFields.paymentMethodTitle) {\n\t\t\t\t\t\tbody.payment_method_title = updateFields.paymentMethodTitle as string;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (updateFields.status) {\n\t\t\t\t\t\tbody.status = updateFields.status as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (updateFields.transactionID) {\n\t\t\t\t\t\tbody.transaction_id = updateFields.transactionID as string;\n\t\t\t\t\t}\n\t\t\t\t\tconst billing = (this.getNodeParameter('billingUi', i) as IDataObject)\n\t\t\t\t\t\t.billingValues as IAddress;\n\t\t\t\t\tif (billing !== undefined) {\n\t\t\t\t\t\tbody.billing = billing;\n\t\t\t\t\t\ttoSnakeCase(billing as IDataObject);\n\t\t\t\t\t}\n\t\t\t\t\tconst shipping = (this.getNodeParameter('shippingUi', i) as IDataObject)\n\t\t\t\t\t\t.shippingValues as IAddress;\n\t\t\t\t\tif (shipping !== undefined) {\n\t\t\t\t\t\tbody.shipping = shipping;\n\t\t\t\t\t\ttoSnakeCase(shipping as IDataObject);\n\t\t\t\t\t}\n\t\t\t\t\tconst couponLines = (this.getNodeParameter('couponLinesUi', i) as IDataObject)\n\t\t\t\t\t\t.couponLinesValues as ICouponLine[];\n\t\t\t\t\tif (couponLines) {\n\t\t\t\t\t\tbody.coupon_lines = couponLines;\n\t\t\t\t\t\tsetMetadata(couponLines);\n\t\t\t\t\t\ttoSnakeCase(couponLines);\n\t\t\t\t\t}\n\t\t\t\t\tconst feeLines = (this.getNodeParameter('feeLinesUi', i) as IDataObject)\n\t\t\t\t\t\t.feeLinesValues as IFeeLine[];\n\t\t\t\t\tif (feeLines) {\n\t\t\t\t\t\tbody.fee_lines = feeLines;\n\t\t\t\t\t\tsetMetadata(feeLines);\n\t\t\t\t\t\ttoSnakeCase(feeLines);\n\t\t\t\t\t}\n\t\t\t\t\tconst lineItems = (this.getNodeParameter('lineItemsUi', i) as IDataObject)\n\t\t\t\t\t\t.lineItemsValues as ILineItem[];\n\t\t\t\t\tif (lineItems) {\n\t\t\t\t\t\tbody.line_items = lineItems;\n\t\t\t\t\t\tsetMetadata(lineItems);\n\t\t\t\t\t\ttoSnakeCase(lineItems);\n\t\t\t\t\t}\n\t\t\t\t\tconst metadata = (this.getNodeParameter('metadataUi', i) as IDataObject)\n\t\t\t\t\t\t.metadataValues as IDataObject[];\n\t\t\t\t\tif (metadata) {\n\t\t\t\t\t\tbody.meta_data = metadata;\n\t\t\t\t\t}\n\t\t\t\t\tconst shippingLines = (this.getNodeParameter('shippingLinesUi', i) as IDataObject)\n\t\t\t\t\t\t.shippingLinesValues as IShoppingLine[];\n\t\t\t\t\tif (shippingLines) {\n\t\t\t\t\t\tbody.shipping_lines = shippingLines;\n\t\t\t\t\t\tsetMetadata(shippingLines);\n\t\t\t\t\t\ttoSnakeCase(shippingLines);\n\t\t\t\t\t}\n\n\t\t\t\t\tresponseData = await woocommerceApiRequest.call(this, 'PUT', `/orders/${orderId}`, body);\n\t\t\t\t}\n\t\t\t\t//https://woocommerce.github.io/woocommerce-rest-api-docs/#retrieve-an-order\n\t\t\t\tif (operation === 'get') {\n\t\t\t\t\tconst orderId = this.getNodeParameter('orderId', i) as string;\n\t\t\t\t\tresponseData = await woocommerceApiRequest.call(\n\t\t\t\t\t\tthis,\n\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t`/orders/${orderId}`,\n\t\t\t\t\t\t{},\n\t\t\t\t\t\tqs,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\t//https://woocommerce.github.io/woocommerce-rest-api-docs/#list-all-orders\n\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\tif (options.after) {\n\t\t\t\t\t\tqs.after = options.after as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.before) {\n\t\t\t\t\t\tqs.before = options.before as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.category) {\n\t\t\t\t\t\tqs.category = options.category as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.customer) {\n\t\t\t\t\t\tqs.customer = parseInt(options.customer as string, 10);\n\t\t\t\t\t}\n\t\t\t\t\tif (options.decimalPoints) {\n\t\t\t\t\t\tqs.dp = options.decimalPoints as number;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.product) {\n\t\t\t\t\t\tqs.product = parseInt(options.product as string, 10);\n\t\t\t\t\t}\n\t\t\t\t\tif (options.order) {\n\t\t\t\t\t\tqs.order = options.order as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.orderBy) {\n\t\t\t\t\t\tqs.orderby = options.orderBy as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.search) {\n\t\t\t\t\t\tqs.search = options.search as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (options.status) {\n\t\t\t\t\t\tqs.status = options.status as string;\n\t\t\t\t\t}\n\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\tresponseData = await woocommerceApiRequestAllItems.call(this, 'GET', '/orders', {}, qs);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tqs.per_page = this.getNodeParameter('limit', i);\n\t\t\t\t\t\tresponseData = await woocommerceApiRequest.call(this, 'GET', '/orders', {}, qs);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t//https://woocommerce.github.io/woocommerce-rest-api-docs/#delete-an-order\n\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\tconst orderId = this.getNodeParameter('orderId', i) as string;\n\t\t\t\t\tresponseData = await woocommerceApiRequest.call(\n\t\t\t\t\t\tthis,\n\t\t\t\t\t\t'DELETE',\n\t\t\t\t\t\t`/orders/${orderId}`,\n\t\t\t\t\t\t{},\n\t\t\t\t\t\t{ force: true },\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\tthis.helpers.returnJsonArray(responseData as IDataObject[]),\n\t\t\t\t{ itemData: { item: i } },\n\t\t\t);\n\t\t\treturnData.push(...executionData);\n\t\t}\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BASO;AAEP,0BAAmD;AACnD,8BAOO;AACP,8BAA6C;AAS7C,gCAAiD;AAG1C,MAAM,YAAiC;AAAA,EAAvC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,QAAQ;AAAA,MAChB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,cAAc;AAAA,MACd,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA;AAAA;AAAA,QAGZ,MAAM,gBAA4E;AACjF,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,aAAa,MAAM,sDAA8B;AAAA,YACtD;AAAA,YACA;AAAA,YACA;AAAA,YACA,CAAC;AAAA,UACF;AACA,qBAAW,YAAY,YAAY;AAClC,kBAAM,eAAe,SAAS;AAC9B,kBAAM,aAAa,SAAS;AAC5B,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,UAAsE;AAC3E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,OAAO,MAAM,sDAA8B,KAAK,MAAM,OAAO,kBAAkB,CAAC,CAAC;AACvF,qBAAW,OAAO,MAAM;AACvB,kBAAM,UAAU,IAAI;AACpB,kBAAM,QAAQ,IAAI;AAClB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAAmC,CAAC;AAC1C,UAAM,SAAS,MAAM;AACrB,QAAI;AACJ,UAAM,KAAkB,CAAC;AACzB,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI,aAAa,YAAY;AAO5B,YAAI,cAAc,UAAU;AAO3B,gBAAM,OAAO;AAAA,YACZ,OAAO,KAAK,iBAAiB,SAAS,CAAC;AAAA,UACxC;AAEA,gBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,cAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,mBAAO,OAAO,UAAM,wCAAe,gBAAgB,CAAC;AAAA,UACrD;AAEA,yBAAe,MAAM,8CAAsB,KAAK,MAAM,QAAQ,cAAc,IAAI;AAAA,QACjF,WAAW,cAAc,UAAU;AAOlC,gBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AAExD,aAAG,QAAQ;AAEX,gBAAM,WAAW,cAAc,UAAU;AACzC,yBAAe,MAAM,8CAAsB,KAAK,MAAM,UAAU,UAAU,CAAC,GAAG,EAAE;AAAA,QACjF,WAAW,cAAc,OAAO;AAO/B,gBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AAExD,gBAAM,WAAW,cAAc,UAAU;AACzC,yBAAe,MAAM,8CAAsB,KAAK,MAAM,OAAO,QAAQ;AAAA,QACtE,WAAW,cAAc,UAAU;AAOlC,gBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,gBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,cAAI,OAAO,KAAK,OAAO,EAAE,QAAQ;AAChC,mBAAO,OAAO,IAAI,OAAO;AAAA,UAC1B;AAEA,cAAI,WAAW;AACd,2BAAe,MAAM,sDAA8B;AAAA,cAClD;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD,CAAC;AAAA,YACF;AAAA,UACD,OAAO;AACN,eAAG,WAAW,KAAK,iBAAiB,SAAS,CAAC;AAC9C,2BAAe,MAAM,8CAAsB,KAAK,MAAM,OAAO,cAAc,CAAC,GAAG,EAAE;AAAA,UAClF;AAAA,QACD,WAAW,cAAc,UAAU;AAOlC,gBAAM,OAAO,CAAC;AACd,gBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,cAAI,OAAO,KAAK,YAAY,EAAE,QAAQ;AACrC,mBAAO,OAAO,UAAM,wCAAe,YAAY,CAAC;AAAA,UACjD;AAEA,gBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AAExD,gBAAM,WAAW,cAAc,UAAU;AACzC,yBAAe,MAAM,8CAAsB,KAAK,MAAM,OAAO,UAAU,IAAI;AAAA,QAC5E;AAAA,MACD,WAAW,aAAa,WAAW;AAElC,YAAI,cAAc,UAAU;AAC3B,gBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC5C,gBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,gBAAM,OAAiB;AAAA,YACtB;AAAA,UACD;AAEA,iDAAU,kBAAkB,IAAI;AAEhC,cAAI,iBAAiB,YAAY;AAChC,iBAAK,aAAc,iBAAiB,WAAwB,IAAI,CAAC,cAAc;AAAA,cAC9E,IAAI,SAAS,UAAU,EAAE;AAAA,YAC1B,EAAE;AAAA,UACH;AAEA,gBAAM,SAAU,KAAK,iBAAiB,YAAY,CAAC,EACjD;AACF,cAAI,QAAQ;AACX,iBAAK,SAAS;AAAA,UACf;AACA,gBAAM,YAAa,KAAK,iBAAiB,gBAAgB,CAAC,EACxD;AACF,cAAI,WAAW;AACd,iBAAK,aAAa;AAAA,UACnB;AACA,gBAAM,WAAY,KAAK,iBAAiB,cAAc,CAAC,EACrD;AACF,cAAI,UAAU;AACb,iBAAK,YAAY;AAAA,UAClB;AACA,yBAAe,MAAM,8CAAsB,KAAK,MAAM,QAAQ,aAAa,IAAI;AAAA,QAChF;AAEA,YAAI,cAAc,UAAU;AAC3B,gBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,gBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,gBAAM,OAAiB,CAAC;AAExB,iDAAU,cAAc,IAAI;AAE5B,gBAAM,SAAU,KAAK,iBAAiB,YAAY,CAAC,EACjD;AACF,cAAI,QAAQ;AACX,iBAAK,SAAS;AAAA,UACf;AACA,gBAAM,YAAa,KAAK,iBAAiB,gBAAgB,CAAC,EACxD;AACF,cAAI,WAAW;AACd,iBAAK,aAAa;AAAA,UACnB;AACA,gBAAM,WAAY,KAAK,iBAAiB,cAAc,CAAC,EACrD;AACF,cAAI,UAAU;AACb,iBAAK,YAAY;AAAA,UAClB;AACA,yBAAe,MAAM,8CAAsB;AAAA,YAC1C;AAAA,YACA;AAAA,YACA,aAAa,SAAS;AAAA,YACtB;AAAA,UACD;AAAA,QACD;AAEA,YAAI,cAAc,OAAO;AACxB,gBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,yBAAe,MAAM,8CAAsB;AAAA,YAC1C;AAAA,YACA;AAAA,YACA,aAAa,SAAS;AAAA,YACtB,CAAC;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAEA,YAAI,cAAc,UAAU;AAC3B,gBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,gBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,cAAI,QAAQ,OAAO;AAClB,eAAG,QAAQ,QAAQ;AAAA,UACpB;AACA,cAAI,QAAQ,QAAQ;AACnB,eAAG,SAAS,QAAQ;AAAA,UACrB;AACA,cAAI,QAAQ,UAAU;AACrB,eAAG,WAAW,QAAQ;AAAA,UACvB;AACA,cAAI,QAAQ,SAAS;AACpB,eAAG,UAAU,QAAQ;AAAA,UACtB;AACA,cAAI,QAAQ,UAAU;AACrB,eAAG,WAAW,QAAQ;AAAA,UACvB;AACA,cAAI,QAAQ,UAAU;AACrB,eAAG,YAAY,QAAQ;AAAA,UACxB;AACA,cAAI,QAAQ,UAAU;AACrB,eAAG,YAAY,QAAQ;AAAA,UACxB;AACA,cAAI,QAAQ,OAAO;AAClB,eAAG,QAAQ,QAAQ;AAAA,UACpB;AACA,cAAI,QAAQ,SAAS;AACpB,eAAG,UAAU,QAAQ;AAAA,UACtB;AACA,cAAI,QAAQ,QAAQ;AACnB,eAAG,SAAS,QAAQ;AAAA,UACrB;AACA,cAAI,QAAQ,KAAK;AAChB,eAAG,MAAM,QAAQ;AAAA,UAClB;AACA,cAAI,QAAQ,MAAM;AACjB,eAAG,OAAO,QAAQ;AAAA,UACnB;AACA,cAAI,QAAQ,QAAQ;AACnB,eAAG,SAAS,QAAQ;AAAA,UACrB;AACA,cAAI,QAAQ,aAAa;AACxB,eAAG,eAAe,QAAQ;AAAA,UAC3B;AACA,cAAI,QAAQ,KAAK;AAChB,eAAG,MAAM,QAAQ;AAAA,UAClB;AACA,cAAI,QAAQ,UAAU;AACrB,eAAG,YAAY,QAAQ;AAAA,UACxB;AACA,cAAI,QAAQ,MAAM;AACjB,eAAG,OAAO,QAAQ;AAAA,UACnB;AACA,cAAI,WAAW;AACd,2BAAe,MAAM,sDAA8B;AAAA,cAClD;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD,OAAO;AACN,eAAG,WAAW,KAAK,iBAAiB,SAAS,CAAC;AAC9C,2BAAe,MAAM,8CAAsB,KAAK,MAAM,OAAO,aAAa,CAAC,GAAG,EAAE;AAAA,UACjF;AAAA,QACD;AAEA,YAAI,cAAc,UAAU;AAC3B,gBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,yBAAe,MAAM,8CAAsB;AAAA,YAC1C;AAAA,YACA;AAAA,YACA,aAAa,SAAS;AAAA,YACtB,CAAC;AAAA,YACD,EAAE,OAAO,KAAK;AAAA,UACf;AAAA,QACD;AAAA,MACD;AACA,UAAI,aAAa,SAAS;AAEzB,YAAI,cAAc,UAAU;AAC3B,gBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,gBAAM,OAAe,CAAC;AAEtB,iDAAU,kBAAkB,IAAI;AAEhC,gBAAM,UAAW,KAAK,iBAAiB,aAAa,CAAC,EACnD;AACF,cAAI,YAAY,QAAW;AAC1B,iBAAK,UAAU;AACf,qDAAY,OAAsB;AAAA,UACnC;AACA,gBAAM,WAAY,KAAK,iBAAiB,cAAc,CAAC,EACrD;AACF,cAAI,aAAa,QAAW;AAC3B,iBAAK,WAAW;AAChB,qDAAY,QAAuB;AAAA,UACpC;AACA,gBAAM,cAAe,KAAK,iBAAiB,iBAAiB,CAAC,EAC3D;AACF,cAAI,aAAa;AAChB,iBAAK,eAAe;AACpB,qDAAY,WAAW;AACvB,qDAAY,WAAW;AAAA,UACxB;AACA,gBAAM,WAAY,KAAK,iBAAiB,cAAc,CAAC,EACrD;AACF,cAAI,UAAU;AACb,iBAAK,YAAY;AACjB,qDAAY,QAAQ;AACpB,qDAAY,QAAQ;AAAA,UACrB;AACA,gBAAM,YAAa,KAAK,iBAAiB,eAAe,CAAC,EACvD;AACF,cAAI,WAAW;AACd,iBAAK,aAAa;AAClB,qDAAY,SAAS;AACrB,qDAAY,SAAS;AAAA,UACtB;AACA,gBAAM,WAAY,KAAK,iBAAiB,cAAc,CAAC,EACrD;AACF,cAAI,UAAU;AACb,iBAAK,YAAY;AAAA,UAClB;AACA,gBAAM,gBAAiB,KAAK,iBAAiB,mBAAmB,CAAC,EAC/D;AACF,cAAI,eAAe;AAClB,iBAAK,iBAAiB;AACtB,qDAAY,aAAa;AACzB,qDAAY,aAAa;AAAA,UAC1B;AACA,yBAAe,MAAM,8CAAsB,KAAK,MAAM,QAAQ,WAAW,IAAI;AAAA,QAC9E;AAEA,YAAI,cAAc,UAAU;AAC3B,gBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,gBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,gBAAM,OAAe,CAAC;AAEtB,cAAI,aAAa,UAAU;AAC1B,iBAAK,WAAW,aAAa;AAAA,UAC9B;AACA,cAAI,aAAa,YAAY;AAC5B,iBAAK,cAAc,SAAS,aAAa,YAAsB,EAAE;AAAA,UAClE;AACA,cAAI,aAAa,cAAc;AAC9B,iBAAK,gBAAgB,aAAa;AAAA,UACnC;AACA,cAAI,aAAa,UAAU;AAC1B,iBAAK,YAAY,SAAS,aAAa,UAAoB,EAAE;AAAA,UAC9D;AACA,cAAI,aAAa,iBAAiB;AACjC,iBAAK,iBAAiB,aAAa;AAAA,UACpC;AACA,cAAI,aAAa,oBAAoB;AACpC,iBAAK,uBAAuB,aAAa;AAAA,UAC1C;AAEA,cAAI,aAAa,QAAQ;AACxB,iBAAK,SAAS,aAAa;AAAA,UAC5B;AACA,cAAI,aAAa,eAAe;AAC/B,iBAAK,iBAAiB,aAAa;AAAA,UACpC;AACA,gBAAM,UAAW,KAAK,iBAAiB,aAAa,CAAC,EACnD;AACF,cAAI,YAAY,QAAW;AAC1B,iBAAK,UAAU;AACf,qDAAY,OAAsB;AAAA,UACnC;AACA,gBAAM,WAAY,KAAK,iBAAiB,cAAc,CAAC,EACrD;AACF,cAAI,aAAa,QAAW;AAC3B,iBAAK,WAAW;AAChB,qDAAY,QAAuB;AAAA,UACpC;AACA,gBAAM,cAAe,KAAK,iBAAiB,iBAAiB,CAAC,EAC3D;AACF,cAAI,aAAa;AAChB,iBAAK,eAAe;AACpB,qDAAY,WAAW;AACvB,qDAAY,WAAW;AAAA,UACxB;AACA,gBAAM,WAAY,KAAK,iBAAiB,cAAc,CAAC,EACrD;AACF,cAAI,UAAU;AACb,iBAAK,YAAY;AACjB,qDAAY,QAAQ;AACpB,qDAAY,QAAQ;AAAA,UACrB;AACA,gBAAM,YAAa,KAAK,iBAAiB,eAAe,CAAC,EACvD;AACF,cAAI,WAAW;AACd,iBAAK,aAAa;AAClB,qDAAY,SAAS;AACrB,qDAAY,SAAS;AAAA,UACtB;AACA,gBAAM,WAAY,KAAK,iBAAiB,cAAc,CAAC,EACrD;AACF,cAAI,UAAU;AACb,iBAAK,YAAY;AAAA,UAClB;AACA,gBAAM,gBAAiB,KAAK,iBAAiB,mBAAmB,CAAC,EAC/D;AACF,cAAI,eAAe;AAClB,iBAAK,iBAAiB;AACtB,qDAAY,aAAa;AACzB,qDAAY,aAAa;AAAA,UAC1B;AAEA,yBAAe,MAAM,8CAAsB,KAAK,MAAM,OAAO,WAAW,OAAO,IAAI,IAAI;AAAA,QACxF;AAEA,YAAI,cAAc,OAAO;AACxB,gBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,yBAAe,MAAM,8CAAsB;AAAA,YAC1C;AAAA,YACA;AAAA,YACA,WAAW,OAAO;AAAA,YAClB,CAAC;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAEA,YAAI,cAAc,UAAU;AAC3B,gBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,gBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,cAAI,QAAQ,OAAO;AAClB,eAAG,QAAQ,QAAQ;AAAA,UACpB;AACA,cAAI,QAAQ,QAAQ;AACnB,eAAG,SAAS,QAAQ;AAAA,UACrB;AACA,cAAI,QAAQ,UAAU;AACrB,eAAG,WAAW,QAAQ;AAAA,UACvB;AACA,cAAI,QAAQ,UAAU;AACrB,eAAG,WAAW,SAAS,QAAQ,UAAoB,EAAE;AAAA,UACtD;AACA,cAAI,QAAQ,eAAe;AAC1B,eAAG,KAAK,QAAQ;AAAA,UACjB;AACA,cAAI,QAAQ,SAAS;AACpB,eAAG,UAAU,SAAS,QAAQ,SAAmB,EAAE;AAAA,UACpD;AACA,cAAI,QAAQ,OAAO;AAClB,eAAG,QAAQ,QAAQ;AAAA,UACpB;AACA,cAAI,QAAQ,SAAS;AACpB,eAAG,UAAU,QAAQ;AAAA,UACtB;AACA,cAAI,QAAQ,QAAQ;AACnB,eAAG,SAAS,QAAQ;AAAA,UACrB;AACA,cAAI,QAAQ,QAAQ;AACnB,eAAG,SAAS,QAAQ;AAAA,UACrB;AACA,cAAI,WAAW;AACd,2BAAe,MAAM,sDAA8B,KAAK,MAAM,OAAO,WAAW,CAAC,GAAG,EAAE;AAAA,UACvF,OAAO;AACN,eAAG,WAAW,KAAK,iBAAiB,SAAS,CAAC;AAC9C,2BAAe,MAAM,8CAAsB,KAAK,MAAM,OAAO,WAAW,CAAC,GAAG,EAAE;AAAA,UAC/E;AAAA,QACD;AAEA,YAAI,cAAc,UAAU;AAC3B,gBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,yBAAe,MAAM,8CAAsB;AAAA,YAC1C;AAAA,YACA;AAAA,YACA,WAAW,OAAO;AAAA,YAClB,CAAC;AAAA,YACD,EAAE,OAAO,KAAK;AAAA,UACf;AAAA,QACD;AAAA,MACD;AACA,YAAM,gBAAgB,KAAK,QAAQ;AAAA,QAClC,KAAK,QAAQ,gBAAgB,YAA6B;AAAA,QAC1D,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,MACzB;AACA,iBAAW,KAAK,GAAG,aAAa;AAAA,IACjC;AACA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": []}