{"version": 3, "sources": ["../../credentials/ZohoOAuth2Api.credentials.ts"], "sourcesContent": ["import type { ICredentialType, INodeProperties } from 'n8n-workflow';\n\nexport class ZohoOAuth2Api implements ICredentialType {\n\tname = 'zohoOAuth2Api';\n\n\textends = ['oAuth2Api'];\n\n\tdisplayName = 'Zoho OAuth2 API';\n\n\tdocumentationUrl = 'zoho';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Grant Type',\n\t\t\tname: 'grantType',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: 'authorizationCode',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Authorization URL',\n\t\t\tname: 'authUrl',\n\t\t\ttype: 'options',\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'https://accounts.zoho.com/oauth/v2/auth',\n\t\t\t\t\tvalue: 'https://accounts.zoho.com/oauth/v2/auth',\n\t\t\t\t\tdescription: 'For the EU, AU, and IN domains',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'https://accounts.zoho.com.cn/oauth/v2/auth',\n\t\t\t\t\tvalue: 'https://accounts.zoho.com.cn/oauth/v2/auth',\n\t\t\t\t\tdescription: 'For the CN domain',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'https://accounts.zoho.com/oauth/v2/auth',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Access Token URL',\n\t\t\tname: 'accessTokenUrl',\n\t\t\ttype: 'options',\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'AU - https://accounts.zoho.com.au/oauth/v2/token',\n\t\t\t\t\tvalue: 'https://accounts.zoho.com.au/oauth/v2/token',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'CN - https://accounts.zoho.com.cn/oauth/v2/token',\n\t\t\t\t\tvalue: 'https://accounts.zoho.com.cn/oauth/v2/token',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'EU - https://accounts.zoho.eu/oauth/v2/token',\n\t\t\t\t\tvalue: 'https://accounts.zoho.eu/oauth/v2/token',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'IN - https://accounts.zoho.in/oauth/v2/token',\n\t\t\t\t\tvalue: 'https://accounts.zoho.in/oauth/v2/token',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'US - https://accounts.zoho.com/oauth/v2/token',\n\t\t\t\t\tvalue: 'https://accounts.zoho.com/oauth/v2/token',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'https://accounts.zoho.com/oauth/v2/token',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Scope',\n\t\t\tname: 'scope',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: 'ZohoCRM.modules.ALL,ZohoCRM.settings.all,ZohoCRM.users.all',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Auth URI Query Parameters',\n\t\t\tname: 'authQueryParameters',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: 'access_type=offline',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Authentication',\n\t\t\tname: 'authentication',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: 'body',\n\t\t},\n\t];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,cAAyC;AAAA,EAA/C;AACN,gBAAO;AAEP,mBAAU,CAAC,WAAW;AAEtB,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,YACP,aAAa;AAAA,UACd;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,YACP,aAAa;AAAA,UACd;AAAA,QACD;AAAA,QACA,SAAS;AAAA,QACT,UAAU;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,SAAS;AAAA,QACT,UAAU;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,IACD;AAAA;AACD;", "names": []}