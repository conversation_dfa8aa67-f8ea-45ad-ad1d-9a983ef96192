{"version": 3, "sources": ["../../../nodes/Wufoo/WufooTrigger.node.ts"], "sourcesContent": ["import { randomBytes } from 'crypto';\nimport type {\n\tIHookFunctions,\n\tIWebhookFunctions,\n\tIDataObject,\n\tILoadOptionsFunctions,\n\tINodePropertyOptions,\n\tINodeType,\n\tINodeTypeDescription,\n\tIWebhookResponseData,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes, jsonParse } from 'n8n-workflow';\n\nimport { wufooApiRequest } from './GenericFunctions';\nimport type { IField, IWebhook } from './Interface';\n\nexport class WufooTrigger implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Wufoo Trigger',\n\t\tname: 'wufooTrigger',\n\t\t// eslint-disable-next-line n8n-nodes-base/node-class-description-icon-not-svg\n\t\ticon: 'file:wufoo.png',\n\t\tgroup: ['trigger'],\n\t\tversion: 1,\n\t\tdescription: 'Handle Wufoo events via webhooks',\n\t\tdefaults: {\n\t\t\tname: 'Wufoo Trigger',\n\t\t},\n\t\tinputs: [],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'wufoo<PERSON><PERSON>',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\twebhooks: [\n\t\t\t{\n\t\t\t\tname: 'default',\n\t\t\t\thttpMethod: 'POST',\n\t\t\t\tresponseMode: 'onReceived',\n\t\t\t\tpath: 'webhook',\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Forms Name or ID',\n\t\t\t\tname: 'form',\n\t\t\t\ttype: 'options',\n\t\t\t\trequired: true,\n\t\t\t\tdefault: '',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getForms',\n\t\t\t\t},\n\t\t\t\tdescription:\n\t\t\t\t\t'The form upon which will trigger this node when a new entry is made. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Only Answers',\n\t\t\t\tname: 'onlyAnswers',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: true,\n\t\t\t\tdescription: 'Whether to return only the answers of the form and not any of the other data',\n\t\t\t},\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\tasync getForms(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\n\t\t\t\t// https://wufoo.github.io/docs/#all-forms\n\t\t\t\tconst formObject = await wufooApiRequest.call(this, 'GET', 'forms.json');\n\t\t\t\tfor (const form of formObject.Forms) {\n\t\t\t\t\tconst name = form.Name;\n\t\t\t\t\tconst value = form.Hash;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname,\n\t\t\t\t\t\tvalue,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t// Entries submitted on the same day are present in separate property in data object\n\t\t\t\tif (formObject.EntryCountToday) {\n\t\t\t\t\tfor (const form of formObject.EntryCountToday) {\n\t\t\t\t\t\tconst name = form.Name;\n\t\t\t\t\t\tconst value = form.Hash;\n\t\t\t\t\t\treturnData.push({\n\t\t\t\t\t\t\tname,\n\t\t\t\t\t\t\tvalue,\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t},\n\t};\n\n\twebhookMethods = {\n\t\tdefault: {\n\t\t\t// No API endpoint to allow checking of existing webhooks.\n\t\t\t// Creating new webhook will not overwrite existing one if parameters are the same.\n\t\t\t// Otherwise an update occurs.\n\t\t\tasync checkExists(this: IHookFunctions): Promise<boolean> {\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tasync create(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\tconst formHash = this.getNodeParameter('form') as IDataObject;\n\t\t\t\tconst endpoint = `forms/${formHash}/webhooks.json`;\n\n\t\t\t\t// Handshake key for webhook endpoint protection\n\t\t\t\twebhookData.handshakeKey = randomBytes(20).toString('hex');\n\t\t\t\tconst body: IWebhook = {\n\t\t\t\t\turl: webhookUrl as string,\n\t\t\t\t\thandshakeKey: webhookData.handshakeKey,\n\t\t\t\t\tmetadata: true,\n\t\t\t\t};\n\n\t\t\t\tconst result = await wufooApiRequest.call(this, 'PUT', endpoint, body);\n\t\t\t\twebhookData.webhookId = result.WebHookPutResult.Hash;\n\n\t\t\t\treturn true;\n\t\t\t},\n\t\t\tasync delete(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\tconst formHash = this.getNodeParameter('form') as IDataObject;\n\t\t\t\tconst endpoint = `forms/${formHash}/webhooks/${webhookData.webhookId}.json`;\n\t\t\t\ttry {\n\t\t\t\t\tawait wufooApiRequest.call(this, 'DELETE', endpoint);\n\t\t\t\t} catch (error) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tdelete webhookData.webhookId;\n\t\t\t\tdelete webhookData.handshakeKey;\n\t\t\t\treturn true;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync webhook(this: IWebhookFunctions): Promise<IWebhookResponseData> {\n\t\tconst req = this.getRequestObject();\n\t\tconst body = this.getBodyData();\n\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\tconst onlyAnswers = this.getNodeParameter('onlyAnswers') as boolean;\n\t\tconst entries: IDataObject = {};\n\t\tlet returnObject: IDataObject = {};\n\n\t\tif (req.body.HandshakeKey !== webhookData.handshakeKey) {\n\t\t\treturn {};\n\t\t}\n\n\t\tconst fieldsObject = jsonParse<any>(req.body.FieldStructure as string, {\n\t\t\terrorMessage: \"Invalid JSON in request body field 'FieldStructure'\",\n\t\t});\n\n\t\tfieldsObject.Fields.map((field: IField) => {\n\t\t\t// TODO\n\t\t\t// Handle docusign field\n\n\t\t\tif (field.Type === 'file') {\n\t\t\t\tentries[field.Title] = req.body[`${field.ID}-url`];\n\t\t\t} else if (field.Type === 'address') {\n\t\t\t\tconst address: IDataObject = {};\n\n\t\t\t\tfor (const subfield of field.SubFields) {\n\t\t\t\t\taddress[subfield.Label] = body[subfield.ID];\n\t\t\t\t}\n\n\t\t\t\tentries[field.Title] = address;\n\t\t\t} else if (field.Type === 'checkbox') {\n\t\t\t\tconst responses: string[] = [];\n\n\t\t\t\tfor (const subfield of field.SubFields) {\n\t\t\t\t\tif (body[subfield.ID] !== '') {\n\t\t\t\t\t\tresponses.push(body[subfield.ID] as string);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tentries[field.Title] = responses;\n\t\t\t} else if (field.Type === 'likert') {\n\t\t\t\tconst likert: IDataObject = {};\n\n\t\t\t\tfor (const subfield of field.SubFields) {\n\t\t\t\t\tlikert[subfield.Label] = body[subfield.ID];\n\t\t\t\t}\n\n\t\t\t\tentries[field.Title] = likert;\n\t\t\t} else if (field.Type === 'shortname') {\n\t\t\t\tconst shortname: IDataObject = {};\n\n\t\t\t\tfor (const subfield of field.SubFields) {\n\t\t\t\t\tshortname[subfield.Label] = body[subfield.ID];\n\t\t\t\t}\n\n\t\t\t\tentries[field.Title] = shortname;\n\t\t\t} else {\n\t\t\t\tentries[field.Title] = req.body[field.ID];\n\t\t\t}\n\t\t});\n\n\t\tif (!onlyAnswers) {\n\t\t\treturnObject = {\n\t\t\t\tcreatedBy: req.body.CreatedBy as string,\n\t\t\t\tentryId: req.body.EntryId as number,\n\t\t\t\tdateCreated: req.body.DateCreated as Date,\n\t\t\t\tformId: req.body.FormId as string,\n\t\t\t\tformStructure: jsonParse(req.body.FormStructure as string, {\n\t\t\t\t\terrorMessage: \"Invalid JSON in request body field 'FormStructure'\",\n\t\t\t\t}),\n\t\t\t\tfieldStructure: jsonParse(req.body.FieldStructure as string, {\n\t\t\t\t\terrorMessage: \"Invalid JSON in request body field 'FieldStructure'\",\n\t\t\t\t}),\n\t\t\t\tentries,\n\t\t\t};\n\n\t\t\treturn {\n\t\t\t\tworkflowData: [this.helpers.returnJsonArray([returnObject as unknown as IDataObject])],\n\t\t\t};\n\t\t} else {\n\t\t\treturn {\n\t\t\t\tworkflowData: [this.helpers.returnJsonArray(entries as unknown as IDataObject)],\n\t\t\t};\n\t\t}\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA4B;AAW5B,0BAA+C;AAE/C,8BAAgC;AAGzB,MAAM,aAAkC;AAAA,EAAxC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA;AAAA,MAEN,MAAM;AAAA,MACN,OAAO,CAAC,SAAS;AAAA,MACjB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,UAAU;AAAA,QACT;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,MAAM;AAAA,QACP;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS;AAAA,UACT,aAAa;AAAA,YACZ,mBAAmB;AAAA,UACpB;AAAA,UACA,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,MACD;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA,QACZ,MAAM,WAAuE;AAC5E,gBAAM,aAAqC,CAAC;AAG5C,gBAAM,aAAa,MAAM,wCAAgB,KAAK,MAAM,OAAO,YAAY;AACvE,qBAAW,QAAQ,WAAW,OAAO;AACpC,kBAAM,OAAO,KAAK;AAClB,kBAAM,QAAQ,KAAK;AACnB,uBAAW,KAAK;AAAA,cACf;AAAA,cACA;AAAA,YACD,CAAC;AAAA,UACF;AAEA,cAAI,WAAW,iBAAiB;AAC/B,uBAAW,QAAQ,WAAW,iBAAiB;AAC9C,oBAAM,OAAO,KAAK;AAClB,oBAAM,QAAQ,KAAK;AACnB,yBAAW,KAAK;AAAA,gBACf;AAAA,gBACA;AAAA,cACD,CAAC;AAAA,YACF;AAAA,UACD;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAEA,0BAAiB;AAAA,MAChB,SAAS;AAAA;AAAA;AAAA;AAAA,QAIR,MAAM,cAAoD;AACzD,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AACnD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,gBAAM,WAAW,KAAK,iBAAiB,MAAM;AAC7C,gBAAM,WAAW,SAAS,QAAQ;AAGlC,sBAAY,mBAAe,2BAAY,EAAE,EAAE,SAAS,KAAK;AACzD,gBAAM,OAAiB;AAAA,YACtB,KAAK;AAAA,YACL,cAAc,YAAY;AAAA,YAC1B,UAAU;AAAA,UACX;AAEA,gBAAM,SAAS,MAAM,wCAAgB,KAAK,MAAM,OAAO,UAAU,IAAI;AACrE,sBAAY,YAAY,OAAO,iBAAiB;AAEhD,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,gBAAM,WAAW,KAAK,iBAAiB,MAAM;AAC7C,gBAAM,WAAW,SAAS,QAAQ,aAAa,YAAY,SAAS;AACpE,cAAI;AACH,kBAAM,wCAAgB,KAAK,MAAM,UAAU,QAAQ;AAAA,UACpD,SAAS,OAAO;AACf,mBAAO;AAAA,UACR;AACA,iBAAO,YAAY;AACnB,iBAAO,YAAY;AACnB,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAgE;AACrE,UAAM,MAAM,KAAK,iBAAiB;AAClC,UAAM,OAAO,KAAK,YAAY;AAC9B,UAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,UAAM,cAAc,KAAK,iBAAiB,aAAa;AACvD,UAAM,UAAuB,CAAC;AAC9B,QAAI,eAA4B,CAAC;AAEjC,QAAI,IAAI,KAAK,iBAAiB,YAAY,cAAc;AACvD,aAAO,CAAC;AAAA,IACT;AAEA,UAAM,mBAAe,+BAAe,IAAI,KAAK,gBAA0B;AAAA,MACtE,cAAc;AAAA,IACf,CAAC;AAED,iBAAa,OAAO,IAAI,CAAC,UAAkB;AAI1C,UAAI,MAAM,SAAS,QAAQ;AAC1B,gBAAQ,MAAM,KAAK,IAAI,IAAI,KAAK,GAAG,MAAM,EAAE,MAAM;AAAA,MAClD,WAAW,MAAM,SAAS,WAAW;AACpC,cAAM,UAAuB,CAAC;AAE9B,mBAAW,YAAY,MAAM,WAAW;AACvC,kBAAQ,SAAS,KAAK,IAAI,KAAK,SAAS,EAAE;AAAA,QAC3C;AAEA,gBAAQ,MAAM,KAAK,IAAI;AAAA,MACxB,WAAW,MAAM,SAAS,YAAY;AACrC,cAAM,YAAsB,CAAC;AAE7B,mBAAW,YAAY,MAAM,WAAW;AACvC,cAAI,KAAK,SAAS,EAAE,MAAM,IAAI;AAC7B,sBAAU,KAAK,KAAK,SAAS,EAAE,CAAW;AAAA,UAC3C;AAAA,QACD;AAEA,gBAAQ,MAAM,KAAK,IAAI;AAAA,MACxB,WAAW,MAAM,SAAS,UAAU;AACnC,cAAM,SAAsB,CAAC;AAE7B,mBAAW,YAAY,MAAM,WAAW;AACvC,iBAAO,SAAS,KAAK,IAAI,KAAK,SAAS,EAAE;AAAA,QAC1C;AAEA,gBAAQ,MAAM,KAAK,IAAI;AAAA,MACxB,WAAW,MAAM,SAAS,aAAa;AACtC,cAAM,YAAyB,CAAC;AAEhC,mBAAW,YAAY,MAAM,WAAW;AACvC,oBAAU,SAAS,KAAK,IAAI,KAAK,SAAS,EAAE;AAAA,QAC7C;AAEA,gBAAQ,MAAM,KAAK,IAAI;AAAA,MACxB,OAAO;AACN,gBAAQ,MAAM,KAAK,IAAI,IAAI,KAAK,MAAM,EAAE;AAAA,MACzC;AAAA,IACD,CAAC;AAED,QAAI,CAAC,aAAa;AACjB,qBAAe;AAAA,QACd,WAAW,IAAI,KAAK;AAAA,QACpB,SAAS,IAAI,KAAK;AAAA,QAClB,aAAa,IAAI,KAAK;AAAA,QACtB,QAAQ,IAAI,KAAK;AAAA,QACjB,mBAAe,+BAAU,IAAI,KAAK,eAAyB;AAAA,UAC1D,cAAc;AAAA,QACf,CAAC;AAAA,QACD,oBAAgB,+BAAU,IAAI,KAAK,gBAA0B;AAAA,UAC5D,cAAc;AAAA,QACf,CAAC;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,QACN,cAAc,CAAC,KAAK,QAAQ,gBAAgB,CAAC,YAAsC,CAAC,CAAC;AAAA,MACtF;AAAA,IACD,OAAO;AACN,aAAO;AAAA,QACN,cAAc,CAAC,KAAK,QAAQ,gBAAgB,OAAiC,CAAC;AAAA,MAC/E;AAAA,IACD;AAAA,EACD;AACD;", "names": []}