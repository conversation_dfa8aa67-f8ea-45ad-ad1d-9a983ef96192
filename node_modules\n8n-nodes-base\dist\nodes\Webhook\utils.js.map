{"version": 3, "sources": ["../../../nodes/Webhook/utils.ts"], "sourcesContent": ["import basicAuth from 'basic-auth';\nimport jwt from 'jsonwebtoken';\nimport { NodeOperationError } from 'n8n-workflow';\nimport type {\n\tIWebhookFunctions,\n\tINodeExecutionData,\n\tIDataObject,\n\tICredentialDataDecryptedObject,\n} from 'n8n-workflow';\n\nimport { WebhookAuthorizationError } from './error';\nimport { formatPrivateKey } from '../../utils/utilities';\n\nexport type WebhookParameters = {\n\thttpMethod: string | string[];\n\tresponseMode: string;\n\tresponseData: string;\n\tresponseCode?: number; //typeVersion <= 1.1\n\toptions?: {\n\t\tresponseData?: string;\n\t\tresponseCode?: {\n\t\t\tvalues?: {\n\t\t\t\tresponseCode: number;\n\t\t\t\tcustomCode?: number;\n\t\t\t};\n\t\t};\n\t\tnoResponseBody?: boolean;\n\t};\n};\n\nexport const getResponseCode = (parameters: WebhookParameters) => {\n\tif (parameters.responseCode) {\n\t\treturn parameters.responseCode;\n\t}\n\tconst responseCodeOptions = parameters.options;\n\tif (responseCodeOptions?.responseCode?.values) {\n\t\tconst { responseCode, customCode } = responseCodeOptions.responseCode.values;\n\n\t\tif (customCode) {\n\t\t\treturn customCode;\n\t\t}\n\n\t\treturn responseCode;\n\t}\n\treturn 200;\n};\n\nexport const getResponseData = (parameters: WebhookParameters) => {\n\tconst { responseData, responseMode, options } = parameters;\n\tif (responseData) return responseData;\n\n\tif (responseMode === 'onReceived') {\n\t\tconst data = options?.responseData;\n\t\tif (data) return data;\n\t}\n\n\tif (options?.noResponseBody) return 'noData';\n\n\treturn undefined;\n};\n\nexport const configuredOutputs = (parameters: WebhookParameters) => {\n\tconst httpMethod = parameters.httpMethod;\n\n\tif (!Array.isArray(httpMethod))\n\t\treturn [\n\t\t\t{\n\t\t\t\ttype: 'main',\n\t\t\t\tdisplayName: httpMethod,\n\t\t\t},\n\t\t];\n\n\tconst outputs = httpMethod.map((method) => {\n\t\treturn {\n\t\t\ttype: 'main',\n\t\t\tdisplayName: method,\n\t\t};\n\t});\n\n\treturn outputs;\n};\n\nexport const setupOutputConnection = (\n\tctx: IWebhookFunctions,\n\tmethod: string,\n\tadditionalData: {\n\t\tjwtPayload?: IDataObject;\n\t},\n) => {\n\tconst httpMethod = ctx.getNodeParameter('httpMethod', []) as string[] | string;\n\tlet webhookUrl = ctx.getNodeWebhookUrl('default') as string;\n\tconst executionMode = ctx.getMode() === 'manual' ? 'test' : 'production';\n\n\tif (executionMode === 'test') {\n\t\twebhookUrl = webhookUrl.replace('/webhook/', '/webhook-test/');\n\t}\n\n\t// multi methods could be set in settings of node, so we need to check if it's an array\n\tif (!Array.isArray(httpMethod)) {\n\t\treturn (outputData: INodeExecutionData): INodeExecutionData[][] => {\n\t\t\toutputData.json.webhookUrl = webhookUrl;\n\t\t\toutputData.json.executionMode = executionMode;\n\t\t\tif (additionalData?.jwtPayload) {\n\t\t\t\toutputData.json.jwtPayload = additionalData.jwtPayload;\n\t\t\t}\n\t\t\treturn [[outputData]];\n\t\t};\n\t}\n\n\tconst outputIndex = httpMethod.indexOf(method.toUpperCase());\n\tconst outputs: INodeExecutionData[][] = httpMethod.map(() => []);\n\n\treturn (outputData: INodeExecutionData): INodeExecutionData[][] => {\n\t\toutputData.json.webhookUrl = webhookUrl;\n\t\toutputData.json.executionMode = executionMode;\n\t\tif (additionalData?.jwtPayload) {\n\t\t\toutputData.json.jwtPayload = additionalData.jwtPayload;\n\t\t}\n\t\toutputs[outputIndex] = [outputData];\n\t\treturn outputs;\n\t};\n};\n\nexport const isIpWhitelisted = (\n\twhitelist: string | string[] | undefined,\n\tips: string[],\n\tip?: string,\n) => {\n\tif (whitelist === undefined || whitelist === '') {\n\t\treturn true;\n\t}\n\n\tif (!Array.isArray(whitelist)) {\n\t\twhitelist = whitelist.split(',').map((entry) => entry.trim());\n\t}\n\n\tfor (const address of whitelist) {\n\t\tif (ip && ip.includes(address)) {\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ips.some((entry) => entry.includes(address))) {\n\t\t\treturn true;\n\t\t}\n\t}\n\n\treturn false;\n};\n\nexport const checkResponseModeConfiguration = (context: IWebhookFunctions) => {\n\tconst responseMode = context.getNodeParameter('responseMode', 'onReceived') as string;\n\tconst connectedNodes = context.getChildNodes(context.getNode().name);\n\n\tconst isRespondToWebhookConnected = connectedNodes.some(\n\t\t(node) => node.type === 'n8n-nodes-base.respondToWebhook',\n\t);\n\n\tif (!isRespondToWebhookConnected && responseMode === 'responseNode') {\n\t\tthrow new NodeOperationError(\n\t\t\tcontext.getNode(),\n\t\t\tnew Error('No Respond to Webhook node found in the workflow'),\n\t\t\t{\n\t\t\t\tdescription:\n\t\t\t\t\t'Insert a Respond to Webhook node to your workflow to respond to the webhook or choose another option for the “Respond” parameter',\n\t\t\t},\n\t\t);\n\t}\n\n\tif (isRespondToWebhookConnected && responseMode !== 'responseNode') {\n\t\tthrow new NodeOperationError(\n\t\t\tcontext.getNode(),\n\t\t\tnew Error('Webhook node not correctly configured'),\n\t\t\t{\n\t\t\t\tdescription:\n\t\t\t\t\t'Set the “Respond” parameter to “Using Respond to Webhook Node” or remove the Respond to Webhook node',\n\t\t\t},\n\t\t);\n\t}\n};\n\nexport async function validateWebhookAuthentication(\n\tctx: IWebhookFunctions,\n\tauthPropertyName: string,\n) {\n\tconst authentication = ctx.getNodeParameter(authPropertyName) as string;\n\tif (authentication === 'none') return;\n\n\tconst req = ctx.getRequestObject();\n\tconst headers = ctx.getHeaderData();\n\n\tif (authentication === 'basicAuth') {\n\t\t// Basic authorization is needed to call webhook\n\t\tlet expectedAuth: ICredentialDataDecryptedObject | undefined;\n\t\ttry {\n\t\t\texpectedAuth = await ctx.getCredentials<ICredentialDataDecryptedObject>('httpBasicAuth');\n\t\t} catch {}\n\n\t\tif (expectedAuth === undefined || !expectedAuth.user || !expectedAuth.password) {\n\t\t\t// Data is not defined on node so can not authenticate\n\t\t\tthrow new WebhookAuthorizationError(500, 'No authentication data defined on node!');\n\t\t}\n\n\t\tconst providedAuth = basicAuth(req);\n\t\t// Authorization data is missing\n\t\tif (!providedAuth) throw new WebhookAuthorizationError(401);\n\n\t\tif (providedAuth.name !== expectedAuth.user || providedAuth.pass !== expectedAuth.password) {\n\t\t\t// Provided authentication data is wrong\n\t\t\tthrow new WebhookAuthorizationError(403);\n\t\t}\n\t} else if (authentication === 'bearerAuth') {\n\t\tlet expectedAuth: ICredentialDataDecryptedObject | undefined;\n\t\ttry {\n\t\t\texpectedAuth = await ctx.getCredentials<ICredentialDataDecryptedObject>('httpBearerAuth');\n\t\t} catch {}\n\n\t\tconst expectedToken = expectedAuth?.token as string;\n\t\tif (!expectedToken) {\n\t\t\tthrow new WebhookAuthorizationError(500, 'No authentication data defined on node!');\n\t\t}\n\n\t\tif (headers.authorization !== `Bearer ${expectedToken}`) {\n\t\t\tthrow new WebhookAuthorizationError(403);\n\t\t}\n\t} else if (authentication === 'headerAuth') {\n\t\t// Special header with value is needed to call webhook\n\t\tlet expectedAuth: ICredentialDataDecryptedObject | undefined;\n\t\ttry {\n\t\t\texpectedAuth = await ctx.getCredentials<ICredentialDataDecryptedObject>('httpHeaderAuth');\n\t\t} catch {}\n\n\t\tif (expectedAuth === undefined || !expectedAuth.name || !expectedAuth.value) {\n\t\t\t// Data is not defined on node so can not authenticate\n\t\t\tthrow new WebhookAuthorizationError(500, 'No authentication data defined on node!');\n\t\t}\n\t\tconst headerName = (expectedAuth.name as string).toLowerCase();\n\t\tconst expectedValue = expectedAuth.value as string;\n\n\t\tif (\n\t\t\t!headers.hasOwnProperty(headerName) ||\n\t\t\t(headers as IDataObject)[headerName] !== expectedValue\n\t\t) {\n\t\t\t// Provided authentication data is wrong\n\t\t\tthrow new WebhookAuthorizationError(403);\n\t\t}\n\t} else if (authentication === 'jwtAuth') {\n\t\tlet expectedAuth;\n\n\t\ttry {\n\t\t\texpectedAuth = await ctx.getCredentials<{\n\t\t\t\tkeyType: 'passphrase' | 'pemKey';\n\t\t\t\tpublicKey: string;\n\t\t\t\tsecret: string;\n\t\t\t\talgorithm: jwt.Algorithm;\n\t\t\t}>('jwtAuth');\n\t\t} catch {}\n\n\t\tif (expectedAuth === undefined) {\n\t\t\t// Data is not defined on node so can not authenticate\n\t\t\tthrow new WebhookAuthorizationError(500, 'No authentication data defined on node!');\n\t\t}\n\n\t\tconst authHeader = req.headers.authorization;\n\t\tconst token = authHeader?.split(' ')[1];\n\n\t\tif (!token) {\n\t\t\tthrow new WebhookAuthorizationError(401, 'No token provided');\n\t\t}\n\n\t\tlet secretOrPublicKey;\n\n\t\tif (expectedAuth.keyType === 'passphrase') {\n\t\t\tsecretOrPublicKey = expectedAuth.secret;\n\t\t} else {\n\t\t\tsecretOrPublicKey = formatPrivateKey(expectedAuth.publicKey, true);\n\t\t}\n\n\t\ttry {\n\t\t\treturn jwt.verify(token, secretOrPublicKey, {\n\t\t\t\talgorithms: [expectedAuth.algorithm],\n\t\t\t}) as IDataObject;\n\t\t} catch (error) {\n\t\t\tthrow new WebhookAuthorizationError(403, error.message);\n\t\t}\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAAsB;AACtB,0BAAgB;AAChB,0BAAmC;AAQnC,mBAA0C;AAC1C,uBAAiC;AAmB1B,MAAM,kBAAkB,CAAC,eAAkC;AACjE,MAAI,WAAW,cAAc;AAC5B,WAAO,WAAW;AAAA,EACnB;AACA,QAAM,sBAAsB,WAAW;AACvC,MAAI,qBAAqB,cAAc,QAAQ;AAC9C,UAAM,EAAE,cAAc,WAAW,IAAI,oBAAoB,aAAa;AAEtE,QAAI,YAAY;AACf,aAAO;AAAA,IACR;AAEA,WAAO;AAAA,EACR;AACA,SAAO;AACR;AAEO,MAAM,kBAAkB,CAAC,eAAkC;AACjE,QAAM,EAAE,cAAc,cAAc,QAAQ,IAAI;AAChD,MAAI,aAAc,QAAO;AAEzB,MAAI,iBAAiB,cAAc;AAClC,UAAM,OAAO,SAAS;AACtB,QAAI,KAAM,QAAO;AAAA,EAClB;AAEA,MAAI,SAAS,eAAgB,QAAO;AAEpC,SAAO;AACR;AAEO,MAAM,oBAAoB,CAAC,eAAkC;AACnE,QAAM,aAAa,WAAW;AAE9B,MAAI,CAAC,MAAM,QAAQ,UAAU;AAC5B,WAAO;AAAA,MACN;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,MACd;AAAA,IACD;AAED,QAAM,UAAU,WAAW,IAAI,CAAC,WAAW;AAC1C,WAAO;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,IACd;AAAA,EACD,CAAC;AAED,SAAO;AACR;AAEO,MAAM,wBAAwB,CACpC,KACA,QACA,mBAGI;AACJ,QAAM,aAAa,IAAI,iBAAiB,cAAc,CAAC,CAAC;AACxD,MAAI,aAAa,IAAI,kBAAkB,SAAS;AAChD,QAAM,gBAAgB,IAAI,QAAQ,MAAM,WAAW,SAAS;AAE5D,MAAI,kBAAkB,QAAQ;AAC7B,iBAAa,WAAW,QAAQ,aAAa,gBAAgB;AAAA,EAC9D;AAGA,MAAI,CAAC,MAAM,QAAQ,UAAU,GAAG;AAC/B,WAAO,CAAC,eAA2D;AAClE,iBAAW,KAAK,aAAa;AAC7B,iBAAW,KAAK,gBAAgB;AAChC,UAAI,gBAAgB,YAAY;AAC/B,mBAAW,KAAK,aAAa,eAAe;AAAA,MAC7C;AACA,aAAO,CAAC,CAAC,UAAU,CAAC;AAAA,IACrB;AAAA,EACD;AAEA,QAAM,cAAc,WAAW,QAAQ,OAAO,YAAY,CAAC;AAC3D,QAAM,UAAkC,WAAW,IAAI,MAAM,CAAC,CAAC;AAE/D,SAAO,CAAC,eAA2D;AAClE,eAAW,KAAK,aAAa;AAC7B,eAAW,KAAK,gBAAgB;AAChC,QAAI,gBAAgB,YAAY;AAC/B,iBAAW,KAAK,aAAa,eAAe;AAAA,IAC7C;AACA,YAAQ,WAAW,IAAI,CAAC,UAAU;AAClC,WAAO;AAAA,EACR;AACD;AAEO,MAAM,kBAAkB,CAC9B,WACA,KACA,OACI;AACJ,MAAI,cAAc,UAAa,cAAc,IAAI;AAChD,WAAO;AAAA,EACR;AAEA,MAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC9B,gBAAY,UAAU,MAAM,GAAG,EAAE,IAAI,CAAC,UAAU,MAAM,KAAK,CAAC;AAAA,EAC7D;AAEA,aAAW,WAAW,WAAW;AAChC,QAAI,MAAM,GAAG,SAAS,OAAO,GAAG;AAC/B,aAAO;AAAA,IACR;AAEA,QAAI,IAAI,KAAK,CAAC,UAAU,MAAM,SAAS,OAAO,CAAC,GAAG;AACjD,aAAO;AAAA,IACR;AAAA,EACD;AAEA,SAAO;AACR;AAEO,MAAM,iCAAiC,CAAC,YAA+B;AAC7E,QAAM,eAAe,QAAQ,iBAAiB,gBAAgB,YAAY;AAC1E,QAAM,iBAAiB,QAAQ,cAAc,QAAQ,QAAQ,EAAE,IAAI;AAEnE,QAAM,8BAA8B,eAAe;AAAA,IAClD,CAAC,SAAS,KAAK,SAAS;AAAA,EACzB;AAEA,MAAI,CAAC,+BAA+B,iBAAiB,gBAAgB;AACpE,UAAM,IAAI;AAAA,MACT,QAAQ,QAAQ;AAAA,MAChB,IAAI,MAAM,kDAAkD;AAAA,MAC5D;AAAA,QACC,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AAEA,MAAI,+BAA+B,iBAAiB,gBAAgB;AACnE,UAAM,IAAI;AAAA,MACT,QAAQ,QAAQ;AAAA,MAChB,IAAI,MAAM,uCAAuC;AAAA,MACjD;AAAA,QACC,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AACD;AAEA,eAAsB,8BACrB,KACA,kBACC;AACD,QAAM,iBAAiB,IAAI,iBAAiB,gBAAgB;AAC5D,MAAI,mBAAmB,OAAQ;AAE/B,QAAM,MAAM,IAAI,iBAAiB;AACjC,QAAM,UAAU,IAAI,cAAc;AAElC,MAAI,mBAAmB,aAAa;AAEnC,QAAI;AACJ,QAAI;AACH,qBAAe,MAAM,IAAI,eAA+C,eAAe;AAAA,IACxF,QAAQ;AAAA,IAAC;AAET,QAAI,iBAAiB,UAAa,CAAC,aAAa,QAAQ,CAAC,aAAa,UAAU;AAE/E,YAAM,IAAI,uCAA0B,KAAK,yCAAyC;AAAA,IACnF;AAEA,UAAM,mBAAe,kBAAAA,SAAU,GAAG;AAElC,QAAI,CAAC,aAAc,OAAM,IAAI,uCAA0B,GAAG;AAE1D,QAAI,aAAa,SAAS,aAAa,QAAQ,aAAa,SAAS,aAAa,UAAU;AAE3F,YAAM,IAAI,uCAA0B,GAAG;AAAA,IACxC;AAAA,EACD,WAAW,mBAAmB,cAAc;AAC3C,QAAI;AACJ,QAAI;AACH,qBAAe,MAAM,IAAI,eAA+C,gBAAgB;AAAA,IACzF,QAAQ;AAAA,IAAC;AAET,UAAM,gBAAgB,cAAc;AACpC,QAAI,CAAC,eAAe;AACnB,YAAM,IAAI,uCAA0B,KAAK,yCAAyC;AAAA,IACnF;AAEA,QAAI,QAAQ,kBAAkB,UAAU,aAAa,IAAI;AACxD,YAAM,IAAI,uCAA0B,GAAG;AAAA,IACxC;AAAA,EACD,WAAW,mBAAmB,cAAc;AAE3C,QAAI;AACJ,QAAI;AACH,qBAAe,MAAM,IAAI,eAA+C,gBAAgB;AAAA,IACzF,QAAQ;AAAA,IAAC;AAET,QAAI,iBAAiB,UAAa,CAAC,aAAa,QAAQ,CAAC,aAAa,OAAO;AAE5E,YAAM,IAAI,uCAA0B,KAAK,yCAAyC;AAAA,IACnF;AACA,UAAM,aAAc,aAAa,KAAgB,YAAY;AAC7D,UAAM,gBAAgB,aAAa;AAEnC,QACC,CAAC,QAAQ,eAAe,UAAU,KACjC,QAAwB,UAAU,MAAM,eACxC;AAED,YAAM,IAAI,uCAA0B,GAAG;AAAA,IACxC;AAAA,EACD,WAAW,mBAAmB,WAAW;AACxC,QAAI;AAEJ,QAAI;AACH,qBAAe,MAAM,IAAI,eAKtB,SAAS;AAAA,IACb,QAAQ;AAAA,IAAC;AAET,QAAI,iBAAiB,QAAW;AAE/B,YAAM,IAAI,uCAA0B,KAAK,yCAAyC;AAAA,IACnF;AAEA,UAAM,aAAa,IAAI,QAAQ;AAC/B,UAAM,QAAQ,YAAY,MAAM,GAAG,EAAE,CAAC;AAEtC,QAAI,CAAC,OAAO;AACX,YAAM,IAAI,uCAA0B,KAAK,mBAAmB;AAAA,IAC7D;AAEA,QAAI;AAEJ,QAAI,aAAa,YAAY,cAAc;AAC1C,0BAAoB,aAAa;AAAA,IAClC,OAAO;AACN,8BAAoB,mCAAiB,aAAa,WAAW,IAAI;AAAA,IAClE;AAEA,QAAI;AACH,aAAO,oBAAAC,QAAI,OAAO,OAAO,mBAAmB;AAAA,QAC3C,YAAY,CAAC,aAAa,SAAS;AAAA,MACpC,CAAC;AAAA,IACF,SAAS,OAAO;AACf,YAAM,IAAI,uCAA0B,KAAK,MAAM,OAAO;AAAA,IACvD;AAAA,EACD;AACD;", "names": ["basicAuth", "jwt"]}