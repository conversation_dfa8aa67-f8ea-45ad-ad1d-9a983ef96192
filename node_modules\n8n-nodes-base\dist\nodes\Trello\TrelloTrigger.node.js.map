{"version": 3, "sources": ["../../../nodes/Trello/TrelloTrigger.node.ts"], "sourcesContent": ["import {\n\ttype IHookFunctions,\n\ttype IWebhookFunctions,\n\ttype INodeType,\n\ttype INodeTypeDescription,\n\ttype IWebhookResponseData,\n\tNodeConnectionTypes,\n} from 'n8n-workflow';\n\nimport { apiRequest } from './GenericFunctions';\n\n// import { createHmac } from 'crypto';\n\nexport class TrelloTrigger implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Trello Trigger',\n\t\tname: 'trelloTrigger',\n\t\ticon: 'file:trello.svg',\n\t\tgroup: ['trigger'],\n\t\tversion: 1,\n\t\tdescription: 'Starts the workflow when Trello events occur',\n\t\tdefaults: {\n\t\t\tname: 'Trell<PERSON> Trigger',\n\t\t},\n\t\tinputs: [],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'trello<PERSON><PERSON>',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\twebhooks: [\n\t\t\t{\n\t\t\t\tname: 'setup',\n\t\t\t\thttpMethod: 'HEAD',\n\t\t\t\tresponseMode: 'onReceived',\n\t\t\t\tpath: 'webhook',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'default',\n\t\t\t\thttpMethod: 'POST',\n\t\t\t\tresponseMode: 'onReceived',\n\t\t\t\tpath: 'webhook',\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Model ID',\n\t\t\t\tname: 'id',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: '4d5ea62fd76aa1136000000c',\n\t\t\t\trequired: true,\n\t\t\t\tdescription: 'ID of the model of which to subscribe to events',\n\t\t\t},\n\t\t],\n\t};\n\n\twebhookMethods = {\n\t\tdefault: {\n\t\t\tasync checkExists(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst credentials = await this.getCredentials('trelloApi');\n\n\t\t\t\t// Check all the webhooks which exist already if it is identical to the\n\t\t\t\t// one that is supposed to get created.\n\t\t\t\tconst endpoint = `tokens/${credentials.apiToken}/webhooks`;\n\n\t\t\t\tconst responseData = await apiRequest.call(this, 'GET', endpoint, {});\n\n\t\t\t\tconst idModel = this.getNodeParameter('id') as string;\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\n\t\t\t\tfor (const webhook of responseData) {\n\t\t\t\t\tif (webhook.idModel === idModel && webhook.callbackURL === webhookUrl) {\n\t\t\t\t\t\t// Set webhook-id to be sure that it can be deleted\n\t\t\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\t\t\twebhookData.webhookId = webhook.id as string;\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tasync create(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\n\t\t\t\tconst credentials = await this.getCredentials('trelloApi');\n\n\t\t\t\tconst idModel = this.getNodeParameter('id') as string;\n\n\t\t\t\tconst endpoint = `tokens/${credentials.apiToken}/webhooks`;\n\n\t\t\t\tconst body = {\n\t\t\t\t\tdescription: `n8n Webhook - ${idModel}`,\n\t\t\t\t\tcallbackURL: webhookUrl,\n\t\t\t\t\tidModel,\n\t\t\t\t};\n\n\t\t\t\tconst responseData = await apiRequest.call(this, 'POST', endpoint, body);\n\n\t\t\t\tif (responseData.id === undefined) {\n\t\t\t\t\t// Required data is missing so was not successful\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\twebhookData.webhookId = responseData.id as string;\n\n\t\t\t\treturn true;\n\t\t\t},\n\t\t\tasync delete(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\n\t\t\t\tif (webhookData.webhookId !== undefined) {\n\t\t\t\t\tconst credentials = await this.getCredentials('trelloApi');\n\n\t\t\t\t\tconst endpoint = `tokens/${credentials.apiToken}/webhooks/${webhookData.webhookId}`;\n\n\t\t\t\t\tconst body = {};\n\n\t\t\t\t\ttry {\n\t\t\t\t\t\tawait apiRequest.call(this, 'DELETE', endpoint, body);\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\n\t\t\t\t\t// Remove from the static workflow data so that it is clear\n\t\t\t\t\t// that no webhooks are registered anymore\n\t\t\t\t\tdelete webhookData.webhookId;\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync webhook(this: IWebhookFunctions): Promise<IWebhookResponseData> {\n\t\tconst webhookName = this.getWebhookName();\n\n\t\tif (webhookName === 'setup') {\n\t\t\t// Is a create webhook confirmation request\n\t\t\tconst res = this.getResponseObject();\n\t\t\tres.status(200).end();\n\t\t\treturn {\n\t\t\t\tnoWebhookResponse: true,\n\t\t\t};\n\t\t}\n\n\t\tconst bodyData = this.getBodyData();\n\n\t\t// TODO: Check why that does not work as expected even though it gets done as described\n\t\t//    https://developers.trello.com/page/webhooks\n\n\t\t//const credentials = await this.getCredentials('trelloApi');\n\t\t// // Check if the request is valid\n\t\t// const headerData = this.getHeaderData() as IDataObject;\n\t\t// const webhookUrl = this.getNodeWebhookUrl('default');\n\t\t// const checkContent = JSON.stringify(bodyData) + webhookUrl;\n\t\t// const computedSignature = createHmac('sha1', credentials.oauthSecret as string).update(checkContent).digest('base64');\n\t\t// if (headerData['x-trello-webhook'] !== computedSignature) {\n\t\t// \t// Signature is not valid so ignore call\n\t\t// \treturn {};\n\t\t// }\n\n\t\treturn {\n\t\t\tworkflowData: [this.helpers.returnJsonArray(bodyData)],\n\t\t};\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAOO;AAEP,8BAA2B;AAIpB,MAAM,cAAmC;AAAA,EAAzC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,SAAS;AAAA,MACjB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,UAAU;AAAA,QACT;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,MAAM;AAAA,QACP;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,MAAM;AAAA,QACP;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,UACb,UAAU;AAAA,UACV,aAAa;AAAA,QACd;AAAA,MACD;AAAA,IACD;AAEA,0BAAiB;AAAA,MAChB,SAAS;AAAA,QACR,MAAM,cAAoD;AACzD,gBAAM,cAAc,MAAM,KAAK,eAAe,WAAW;AAIzD,gBAAM,WAAW,UAAU,YAAY,QAAQ;AAE/C,gBAAM,eAAe,MAAM,mCAAW,KAAK,MAAM,OAAO,UAAU,CAAC,CAAC;AAEpE,gBAAM,UAAU,KAAK,iBAAiB,IAAI;AAC1C,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AAEnD,qBAAW,WAAW,cAAc;AACnC,gBAAI,QAAQ,YAAY,WAAW,QAAQ,gBAAgB,YAAY;AAEtE,oBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,0BAAY,YAAY,QAAQ;AAChC,qBAAO;AAAA,YACR;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AAEnD,gBAAM,cAAc,MAAM,KAAK,eAAe,WAAW;AAEzD,gBAAM,UAAU,KAAK,iBAAiB,IAAI;AAE1C,gBAAM,WAAW,UAAU,YAAY,QAAQ;AAE/C,gBAAM,OAAO;AAAA,YACZ,aAAa,iBAAiB,OAAO;AAAA,YACrC,aAAa;AAAA,YACb;AAAA,UACD;AAEA,gBAAM,eAAe,MAAM,mCAAW,KAAK,MAAM,QAAQ,UAAU,IAAI;AAEvE,cAAI,aAAa,OAAO,QAAW;AAElC,mBAAO;AAAA,UACR;AAEA,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,sBAAY,YAAY,aAAa;AAErC,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AAErD,cAAI,YAAY,cAAc,QAAW;AACxC,kBAAM,cAAc,MAAM,KAAK,eAAe,WAAW;AAEzD,kBAAM,WAAW,UAAU,YAAY,QAAQ,aAAa,YAAY,SAAS;AAEjF,kBAAM,OAAO,CAAC;AAEd,gBAAI;AACH,oBAAM,mCAAW,KAAK,MAAM,UAAU,UAAU,IAAI;AAAA,YACrD,SAAS,OAAO;AACf,qBAAO;AAAA,YACR;AAIA,mBAAO,YAAY;AAAA,UACpB;AAEA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAgE;AACrE,UAAM,cAAc,KAAK,eAAe;AAExC,QAAI,gBAAgB,SAAS;AAE5B,YAAM,MAAM,KAAK,kBAAkB;AACnC,UAAI,OAAO,GAAG,EAAE,IAAI;AACpB,aAAO;AAAA,QACN,mBAAmB;AAAA,MACpB;AAAA,IACD;AAEA,UAAM,WAAW,KAAK,YAAY;AAgBlC,WAAO;AAAA,MACN,cAAc,CAAC,KAAK,QAAQ,gBAAgB,QAAQ,CAAC;AAAA,IACtD;AAAA,EACD;AACD;", "names": []}