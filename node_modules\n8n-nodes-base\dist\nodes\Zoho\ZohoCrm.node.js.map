{"version": 3, "sources": ["../../../nodes/Zoho/ZohoCrm.node.ts"], "sourcesContent": ["import {\n\ttype IExecuteFunctions,\n\ttype IDataObject,\n\ttype ILoadOptionsFunctions,\n\ttype INodeExecutionData,\n\ttype INodeType,\n\ttype INodeTypeDescription,\n\tNodeConnectionTypes,\n} from 'n8n-workflow';\n\nimport {\n\taccountFields,\n\taccountOperations,\n\tcontactFields,\n\tcontactOperations,\n\tdealFields,\n\tdealOperations,\n\tinvoiceFields,\n\tinvoiceOperations,\n\tleadFields,\n\tleadOperations,\n\tproductFields,\n\tproductOperations,\n\tpurchaseOrderFields,\n\tpurchaseOrderOperations,\n\tquoteFields,\n\tquoteOperations,\n\tsalesOrderFields,\n\tsalesOrderOperations,\n\tvendorFields,\n\tvendorOperations,\n} from './descriptions';\nimport {\n\taddGetAllFilterOptions,\n\tadjustAccountPayload,\n\tadjustContactPayload,\n\tadjustDealPayload,\n\tadjustInvoicePayload,\n\tadjustInvoicePayloadOnUpdate,\n\tadjustLeadPayload,\n\tadjustProductDetails,\n\tadjustProductPayload,\n\tadjustPurchaseOrderPayload,\n\tadjustQuotePayload,\n\tadjustSalesOrderPayload,\n\tadjustVendorPayload,\n\tgetFields,\n\tgetPicklistOptions,\n\thandleListing,\n\tthrowOnEmptyUpdate,\n\tthrowOnMissingProducts,\n\ttoLoadOptions,\n\tzohoApiRequest,\n\tzohoApiRequestAllItems,\n} from './GenericFunctions';\nimport type {\n\tCamelCaseResource,\n\tGetAllFilterOptions,\n\tLoadedAccounts,\n\tLoadedContacts,\n\tLoadedDeals,\n\tLoadedProducts,\n\tLoadedVendors,\n\tProductDetails,\n} from './types';\n\nexport class ZohoCrm implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Zoho CRM',\n\t\tname: 'zohoCrm',\n\t\ticon: 'file:zoho.svg',\n\t\tgroup: ['transform'],\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tversion: 1,\n\t\tdescription: 'Consume Zoho CRM API',\n\t\tdefaults: {\n\t\t\tname: 'Zoho CRM',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'zohoOAuth2Api',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Account',\n\t\t\t\t\t\tvalue: 'account',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Contact',\n\t\t\t\t\t\tvalue: 'contact',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Deal',\n\t\t\t\t\t\tvalue: 'deal',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Invoice',\n\t\t\t\t\t\tvalue: 'invoice',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Lead',\n\t\t\t\t\t\tvalue: 'lead',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Product',\n\t\t\t\t\t\tvalue: 'product',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Purchase Order',\n\t\t\t\t\t\tvalue: 'purchaseOrder',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Quote',\n\t\t\t\t\t\tvalue: 'quote',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Sales Order',\n\t\t\t\t\t\tvalue: 'salesOrder',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Vendor',\n\t\t\t\t\t\tvalue: 'vendor',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'account',\n\t\t\t},\n\t\t\t...accountOperations,\n\t\t\t...accountFields,\n\t\t\t...contactOperations,\n\t\t\t...contactFields,\n\t\t\t...dealOperations,\n\t\t\t...dealFields,\n\t\t\t...invoiceOperations,\n\t\t\t...invoiceFields,\n\t\t\t...leadOperations,\n\t\t\t...leadFields,\n\t\t\t...productOperations,\n\t\t\t...productFields,\n\t\t\t...purchaseOrderOperations,\n\t\t\t...purchaseOrderFields,\n\t\t\t...quoteOperations,\n\t\t\t...quoteFields,\n\t\t\t...salesOrderOperations,\n\t\t\t...salesOrderFields,\n\t\t\t...vendorOperations,\n\t\t\t...vendorFields,\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\t// ----------------------------------------\n\t\t\t//               resources\n\t\t\t// ----------------------------------------\n\n\t\t\tasync getAccounts(this: ILoadOptionsFunctions) {\n\t\t\t\tconst accounts = (await zohoApiRequestAllItems.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/accounts',\n\t\t\t\t)) as LoadedAccounts;\n\t\t\t\treturn toLoadOptions(accounts, 'Account_Name');\n\t\t\t},\n\n\t\t\tasync getContacts(this: ILoadOptionsFunctions) {\n\t\t\t\tconst contacts = (await zohoApiRequestAllItems.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/contacts',\n\t\t\t\t)) as LoadedContacts;\n\t\t\t\treturn toLoadOptions(contacts, 'Full_Name');\n\t\t\t},\n\n\t\t\tasync getDeals(this: ILoadOptionsFunctions) {\n\t\t\t\tconst deals = (await zohoApiRequestAllItems.call(this, 'GET', '/deals')) as LoadedDeals;\n\t\t\t\treturn toLoadOptions(deals, 'Deal_Name');\n\t\t\t},\n\n\t\t\tasync getProducts(this: ILoadOptionsFunctions) {\n\t\t\t\tconst products = (await zohoApiRequestAllItems.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/products',\n\t\t\t\t)) as LoadedProducts;\n\t\t\t\treturn toLoadOptions(products, 'Product_Name');\n\t\t\t},\n\n\t\t\tasync getVendors(this: ILoadOptionsFunctions) {\n\t\t\t\tconst vendors = (await zohoApiRequestAllItems.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/vendors',\n\t\t\t\t)) as LoadedVendors;\n\t\t\t\treturn toLoadOptions(vendors, 'Vendor_Name');\n\t\t\t},\n\n\t\t\t// ----------------------------------------\n\t\t\t//             resource fields\n\t\t\t// ----------------------------------------\n\n\t\t\t// standard fields - called from `makeGetAllFields`\n\n\t\t\tasync getAccountFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'account');\n\t\t\t},\n\n\t\t\tasync getContactFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'contact');\n\t\t\t},\n\n\t\t\tasync getDealFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'deal');\n\t\t\t},\n\n\t\t\tasync getInvoiceFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'invoice');\n\t\t\t},\n\n\t\t\tasync getLeadFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'lead');\n\t\t\t},\n\n\t\t\tasync getProductFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'product');\n\t\t\t},\n\n\t\t\tasync getPurchaseOrderFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'purchase_order');\n\t\t\t},\n\n\t\t\tasync getVendorOrderFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'vendor');\n\t\t\t},\n\n\t\t\tasync getQuoteFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'quote');\n\t\t\t},\n\n\t\t\tasync getSalesOrderFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'sales_order');\n\t\t\t},\n\n\t\t\tasync getVendorFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'vendor');\n\t\t\t},\n\n\t\t\t// custom fields\n\n\t\t\tasync getCustomAccountFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'account', { onlyCustom: true });\n\t\t\t},\n\n\t\t\tasync getCustomContactFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'contact', { onlyCustom: true });\n\t\t\t},\n\n\t\t\tasync getCustomDealFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'deal', { onlyCustom: true });\n\t\t\t},\n\n\t\t\tasync getCustomInvoiceFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'invoice', { onlyCustom: true });\n\t\t\t},\n\n\t\t\tasync getCustomLeadFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'lead', { onlyCustom: true });\n\t\t\t},\n\n\t\t\tasync getCustomProductFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'product', { onlyCustom: true });\n\t\t\t},\n\n\t\t\tasync getCustomPurchaseOrderFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'purchase_order', { onlyCustom: true });\n\t\t\t},\n\n\t\t\tasync getCustomVendorOrderFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'vendor', { onlyCustom: true });\n\t\t\t},\n\n\t\t\tasync getCustomQuoteFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'quote', { onlyCustom: true });\n\t\t\t},\n\n\t\t\tasync getCustomSalesOrderFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'sales_order', { onlyCustom: true });\n\t\t\t},\n\n\t\t\tasync getCustomVendorFields(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getFields.call(this, 'vendor', { onlyCustom: true });\n\t\t\t},\n\n\t\t\t// ----------------------------------------\n\t\t\t//        resource picklist options\n\t\t\t// ----------------------------------------\n\n\t\t\tasync getAccountType(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getPicklistOptions.call(this, 'account', 'Account_Type');\n\t\t\t},\n\n\t\t\tasync getDealStage(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getPicklistOptions.call(this, 'deal', 'Stage');\n\t\t\t},\n\n\t\t\tasync getPurchaseOrderStatus(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getPicklistOptions.call(this, 'purchaseOrder', 'Status');\n\t\t\t},\n\n\t\t\tasync getSalesOrderStatus(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getPicklistOptions.call(this, 'salesOrder', 'Status');\n\t\t\t},\n\n\t\t\tasync getQuoteStage(this: ILoadOptionsFunctions) {\n\t\t\t\treturn await getPicklistOptions.call(this, 'quote', 'Quote_Stage');\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: INodeExecutionData[] = [];\n\n\t\tconst resource = this.getNodeParameter('resource', 0) as CamelCaseResource;\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\n\t\tlet responseData;\n\n\t\tfor (let i = 0; i < items.length; i++) {\n\t\t\t// https://www.zoho.com/crm/developer/docs/api/insert-records.html\n\t\t\t// https://www.zoho.com/crm/developer/docs/api/get-records.html\n\t\t\t// https://www.zoho.com/crm/developer/docs/api/update-specific-record.html\n\t\t\t// https://www.zoho.com/crm/developer/docs/api/delete-specific-record.html\n\t\t\t// https://www.zoho.com/crm/developer/docs/api/v2/upsert-records.html\n\n\t\t\ttry {\n\t\t\t\tif (resource === 'account') {\n\t\t\t\t\t// **********************************************************************\n\t\t\t\t\t//                                account\n\t\t\t\t\t// **********************************************************************\n\n\t\t\t\t\t// https://www.zoho.com/crm/developer/docs/api/v2/accounts-response.html\n\t\t\t\t\t// https://help.zoho.com/portal/en/kb/crm/customize-crm-account/customizing-fields/articles/standard-modules-fields#Accounts\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             account: create\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tAccount_Name: this.getNodeParameter('accountName', i),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustAccountPayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/accounts', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             account: delete\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst accountId = this.getNodeParameter('accountId', i);\n\n\t\t\t\t\t\tconst endpoint = `/accounts/${accountId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'DELETE', endpoint);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//               account: get\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst accountId = this.getNodeParameter('accountId', i);\n\n\t\t\t\t\t\tconst endpoint = `/accounts/${accountId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'GET', endpoint);\n\t\t\t\t\t\tresponseData = responseData.data;\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             account: getAll\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst qs: IDataObject = {};\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i) as GetAllFilterOptions;\n\n\t\t\t\t\t\taddGetAllFilterOptions(qs, options);\n\n\t\t\t\t\t\tresponseData = await handleListing.call(this, 'GET', '/accounts', {}, qs);\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             account: update\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {};\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\tif (Object.keys(updateFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustAccountPayload(updateFields));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrowOnEmptyUpdate.call(this, resource);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst accountId = this.getNodeParameter('accountId', i);\n\n\t\t\t\t\t\tconst endpoint = `/accounts/${accountId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'PUT', endpoint, body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'upsert') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             account: upsert\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tAccount_Name: this.getNodeParameter('accountName', i),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustAccountPayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/accounts/upsert', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'contact') {\n\t\t\t\t\t// **********************************************************************\n\t\t\t\t\t//                                contact\n\t\t\t\t\t// **********************************************************************\n\n\t\t\t\t\t// https://www.zoho.com/crm/developer/docs/api/v2/contacts-response.html\n\t\t\t\t\t// https://help.zoho.com/portal/en/kb/crm/customize-crm-account/customizing-fields/articles/standard-modules-fields#Contacts\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             contact: create\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tLast_Name: this.getNodeParameter('lastName', i),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustContactPayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/contacts', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             contact: delete\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst contactId = this.getNodeParameter('contactId', i);\n\n\t\t\t\t\t\tconst endpoint = `/contacts/${contactId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'DELETE', endpoint);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//               contact: get\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst contactId = this.getNodeParameter('contactId', i);\n\n\t\t\t\t\t\tconst endpoint = `/contacts/${contactId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'GET', endpoint);\n\t\t\t\t\t\tresponseData = responseData.data;\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             contact: getAll\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst qs: IDataObject = {};\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i) as GetAllFilterOptions;\n\n\t\t\t\t\t\taddGetAllFilterOptions(qs, options);\n\n\t\t\t\t\t\tresponseData = await handleListing.call(this, 'GET', '/contacts', {}, qs);\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             contact: update\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {};\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\tif (Object.keys(updateFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustContactPayload(updateFields));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrowOnEmptyUpdate.call(this, resource);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst contactId = this.getNodeParameter('contactId', i);\n\n\t\t\t\t\t\tconst endpoint = `/contacts/${contactId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'PUT', endpoint, body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'upsert') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             contact: upsert\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tLast_Name: this.getNodeParameter('lastName', i),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustContactPayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/contacts/upsert', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'deal') {\n\t\t\t\t\t// **********************************************************************\n\t\t\t\t\t//                                deal\n\t\t\t\t\t// **********************************************************************\n\n\t\t\t\t\t// https://www.zoho.com/crm/developer/docs/api/v2/deals-response.html\n\t\t\t\t\t// https://help.zoho.com/portal/en/kb/crm/customize-crm-account/customizing-fields/articles/standard-modules-fields#Deals\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//               deal: create\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tDeal_Name: this.getNodeParameter('dealName', i),\n\t\t\t\t\t\t\tStage: this.getNodeParameter('stage', i),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustDealPayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/deals', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//               deal: delete\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst dealId = this.getNodeParameter('dealId', i);\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'DELETE', `/deals/${dealId}`);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//                deal: get\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst dealId = this.getNodeParameter('dealId', i);\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'GET', `/deals/${dealId}`);\n\t\t\t\t\t\tresponseData = responseData.data;\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//               deal: getAll\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst qs: IDataObject = {};\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i) as GetAllFilterOptions;\n\n\t\t\t\t\t\taddGetAllFilterOptions(qs, options);\n\n\t\t\t\t\t\tresponseData = await handleListing.call(this, 'GET', '/deals', {}, qs);\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//               deal: update\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {};\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\tif (Object.keys(updateFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustDealPayload(updateFields));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrowOnEmptyUpdate.call(this, resource);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst dealId = this.getNodeParameter('dealId', i);\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'PUT', `/deals/${dealId}`, body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'upsert') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//              deal: upsert\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tDeal_Name: this.getNodeParameter('dealName', i),\n\t\t\t\t\t\t\tStage: this.getNodeParameter('stage', i),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustDealPayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/deals/upsert', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'invoice') {\n\t\t\t\t\t// **********************************************************************\n\t\t\t\t\t//                                invoice\n\t\t\t\t\t// **********************************************************************\n\n\t\t\t\t\t// https://www.zoho.com/crm/developer/docs/api/v2/invoices-response.html\n\t\t\t\t\t// https://help.zoho.com/portal/en/kb/crm/customize-crm-account/customizing-fields/articles/standard-modules-fields#Invoices\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             invoice: create\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst productDetails = this.getNodeParameter('Product_Details', i) as ProductDetails;\n\n\t\t\t\t\t\tthrowOnMissingProducts.call(this, resource, productDetails);\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tSubject: this.getNodeParameter('subject', i),\n\t\t\t\t\t\t\tProduct_Details: adjustProductDetails(productDetails),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustInvoicePayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/invoices', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             invoice: delete\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst invoiceId = this.getNodeParameter('invoiceId', i);\n\n\t\t\t\t\t\tconst endpoint = `/invoices/${invoiceId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'DELETE', endpoint);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//               invoice: get\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst invoiceId = this.getNodeParameter('invoiceId', i);\n\n\t\t\t\t\t\tconst endpoint = `/invoices/${invoiceId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'GET', endpoint);\n\t\t\t\t\t\tresponseData = responseData.data;\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             invoice: getAll\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst qs: IDataObject = {};\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i) as GetAllFilterOptions;\n\n\t\t\t\t\t\taddGetAllFilterOptions(qs, options);\n\n\t\t\t\t\t\tresponseData = await handleListing.call(this, 'GET', '/invoices', {}, qs);\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             invoice: update\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {};\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\tif (Object.keys(updateFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustInvoicePayloadOnUpdate(updateFields));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrowOnEmptyUpdate.call(this, resource);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst invoiceId = this.getNodeParameter('invoiceId', i);\n\n\t\t\t\t\t\tconst endpoint = `/invoices/${invoiceId}`;\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'PUT', endpoint, body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'upsert') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             invoice: upsert\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst productDetails = this.getNodeParameter('Product_Details', i) as ProductDetails;\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tSubject: this.getNodeParameter('subject', i),\n\t\t\t\t\t\t\tProduct_Details: adjustProductDetails(productDetails),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustInvoicePayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/invoices/upsert', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'lead') {\n\t\t\t\t\t// **********************************************************************\n\t\t\t\t\t//                                  lead\n\t\t\t\t\t// **********************************************************************\n\n\t\t\t\t\t// https://www.zoho.com/crm/developer/docs/api/v2/leads-response.html\n\t\t\t\t\t// https://help.zoho.com/portal/en/kb/crm/customize-crm-account/customizing-fields/articles/standard-modules-fields#Leads\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//               lead: create\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tCompany: this.getNodeParameter('Company', i),\n\t\t\t\t\t\t\tLast_Name: this.getNodeParameter('lastName', i),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustLeadPayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/leads', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//               lead: delete\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst leadId = this.getNodeParameter('leadId', i);\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'DELETE', `/leads/${leadId}`);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//                lead: get\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst leadId = this.getNodeParameter('leadId', i);\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'GET', `/leads/${leadId}`);\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//               lead: getAll\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst qs: IDataObject = {};\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i) as GetAllFilterOptions;\n\n\t\t\t\t\t\taddGetAllFilterOptions(qs, options);\n\n\t\t\t\t\t\tresponseData = await handleListing.call(this, 'GET', '/leads', {}, qs);\n\t\t\t\t\t} else if (operation === 'getFields') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//            lead: getFields\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t'/settings/fields',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t{ module: 'leads' },\n\t\t\t\t\t\t);\n\t\t\t\t\t\tresponseData = responseData.fields;\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//               lead: update\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {};\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\tif (Object.keys(updateFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustLeadPayload(updateFields));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrowOnEmptyUpdate.call(this, resource);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst leadId = this.getNodeParameter('leadId', i);\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'PUT', `/leads/${leadId}`, body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'upsert') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//              lead: upsert\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tCompany: this.getNodeParameter('Company', i),\n\t\t\t\t\t\t\tLast_Name: this.getNodeParameter('lastName', i),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustLeadPayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/leads/upsert', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'product') {\n\t\t\t\t\t// **********************************************************************\n\t\t\t\t\t//                              product\n\t\t\t\t\t// **********************************************************************\n\n\t\t\t\t\t// https://www.zoho.com/crm/developer/docs/api/v2/products-response.html\n\t\t\t\t\t// https://help.zoho.com/portal/en/kb/crm/customize-crm-account/customizing-fields/articles/standard-modules-fields#Products\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             product: create\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tProduct_Name: this.getNodeParameter('productName', i),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustProductPayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/products', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//            product: delete\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst productId = this.getNodeParameter('productId', i);\n\n\t\t\t\t\t\tconst endpoint = `/products/${productId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'DELETE', endpoint);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//              product: get\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst productId = this.getNodeParameter('productId', i);\n\n\t\t\t\t\t\tconst endpoint = `/products/${productId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'GET', endpoint);\n\t\t\t\t\t\tresponseData = responseData.data;\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//            product: getAll\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst qs: IDataObject = {};\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i) as GetAllFilterOptions;\n\n\t\t\t\t\t\taddGetAllFilterOptions(qs, options);\n\n\t\t\t\t\t\tresponseData = await handleListing.call(this, 'GET', '/products', {}, qs);\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//            product: update\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {};\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\tif (Object.keys(updateFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustProductPayload(updateFields));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrowOnEmptyUpdate.call(this, resource);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst productId = this.getNodeParameter('productId', i);\n\n\t\t\t\t\t\tconst endpoint = `/products/${productId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'PUT', endpoint, body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'upsert') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             product: upsert\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tProduct_Name: this.getNodeParameter('productName', i),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustProductPayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/products/upsert', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'purchaseOrder') {\n\t\t\t\t\t// **********************************************************************\n\t\t\t\t\t//                             purchaseOrder\n\t\t\t\t\t// **********************************************************************\n\n\t\t\t\t\t// https://www.zoho.com/crm/developer/docs/api/v2/purchase-orders-response.html\n\t\t\t\t\t// https://help.zoho.com/portal/en/kb/crm/customize-crm-account/customizing-fields/articles/standard-modules-fields#Purchase_Order\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//          purchaseOrder: create\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst productDetails = this.getNodeParameter('Product_Details', i) as ProductDetails;\n\n\t\t\t\t\t\tthrowOnMissingProducts.call(this, resource, productDetails);\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tSubject: this.getNodeParameter('subject', i),\n\t\t\t\t\t\t\tVendor_Name: { id: this.getNodeParameter('vendorId', i) },\n\t\t\t\t\t\t\tProduct_Details: adjustProductDetails(productDetails),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustPurchaseOrderPayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/purchase_orders', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//          purchaseOrder: delete\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst purchaseOrderId = this.getNodeParameter('purchaseOrderId', i);\n\n\t\t\t\t\t\tconst endpoint = `/purchase_orders/${purchaseOrderId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'DELETE', endpoint);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//            purchaseOrder: get\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst purchaseOrderId = this.getNodeParameter('purchaseOrderId', i);\n\n\t\t\t\t\t\tconst endpoint = `/purchase_orders/${purchaseOrderId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'GET', endpoint);\n\t\t\t\t\t\tresponseData = responseData.data;\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//          purchaseOrder: getAll\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst qs: IDataObject = {};\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i) as GetAllFilterOptions;\n\n\t\t\t\t\t\taddGetAllFilterOptions(qs, options);\n\n\t\t\t\t\t\tresponseData = await handleListing.call(this, 'GET', '/purchase_orders', {}, qs);\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//          purchaseOrder: update\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {};\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\tif (Object.keys(updateFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustPurchaseOrderPayload(updateFields));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrowOnEmptyUpdate.call(this, resource);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst purchaseOrderId = this.getNodeParameter('purchaseOrderId', i);\n\n\t\t\t\t\t\tconst endpoint = `/purchase_orders/${purchaseOrderId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'PUT', endpoint, body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'upsert') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//          purchaseOrder: upsert\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst productDetails = this.getNodeParameter('Product_Details', i) as ProductDetails;\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tSubject: this.getNodeParameter('subject', i),\n\t\t\t\t\t\t\tVendor_Name: { id: this.getNodeParameter('vendorId', i) },\n\t\t\t\t\t\t\tProduct_Details: adjustProductDetails(productDetails),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustPurchaseOrderPayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/purchase_orders/upsert', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'quote') {\n\t\t\t\t\t// **********************************************************************\n\t\t\t\t\t//                                 quote\n\t\t\t\t\t// **********************************************************************\n\n\t\t\t\t\t// https://www.zoho.com/crm/developer/docs/api/v2/quotes-response.html\n\t\t\t\t\t// https://help.zoho.com/portal/en/kb/crm/customize-crm-account/customizing-fields/articles/standard-modules-fields#Quotes\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//              quote: create\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst productDetails = this.getNodeParameter('Product_Details', i) as ProductDetails;\n\n\t\t\t\t\t\tthrowOnMissingProducts.call(this, resource, productDetails);\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tSubject: this.getNodeParameter('subject', i),\n\t\t\t\t\t\t\tProduct_Details: adjustProductDetails(productDetails),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustQuotePayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/quotes', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//              quote: delete\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst quoteId = this.getNodeParameter('quoteId', i);\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'DELETE', `/quotes/${quoteId}`);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//                quote: get\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst quoteId = this.getNodeParameter('quoteId', i);\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'GET', `/quotes/${quoteId}`);\n\t\t\t\t\t\tresponseData = responseData.data;\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//              quote: getAll\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst qs: IDataObject = {};\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i) as GetAllFilterOptions;\n\n\t\t\t\t\t\taddGetAllFilterOptions(qs, options);\n\n\t\t\t\t\t\tresponseData = await handleListing.call(this, 'GET', '/quotes', {}, qs);\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//              quote: update\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {};\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\tif (Object.keys(updateFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustQuotePayload(updateFields));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrowOnEmptyUpdate.call(this, resource);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst quoteId = this.getNodeParameter('quoteId', i);\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'PUT', `/quotes/${quoteId}`, body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'upsert') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//              quote: upsert\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst productDetails = this.getNodeParameter('Product_Details', i) as ProductDetails;\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tSubject: this.getNodeParameter('subject', i),\n\t\t\t\t\t\t\tProduct_Details: adjustProductDetails(productDetails),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustQuotePayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/quotes/upsert', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'salesOrder') {\n\t\t\t\t\t// **********************************************************************\n\t\t\t\t\t//                               salesOrder\n\t\t\t\t\t// **********************************************************************\n\n\t\t\t\t\t// https://www.zoho.com/crm/developer/docs/api/v2/sales-orders-response.html\n\t\t\t\t\t// https://help.zoho.com/portal/en/kb/crm/customize-crm-account/customizing-fields/articles/standard-modules-fields#Sales_Orders\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//            salesOrder: create\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst productDetails = this.getNodeParameter('Product_Details', i) as ProductDetails;\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tAccount_Name: { id: this.getNodeParameter('accountId', i) },\n\t\t\t\t\t\t\tSubject: this.getNodeParameter('subject', i),\n\t\t\t\t\t\t\tProduct_Details: adjustProductDetails(productDetails),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustSalesOrderPayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/sales_orders', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//            salesOrder: delete\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst salesOrderId = this.getNodeParameter('salesOrderId', i);\n\n\t\t\t\t\t\tconst endpoint = `/sales_orders/${salesOrderId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'DELETE', endpoint);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             salesOrder: get\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst salesOrderId = this.getNodeParameter('salesOrderId', i);\n\n\t\t\t\t\t\tconst endpoint = `/sales_orders/${salesOrderId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'GET', endpoint);\n\t\t\t\t\t\tresponseData = responseData.data;\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//            salesOrder: getAll\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst qs: IDataObject = {};\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i) as GetAllFilterOptions;\n\n\t\t\t\t\t\taddGetAllFilterOptions(qs, options);\n\n\t\t\t\t\t\tresponseData = await handleListing.call(this, 'GET', '/sales_orders', {}, qs);\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//            salesOrder: update\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {};\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\tif (Object.keys(updateFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustSalesOrderPayload(updateFields));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrowOnEmptyUpdate.call(this, resource);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst salesOrderId = this.getNodeParameter('salesOrderId', i);\n\n\t\t\t\t\t\tconst endpoint = `/sales_orders/${salesOrderId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'PUT', endpoint, body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'upsert') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//           salesOrder: upsert\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst productDetails = this.getNodeParameter('Product_Details', i) as ProductDetails;\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tAccount_Name: { id: this.getNodeParameter('accountId', i) },\n\t\t\t\t\t\t\tSubject: this.getNodeParameter('subject', i),\n\t\t\t\t\t\t\tProduct_Details: adjustProductDetails(productDetails, 'upsert'),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustSalesOrderPayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/sales_orders/upsert', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'vendor') {\n\t\t\t\t\t// **********************************************************************\n\t\t\t\t\t//                               vendor\n\t\t\t\t\t// **********************************************************************\n\n\t\t\t\t\t// https://www.zoho.com/crm/developer/docs/api/v2/vendors-response.html\n\t\t\t\t\t// https://help.zoho.com/portal/en/kb/crm/customize-crm-account/customizing-fields/articles/standard-modules-fields#Vendors\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//            vendor: create\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tVendor_Name: this.getNodeParameter('vendorName', i),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustVendorPayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/vendors', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//            vendor: delete\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst vendorId = this.getNodeParameter('vendorId', i);\n\n\t\t\t\t\t\tconst endpoint = `/vendors/${vendorId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'DELETE', endpoint);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             vendor: get\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst vendorId = this.getNodeParameter('vendorId', i);\n\n\t\t\t\t\t\tconst endpoint = `/vendors/${vendorId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'GET', endpoint);\n\t\t\t\t\t\tresponseData = responseData.data;\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//            vendor: getAll\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst qs: IDataObject = {};\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i) as GetAllFilterOptions;\n\n\t\t\t\t\t\taddGetAllFilterOptions(qs, options);\n\n\t\t\t\t\t\tresponseData = await handleListing.call(this, 'GET', '/vendors', {}, qs);\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//            vendor: update\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {};\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\tif (Object.keys(updateFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustVendorPayload(updateFields));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrowOnEmptyUpdate.call(this, resource);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst vendorId = this.getNodeParameter('vendorId', i);\n\n\t\t\t\t\t\tconst endpoint = `/vendors/${vendorId}`;\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'PUT', endpoint, body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t} else if (operation === 'upsert') {\n\t\t\t\t\t\t// ----------------------------------------\n\t\t\t\t\t\t//             vendor: upsert\n\t\t\t\t\t\t// ----------------------------------------\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tVendor_Name: this.getNodeParameter('vendorName', i),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (Object.keys(additionalFields).length) {\n\t\t\t\t\t\t\tObject.assign(body, adjustVendorPayload(additionalFields));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zohoApiRequest.call(this, 'POST', '/vendors/upsert', body);\n\t\t\t\t\t\tresponseData = responseData.data[0].details;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ error: error.message, json: {} });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\tthis.helpers.returnJsonArray(responseData as IDataObject),\n\t\t\t\t{ itemData: { item: i } },\n\t\t\t);\n\t\t\treturnData.push(...executionData);\n\t\t}\n\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAQO;AAEP,0BAqBO;AACP,8BAsBO;AAYA,MAAM,QAA6B;AAAA,EAAnC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,WAAW;AAAA,MACnB,UAAU;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA;AAAA;AAAA;AAAA,QAKZ,MAAM,cAAyC;AAC9C,gBAAM,WAAY,MAAM,+CAAuB;AAAA,YAC9C;AAAA,YACA;AAAA,YACA;AAAA,UACD;AACA,qBAAO,uCAAc,UAAU,cAAc;AAAA,QAC9C;AAAA,QAEA,MAAM,cAAyC;AAC9C,gBAAM,WAAY,MAAM,+CAAuB;AAAA,YAC9C;AAAA,YACA;AAAA,YACA;AAAA,UACD;AACA,qBAAO,uCAAc,UAAU,WAAW;AAAA,QAC3C;AAAA,QAEA,MAAM,WAAsC;AAC3C,gBAAM,QAAS,MAAM,+CAAuB,KAAK,MAAM,OAAO,QAAQ;AACtE,qBAAO,uCAAc,OAAO,WAAW;AAAA,QACxC;AAAA,QAEA,MAAM,cAAyC;AAC9C,gBAAM,WAAY,MAAM,+CAAuB;AAAA,YAC9C;AAAA,YACA;AAAA,YACA;AAAA,UACD;AACA,qBAAO,uCAAc,UAAU,cAAc;AAAA,QAC9C;AAAA,QAEA,MAAM,aAAwC;AAC7C,gBAAM,UAAW,MAAM,+CAAuB;AAAA,YAC7C;AAAA,YACA;AAAA,YACA;AAAA,UACD;AACA,qBAAO,uCAAc,SAAS,aAAa;AAAA,QAC5C;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,MAAM,mBAA8C;AACnD,iBAAO,MAAM,kCAAU,KAAK,MAAM,SAAS;AAAA,QAC5C;AAAA,QAEA,MAAM,mBAA8C;AACnD,iBAAO,MAAM,kCAAU,KAAK,MAAM,SAAS;AAAA,QAC5C;AAAA,QAEA,MAAM,gBAA2C;AAChD,iBAAO,MAAM,kCAAU,KAAK,MAAM,MAAM;AAAA,QACzC;AAAA,QAEA,MAAM,mBAA8C;AACnD,iBAAO,MAAM,kCAAU,KAAK,MAAM,SAAS;AAAA,QAC5C;AAAA,QAEA,MAAM,gBAA2C;AAChD,iBAAO,MAAM,kCAAU,KAAK,MAAM,MAAM;AAAA,QACzC;AAAA,QAEA,MAAM,mBAA8C;AACnD,iBAAO,MAAM,kCAAU,KAAK,MAAM,SAAS;AAAA,QAC5C;AAAA,QAEA,MAAM,yBAAoD;AACzD,iBAAO,MAAM,kCAAU,KAAK,MAAM,gBAAgB;AAAA,QACnD;AAAA,QAEA,MAAM,uBAAkD;AACvD,iBAAO,MAAM,kCAAU,KAAK,MAAM,QAAQ;AAAA,QAC3C;AAAA,QAEA,MAAM,iBAA4C;AACjD,iBAAO,MAAM,kCAAU,KAAK,MAAM,OAAO;AAAA,QAC1C;AAAA,QAEA,MAAM,sBAAiD;AACtD,iBAAO,MAAM,kCAAU,KAAK,MAAM,aAAa;AAAA,QAChD;AAAA,QAEA,MAAM,kBAA6C;AAClD,iBAAO,MAAM,kCAAU,KAAK,MAAM,QAAQ;AAAA,QAC3C;AAAA;AAAA,QAIA,MAAM,yBAAoD;AACzD,iBAAO,MAAM,kCAAU,KAAK,MAAM,WAAW,EAAE,YAAY,KAAK,CAAC;AAAA,QAClE;AAAA,QAEA,MAAM,yBAAoD;AACzD,iBAAO,MAAM,kCAAU,KAAK,MAAM,WAAW,EAAE,YAAY,KAAK,CAAC;AAAA,QAClE;AAAA,QAEA,MAAM,sBAAiD;AACtD,iBAAO,MAAM,kCAAU,KAAK,MAAM,QAAQ,EAAE,YAAY,KAAK,CAAC;AAAA,QAC/D;AAAA,QAEA,MAAM,yBAAoD;AACzD,iBAAO,MAAM,kCAAU,KAAK,MAAM,WAAW,EAAE,YAAY,KAAK,CAAC;AAAA,QAClE;AAAA,QAEA,MAAM,sBAAiD;AACtD,iBAAO,MAAM,kCAAU,KAAK,MAAM,QAAQ,EAAE,YAAY,KAAK,CAAC;AAAA,QAC/D;AAAA,QAEA,MAAM,yBAAoD;AACzD,iBAAO,MAAM,kCAAU,KAAK,MAAM,WAAW,EAAE,YAAY,KAAK,CAAC;AAAA,QAClE;AAAA,QAEA,MAAM,+BAA0D;AAC/D,iBAAO,MAAM,kCAAU,KAAK,MAAM,kBAAkB,EAAE,YAAY,KAAK,CAAC;AAAA,QACzE;AAAA,QAEA,MAAM,6BAAwD;AAC7D,iBAAO,MAAM,kCAAU,KAAK,MAAM,UAAU,EAAE,YAAY,KAAK,CAAC;AAAA,QACjE;AAAA,QAEA,MAAM,uBAAkD;AACvD,iBAAO,MAAM,kCAAU,KAAK,MAAM,SAAS,EAAE,YAAY,KAAK,CAAC;AAAA,QAChE;AAAA,QAEA,MAAM,4BAAuD;AAC5D,iBAAO,MAAM,kCAAU,KAAK,MAAM,eAAe,EAAE,YAAY,KAAK,CAAC;AAAA,QACtE;AAAA,QAEA,MAAM,wBAAmD;AACxD,iBAAO,MAAM,kCAAU,KAAK,MAAM,UAAU,EAAE,YAAY,KAAK,CAAC;AAAA,QACjE;AAAA;AAAA;AAAA;AAAA,QAMA,MAAM,iBAA4C;AACjD,iBAAO,MAAM,2CAAmB,KAAK,MAAM,WAAW,cAAc;AAAA,QACrE;AAAA,QAEA,MAAM,eAA0C;AAC/C,iBAAO,MAAM,2CAAmB,KAAK,MAAM,QAAQ,OAAO;AAAA,QAC3D;AAAA,QAEA,MAAM,yBAAoD;AACzD,iBAAO,MAAM,2CAAmB,KAAK,MAAM,iBAAiB,QAAQ;AAAA,QACrE;AAAA,QAEA,MAAM,sBAAiD;AACtD,iBAAO,MAAM,2CAAmB,KAAK,MAAM,cAAc,QAAQ;AAAA,QAClE;AAAA,QAEA,MAAM,gBAA2C;AAChD,iBAAO,MAAM,2CAAmB,KAAK,MAAM,SAAS,aAAa;AAAA,QAClE;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAAmC,CAAC;AAE1C,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,QAAI;AAEJ,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAOtC,UAAI;AACH,YAAI,aAAa,WAAW;AAQ3B,cAAI,cAAc,UAAU;AAK3B,kBAAM,OAAoB;AAAA,cACzB,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAAA,YACrD;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,8CAAqB,gBAAgB,CAAC;AAAA,YAC3D;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,aAAa,IAAI;AACxE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,WAAW,aAAa,SAAS;AACvC,2BAAe,MAAM,uCAAe,KAAK,MAAM,UAAU,QAAQ;AACjE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,OAAO;AAK/B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,WAAW,aAAa,SAAS;AACvC,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,QAAQ;AAC9D,2BAAe,aAAa;AAAA,UAC7B,WAAW,cAAc,UAAU;AAKlC,kBAAM,KAAkB,CAAC;AACzB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gEAAuB,IAAI,OAAO;AAElC,2BAAe,MAAM,sCAAc,KAAK,MAAM,OAAO,aAAa,CAAC,GAAG,EAAE;AAAA,UACzE,WAAW,cAAc,UAAU;AAKlC,kBAAM,OAAoB,CAAC;AAC3B,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,gBAAI,OAAO,KAAK,YAAY,EAAE,QAAQ;AACrC,qBAAO,OAAO,UAAM,8CAAqB,YAAY,CAAC;AAAA,YACvD,OAAO;AACN,yDAAmB,KAAK,MAAM,QAAQ;AAAA,YACvC;AAEA,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,WAAW,aAAa,SAAS;AACvC,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,UAAU,IAAI;AACpE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,OAAoB;AAAA,cACzB,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAAA,YACrD;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,8CAAqB,gBAAgB,CAAC;AAAA,YAC3D;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,oBAAoB,IAAI;AAC/E,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC;AAAA,QACD,WAAW,aAAa,WAAW;AAQlC,cAAI,cAAc,UAAU;AAK3B,kBAAM,OAAoB;AAAA,cACzB,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAAA,YAC/C;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,8CAAqB,gBAAgB,CAAC;AAAA,YAC3D;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,aAAa,IAAI;AACxE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,WAAW,aAAa,SAAS;AACvC,2BAAe,MAAM,uCAAe,KAAK,MAAM,UAAU,QAAQ;AACjE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,OAAO;AAK/B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,WAAW,aAAa,SAAS;AACvC,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,QAAQ;AAC9D,2BAAe,aAAa;AAAA,UAC7B,WAAW,cAAc,UAAU;AAKlC,kBAAM,KAAkB,CAAC;AACzB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gEAAuB,IAAI,OAAO;AAElC,2BAAe,MAAM,sCAAc,KAAK,MAAM,OAAO,aAAa,CAAC,GAAG,EAAE;AAAA,UACzE,WAAW,cAAc,UAAU;AAKlC,kBAAM,OAAoB,CAAC;AAC3B,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,gBAAI,OAAO,KAAK,YAAY,EAAE,QAAQ;AACrC,qBAAO,OAAO,UAAM,8CAAqB,YAAY,CAAC;AAAA,YACvD,OAAO;AACN,yDAAmB,KAAK,MAAM,QAAQ;AAAA,YACvC;AAEA,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,WAAW,aAAa,SAAS;AACvC,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,UAAU,IAAI;AACpE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,OAAoB;AAAA,cACzB,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAAA,YAC/C;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,8CAAqB,gBAAgB,CAAC;AAAA,YAC3D;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,oBAAoB,IAAI;AAC/E,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC;AAAA,QACD,WAAW,aAAa,QAAQ;AAQ/B,cAAI,cAAc,UAAU;AAK3B,kBAAM,OAAoB;AAAA,cACzB,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAAA,cAC9C,OAAO,KAAK,iBAAiB,SAAS,CAAC;AAAA,YACxC;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,2CAAkB,gBAAgB,CAAC;AAAA,YACxD;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,UAAU,IAAI;AACrE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,2BAAe,MAAM,uCAAe,KAAK,MAAM,UAAU,UAAU,MAAM,EAAE;AAC3E,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,OAAO;AAK/B,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,UAAU,MAAM,EAAE;AACxE,2BAAe,aAAa;AAAA,UAC7B,WAAW,cAAc,UAAU;AAKlC,kBAAM,KAAkB,CAAC;AACzB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gEAAuB,IAAI,OAAO;AAElC,2BAAe,MAAM,sCAAc,KAAK,MAAM,OAAO,UAAU,CAAC,GAAG,EAAE;AAAA,UACtE,WAAW,cAAc,UAAU;AAKlC,kBAAM,OAAoB,CAAC;AAC3B,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,gBAAI,OAAO,KAAK,YAAY,EAAE,QAAQ;AACrC,qBAAO,OAAO,UAAM,2CAAkB,YAAY,CAAC;AAAA,YACpD,OAAO;AACN,yDAAmB,KAAK,MAAM,QAAQ;AAAA,YACvC;AAEA,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,UAAU,MAAM,IAAI,IAAI;AAC9E,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,OAAoB;AAAA,cACzB,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAAA,cAC9C,OAAO,KAAK,iBAAiB,SAAS,CAAC;AAAA,YACxC;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,2CAAkB,gBAAgB,CAAC;AAAA,YACxD;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,iBAAiB,IAAI;AAC5E,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC;AAAA,QACD,WAAW,aAAa,WAAW;AAQlC,cAAI,cAAc,UAAU;AAK3B,kBAAM,iBAAiB,KAAK,iBAAiB,mBAAmB,CAAC;AAEjE,2DAAuB,KAAK,MAAM,UAAU,cAAc;AAE1D,kBAAM,OAAoB;AAAA,cACzB,SAAS,KAAK,iBAAiB,WAAW,CAAC;AAAA,cAC3C,qBAAiB,8CAAqB,cAAc;AAAA,YACrD;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,8CAAqB,gBAAgB,CAAC;AAAA,YAC3D;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,aAAa,IAAI;AACxE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,WAAW,aAAa,SAAS;AACvC,2BAAe,MAAM,uCAAe,KAAK,MAAM,UAAU,QAAQ;AACjE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,OAAO;AAK/B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,WAAW,aAAa,SAAS;AACvC,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,QAAQ;AAC9D,2BAAe,aAAa;AAAA,UAC7B,WAAW,cAAc,UAAU;AAKlC,kBAAM,KAAkB,CAAC;AACzB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gEAAuB,IAAI,OAAO;AAElC,2BAAe,MAAM,sCAAc,KAAK,MAAM,OAAO,aAAa,CAAC,GAAG,EAAE;AAAA,UACzE,WAAW,cAAc,UAAU;AAKlC,kBAAM,OAAoB,CAAC;AAC3B,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,gBAAI,OAAO,KAAK,YAAY,EAAE,QAAQ;AACrC,qBAAO,OAAO,UAAM,sDAA6B,YAAY,CAAC;AAAA,YAC/D,OAAO;AACN,yDAAmB,KAAK,MAAM,QAAQ;AAAA,YACvC;AAEA,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,WAAW,aAAa,SAAS;AAEvC,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,UAAU,IAAI;AACpE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,iBAAiB,KAAK,iBAAiB,mBAAmB,CAAC;AAEjE,kBAAM,OAAoB;AAAA,cACzB,SAAS,KAAK,iBAAiB,WAAW,CAAC;AAAA,cAC3C,qBAAiB,8CAAqB,cAAc;AAAA,YACrD;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,8CAAqB,gBAAgB,CAAC;AAAA,YAC3D;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,oBAAoB,IAAI;AAC/E,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC;AAAA,QACD,WAAW,aAAa,QAAQ;AAQ/B,cAAI,cAAc,UAAU;AAK3B,kBAAM,OAAoB;AAAA,cACzB,SAAS,KAAK,iBAAiB,WAAW,CAAC;AAAA,cAC3C,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAAA,YAC/C;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,2CAAkB,gBAAgB,CAAC;AAAA,YACxD;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,UAAU,IAAI;AACrE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,2BAAe,MAAM,uCAAe,KAAK,MAAM,UAAU,UAAU,MAAM,EAAE;AAC3E,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,OAAO;AAK/B,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,UAAU,MAAM,EAAE;AAAA,UACzE,WAAW,cAAc,UAAU;AAKlC,kBAAM,KAAkB,CAAC;AACzB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gEAAuB,IAAI,OAAO;AAElC,2BAAe,MAAM,sCAAc,KAAK,MAAM,OAAO,UAAU,CAAC,GAAG,EAAE;AAAA,UACtE,WAAW,cAAc,aAAa;AAKrC,2BAAe,MAAM,uCAAe;AAAA,cACnC;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD,EAAE,QAAQ,QAAQ;AAAA,YACnB;AACA,2BAAe,aAAa;AAAA,UAC7B,WAAW,cAAc,UAAU;AAKlC,kBAAM,OAAoB,CAAC;AAC3B,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,gBAAI,OAAO,KAAK,YAAY,EAAE,QAAQ;AACrC,qBAAO,OAAO,UAAM,2CAAkB,YAAY,CAAC;AAAA,YACpD,OAAO;AACN,yDAAmB,KAAK,MAAM,QAAQ;AAAA,YACvC;AAEA,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,UAAU,MAAM,IAAI,IAAI;AAC9E,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,OAAoB;AAAA,cACzB,SAAS,KAAK,iBAAiB,WAAW,CAAC;AAAA,cAC3C,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAAA,YAC/C;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,2CAAkB,gBAAgB,CAAC;AAAA,YACxD;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,iBAAiB,IAAI;AAC5E,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC;AAAA,QACD,WAAW,aAAa,WAAW;AAQlC,cAAI,cAAc,UAAU;AAK3B,kBAAM,OAAoB;AAAA,cACzB,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAAA,YACrD;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,8CAAqB,gBAAgB,CAAC;AAAA,YAC3D;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,aAAa,IAAI;AACxE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,WAAW,aAAa,SAAS;AACvC,2BAAe,MAAM,uCAAe,KAAK,MAAM,UAAU,QAAQ;AACjE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,OAAO;AAK/B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,WAAW,aAAa,SAAS;AACvC,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,QAAQ;AAC9D,2BAAe,aAAa;AAAA,UAC7B,WAAW,cAAc,UAAU;AAKlC,kBAAM,KAAkB,CAAC;AACzB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gEAAuB,IAAI,OAAO;AAElC,2BAAe,MAAM,sCAAc,KAAK,MAAM,OAAO,aAAa,CAAC,GAAG,EAAE;AAAA,UACzE,WAAW,cAAc,UAAU;AAKlC,kBAAM,OAAoB,CAAC;AAC3B,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,gBAAI,OAAO,KAAK,YAAY,EAAE,QAAQ;AACrC,qBAAO,OAAO,UAAM,8CAAqB,YAAY,CAAC;AAAA,YACvD,OAAO;AACN,yDAAmB,KAAK,MAAM,QAAQ;AAAA,YACvC;AAEA,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,WAAW,aAAa,SAAS;AACvC,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,UAAU,IAAI;AACpE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,OAAoB;AAAA,cACzB,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAAA,YACrD;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,8CAAqB,gBAAgB,CAAC;AAAA,YAC3D;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,oBAAoB,IAAI;AAC/E,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC;AAAA,QACD,WAAW,aAAa,iBAAiB;AAQxC,cAAI,cAAc,UAAU;AAK3B,kBAAM,iBAAiB,KAAK,iBAAiB,mBAAmB,CAAC;AAEjE,2DAAuB,KAAK,MAAM,UAAU,cAAc;AAE1D,kBAAM,OAAoB;AAAA,cACzB,SAAS,KAAK,iBAAiB,WAAW,CAAC;AAAA,cAC3C,aAAa,EAAE,IAAI,KAAK,iBAAiB,YAAY,CAAC,EAAE;AAAA,cACxD,qBAAiB,8CAAqB,cAAc;AAAA,YACrD;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,oDAA2B,gBAAgB,CAAC;AAAA,YACjE;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,oBAAoB,IAAI;AAC/E,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,kBAAkB,KAAK,iBAAiB,mBAAmB,CAAC;AAElE,kBAAM,WAAW,oBAAoB,eAAe;AACpD,2BAAe,MAAM,uCAAe,KAAK,MAAM,UAAU,QAAQ;AACjE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,OAAO;AAK/B,kBAAM,kBAAkB,KAAK,iBAAiB,mBAAmB,CAAC;AAElE,kBAAM,WAAW,oBAAoB,eAAe;AACpD,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,QAAQ;AAC9D,2BAAe,aAAa;AAAA,UAC7B,WAAW,cAAc,UAAU;AAKlC,kBAAM,KAAkB,CAAC;AACzB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gEAAuB,IAAI,OAAO;AAElC,2BAAe,MAAM,sCAAc,KAAK,MAAM,OAAO,oBAAoB,CAAC,GAAG,EAAE;AAAA,UAChF,WAAW,cAAc,UAAU;AAKlC,kBAAM,OAAoB,CAAC;AAC3B,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,gBAAI,OAAO,KAAK,YAAY,EAAE,QAAQ;AACrC,qBAAO,OAAO,UAAM,oDAA2B,YAAY,CAAC;AAAA,YAC7D,OAAO;AACN,yDAAmB,KAAK,MAAM,QAAQ;AAAA,YACvC;AAEA,kBAAM,kBAAkB,KAAK,iBAAiB,mBAAmB,CAAC;AAElE,kBAAM,WAAW,oBAAoB,eAAe;AACpD,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,UAAU,IAAI;AACpE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,iBAAiB,KAAK,iBAAiB,mBAAmB,CAAC;AAEjE,kBAAM,OAAoB;AAAA,cACzB,SAAS,KAAK,iBAAiB,WAAW,CAAC;AAAA,cAC3C,aAAa,EAAE,IAAI,KAAK,iBAAiB,YAAY,CAAC,EAAE;AAAA,cACxD,qBAAiB,8CAAqB,cAAc;AAAA,YACrD;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,oDAA2B,gBAAgB,CAAC;AAAA,YACjE;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,2BAA2B,IAAI;AACtF,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC;AAAA,QACD,WAAW,aAAa,SAAS;AAQhC,cAAI,cAAc,UAAU;AAK3B,kBAAM,iBAAiB,KAAK,iBAAiB,mBAAmB,CAAC;AAEjE,2DAAuB,KAAK,MAAM,UAAU,cAAc;AAE1D,kBAAM,OAAoB;AAAA,cACzB,SAAS,KAAK,iBAAiB,WAAW,CAAC;AAAA,cAC3C,qBAAiB,8CAAqB,cAAc;AAAA,YACrD;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,4CAAmB,gBAAgB,CAAC;AAAA,YACzD;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,WAAW,IAAI;AACtE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,2BAAe,MAAM,uCAAe,KAAK,MAAM,UAAU,WAAW,OAAO,EAAE;AAC7E,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,OAAO;AAK/B,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,WAAW,OAAO,EAAE;AAC1E,2BAAe,aAAa;AAAA,UAC7B,WAAW,cAAc,UAAU;AAKlC,kBAAM,KAAkB,CAAC;AACzB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gEAAuB,IAAI,OAAO;AAElC,2BAAe,MAAM,sCAAc,KAAK,MAAM,OAAO,WAAW,CAAC,GAAG,EAAE;AAAA,UACvE,WAAW,cAAc,UAAU;AAKlC,kBAAM,OAAoB,CAAC;AAC3B,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,gBAAI,OAAO,KAAK,YAAY,EAAE,QAAQ;AACrC,qBAAO,OAAO,UAAM,4CAAmB,YAAY,CAAC;AAAA,YACrD,OAAO;AACN,yDAAmB,KAAK,MAAM,QAAQ;AAAA,YACvC;AAEA,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,WAAW,OAAO,IAAI,IAAI;AAChF,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,iBAAiB,KAAK,iBAAiB,mBAAmB,CAAC;AAEjE,kBAAM,OAAoB;AAAA,cACzB,SAAS,KAAK,iBAAiB,WAAW,CAAC;AAAA,cAC3C,qBAAiB,8CAAqB,cAAc;AAAA,YACrD;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,4CAAmB,gBAAgB,CAAC;AAAA,YACzD;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,kBAAkB,IAAI;AAC7E,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC;AAAA,QACD,WAAW,aAAa,cAAc;AAQrC,cAAI,cAAc,UAAU;AAK3B,kBAAM,iBAAiB,KAAK,iBAAiB,mBAAmB,CAAC;AAEjE,kBAAM,OAAoB;AAAA,cACzB,cAAc,EAAE,IAAI,KAAK,iBAAiB,aAAa,CAAC,EAAE;AAAA,cAC1D,SAAS,KAAK,iBAAiB,WAAW,CAAC;AAAA,cAC3C,qBAAiB,8CAAqB,cAAc;AAAA,YACrD;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,iDAAwB,gBAAgB,CAAC;AAAA,YAC9D;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,iBAAiB,IAAI;AAC5E,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,kBAAM,WAAW,iBAAiB,YAAY;AAC9C,2BAAe,MAAM,uCAAe,KAAK,MAAM,UAAU,QAAQ;AACjE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,OAAO;AAK/B,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,kBAAM,WAAW,iBAAiB,YAAY;AAC9C,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,QAAQ;AAC9D,2BAAe,aAAa;AAAA,UAC7B,WAAW,cAAc,UAAU;AAKlC,kBAAM,KAAkB,CAAC;AACzB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gEAAuB,IAAI,OAAO;AAElC,2BAAe,MAAM,sCAAc,KAAK,MAAM,OAAO,iBAAiB,CAAC,GAAG,EAAE;AAAA,UAC7E,WAAW,cAAc,UAAU;AAKlC,kBAAM,OAAoB,CAAC;AAC3B,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,gBAAI,OAAO,KAAK,YAAY,EAAE,QAAQ;AACrC,qBAAO,OAAO,UAAM,iDAAwB,YAAY,CAAC;AAAA,YAC1D,OAAO;AACN,yDAAmB,KAAK,MAAM,QAAQ;AAAA,YACvC;AAEA,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,kBAAM,WAAW,iBAAiB,YAAY;AAC9C,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,UAAU,IAAI;AACpE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,iBAAiB,KAAK,iBAAiB,mBAAmB,CAAC;AAEjE,kBAAM,OAAoB;AAAA,cACzB,cAAc,EAAE,IAAI,KAAK,iBAAiB,aAAa,CAAC,EAAE;AAAA,cAC1D,SAAS,KAAK,iBAAiB,WAAW,CAAC;AAAA,cAC3C,qBAAiB,8CAAqB,gBAAgB,QAAQ;AAAA,YAC/D;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,iDAAwB,gBAAgB,CAAC;AAAA,YAC9D;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,wBAAwB,IAAI;AACnF,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC;AAAA,QACD,WAAW,aAAa,UAAU;AAQjC,cAAI,cAAc,UAAU;AAK3B,kBAAM,OAAoB;AAAA,cACzB,aAAa,KAAK,iBAAiB,cAAc,CAAC;AAAA,YACnD;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,6CAAoB,gBAAgB,CAAC;AAAA,YAC1D;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,YAAY,IAAI;AACvE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAEpD,kBAAM,WAAW,YAAY,QAAQ;AACrC,2BAAe,MAAM,uCAAe,KAAK,MAAM,UAAU,QAAQ;AACjE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,OAAO;AAK/B,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAEpD,kBAAM,WAAW,YAAY,QAAQ;AACrC,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,QAAQ;AAC9D,2BAAe,aAAa;AAAA,UAC7B,WAAW,cAAc,UAAU;AAKlC,kBAAM,KAAkB,CAAC;AACzB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gEAAuB,IAAI,OAAO;AAElC,2BAAe,MAAM,sCAAc,KAAK,MAAM,OAAO,YAAY,CAAC,GAAG,EAAE;AAAA,UACxE,WAAW,cAAc,UAAU;AAKlC,kBAAM,OAAoB,CAAC;AAC3B,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,gBAAI,OAAO,KAAK,YAAY,EAAE,QAAQ;AACrC,qBAAO,OAAO,UAAM,6CAAoB,YAAY,CAAC;AAAA,YACtD,OAAO;AACN,yDAAmB,KAAK,MAAM,QAAQ;AAAA,YACvC;AAEA,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAEpD,kBAAM,WAAW,YAAY,QAAQ;AACrC,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,UAAU,IAAI;AACpE,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,kBAAM,OAAoB;AAAA,cACzB,aAAa,KAAK,iBAAiB,cAAc,CAAC;AAAA,YACnD;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AACzC,qBAAO,OAAO,UAAM,6CAAoB,gBAAgB,CAAC;AAAA,YAC1D;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,mBAAmB,IAAI;AAC9E,2BAAe,aAAa,KAAK,CAAC,EAAE;AAAA,UACrC;AAAA,QACD;AAAA,MACD,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,OAAO,MAAM,SAAS,MAAM,CAAC,EAAE,CAAC;AAClD;AAAA,QACD;AACA,cAAM;AAAA,MACP;AACA,YAAM,gBAAgB,KAAK,QAAQ;AAAA,QAClC,KAAK,QAAQ,gBAAgB,YAA2B;AAAA,QACxD,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,MACzB;AACA,iBAAW,KAAK,GAAG,aAAa;AAAA,IACjC;AAEA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": []}