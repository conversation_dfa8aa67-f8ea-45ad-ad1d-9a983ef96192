{"version": 3, "sources": ["../../../nodes/Iterable/UserListDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const userListOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userList'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Add',\n\t\t\t\tvalue: 'add',\n\t\t\t\tdescription: 'Add user to list',\n\t\t\t\taction: 'Add a user to a list',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Remove',\n\t\t\t\tvalue: 'remove',\n\t\t\t\tdescription: 'Remove a user from a list',\n\t\t\t\taction: 'Remove a user from a list',\n\t\t\t},\n\t\t],\n\t\tdefault: 'add',\n\t},\n];\n\nexport const userListFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                                userList:add                                */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'List Name or ID',\n\t\tname: 'listId',\n\t\ttype: 'options',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getLists',\n\t\t},\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userList'],\n\t\t\t\toperation: ['add'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription:\n\t\t\t'Identifier to be used. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'Identifier',\n\t\tname: 'identifier',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Email',\n\t\t\t\tvalue: 'email',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'User ID',\n\t\t\t\tvalue: 'userId',\n\t\t\t},\n\t\t],\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userList'],\n\t\t\t\toperation: ['add'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription: 'Identifier to be used',\n\t},\n\t{\n\t\tdisplayName: 'Value',\n\t\tname: 'value',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userList'],\n\t\t\t\toperation: ['add'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                userList:remove                             */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'List Name or ID',\n\t\tname: 'listId',\n\t\ttype: 'options',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getLists',\n\t\t},\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userList'],\n\t\t\t\toperation: ['remove'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription:\n\t\t\t'Identifier to be used. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t},\n\t{\n\t\tdisplayName: 'Identifier',\n\t\tname: 'identifier',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Email',\n\t\t\t\tvalue: 'email',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'User ID',\n\t\t\t\tvalue: 'userId',\n\t\t\t},\n\t\t],\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userList'],\n\t\t\t\toperation: ['remove'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription: 'Identifier to be used',\n\t},\n\t{\n\t\tdisplayName: 'Value',\n\t\tname: 'value',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userList'],\n\t\t\t\toperation: ['remove'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userList'],\n\t\t\t\toperation: ['remove'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Campaign ID',\n\t\t\t\tname: 'campaignId',\n\t\t\t\ttype: 'number',\n\t\t\t\tdefault: 0,\n\t\t\t\tdescription: 'Attribute unsubscribe to a campaign',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Channel Unsubscribe',\n\t\t\t\tname: 'channelUnsubscribe',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription:\n\t\t\t\t\t\"Whether to unsubscribe email from list's associated channel - essentially a global unsubscribe\",\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,qBAAwC;AAAA,EACpD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,MACtB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,iBAAoC;AAAA;AAAA;AAAA;AAAA,EAIhD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,QACrB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AACD;", "names": []}