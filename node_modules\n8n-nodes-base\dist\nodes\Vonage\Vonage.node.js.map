{"version": 3, "sources": ["../../../nodes/Vonage/Vonage.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tIDataObject,\n\tINodeExecutionData,\n\tINodeType,\n\tINodeTypeDescription,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes } from 'n8n-workflow';\n\nimport { vonageApiRequest } from './GenericFunctions';\n\nexport class Vonage implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Vonage',\n\t\tname: 'vonage',\n\n\t\ticon: { light: 'file:vonage.svg', dark: 'file:vonage.dark.svg' },\n\t\tgroup: ['input'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume Vonage API',\n\t\tdefaults: {\n\t\t\tname: 'Vonage',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'vonage<PERSON><PERSON>',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'SMS',\n\t\t\t\t\t\tvalue: 'sms',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'sms',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Operation',\n\t\t\t\tname: 'operation',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Send',\n\t\t\t\t\t\tvalue: 'send',\n\t\t\t\t\t\taction: 'Send an SMS',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresource: ['sms'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdefault: 'send',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'From',\n\t\t\t\tname: 'from',\n\t\t\t\ttype: 'string',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresource: ['sms'],\n\t\t\t\t\t\toperation: ['send'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The name or number the message should be sent from',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'To',\n\t\t\t\tname: 'to',\n\t\t\t\ttype: 'string',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresource: ['sms'],\n\t\t\t\t\t\toperation: ['send'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'The number that the message should be sent to. Numbers are specified in E.164 format.',\n\t\t\t},\n\t\t\t// {\n\t\t\t// \tdisplayName: 'Type',\n\t\t\t// \tname: 'type',\n\t\t\t// \ttype: 'options',\n\t\t\t// \tdisplayOptions: {\n\t\t\t// \t\tshow: {\n\t\t\t// \t\t\tresource: [\n\t\t\t// \t\t\t\t'sms',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\toperation: [\n\t\t\t// \t\t\t\t'send',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t},\n\t\t\t// \t},\n\t\t\t// \toptions: [\n\t\t\t// \t\t{\n\t\t\t// \t\t\tname: 'Binary',\n\t\t\t// \t\t\tvalue: 'binary',\n\t\t\t// \t\t},\n\t\t\t// \t\t{\n\t\t\t// \t\t\tname: 'Text',\n\t\t\t// \t\t\tvalue: 'text',\n\t\t\t// \t\t},\n\t\t\t// \t\t{\n\t\t\t// \t\t\tname: 'Wappush',\n\t\t\t// \t\t\tvalue: 'wappush',\n\t\t\t// \t\t},\n\t\t\t// \t\t{\n\t\t\t// \t\t\tname: 'Unicode',\n\t\t\t// \t\t\tvalue: 'unicode',\n\t\t\t// \t\t},\n\t\t\t// \t\t{\n\t\t\t// \t\t\tname: 'VCAL',\n\t\t\t// \t\t\tvalue: 'vcal',\n\t\t\t// \t\t},\n\t\t\t// \t\t{\n\t\t\t// \t\t\tname: 'VCARD',\n\t\t\t// \t\t\tvalue: 'vcard',\n\t\t\t// \t\t},\n\t\t\t// \t],\n\t\t\t// \tdefault: 'text',\n\t\t\t// \tdescription: 'The format of the message body',\n\t\t\t// },\n\t\t\t// {\n\t\t\t// \tdisplayName: 'Input Binary Field',\n\t\t\t// \tname: 'binaryPropertyName',\n\t\t\t// \tdisplayOptions: {\n\t\t\t// \t\tshow: {\n\t\t\t// \t\t\tresource: [\n\t\t\t// \t\t\t\t'sms',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\toperation: [\n\t\t\t// \t\t\t\t'send',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\ttype: [\n\t\t\t// \t\t\t\t'binary',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t},\n\t\t\t// \t},\n\t\t\t// \ttype: 'string',\n\t\t\t// \tdefault: 'data',\n\t\t\t// \tdescription: 'Object property name which holds binary data.',\n\t\t\t// \trequired: true,\n\t\t\t// },\n\t\t\t// {\n\t\t\t// \tdisplayName: 'Body',\n\t\t\t// \tname: 'body',\n\t\t\t// \ttype: 'string',\n\t\t\t// \tdisplayOptions: {\n\t\t\t// \t\tshow: {\n\t\t\t// \t\t\tresource: [\n\t\t\t// \t\t\t\t'sms',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\toperation: [\n\t\t\t// \t\t\t\t'send',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\ttype: [\n\t\t\t// \t\t\t\t'binary',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t},\n\t\t\t// \t},\n\t\t\t// \tdefault: '',\n\t\t\t// \tdescription: 'Hex encoded binary data',\n\t\t\t// },\n\t\t\t// {\n\t\t\t// \tdisplayName: 'UDH',\n\t\t\t// \tname: 'udh',\n\t\t\t// \ttype: 'string',\n\t\t\t// \tdisplayOptions: {\n\t\t\t// \t\tshow: {\n\t\t\t// \t\t\tresource: [\n\t\t\t// \t\t\t\t'sms',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\toperation: [\n\t\t\t// \t\t\t\t'send',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\ttype: [\n\t\t\t// \t\t\t\t'binary',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t},\n\t\t\t// \t},\n\t\t\t// \tdefault: '',\n\t\t\t// \tdescription: 'Your custom Hex encoded User Data Header',\n\t\t\t// },\n\t\t\t// {\n\t\t\t// \tdisplayName: 'Title',\n\t\t\t// \tname: 'title',\n\t\t\t// \tdisplayOptions: {\n\t\t\t// \t\tshow: {\n\t\t\t// \t\t\tresource: [\n\t\t\t// \t\t\t\t'sms',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\toperation: [\n\t\t\t// \t\t\t\t'send',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\ttype: [\n\t\t\t// \t\t\t\t'wappush',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t},\n\t\t\t// \t},\n\t\t\t// \ttype: 'string',\n\t\t\t// \tdefault: '',\n\t\t\t// \tdescription: 'The title for a wappush SMS',\n\t\t\t// },\n\t\t\t// {\n\t\t\t// \tdisplayName: 'URL',\n\t\t\t// \tname: 'url',\n\t\t\t// \ttype: 'string',\n\t\t\t// \tdisplayOptions: {\n\t\t\t// \t\tshow: {\n\t\t\t// \t\t\tresource: [\n\t\t\t// \t\t\t\t'sms',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\toperation: [\n\t\t\t// \t\t\t\t'send',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\ttype: [\n\t\t\t// \t\t\t\t'wappush',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t},\n\t\t\t// \t},\n\t\t\t// \tdefault: '',\n\t\t\t// \tdescription: 'The URL of your website',\n\t\t\t// },\n\t\t\t// {\n\t\t\t// \tdisplayName: 'Validity (in minutes)',\n\t\t\t// \tname: 'validity',\n\t\t\t// \ttype: 'number',\n\t\t\t// \tdefault: 0,\n\t\t\t// \tdisplayOptions: {\n\t\t\t// \t\tshow: {\n\t\t\t// \t\t\tresource: [\n\t\t\t// \t\t\t\t'sms',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\toperation: [\n\t\t\t// \t\t\t\t'send',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\ttype: [\n\t\t\t// \t\t\t\t'wappush',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t},\n\t\t\t// \t},\n\t\t\t// \tdescription: 'The availability for an SMS in minutes',\n\t\t\t// },\n\t\t\t{\n\t\t\t\tdisplayName: 'Message',\n\t\t\t\tname: 'message',\n\t\t\t\ttype: 'string',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresource: ['sms'],\n\t\t\t\t\t\toperation: ['send'],\n\t\t\t\t\t\t// type: [\n\t\t\t\t\t\t// \t'text',\n\t\t\t\t\t\t// \t'unicode',\n\t\t\t\t\t\t// ],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The body of the message being sent',\n\t\t\t},\n\t\t\t// {\n\t\t\t// \tdisplayName: 'VCard',\n\t\t\t// \tname: 'vcard',\n\t\t\t// \ttype: 'string',\n\t\t\t// \tdisplayOptions: {\n\t\t\t// \t\tshow: {\n\t\t\t// \t\t\tresource: [\n\t\t\t// \t\t\t\t'sms',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\toperation: [\n\t\t\t// \t\t\t\t'send',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\ttype: [\n\t\t\t// \t\t\t\t'vcard',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t},\n\t\t\t// \t},\n\t\t\t// \tdefault: '',\n\t\t\t// \tdescription: 'A business card in vCard format',\n\t\t\t// },\n\t\t\t// {\n\t\t\t// \tdisplayName: 'VCal',\n\t\t\t// \tname: 'vcal',\n\t\t\t// \ttype: 'string',\n\t\t\t// \tdisplayOptions: {\n\t\t\t// \t\tshow: {\n\t\t\t// \t\t\tresource: [\n\t\t\t// \t\t\t\t'sms',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\toperation: [\n\t\t\t// \t\t\t\t'send',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t\ttype: [\n\t\t\t// \t\t\t\t'vcal',\n\t\t\t// \t\t\t],\n\t\t\t// \t\t},\n\t\t\t// \t},\n\t\t\t// \tdefault: '',\n\t\t\t// \tdescription: 'A calendar event in vCal format',\n\t\t\t// },\n\t\t\t{\n\t\t\t\tdisplayName: 'Additional Fields',\n\t\t\t\tname: 'additionalFields',\n\t\t\t\ttype: 'collection',\n\t\t\t\tplaceholder: 'Add Field',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresource: ['sms'],\n\t\t\t\t\t\toperation: ['send'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Account Ref',\n\t\t\t\t\t\tname: 'account-ref',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'An optional string used to identify separate accounts using the SMS endpoint for billing purposes. To use this feature, <NAME_EMAIL>.',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Callback',\n\t\t\t\t\t\tname: 'callback',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'The webhook endpoint the delivery receipt for this sms is sent to. This parameter overrides the webhook endpoint you set in Dashboard.',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Client Ref',\n\t\t\t\t\t\tname: 'client-ref',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription: 'You can optionally include your own reference of up to 40 characters',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Message Class',\n\t\t\t\t\t\tname: 'message-class',\n\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: '0',\n\t\t\t\t\t\t\t\tvalue: 0,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: '1',\n\t\t\t\t\t\t\t\tvalue: 1,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: '2',\n\t\t\t\t\t\t\t\tvalue: 2,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: '3',\n\t\t\t\t\t\t\t\tvalue: 3,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription: 'The Data Coding Scheme value of the message',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Protocol ID',\n\t\t\t\t\t\tname: 'protocol-id',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'The value of the protocol identifier to use. Ensure that the value is aligned with udh.',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Status Report Req',\n\t\t\t\t\t\tname: 'status-report-req',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\tdescription: 'Whether to receive a Delivery Receipt',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'TTL (in Minutes)',\n\t\t\t\t\t\tname: 'ttl',\n\t\t\t\t\t\ttype: 'number',\n\t\t\t\t\t\tdefault: 4320,\n\t\t\t\t\t\tdescription: 'By default Nexmo attempt delivery for 72 hours',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: IDataObject[] = [];\n\t\tconst length = items.length;\n\t\tlet responseData;\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'sms') {\n\t\t\t\t\tif (operation === 'send') {\n\t\t\t\t\t\tconst from = this.getNodeParameter('from', i) as string;\n\n\t\t\t\t\t\tconst to = this.getNodeParameter('to', i) as string;\n\n\t\t\t\t\t\tconst type = this.getNodeParameter('type', i, 'text') as string;\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tfrom,\n\t\t\t\t\t\t\tto,\n\t\t\t\t\t\t\ttype,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (type === 'text' || type === 'unicode') {\n\t\t\t\t\t\t\tconst message = this.getNodeParameter('message', i) as string;\n\n\t\t\t\t\t\t\tbody.text = message;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (type === 'binary') {\n\t\t\t\t\t\t\tconst data = this.getNodeParameter('body', i) as string;\n\n\t\t\t\t\t\t\tconst udh = this.getNodeParameter('udh', i) as string;\n\n\t\t\t\t\t\t\tbody.udh = udh;\n\n\t\t\t\t\t\t\tbody.body = data;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (type === 'wappush') {\n\t\t\t\t\t\t\tconst title = this.getNodeParameter('title', i) as string;\n\n\t\t\t\t\t\t\tconst url = this.getNodeParameter('url', i) as string;\n\n\t\t\t\t\t\t\tconst validity = this.getNodeParameter('validity', i) as number;\n\n\t\t\t\t\t\t\tbody.title = title;\n\n\t\t\t\t\t\t\tbody.url = url;\n\n\t\t\t\t\t\t\tbody.validity = validity * 60000;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (type === 'vcard') {\n\t\t\t\t\t\t\tconst vcard = this.getNodeParameter('vcard', i) as string;\n\n\t\t\t\t\t\t\tbody.vcard = vcard;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (type === 'vcal') {\n\t\t\t\t\t\t\tconst vcal = this.getNodeParameter('vcal', i) as string;\n\n\t\t\t\t\t\t\tbody.vcal = vcal;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\n\t\t\t\t\t\tif (body.ttl) {\n\t\t\t\t\t\t\t// transform minutes to milliseconds\n\t\t\t\t\t\t\tbody.ttl = (body.ttl as number) * 60000;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await vonageApiRequest.call(this, 'POST', '/sms/json', body);\n\n\t\t\t\t\t\tresponseData = responseData.messages;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ error: error.message });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t\tif (Array.isArray(responseData)) {\n\t\t\t\treturnData.push.apply(returnData, responseData as IDataObject[]);\n\t\t\t} else if (responseData !== undefined) {\n\t\t\t\treturnData.push(responseData as IDataObject);\n\t\t\t}\n\t\t}\n\t\treturn [this.helpers.returnJsonArray(returnData)];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,0BAAoC;AAEpC,8BAAiC;AAE1B,MAAM,OAA4B;AAAA,EAAlC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MAEN,MAAM,EAAE,OAAO,mBAAmB,MAAM,uBAAuB;AAAA,MAC/D,OAAO,CAAC,OAAO;AAAA,MACf,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,QAAQ;AAAA,YACT;AAAA,UACD;AAAA,UACA,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,UAAU,CAAC,KAAK;AAAA,YACjB;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,UAAU,CAAC,KAAK;AAAA,cAChB,WAAW,CAAC,MAAM;AAAA,YACnB;AAAA,UACD;AAAA,UACA,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,UAAU,CAAC,KAAK;AAAA,cAChB,WAAW,CAAC,MAAM;AAAA,YACnB;AAAA,UACD;AAAA,UACA,SAAS;AAAA,UACT,aACC;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAqKA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,UAAU,CAAC,KAAK;AAAA,cAChB,WAAW,CAAC,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,YAKnB;AAAA,UACD;AAAA,UACA,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAyCA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,UAAU,CAAC,KAAK;AAAA,cAChB,WAAW,CAAC,MAAM;AAAA,YACnB;AAAA,UACD;AAAA,UACA,SAAS,CAAC;AAAA,UACV,SAAS;AAAA,YACR;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aACC;AAAA,YACF;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aACC;AAAA,YACF;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,gBACR;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,gBACA;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,gBACA;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,gBACA;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,cACD;AAAA,cACA,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aACC;AAAA,YACF;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAA4B,CAAC;AACnC,UAAM,SAAS,MAAM;AACrB,QAAI;AACJ,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,YAAI,aAAa,OAAO;AACvB,cAAI,cAAc,QAAQ;AACzB,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAE5C,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,GAAG,MAAM;AAEpD,kBAAM,OAAoB;AAAA,cACzB;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAEA,gBAAI,SAAS,UAAU,SAAS,WAAW;AAC1C,oBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,mBAAK,OAAO;AAAA,YACb;AAEA,gBAAI,SAAS,UAAU;AACtB,oBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAE5C,oBAAM,MAAM,KAAK,iBAAiB,OAAO,CAAC;AAE1C,mBAAK,MAAM;AAEX,mBAAK,OAAO;AAAA,YACb;AAEA,gBAAI,SAAS,WAAW;AACvB,oBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAE9C,oBAAM,MAAM,KAAK,iBAAiB,OAAO,CAAC;AAE1C,oBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAEpD,mBAAK,QAAQ;AAEb,mBAAK,MAAM;AAEX,mBAAK,WAAW,WAAW;AAAA,YAC5B;AAEA,gBAAI,SAAS,SAAS;AACrB,oBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAE9C,mBAAK,QAAQ;AAAA,YACd;AAEA,gBAAI,SAAS,QAAQ;AACpB,oBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAE5C,mBAAK,OAAO;AAAA,YACb;AAEA,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,mBAAO,OAAO,MAAM,gBAAgB;AAEpC,gBAAI,KAAK,KAAK;AAEb,mBAAK,MAAO,KAAK,MAAiB;AAAA,YACnC;AAEA,2BAAe,MAAM,yCAAiB,KAAK,MAAM,QAAQ,aAAa,IAAI;AAE1E,2BAAe,aAAa;AAAA,UAC7B;AAAA,QACD;AAAA,MACD,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,OAAO,MAAM,QAAQ,CAAC;AACxC;AAAA,QACD;AACA,cAAM;AAAA,MACP;AACA,UAAI,MAAM,QAAQ,YAAY,GAAG;AAChC,mBAAW,KAAK,MAAM,YAAY,YAA6B;AAAA,MAChE,WAAW,iBAAiB,QAAW;AACtC,mBAAW,KAAK,YAA2B;AAAA,MAC5C;AAAA,IACD;AACA,WAAO,CAAC,KAAK,QAAQ,gBAAgB,UAAU,CAAC;AAAA,EACjD;AACD;", "names": []}