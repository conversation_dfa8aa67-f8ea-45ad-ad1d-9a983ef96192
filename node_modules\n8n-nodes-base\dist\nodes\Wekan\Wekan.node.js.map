{"version": 3, "sources": ["../../../nodes/Wekan/Wekan.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tIDataObject,\n\tILoadOptionsFunctions,\n\tINodeExecutionData,\n\tINodePropertyOptions,\n\tINodeType,\n\tINodeTypeDescription,\n\tIHttpRequestMethods,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes, NodeOperationError } from 'n8n-workflow';\n\nimport { boardFields, boardOperations } from './BoardDescription';\nimport { cardCommentFields, cardCommentOperations } from './CardCommentDescription';\nimport { cardFields, cardOperations } from './CardDescription';\nimport { checklistFields, checklistOperations } from './ChecklistDescription';\nimport { checklistItemFields, checklistItemOperations } from './ChecklistItemDescription';\nimport { apiRequest } from './GenericFunctions';\nimport { listFields, listOperations } from './ListDescription';\nimport { wrapData } from '../../utils/utilities';\n\n// https://wekan.github.io/api/v4.41/\n\nexport class Wekan implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Wekan',\n\t\tname: 'wekan',\n\n\t\ticon: 'file:wekan.svg',\n\t\tgroup: ['transform'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume Wekan API',\n\t\tdefaults: {\n\t\t\tname: 'Wekan',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'wekanApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Board',\n\t\t\t\t\t\tvalue: 'board',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Card',\n\t\t\t\t\t\tvalue: 'card',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Card Comment',\n\t\t\t\t\t\tvalue: 'cardComment',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Checklist',\n\t\t\t\t\t\tvalue: 'checklist',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Checklist Item',\n\t\t\t\t\t\tvalue: 'checklistItem',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'List',\n\t\t\t\t\t\tvalue: 'list',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'card',\n\t\t\t},\n\n\t\t\t// ----------------------------------\n\t\t\t//         operations\n\t\t\t// ----------------------------------\n\t\t\t...boardOperations,\n\t\t\t...cardOperations,\n\t\t\t...cardCommentOperations,\n\t\t\t...checklistOperations,\n\t\t\t...checklistItemOperations,\n\t\t\t...listOperations,\n\n\t\t\t// ----------------------------------\n\t\t\t//         fields\n\t\t\t// ----------------------------------\n\t\t\t...boardFields,\n\t\t\t...cardFields,\n\t\t\t...cardCommentFields,\n\t\t\t...checklistFields,\n\t\t\t...checklistItemFields,\n\t\t\t...listFields,\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\tasync getUsers(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst users = await apiRequest.call(this, 'GET', 'users', {}, {});\n\t\t\t\tfor (const user of users) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: user.username,\n\t\t\t\t\t\tvalue: user._id,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\tasync getBoards(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst user = await apiRequest.call(this, 'GET', 'user', {}, {});\n\t\t\t\tconst boards = await apiRequest.call(this, 'GET', `users/${user._id}/boards`, {}, {});\n\t\t\t\tfor (const board of boards) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: board.title,\n\t\t\t\t\t\tvalue: board._id,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\tasync getLists(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst boardId = this.getCurrentNodeParameter('boardId') as string;\n\t\t\t\tconst lists = await apiRequest.call(this, 'GET', `boards/${boardId}/lists`, {}, {});\n\t\t\t\tfor (const list of lists) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: list.title,\n\t\t\t\t\t\tvalue: list._id,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\tasync getSwimlanes(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst boardId = this.getCurrentNodeParameter('boardId') as string;\n\t\t\t\tconst swimlanes = await apiRequest.call(this, 'GET', `boards/${boardId}/swimlanes`, {}, {});\n\t\t\t\tfor (const swimlane of swimlanes) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: swimlane.title,\n\t\t\t\t\t\tvalue: swimlane._id,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\tasync getCards(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst boardId = this.getCurrentNodeParameter('boardId') as string;\n\t\t\t\tconst listId = this.getCurrentNodeParameter('listId') as string;\n\t\t\t\tconst cards = await apiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t`boards/${boardId}/lists/${listId}/cards`,\n\t\t\t\t\t{},\n\t\t\t\t\t{},\n\t\t\t\t);\n\t\t\t\tfor (const card of cards) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: card.title,\n\t\t\t\t\t\tvalue: card._id,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\tasync getChecklists(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst boardId = this.getCurrentNodeParameter('boardId') as string;\n\t\t\t\tconst cardId = this.getCurrentNodeParameter('cardId') as string;\n\t\t\t\tconst checklists = await apiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t`boards/${boardId}/cards/${cardId}/checklists`,\n\t\t\t\t\t{},\n\t\t\t\t\t{},\n\t\t\t\t);\n\t\t\t\tfor (const checklist of checklists) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: checklist.title,\n\t\t\t\t\t\tvalue: checklist._id,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\tasync getChecklistItems(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst boardId = this.getCurrentNodeParameter('boardId') as string;\n\t\t\t\tconst cardId = this.getCurrentNodeParameter('cardId') as string;\n\t\t\t\tconst checklistId = this.getCurrentNodeParameter('checklistId') as string;\n\t\t\t\tconst checklist = await apiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t`boards/${boardId}/cards/${cardId}/checklists/${checklistId}`,\n\t\t\t\t\t{},\n\t\t\t\t\t{},\n\t\t\t\t);\n\t\t\t\tfor (const item of checklist.items) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: item.title,\n\t\t\t\t\t\tvalue: item._id,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\tasync getComments(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst boardId = this.getCurrentNodeParameter('boardId') as string;\n\t\t\t\tconst cardId = this.getCurrentNodeParameter('cardId') as string;\n\t\t\t\tconst comments = await apiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t`boards/${boardId}/cards/${cardId}/comments`,\n\t\t\t\t\t{},\n\t\t\t\t\t{},\n\t\t\t\t);\n\t\t\t\tfor (const comment of comments) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: comment.comment,\n\t\t\t\t\t\tvalue: comment._id,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: INodeExecutionData[] = [];\n\t\tlet returnAll;\n\t\tlet limit;\n\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\n\t\t// For Post\n\t\tlet body: IDataObject;\n\t\t// For Query string\n\t\tlet qs: IDataObject;\n\n\t\tlet requestMethod: IHttpRequestMethods;\n\t\tlet endpoint: string;\n\n\t\tfor (let i = 0; i < items.length; i++) {\n\t\t\ttry {\n\t\t\t\trequestMethod = 'GET';\n\t\t\t\tendpoint = '';\n\t\t\t\tbody = {};\n\t\t\t\tqs = {};\n\n\t\t\t\tif (resource === 'board') {\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'POST';\n\t\t\t\t\t\tendpoint = 'boards';\n\n\t\t\t\t\t\tbody.title = this.getNodeParameter('title', i) as string;\n\t\t\t\t\t\tbody.owner = this.getNodeParameter('owner', i) as string;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'DELETE';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}`;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}`;\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst userId = this.getNodeParameter('IdUser', i) as string;\n\n\t\t\t\t\t\treturnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tendpoint = `users/${userId}/boards`;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t`The operation \"${operation}\" is not known!`,\n\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'card') {\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'POST';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst listId = this.getNodeParameter('listId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/lists/${listId}/cards`;\n\n\t\t\t\t\t\tbody.title = this.getNodeParameter('title', i) as string;\n\t\t\t\t\t\tbody.swimlaneId = this.getNodeParameter('swimlaneId', i) as string;\n\t\t\t\t\t\tbody.authorId = this.getNodeParameter('authorId', i) as string;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'DELETE';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst listId = this.getNodeParameter('listId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/lists/${listId}/cards/${cardId}`;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst listId = this.getNodeParameter('listId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/lists/${listId}/cards/${cardId}`;\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst fromObject = this.getNodeParameter('fromObject', i) as string;\n\t\t\t\t\t\treturnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tif (fromObject === 'list') {\n\t\t\t\t\t\t\tconst listId = this.getNodeParameter('listId', i) as string;\n\n\t\t\t\t\t\t\tendpoint = `boards/${boardId}/lists/${listId}/cards`;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (fromObject === 'swimlane') {\n\t\t\t\t\t\t\tconst swimlaneId = this.getNodeParameter('swimlaneId', i) as string;\n\n\t\t\t\t\t\t\tendpoint = `boards/${boardId}/swimlanes/${swimlaneId}/cards`;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         update\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'PUT';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst listId = this.getNodeParameter('listId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/lists/${listId}/cards/${cardId}`;\n\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\t\tObject.assign(body, updateFields);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t`The operation \"${operation}\" is not known!`,\n\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'cardComment') {\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'POST';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/cards/${cardId}/comments`;\n\n\t\t\t\t\t\tbody.authorId = this.getNodeParameter('authorId', i) as string;\n\t\t\t\t\t\tbody.comment = this.getNodeParameter('comment', i) as string;\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'DELETE';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\t\t\t\t\t\tconst commentId = this.getNodeParameter('commentId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/cards/${cardId}/comments/${commentId}`;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\t\t\t\t\t\tconst commentId = this.getNodeParameter('commentId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/cards/${cardId}/comments/${commentId}`;\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/cards/${cardId}/comments`;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t`The operation \"${operation}\" is not known!`,\n\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'list') {\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'POST';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/lists`;\n\n\t\t\t\t\t\tbody.title = this.getNodeParameter('title', i) as string;\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'DELETE';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst listId = this.getNodeParameter('listId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/lists/${listId}`;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst listId = this.getNodeParameter('listId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/lists/${listId}`;\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\treturnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/lists`;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t`The operation \"${operation}\" is not known!`,\n\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'checklist') {\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'POST';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/cards/${cardId}/checklists`;\n\n\t\t\t\t\t\tbody.title = this.getNodeParameter('title', i) as string;\n\n\t\t\t\t\t\tbody.items = this.getNodeParameter('items', i) as string[];\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'DELETE';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\t\t\t\t\t\tconst checklistId = this.getNodeParameter('checklistId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/cards/${cardId}/checklists/${checklistId}`;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\t\t\t\t\t\tconst checklistId = this.getNodeParameter('checklistId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/cards/${cardId}/checklists/${checklistId}`;\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\t\t\t\t\t\treturnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/cards/${cardId}/checklists`;\n\t\t\t\t\t} else if (operation === 'getCheckItem') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         getCheckItem\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\t\t\t\t\t\tconst checklistId = this.getNodeParameter('checklistId', i) as string;\n\t\t\t\t\t\tconst itemId = this.getNodeParameter('itemId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/cards/${cardId}/checklists/${checklistId}/items/${itemId}`;\n\t\t\t\t\t} else if (operation === 'deleteCheckItem') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         deleteCheckItem\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'DELETE';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\t\t\t\t\t\tconst checklistId = this.getNodeParameter('checklistId', i) as string;\n\t\t\t\t\t\tconst itemId = this.getNodeParameter('itemId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/cards/${cardId}/checklists/${checklistId}/items/${itemId}`;\n\t\t\t\t\t} else if (operation === 'updateCheckItem') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         updateCheckItem\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'PUT';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\t\t\t\t\t\tconst checklistId = this.getNodeParameter('checklistId', i) as string;\n\t\t\t\t\t\tconst itemId = this.getNodeParameter('itemId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/cards/${cardId}/checklists/${checklistId}/items/${itemId}`;\n\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\t\tObject.assign(body, updateFields);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t`The operation \"${operation}\" is not known!`,\n\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'checklistItem') {\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\t\t\t\t\t\tconst checklistId = this.getNodeParameter('checklistId', i) as string;\n\t\t\t\t\t\tconst itemId = this.getNodeParameter('checklistItemId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/cards/${cardId}/checklists/${checklistId}/items/${itemId}`;\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'DELETE';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\t\t\t\t\t\tconst checklistId = this.getNodeParameter('checklistId', i) as string;\n\t\t\t\t\t\tconst itemId = this.getNodeParameter('checklistItemId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/cards/${cardId}/checklists/${checklistId}/items/${itemId}`;\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         update\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'PUT';\n\n\t\t\t\t\t\tconst boardId = this.getNodeParameter('boardId', i) as string;\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i) as string;\n\t\t\t\t\t\tconst checklistId = this.getNodeParameter('checklistId', i) as string;\n\t\t\t\t\t\tconst itemId = this.getNodeParameter('checklistItemId', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${boardId}/cards/${cardId}/checklists/${checklistId}/items/${itemId}`;\n\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\t\tObject.assign(body, updateFields);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tlet responseData = await apiRequest.call(this, requestMethod, endpoint, body, qs);\n\n\t\t\t\tif (returnAll === false && Array.isArray(responseData)) {\n\t\t\t\t\tlimit = this.getNodeParameter('limit', i);\n\t\t\t\t\tresponseData = responseData.splice(0, limit);\n\t\t\t\t}\n\n\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\twrapData(responseData as IDataObject[]),\n\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t);\n\n\t\t\t\treturnData.push(...executionData);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ json: { error: error.message }, pairedItem: { item: i } });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\n\t\treturn [this.helpers.returnJsonArray(returnData)];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,0BAAwD;AAExD,8BAA6C;AAC7C,oCAAyD;AACzD,6BAA2C;AAC3C,kCAAqD;AACrD,sCAA6D;AAC7D,8BAA2B;AAC3B,6BAA2C;AAC3C,uBAAyB;AAIlB,MAAM,MAA2B;AAAA,EAAjC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MAEN,MAAM;AAAA,MACN,OAAO,CAAC,WAAW;AAAA,MACnB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA;AAAA;AAAA;AAAA,QAKA,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA;AAAA;AAAA;AAAA,QAKH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA,QACZ,MAAM,WAAuE;AAC5E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,QAAQ,MAAM,mCAAW,KAAK,MAAM,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;AAChE,qBAAW,QAAQ,OAAO;AACzB,uBAAW,KAAK;AAAA,cACf,MAAM,KAAK;AAAA,cACX,OAAO,KAAK;AAAA,YACb,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,YAAwE;AAC7E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,OAAO,MAAM,mCAAW,KAAK,MAAM,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC9D,gBAAM,SAAS,MAAM,mCAAW,KAAK,MAAM,OAAO,SAAS,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AACpF,qBAAW,SAAS,QAAQ;AAC3B,uBAAW,KAAK;AAAA,cACf,MAAM,MAAM;AAAA,cACZ,OAAO,MAAM;AAAA,YACd,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,WAAuE;AAC5E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,UAAU,KAAK,wBAAwB,SAAS;AACtD,gBAAM,QAAQ,MAAM,mCAAW,KAAK,MAAM,OAAO,UAAU,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC;AAClF,qBAAW,QAAQ,OAAO;AACzB,uBAAW,KAAK;AAAA,cACf,MAAM,KAAK;AAAA,cACX,OAAO,KAAK;AAAA,YACb,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,eAA2E;AAChF,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,UAAU,KAAK,wBAAwB,SAAS;AACtD,gBAAM,YAAY,MAAM,mCAAW,KAAK,MAAM,OAAO,UAAU,OAAO,cAAc,CAAC,GAAG,CAAC,CAAC;AAC1F,qBAAW,YAAY,WAAW;AACjC,uBAAW,KAAK;AAAA,cACf,MAAM,SAAS;AAAA,cACf,OAAO,SAAS;AAAA,YACjB,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,WAAuE;AAC5E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,UAAU,KAAK,wBAAwB,SAAS;AACtD,gBAAM,SAAS,KAAK,wBAAwB,QAAQ;AACpD,gBAAM,QAAQ,MAAM,mCAAW;AAAA,YAC9B;AAAA,YACA;AAAA,YACA,UAAU,OAAO,UAAU,MAAM;AAAA,YACjC,CAAC;AAAA,YACD,CAAC;AAAA,UACF;AACA,qBAAW,QAAQ,OAAO;AACzB,uBAAW,KAAK;AAAA,cACf,MAAM,KAAK;AAAA,cACX,OAAO,KAAK;AAAA,YACb,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,gBAA4E;AACjF,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,UAAU,KAAK,wBAAwB,SAAS;AACtD,gBAAM,SAAS,KAAK,wBAAwB,QAAQ;AACpD,gBAAM,aAAa,MAAM,mCAAW;AAAA,YACnC;AAAA,YACA;AAAA,YACA,UAAU,OAAO,UAAU,MAAM;AAAA,YACjC,CAAC;AAAA,YACD,CAAC;AAAA,UACF;AACA,qBAAW,aAAa,YAAY;AACnC,uBAAW,KAAK;AAAA,cACf,MAAM,UAAU;AAAA,cAChB,OAAO,UAAU;AAAA,YAClB,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,oBAAgF;AACrF,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,UAAU,KAAK,wBAAwB,SAAS;AACtD,gBAAM,SAAS,KAAK,wBAAwB,QAAQ;AACpD,gBAAM,cAAc,KAAK,wBAAwB,aAAa;AAC9D,gBAAM,YAAY,MAAM,mCAAW;AAAA,YAClC;AAAA,YACA;AAAA,YACA,UAAU,OAAO,UAAU,MAAM,eAAe,WAAW;AAAA,YAC3D,CAAC;AAAA,YACD,CAAC;AAAA,UACF;AACA,qBAAW,QAAQ,UAAU,OAAO;AACnC,uBAAW,KAAK;AAAA,cACf,MAAM,KAAK;AAAA,cACX,OAAO,KAAK;AAAA,YACb,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,cAA0E;AAC/E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,UAAU,KAAK,wBAAwB,SAAS;AACtD,gBAAM,SAAS,KAAK,wBAAwB,QAAQ;AACpD,gBAAM,WAAW,MAAM,mCAAW;AAAA,YACjC;AAAA,YACA;AAAA,YACA,UAAU,OAAO,UAAU,MAAM;AAAA,YACjC,CAAC;AAAA,YACD,CAAC;AAAA,UACF;AACA,qBAAW,WAAW,UAAU;AAC/B,uBAAW,KAAK;AAAA,cACf,MAAM,QAAQ;AAAA,cACd,OAAO,QAAQ;AAAA,YAChB,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAAmC,CAAC;AAC1C,QAAI;AACJ,QAAI;AAEJ,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAGpD,QAAI;AAEJ,QAAI;AAEJ,QAAI;AACJ,QAAI;AAEJ,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,UAAI;AACH,wBAAgB;AAChB,mBAAW;AACX,eAAO,CAAC;AACR,aAAK,CAAC;AAEN,YAAI,aAAa,SAAS;AACzB,cAAI,cAAc,UAAU;AAK3B,4BAAgB;AAChB,uBAAW;AAEX,iBAAK,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC7C,iBAAK,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAE7C,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,MAAM,gBAAgB;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,uBAAW,UAAU,OAAO;AAAA,UAC7B,WAAW,cAAc,OAAO;AAK/B,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,uBAAW,UAAU,OAAO;AAAA,UAC7B,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,wBAAY,KAAK,iBAAiB,aAAa,CAAC;AAEhD,uBAAW,SAAS,MAAM;AAAA,UAC3B,OAAO;AACN,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,kBAAkB,SAAS;AAAA,cAC3B,EAAE,WAAW,EAAE;AAAA,YAChB;AAAA,UACD;AAAA,QACD,WAAW,aAAa,QAAQ;AAC/B,cAAI,cAAc,UAAU;AAK3B,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,uBAAW,UAAU,OAAO,UAAU,MAAM;AAE5C,iBAAK,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC7C,iBAAK,aAAa,KAAK,iBAAiB,cAAc,CAAC;AACvD,iBAAK,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAEnD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,MAAM,gBAAgB;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,uBAAW,UAAU,OAAO,UAAU,MAAM,UAAU,MAAM;AAAA,UAC7D,WAAW,cAAc,OAAO;AAK/B,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,uBAAW,UAAU,OAAO,UAAU,MAAM,UAAU,MAAM;AAAA,UAC7D,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AACxD,wBAAY,KAAK,iBAAiB,aAAa,CAAC;AAEhD,gBAAI,eAAe,QAAQ;AAC1B,oBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,yBAAW,UAAU,OAAO,UAAU,MAAM;AAAA,YAC7C;AAEA,gBAAI,eAAe,YAAY;AAC9B,oBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AAExD,yBAAW,UAAU,OAAO,cAAc,UAAU;AAAA,YACrD;AAAA,UACD,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,uBAAW,UAAU,OAAO,UAAU,MAAM,UAAU,MAAM;AAE5D,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,mBAAO,OAAO,MAAM,YAAY;AAAA,UACjC,OAAO;AACN,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,kBAAkB,SAAS;AAAA,cAC3B,EAAE,WAAW,EAAE;AAAA,YAChB;AAAA,UACD;AAAA,QACD,WAAW,aAAa,eAAe;AACtC,cAAI,cAAc,UAAU;AAK3B,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,uBAAW,UAAU,OAAO,UAAU,MAAM;AAE5C,iBAAK,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACnD,iBAAK,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAAA,UAClD,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,uBAAW,UAAU,OAAO,UAAU,MAAM,aAAa,SAAS;AAAA,UACnE,WAAW,cAAc,OAAO;AAK/B,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,uBAAW,UAAU,OAAO,UAAU,MAAM,aAAa,SAAS;AAAA,UACnE,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,uBAAW,UAAU,OAAO,UAAU,MAAM;AAAA,UAC7C,OAAO;AACN,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,kBAAkB,SAAS;AAAA,cAC3B,EAAE,WAAW,EAAE;AAAA,YAChB;AAAA,UACD;AAAA,QACD,WAAW,aAAa,QAAQ;AAC/B,cAAI,cAAc,UAAU;AAK3B,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,uBAAW,UAAU,OAAO;AAE5B,iBAAK,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAAA,UAC9C,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,uBAAW,UAAU,OAAO,UAAU,MAAM;AAAA,UAC7C,WAAW,cAAc,OAAO;AAK/B,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,uBAAW,UAAU,OAAO,UAAU,MAAM;AAAA,UAC7C,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,wBAAY,KAAK,iBAAiB,aAAa,CAAC;AAEhD,uBAAW,UAAU,OAAO;AAAA,UAC7B,OAAO;AACN,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,kBAAkB,SAAS;AAAA,cAC3B,EAAE,WAAW,EAAE;AAAA,YAChB;AAAA,UACD;AAAA,QACD,WAAW,aAAa,aAAa;AACpC,cAAI,cAAc,UAAU;AAK3B,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,uBAAW,UAAU,OAAO,UAAU,MAAM;AAE5C,iBAAK,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAE7C,iBAAK,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAAA,UAC9C,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAE1D,uBAAW,UAAU,OAAO,UAAU,MAAM,eAAe,WAAW;AAAA,UACvE,WAAW,cAAc,OAAO;AAK/B,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAE1D,uBAAW,UAAU,OAAO,UAAU,MAAM,eAAe,WAAW;AAAA,UACvE,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,wBAAY,KAAK,iBAAiB,aAAa,CAAC;AAEhD,uBAAW,UAAU,OAAO,UAAU,MAAM;AAAA,UAC7C,WAAW,cAAc,gBAAgB;AAKxC,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAC1D,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,uBAAW,UAAU,OAAO,UAAU,MAAM,eAAe,WAAW,UAAU,MAAM;AAAA,UACvF,WAAW,cAAc,mBAAmB;AAK3C,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAC1D,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,uBAAW,UAAU,OAAO,UAAU,MAAM,eAAe,WAAW,UAAU,MAAM;AAAA,UACvF,WAAW,cAAc,mBAAmB;AAK3C,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAC1D,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,uBAAW,UAAU,OAAO,UAAU,MAAM,eAAe,WAAW,UAAU,MAAM;AAEtF,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,mBAAO,OAAO,MAAM,YAAY;AAAA,UACjC,OAAO;AACN,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,kBAAkB,SAAS;AAAA,cAC3B,EAAE,WAAW,EAAE;AAAA,YAChB;AAAA,UACD;AAAA,QACD,WAAW,aAAa,iBAAiB;AACxC,cAAI,cAAc,OAAO;AAKxB,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAC1D,kBAAM,SAAS,KAAK,iBAAiB,mBAAmB,CAAC;AAEzD,uBAAW,UAAU,OAAO,UAAU,MAAM,eAAe,WAAW,UAAU,MAAM;AAAA,UACvF,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAC1D,kBAAM,SAAS,KAAK,iBAAiB,mBAAmB,CAAC;AAEzD,uBAAW,UAAU,OAAO,UAAU,MAAM,eAAe,WAAW,UAAU,MAAM;AAAA,UACvF,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAC1D,kBAAM,SAAS,KAAK,iBAAiB,mBAAmB,CAAC;AAEzD,uBAAW,UAAU,OAAO,UAAU,MAAM,eAAe,WAAW,UAAU,MAAM;AAEtF,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,mBAAO,OAAO,MAAM,YAAY;AAAA,UACjC;AAAA,QACD;AACA,YAAI,eAAe,MAAM,mCAAW,KAAK,MAAM,eAAe,UAAU,MAAM,EAAE;AAEhF,YAAI,cAAc,SAAS,MAAM,QAAQ,YAAY,GAAG;AACvD,kBAAQ,KAAK,iBAAiB,SAAS,CAAC;AACxC,yBAAe,aAAa,OAAO,GAAG,KAAK;AAAA,QAC5C;AAEA,cAAM,gBAAgB,KAAK,QAAQ;AAAA,cAClC,2BAAS,YAA6B;AAAA,UACtC,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,QACzB;AAEA,mBAAW,KAAK,GAAG,aAAa;AAAA,MACjC,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,MAAM,EAAE,OAAO,MAAM,QAAQ,GAAG,YAAY,EAAE,MAAM,EAAE,EAAE,CAAC;AAC3E;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AAEA,WAAO,CAAC,KAAK,QAAQ,gBAAgB,UAAU,CAAC;AAAA,EACjD;AACD;", "names": []}