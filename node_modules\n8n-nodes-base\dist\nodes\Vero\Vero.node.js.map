{"version": 3, "sources": ["../../../nodes/Vero/Vero.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tIDataObject,\n\tINodeExecutionData,\n\tINodeType,\n\tINodeTypeDescription,\n\tJsonObject,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes, NodeApiError } from 'n8n-workflow';\n\nimport { eventFields, eventOperations } from './EventDescripion';\nimport { validateJSON, veroApiRequest } from './GenericFunctions';\nimport { userFields, userOperations } from './UserDescription';\n\nexport class Vero implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Vero',\n\t\tname: 'vero',\n\t\ticon: 'file:vero.svg',\n\t\tgroup: ['output'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume Vero API',\n\t\tdefaults: {\n\t\t\tname: 'Vero',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'vero<PERSON><PERSON>',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'User',\n\t\t\t\t\t\tvalue: 'user',\n\t\t\t\t\t\tdescription: 'Create, update and manage the subscription status of your users',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Event',\n\t\t\t\t\t\tvalue: 'event',\n\t\t\t\t\t\tdescription: 'Track events based on actions your customers take in real time',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'user',\n\t\t\t},\n\t\t\t...userOperations,\n\t\t\t...eventOperations,\n\t\t\t...userFields,\n\t\t\t...eventFields,\n\t\t],\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: IDataObject[] = [];\n\t\tconst length = items.length;\n\t\tlet responseData;\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\t\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\t\t\t//https://developers.getvero.com/?bash#users\n\t\t\t\tif (resource === 'user') {\n\t\t\t\t\t//https://developers.getvero.com/?bash#users-identify\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tconst jsonActive = this.getNodeParameter('jsonParameters', i);\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tid,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tif (additionalFields.email) {\n\t\t\t\t\t\t\tbody.email = additionalFields.email as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (!jsonActive) {\n\t\t\t\t\t\t\tconst dataAttributesValues = (\n\t\t\t\t\t\t\t\tthis.getNodeParameter('dataAttributesUi', i) as IDataObject\n\t\t\t\t\t\t\t).dataAttributesValues as IDataObject[];\n\t\t\t\t\t\t\tif (dataAttributesValues) {\n\t\t\t\t\t\t\t\tconst dataAttributes: IDataObject = {};\n\t\t\t\t\t\t\t\tfor (let index = 0; index < dataAttributesValues.length; index++) {\n\t\t\t\t\t\t\t\t\tdataAttributes[dataAttributesValues[index].key as string] =\n\t\t\t\t\t\t\t\t\t\tdataAttributesValues[index].value;\n\t\t\t\t\t\t\t\t\tbody.data = dataAttributes;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst dataAttributesJson = validateJSON(\n\t\t\t\t\t\t\t\tthis.getNodeParameter('dataAttributesJson', i) as string,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tif (dataAttributesJson) {\n\t\t\t\t\t\t\t\tbody.data = dataAttributesJson;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tresponseData = await veroApiRequest.call(this, 'POST', '/users/track', body);\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tthrow new NodeApiError(this.getNode(), error as JsonObject);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.getvero.com/?bash#users-alias\n\t\t\t\t\tif (operation === 'alias') {\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tconst newId = this.getNodeParameter('newId', i) as string;\n\t\t\t\t\t\tconst body = {\n\t\t\t\t\t\t\tid,\n\t\t\t\t\t\t\tnew_id: newId,\n\t\t\t\t\t\t};\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tresponseData = await veroApiRequest.call(this, 'PUT', '/users/reidentify', body);\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tthrow new NodeApiError(this.getNode(), error as JsonObject);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.getvero.com/?bash#users-unsubscribe\n\t\t\t\t\t//https://developers.getvero.com/?bash#users-resubscribe\n\t\t\t\t\t//https://developers.getvero.com/?bash#users-delete\n\t\t\t\t\tif (\n\t\t\t\t\t\toperation === 'unsubscribe' ||\n\t\t\t\t\t\toperation === 'resubscribe' ||\n\t\t\t\t\t\toperation === 'delete'\n\t\t\t\t\t) {\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tconst body = {\n\t\t\t\t\t\t\tid,\n\t\t\t\t\t\t};\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tresponseData = await veroApiRequest.call(this, 'POST', `/users/${operation}`, body);\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tthrow new NodeApiError(this.getNode(), error as JsonObject);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.getvero.com/?bash#tags-add\n\t\t\t\t\t//https://developers.getvero.com/?bash#tags-remove\n\t\t\t\t\tif (operation === 'addTags' || operation === 'removeTags') {\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tconst tags = (this.getNodeParameter('tags', i) as string).split(',');\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tid,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tif (operation === 'addTags') {\n\t\t\t\t\t\t\tbody.add = JSON.stringify(tags);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (operation === 'removeTags') {\n\t\t\t\t\t\t\tbody.remove = JSON.stringify(tags);\n\t\t\t\t\t\t}\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tresponseData = await veroApiRequest.call(this, 'PUT', '/users/tags/edit', body);\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tthrow new NodeApiError(this.getNode(), error as JsonObject);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t//https://developers.getvero.com/?bash#events\n\t\t\t\tif (resource === 'event') {\n\t\t\t\t\t//https://developers.getvero.com/?bash#events-track\n\t\t\t\t\tif (operation === 'track') {\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tconst email = this.getNodeParameter('email', i) as string;\n\t\t\t\t\t\tconst eventName = this.getNodeParameter('eventName', i) as string;\n\t\t\t\t\t\tconst jsonActive = this.getNodeParameter('jsonParameters', i);\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tidentity: { id, email },\n\t\t\t\t\t\t\tevent_name: eventName,\n\t\t\t\t\t\t\temail,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tif (!jsonActive) {\n\t\t\t\t\t\t\tconst dataAttributesValues = (\n\t\t\t\t\t\t\t\tthis.getNodeParameter('dataAttributesUi', i) as IDataObject\n\t\t\t\t\t\t\t).dataAttributesValues as IDataObject[];\n\t\t\t\t\t\t\tif (dataAttributesValues) {\n\t\t\t\t\t\t\t\tconst dataAttributes: IDataObject = {};\n\t\t\t\t\t\t\t\tfor (let index = 0; index < dataAttributesValues.length; index++) {\n\t\t\t\t\t\t\t\t\tdataAttributes[dataAttributesValues[index].key as string] =\n\t\t\t\t\t\t\t\t\t\tdataAttributesValues[index].value;\n\t\t\t\t\t\t\t\t\tbody.data = JSON.stringify(dataAttributes);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tconst extraAttributesValues = (\n\t\t\t\t\t\t\t\tthis.getNodeParameter('extraAttributesUi', i) as IDataObject\n\t\t\t\t\t\t\t).extraAttributesValues as IDataObject[];\n\t\t\t\t\t\t\tif (extraAttributesValues) {\n\t\t\t\t\t\t\t\tconst extraAttributes: IDataObject = {};\n\t\t\t\t\t\t\t\tfor (let index = 0; index < extraAttributesValues.length; index++) {\n\t\t\t\t\t\t\t\t\textraAttributes[extraAttributesValues[index].key as string] =\n\t\t\t\t\t\t\t\t\t\textraAttributesValues[index].value;\n\t\t\t\t\t\t\t\t\tbody.extras = JSON.stringify(extraAttributes);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst dataAttributesJson = validateJSON(\n\t\t\t\t\t\t\t\tthis.getNodeParameter('dataAttributesJson', i) as string,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tif (dataAttributesJson) {\n\t\t\t\t\t\t\t\tbody.data = JSON.stringify(dataAttributesJson);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tconst extraAttributesJson = validateJSON(\n\t\t\t\t\t\t\t\tthis.getNodeParameter('extraAttributesJson', i) as string,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tif (extraAttributesJson) {\n\t\t\t\t\t\t\t\tbody.extras = JSON.stringify(extraAttributesJson);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tresponseData = await veroApiRequest.call(this, 'POST', '/events/track', body);\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tthrow new NodeApiError(this.getNode(), error as JsonObject);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (Array.isArray(responseData)) {\n\t\t\t\t\treturnData.push.apply(returnData, responseData as IDataObject[]);\n\t\t\t\t} else {\n\t\t\t\t\treturnData.push(responseData as IDataObject);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ error: error.message });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\t\treturn [this.helpers.returnJsonArray(returnData)];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,0BAAkD;AAElD,6BAA6C;AAC7C,8BAA6C;AAC7C,6BAA2C;AAEpC,MAAM,KAA0B;AAAA,EAAhC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,QAAQ;AAAA,MAChB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAA4B,CAAC;AACnC,UAAM,SAAS,MAAM;AACrB,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,cAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,cAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,YAAI,aAAa,QAAQ;AAExB,cAAI,cAAc,UAAU;AAC3B,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AACxC,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,kBAAM,aAAa,KAAK,iBAAiB,kBAAkB,CAAC;AAC5D,kBAAM,OAAoB;AAAA,cACzB;AAAA,YACD;AACA,gBAAI,iBAAiB,OAAO;AAC3B,mBAAK,QAAQ,iBAAiB;AAAA,YAC/B;AACA,gBAAI,CAAC,YAAY;AAChB,oBAAM,uBACL,KAAK,iBAAiB,oBAAoB,CAAC,EAC1C;AACF,kBAAI,sBAAsB;AACzB,sBAAM,iBAA8B,CAAC;AACrC,yBAAS,QAAQ,GAAG,QAAQ,qBAAqB,QAAQ,SAAS;AACjE,iCAAe,qBAAqB,KAAK,EAAE,GAAa,IACvD,qBAAqB,KAAK,EAAE;AAC7B,uBAAK,OAAO;AAAA,gBACb;AAAA,cACD;AAAA,YACD,OAAO;AACN,oBAAM,yBAAqB;AAAA,gBAC1B,KAAK,iBAAiB,sBAAsB,CAAC;AAAA,cAC9C;AACA,kBAAI,oBAAoB;AACvB,qBAAK,OAAO;AAAA,cACb;AAAA,YACD;AACA,gBAAI;AACH,6BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,gBAAgB,IAAI;AAAA,YAC5E,SAAS,OAAO;AACf,oBAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,KAAmB;AAAA,YAC3D;AAAA,UACD;AAEA,cAAI,cAAc,SAAS;AAC1B,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AACxC,kBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,kBAAM,OAAO;AAAA,cACZ;AAAA,cACA,QAAQ;AAAA,YACT;AACA,gBAAI;AACH,6BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,qBAAqB,IAAI;AAAA,YAChF,SAAS,OAAO;AACf,oBAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,KAAmB;AAAA,YAC3D;AAAA,UACD;AAIA,cACC,cAAc,iBACd,cAAc,iBACd,cAAc,UACb;AACD,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AACxC,kBAAM,OAAO;AAAA,cACZ;AAAA,YACD;AACA,gBAAI;AACH,6BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,UAAU,SAAS,IAAI,IAAI;AAAA,YACnF,SAAS,OAAO;AACf,oBAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,KAAmB;AAAA,YAC3D;AAAA,UACD;AAGA,cAAI,cAAc,aAAa,cAAc,cAAc;AAC1D,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AACxC,kBAAM,OAAQ,KAAK,iBAAiB,QAAQ,CAAC,EAAa,MAAM,GAAG;AACnE,kBAAM,OAAoB;AAAA,cACzB;AAAA,YACD;AACA,gBAAI,cAAc,WAAW;AAC5B,mBAAK,MAAM,KAAK,UAAU,IAAI;AAAA,YAC/B;AACA,gBAAI,cAAc,cAAc;AAC/B,mBAAK,SAAS,KAAK,UAAU,IAAI;AAAA,YAClC;AACA,gBAAI;AACH,6BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,oBAAoB,IAAI;AAAA,YAC/E,SAAS,OAAO;AACf,oBAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,KAAmB;AAAA,YAC3D;AAAA,UACD;AAAA,QACD;AAEA,YAAI,aAAa,SAAS;AAEzB,cAAI,cAAc,SAAS;AAC1B,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AACxC,kBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,aAAa,KAAK,iBAAiB,kBAAkB,CAAC;AAC5D,kBAAM,OAAoB;AAAA,cACzB,UAAU,EAAE,IAAI,MAAM;AAAA,cACtB,YAAY;AAAA,cACZ;AAAA,YACD;AACA,gBAAI,CAAC,YAAY;AAChB,oBAAM,uBACL,KAAK,iBAAiB,oBAAoB,CAAC,EAC1C;AACF,kBAAI,sBAAsB;AACzB,sBAAM,iBAA8B,CAAC;AACrC,yBAAS,QAAQ,GAAG,QAAQ,qBAAqB,QAAQ,SAAS;AACjE,iCAAe,qBAAqB,KAAK,EAAE,GAAa,IACvD,qBAAqB,KAAK,EAAE;AAC7B,uBAAK,OAAO,KAAK,UAAU,cAAc;AAAA,gBAC1C;AAAA,cACD;AACA,oBAAM,wBACL,KAAK,iBAAiB,qBAAqB,CAAC,EAC3C;AACF,kBAAI,uBAAuB;AAC1B,sBAAM,kBAA+B,CAAC;AACtC,yBAAS,QAAQ,GAAG,QAAQ,sBAAsB,QAAQ,SAAS;AAClE,kCAAgB,sBAAsB,KAAK,EAAE,GAAa,IACzD,sBAAsB,KAAK,EAAE;AAC9B,uBAAK,SAAS,KAAK,UAAU,eAAe;AAAA,gBAC7C;AAAA,cACD;AAAA,YACD,OAAO;AACN,oBAAM,yBAAqB;AAAA,gBAC1B,KAAK,iBAAiB,sBAAsB,CAAC;AAAA,cAC9C;AACA,kBAAI,oBAAoB;AACvB,qBAAK,OAAO,KAAK,UAAU,kBAAkB;AAAA,cAC9C;AACA,oBAAM,0BAAsB;AAAA,gBAC3B,KAAK,iBAAiB,uBAAuB,CAAC;AAAA,cAC/C;AACA,kBAAI,qBAAqB;AACxB,qBAAK,SAAS,KAAK,UAAU,mBAAmB;AAAA,cACjD;AAAA,YACD;AACA,gBAAI;AACH,6BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,iBAAiB,IAAI;AAAA,YAC7E,SAAS,OAAO;AACf,oBAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,KAAmB;AAAA,YAC3D;AAAA,UACD;AAAA,QACD;AACA,YAAI,MAAM,QAAQ,YAAY,GAAG;AAChC,qBAAW,KAAK,MAAM,YAAY,YAA6B;AAAA,QAChE,OAAO;AACN,qBAAW,KAAK,YAA2B;AAAA,QAC5C;AAAA,MACD,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,OAAO,MAAM,QAAQ,CAAC;AACxC;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AACA,WAAO,CAAC,KAAK,QAAQ,gBAAgB,UAAU,CAAC;AAAA,EACjD;AACD;", "names": []}