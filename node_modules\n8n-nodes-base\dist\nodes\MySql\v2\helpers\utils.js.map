{"version": 3, "sources": ["../../../../../nodes/MySql/v2/helpers/utils.ts"], "sourcesContent": ["import type {\n\tIDataObject,\n\tIExecuteFunctions,\n\tINode,\n\tINodeExecutionData,\n\tIPairedItemData,\n\tNodeExecutionWithMetadata,\n} from 'n8n-workflow';\nimport { NodeOperationError } from 'n8n-workflow';\n\nimport type {\n\tMysql2Pool,\n\tQueryMode,\n\tQueryValues,\n\tQueryWithValues,\n\tSortRule,\n\tWhereClause,\n} from './interfaces';\nimport { BATCH_MODE } from './interfaces';\nimport { generatePairedItemData } from '../../../../utils/utilities';\n\nexport function escapeSqlIdentifier(identifier: string): string {\n\tconst parts = identifier.match(/(`[^`]*`|[^.`]+)/g) ?? [];\n\n\treturn parts\n\t\t.map((part) => {\n\t\t\tconst trimmedPart = part.trim();\n\n\t\t\tif (trimmedPart.startsWith('`') && trimmedPart.endsWith('`')) {\n\t\t\t\treturn trimmedPart;\n\t\t\t}\n\n\t\t\treturn `\\`${trimmedPart}\\``;\n\t\t})\n\t\t.join('.');\n}\n\nexport const prepareQueryAndReplacements = (rawQuery: string, replacements?: QueryValues) => {\n\tif (replacements === undefined) {\n\t\treturn { query: rawQuery, values: [] };\n\t}\n\t// in UI for replacements we use syntax identical to Postgres Query Replacement, but we need to convert it to mysql2 replacement syntax\n\tlet query: string = rawQuery;\n\tconst values: QueryValues = [];\n\n\tconst regex = /\\$(\\d+)(?::name)?/g;\n\tconst matches = rawQuery.match(regex) || [];\n\n\tfor (const match of matches) {\n\t\tif (match.includes(':name')) {\n\t\t\tconst matchIndex = Number(match.replace('$', '').replace(':name', '')) - 1;\n\t\t\tquery = query.replace(match, escapeSqlIdentifier(replacements[matchIndex].toString()));\n\t\t} else {\n\t\t\tconst matchIndex = Number(match.replace('$', '')) - 1;\n\t\t\tquery = query.replace(match, '?');\n\t\t\tvalues.push(replacements[matchIndex]);\n\t\t}\n\t}\n\n\treturn { query, values };\n};\n\nexport function prepareErrorItem(\n\titem: IDataObject,\n\terror: IDataObject | NodeOperationError | Error,\n\tindex: number,\n) {\n\treturn {\n\t\tjson: { message: error.message, item: { ...item }, itemIndex: index, error: { ...error } },\n\t\tpairedItem: { item: index },\n\t} as INodeExecutionData;\n}\n\nexport function parseMySqlError(\n\tthis: IExecuteFunctions,\n\terror: any,\n\titemIndex = 0,\n\tqueries?: string[],\n) {\n\tlet message: string = error.message;\n\tconst description = `sql: ${error.sql}, code: ${error.code}`;\n\n\tif (\n\t\tqueries?.length &&\n\t\t(message || '').toLowerCase().includes('you have an error in your sql syntax')\n\t) {\n\t\tlet queryIndex = itemIndex;\n\t\tconst failedStatement = ((message.split(\"near '\")[1] || '').split(\"' at\")[0] || '').split(\n\t\t\t';',\n\t\t)[0];\n\n\t\tif (failedStatement) {\n\t\t\tif (queryIndex === 0 && queries.length > 1) {\n\t\t\t\tconst failedQueryIndex = queries.findIndex((query) => query.includes(failedStatement));\n\t\t\t\tif (failedQueryIndex !== -1) {\n\t\t\t\t\tqueryIndex = failedQueryIndex;\n\t\t\t\t}\n\t\t\t}\n\t\t\tconst lines = queries[queryIndex].split('\\n');\n\n\t\t\tconst failedLine = lines.findIndex((line) => line.includes(failedStatement));\n\t\t\tif (failedLine !== -1) {\n\t\t\t\tmessage = `You have an error in your SQL syntax on line ${\n\t\t\t\t\tfailedLine + 1\n\t\t\t\t} near '${failedStatement}'`;\n\t\t\t}\n\t\t}\n\t}\n\n\tif ((error?.message as string).includes('ECONNREFUSED')) {\n\t\tmessage = 'Connection refused';\n\t}\n\n\treturn new NodeOperationError(this.getNode(), error as Error, {\n\t\tmessage,\n\t\tdescription,\n\t\titemIndex,\n\t});\n}\n\nexport function wrapData(data: IDataObject | IDataObject[]): INodeExecutionData[] {\n\tif (!Array.isArray(data)) {\n\t\treturn [{ json: data }];\n\t}\n\treturn data.map((item) => ({\n\t\tjson: item,\n\t}));\n}\n\nexport function prepareOutput(\n\tresponse: IDataObject[],\n\toptions: IDataObject,\n\tstatements: string[],\n\tconstructExecutionHelper: (\n\t\tinputData: INodeExecutionData[],\n\t\toptions: {\n\t\t\titemData: IPairedItemData | IPairedItemData[];\n\t\t},\n\t) => NodeExecutionWithMetadata[],\n\titemData: IPairedItemData | IPairedItemData[],\n) {\n\tlet returnData: INodeExecutionData[] = [];\n\n\tif (options.detailedOutput) {\n\t\tresponse.forEach((entry, index) => {\n\t\t\tconst item = {\n\t\t\t\tsql: statements[index],\n\t\t\t\tdata: entry,\n\t\t\t};\n\n\t\t\tconst executionData = constructExecutionHelper(wrapData(item), {\n\t\t\t\titemData,\n\t\t\t});\n\n\t\t\treturnData = returnData.concat(executionData);\n\t\t});\n\t} else {\n\t\tresponse\n\t\t\t.filter((entry) => Array.isArray(entry))\n\t\t\t.forEach((entry, index) => {\n\t\t\t\tconst executionData = constructExecutionHelper(wrapData(entry), {\n\t\t\t\t\titemData: Array.isArray(itemData) ? itemData[index] : itemData,\n\t\t\t\t});\n\n\t\t\t\treturnData = returnData.concat(executionData);\n\t\t\t});\n\t}\n\n\tif (!returnData.length) {\n\t\tif ((options?.nodeVersion as number) < 2.2) {\n\t\t\treturnData.push({ json: { success: true }, pairedItem: itemData });\n\t\t} else {\n\t\t\tconst isSelectQuery = statements\n\t\t\t\t.filter((statement) => !statement.startsWith('--'))\n\t\t\t\t.every((statement) =>\n\t\t\t\t\tstatement\n\t\t\t\t\t\t.replace(/\\/\\*.*?\\*\\//g, '') // remove multiline comments\n\t\t\t\t\t\t.replace(/\\n/g, '')\n\t\t\t\t\t\t.toLowerCase()\n\t\t\t\t\t\t.startsWith('select'),\n\t\t\t\t);\n\n\t\t\tif (!isSelectQuery) {\n\t\t\t\treturnData.push({ json: { success: true }, pairedItem: itemData });\n\t\t\t}\n\t\t}\n\t}\n\n\treturn returnData;\n}\nconst END_OF_STATEMENT = /;(?=(?:[^'\\\\]|'[^']*?'|\\\\[\\s\\S])*?$)/g;\nexport const splitQueryToStatements = (query: string, filterOutEmpty = true) => {\n\tconst statements = query\n\t\t.replace(/\\n/g, '')\n\t\t.split(END_OF_STATEMENT)\n\t\t.map((statement) => statement.trim());\n\treturn filterOutEmpty ? statements.filter((statement) => statement !== '') : statements;\n};\n\nexport function configureQueryRunner(\n\tthis: IExecuteFunctions,\n\toptions: IDataObject,\n\tpool: Mysql2Pool,\n) {\n\treturn async (queries: QueryWithValues[]) => {\n\t\tif (queries.length === 0) {\n\t\t\treturn [];\n\t\t}\n\n\t\tlet returnData: INodeExecutionData[] = [];\n\t\tconst mode = (options.queryBatching as QueryMode) || BATCH_MODE.SINGLE;\n\n\t\tconst connection = await pool.getConnection();\n\n\t\tif (mode === BATCH_MODE.SINGLE) {\n\t\t\tconst formattedQueries = queries.map(({ query, values }) => connection.format(query, values));\n\t\t\ttry {\n\t\t\t\t//releasing connection after formatting queries, otherwise pool.query() will fail with timeout\n\t\t\t\tconnection.release();\n\n\t\t\t\tlet singleQuery = '';\n\t\t\t\tif (formattedQueries.length > 1) {\n\t\t\t\t\tsingleQuery = formattedQueries.map((query) => query.trim().replace(/;$/, '')).join(';');\n\t\t\t\t} else {\n\t\t\t\t\tsingleQuery = formattedQueries[0];\n\t\t\t\t}\n\n\t\t\t\tlet response: IDataObject | IDataObject[] = (\n\t\t\t\t\tawait pool.query(singleQuery)\n\t\t\t\t)[0] as unknown as IDataObject;\n\n\t\t\t\tif (!response) return [];\n\n\t\t\t\tlet statements;\n\t\t\t\tif ((options?.nodeVersion as number) <= 2.3) {\n\t\t\t\t\tstatements = singleQuery\n\t\t\t\t\t\t.replace(/\\n/g, '')\n\t\t\t\t\t\t.split(';')\n\t\t\t\t\t\t.filter((statement) => statement !== '');\n\t\t\t\t} else {\n\t\t\t\t\tstatements = splitQueryToStatements(singleQuery);\n\t\t\t\t}\n\n\t\t\t\tif (Array.isArray(response)) {\n\t\t\t\t\tif (statements.length === 1) response = [response];\n\t\t\t\t} else {\n\t\t\t\t\tresponse = [response];\n\t\t\t\t}\n\n\t\t\t\t//because single query is used in this mode mapping itemIndex not possible, setting all items as paired\n\t\t\t\tconst pairedItem = generatePairedItemData(queries.length);\n\n\t\t\t\treturnData = returnData.concat(\n\t\t\t\t\tprepareOutput(\n\t\t\t\t\t\tresponse,\n\t\t\t\t\t\toptions,\n\t\t\t\t\t\tstatements,\n\t\t\t\t\t\tthis.helpers.constructExecutionMetaData,\n\t\t\t\t\t\tpairedItem,\n\t\t\t\t\t),\n\t\t\t\t);\n\t\t\t} catch (err) {\n\t\t\t\tconst error = parseMySqlError.call(this, err, 0, formattedQueries);\n\n\t\t\t\tif (!this.continueOnFail()) throw error;\n\t\t\t\treturnData.push({ json: { message: error.message, error: { ...error } } });\n\t\t\t}\n\t\t} else {\n\t\t\tif (mode === BATCH_MODE.INDEPENDENTLY) {\n\t\t\t\tlet formattedQuery = '';\n\t\t\t\tfor (const [index, queryWithValues] of queries.entries()) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst { query, values } = queryWithValues;\n\t\t\t\t\t\tformattedQuery = connection.format(query, values);\n\n\t\t\t\t\t\tlet statements;\n\t\t\t\t\t\tif ((options?.nodeVersion as number) <= 2.3) {\n\t\t\t\t\t\t\tstatements = formattedQuery.split(';').map((q) => q.trim());\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tstatements = splitQueryToStatements(formattedQuery, false);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst responses: IDataObject[] = [];\n\t\t\t\t\t\tfor (const statement of statements) {\n\t\t\t\t\t\t\tif (statement === '') continue;\n\t\t\t\t\t\t\tconst response = (await connection.query(statement))[0] as unknown as IDataObject;\n\n\t\t\t\t\t\t\tresponses.push(response);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturnData = returnData.concat(\n\t\t\t\t\t\t\tprepareOutput(\n\t\t\t\t\t\t\t\tresponses,\n\t\t\t\t\t\t\t\toptions,\n\t\t\t\t\t\t\t\tstatements,\n\t\t\t\t\t\t\t\tthis.helpers.constructExecutionMetaData,\n\t\t\t\t\t\t\t\t{ item: index },\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\t);\n\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\tconst error = parseMySqlError.call(this, err, index, [formattedQuery]);\n\n\t\t\t\t\t\tif (!this.continueOnFail()) {\n\t\t\t\t\t\t\tconnection.release();\n\t\t\t\t\t\t\tthrow error;\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturnData.push(prepareErrorItem(queries[index], error as Error, index));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (mode === BATCH_MODE.TRANSACTION) {\n\t\t\t\tawait connection.beginTransaction();\n\n\t\t\t\tlet formattedQuery = '';\n\t\t\t\tfor (const [index, queryWithValues] of queries.entries()) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst { query, values } = queryWithValues;\n\t\t\t\t\t\tformattedQuery = connection.format(query, values);\n\n\t\t\t\t\t\tlet statements;\n\t\t\t\t\t\tif ((options?.nodeVersion as number) <= 2.3) {\n\t\t\t\t\t\t\tstatements = formattedQuery.split(';').map((q) => q.trim());\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tstatements = splitQueryToStatements(formattedQuery, false);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst responses: IDataObject[] = [];\n\t\t\t\t\t\tfor (const statement of statements) {\n\t\t\t\t\t\t\tif (statement === '') continue;\n\t\t\t\t\t\t\tconst response = (await connection.query(statement))[0] as unknown as IDataObject;\n\n\t\t\t\t\t\t\tresponses.push(response);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturnData = returnData.concat(\n\t\t\t\t\t\t\tprepareOutput(\n\t\t\t\t\t\t\t\tresponses,\n\t\t\t\t\t\t\t\toptions,\n\t\t\t\t\t\t\t\tstatements,\n\t\t\t\t\t\t\t\tthis.helpers.constructExecutionMetaData,\n\t\t\t\t\t\t\t\t{ item: index },\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\t);\n\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\tconst error = parseMySqlError.call(this, err, index, [formattedQuery]);\n\n\t\t\t\t\t\tif (connection) {\n\t\t\t\t\t\t\tawait connection.rollback();\n\t\t\t\t\t\t\tconnection.release();\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (!this.continueOnFail()) throw error;\n\t\t\t\t\t\treturnData.push(prepareErrorItem(queries[index], error as Error, index));\n\n\t\t\t\t\t\t// Return here because we already rolled back the transaction\n\t\t\t\t\t\treturn returnData;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tawait connection.commit();\n\t\t\t}\n\n\t\t\tconnection.release();\n\t\t}\n\n\t\treturn returnData;\n\t};\n}\n\nexport function addWhereClauses(\n\tnode: INode,\n\titemIndex: number,\n\tquery: string,\n\tclauses: WhereClause[],\n\treplacements: QueryValues,\n\tcombineConditions?: string,\n): [string, QueryValues] {\n\tif (clauses.length === 0) return [query, replacements];\n\n\tlet combineWith = 'AND';\n\n\tif (combineConditions === 'OR') {\n\t\tcombineWith = 'OR';\n\t}\n\n\tlet whereQuery = ' WHERE';\n\tconst values: QueryValues = [];\n\n\tclauses.forEach((clause, index) => {\n\t\tif (clause.condition === 'equal') {\n\t\t\tclause.condition = '=';\n\t\t}\n\n\t\tif (['>', '<', '>=', '<='].includes(clause.condition)) {\n\t\t\tconst value = Number(clause.value);\n\n\t\t\tif (Number.isNaN(value)) {\n\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\tnode,\n\t\t\t\t\t`Operator in entry ${index + 1} of 'Select Rows' works with numbers, but value ${\n\t\t\t\t\t\tclause.value\n\t\t\t\t\t} is not a number`,\n\t\t\t\t\t{\n\t\t\t\t\t\titemIndex,\n\t\t\t\t\t},\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tclause.value = value;\n\t\t}\n\n\t\tlet valueReplacement = ' ';\n\t\tif (clause.condition !== 'IS NULL' && clause.condition !== 'IS NOT NULL') {\n\t\t\tvalueReplacement = ' ?';\n\t\t\tvalues.push(clause.value);\n\t\t}\n\n\t\tconst operator = index === clauses.length - 1 ? '' : ` ${combineWith}`;\n\n\t\twhereQuery += ` ${escapeSqlIdentifier(clause.column)} ${\n\t\t\tclause.condition\n\t\t}${valueReplacement}${operator}`;\n\t});\n\n\treturn [`${query}${whereQuery}`, replacements.concat(...values)];\n}\n\nexport function addSortRules(\n\tquery: string,\n\trules: SortRule[],\n\treplacements: QueryValues,\n): [string, QueryValues] {\n\tif (rules.length === 0) return [query, replacements];\n\n\tlet orderByQuery = ' ORDER BY';\n\tconst values: string[] = [];\n\n\trules.forEach((rule, index) => {\n\t\tconst endWith = index === rules.length - 1 ? '' : ',';\n\n\t\torderByQuery += ` ${escapeSqlIdentifier(rule.column)} ${rule.direction}${endWith}`;\n\t});\n\n\treturn [`${query}${orderByQuery}`, replacements.concat(...values)];\n}\n\nexport function replaceEmptyStringsByNulls(\n\titems: INodeExecutionData[],\n\treplace?: boolean,\n): INodeExecutionData[] {\n\tif (!replace) return [...items];\n\n\tconst returnData: INodeExecutionData[] = items.map((item) => {\n\t\tconst newItem = { ...item };\n\t\tconst keys = Object.keys(newItem.json);\n\n\t\tfor (const key of keys) {\n\t\t\tif (newItem.json[key] === '') {\n\t\t\t\tnewItem.json[key] = null;\n\t\t\t}\n\t\t}\n\n\t\treturn newItem;\n\t});\n\n\treturn returnData;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,0BAAmC;AAUnC,wBAA2B;AAC3B,uBAAuC;AAEhC,SAAS,oBAAoB,YAA4B;AAC/D,QAAM,QAAQ,WAAW,MAAM,mBAAmB,KAAK,CAAC;AAExD,SAAO,MACL,IAAI,CAAC,SAAS;AACd,UAAM,cAAc,KAAK,KAAK;AAE9B,QAAI,YAAY,WAAW,GAAG,KAAK,YAAY,SAAS,GAAG,GAAG;AAC7D,aAAO;AAAA,IACR;AAEA,WAAO,KAAK,WAAW;AAAA,EACxB,CAAC,EACA,KAAK,GAAG;AACX;AAEO,MAAM,8BAA8B,CAAC,UAAkB,iBAA+B;AAC5F,MAAI,iBAAiB,QAAW;AAC/B,WAAO,EAAE,OAAO,UAAU,QAAQ,CAAC,EAAE;AAAA,EACtC;AAEA,MAAI,QAAgB;AACpB,QAAM,SAAsB,CAAC;AAE7B,QAAM,QAAQ;AACd,QAAM,UAAU,SAAS,MAAM,KAAK,KAAK,CAAC;AAE1C,aAAW,SAAS,SAAS;AAC5B,QAAI,MAAM,SAAS,OAAO,GAAG;AAC5B,YAAM,aAAa,OAAO,MAAM,QAAQ,KAAK,EAAE,EAAE,QAAQ,SAAS,EAAE,CAAC,IAAI;AACzE,cAAQ,MAAM,QAAQ,OAAO,oBAAoB,aAAa,UAAU,EAAE,SAAS,CAAC,CAAC;AAAA,IACtF,OAAO;AACN,YAAM,aAAa,OAAO,MAAM,QAAQ,KAAK,EAAE,CAAC,IAAI;AACpD,cAAQ,MAAM,QAAQ,OAAO,GAAG;AAChC,aAAO,KAAK,aAAa,UAAU,CAAC;AAAA,IACrC;AAAA,EACD;AAEA,SAAO,EAAE,OAAO,OAAO;AACxB;AAEO,SAAS,iBACf,MACA,OACA,OACC;AACD,SAAO;AAAA,IACN,MAAM,EAAE,SAAS,MAAM,SAAS,MAAM,EAAE,GAAG,KAAK,GAAG,WAAW,OAAO,OAAO,EAAE,GAAG,MAAM,EAAE;AAAA,IACzF,YAAY,EAAE,MAAM,MAAM;AAAA,EAC3B;AACD;AAEO,SAAS,gBAEf,OACA,YAAY,GACZ,SACC;AACD,MAAI,UAAkB,MAAM;AAC5B,QAAM,cAAc,QAAQ,MAAM,GAAG,WAAW,MAAM,IAAI;AAE1D,MACC,SAAS,WACR,WAAW,IAAI,YAAY,EAAE,SAAS,sCAAsC,GAC5E;AACD,QAAI,aAAa;AACjB,UAAM,oBAAoB,QAAQ,MAAM,QAAQ,EAAE,CAAC,KAAK,IAAI,MAAM,MAAM,EAAE,CAAC,KAAK,IAAI;AAAA,MACnF;AAAA,IACD,EAAE,CAAC;AAEH,QAAI,iBAAiB;AACpB,UAAI,eAAe,KAAK,QAAQ,SAAS,GAAG;AAC3C,cAAM,mBAAmB,QAAQ,UAAU,CAAC,UAAU,MAAM,SAAS,eAAe,CAAC;AACrF,YAAI,qBAAqB,IAAI;AAC5B,uBAAa;AAAA,QACd;AAAA,MACD;AACA,YAAM,QAAQ,QAAQ,UAAU,EAAE,MAAM,IAAI;AAE5C,YAAM,aAAa,MAAM,UAAU,CAAC,SAAS,KAAK,SAAS,eAAe,CAAC;AAC3E,UAAI,eAAe,IAAI;AACtB,kBAAU,gDACT,aAAa,CACd,UAAU,eAAe;AAAA,MAC1B;AAAA,IACD;AAAA,EACD;AAEA,OAAK,OAAO,SAAmB,SAAS,cAAc,GAAG;AACxD,cAAU;AAAA,EACX;AAEA,SAAO,IAAI,uCAAmB,KAAK,QAAQ,GAAG,OAAgB;AAAA,IAC7D;AAAA,IACA;AAAA,IACA;AAAA,EACD,CAAC;AACF;AAEO,SAAS,SAAS,MAAyD;AACjF,MAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACzB,WAAO,CAAC,EAAE,MAAM,KAAK,CAAC;AAAA,EACvB;AACA,SAAO,KAAK,IAAI,CAAC,UAAU;AAAA,IAC1B,MAAM;AAAA,EACP,EAAE;AACH;AAEO,SAAS,cACf,UACA,SACA,YACA,0BAMA,UACC;AACD,MAAI,aAAmC,CAAC;AAExC,MAAI,QAAQ,gBAAgB;AAC3B,aAAS,QAAQ,CAAC,OAAO,UAAU;AAClC,YAAM,OAAO;AAAA,QACZ,KAAK,WAAW,KAAK;AAAA,QACrB,MAAM;AAAA,MACP;AAEA,YAAM,gBAAgB,yBAAyB,SAAS,IAAI,GAAG;AAAA,QAC9D;AAAA,MACD,CAAC;AAED,mBAAa,WAAW,OAAO,aAAa;AAAA,IAC7C,CAAC;AAAA,EACF,OAAO;AACN,aACE,OAAO,CAAC,UAAU,MAAM,QAAQ,KAAK,CAAC,EACtC,QAAQ,CAAC,OAAO,UAAU;AAC1B,YAAM,gBAAgB,yBAAyB,SAAS,KAAK,GAAG;AAAA,QAC/D,UAAU,MAAM,QAAQ,QAAQ,IAAI,SAAS,KAAK,IAAI;AAAA,MACvD,CAAC;AAED,mBAAa,WAAW,OAAO,aAAa;AAAA,IAC7C,CAAC;AAAA,EACH;AAEA,MAAI,CAAC,WAAW,QAAQ;AACvB,QAAK,SAAS,cAAyB,KAAK;AAC3C,iBAAW,KAAK,EAAE,MAAM,EAAE,SAAS,KAAK,GAAG,YAAY,SAAS,CAAC;AAAA,IAClE,OAAO;AACN,YAAM,gBAAgB,WACpB,OAAO,CAAC,cAAc,CAAC,UAAU,WAAW,IAAI,CAAC,EACjD;AAAA,QAAM,CAAC,cACP,UACE,QAAQ,gBAAgB,EAAE,EAC1B,QAAQ,OAAO,EAAE,EACjB,YAAY,EACZ,WAAW,QAAQ;AAAA,MACtB;AAED,UAAI,CAAC,eAAe;AACnB,mBAAW,KAAK,EAAE,MAAM,EAAE,SAAS,KAAK,GAAG,YAAY,SAAS,CAAC;AAAA,MAClE;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AACA,MAAM,mBAAmB;AAClB,MAAM,yBAAyB,CAAC,OAAe,iBAAiB,SAAS;AAC/E,QAAM,aAAa,MACjB,QAAQ,OAAO,EAAE,EACjB,MAAM,gBAAgB,EACtB,IAAI,CAAC,cAAc,UAAU,KAAK,CAAC;AACrC,SAAO,iBAAiB,WAAW,OAAO,CAAC,cAAc,cAAc,EAAE,IAAI;AAC9E;AAEO,SAAS,qBAEf,SACA,MACC;AACD,SAAO,OAAO,YAA+B;AAC5C,QAAI,QAAQ,WAAW,GAAG;AACzB,aAAO,CAAC;AAAA,IACT;AAEA,QAAI,aAAmC,CAAC;AACxC,UAAM,OAAQ,QAAQ,iBAA+B,6BAAW;AAEhE,UAAM,aAAa,MAAM,KAAK,cAAc;AAE5C,QAAI,SAAS,6BAAW,QAAQ;AAC/B,YAAM,mBAAmB,QAAQ,IAAI,CAAC,EAAE,OAAO,OAAO,MAAM,WAAW,OAAO,OAAO,MAAM,CAAC;AAC5F,UAAI;AAEH,mBAAW,QAAQ;AAEnB,YAAI,cAAc;AAClB,YAAI,iBAAiB,SAAS,GAAG;AAChC,wBAAc,iBAAiB,IAAI,CAAC,UAAU,MAAM,KAAK,EAAE,QAAQ,MAAM,EAAE,CAAC,EAAE,KAAK,GAAG;AAAA,QACvF,OAAO;AACN,wBAAc,iBAAiB,CAAC;AAAA,QACjC;AAEA,YAAI,YACH,MAAM,KAAK,MAAM,WAAW,GAC3B,CAAC;AAEH,YAAI,CAAC,SAAU,QAAO,CAAC;AAEvB,YAAI;AACJ,YAAK,SAAS,eAA0B,KAAK;AAC5C,uBAAa,YACX,QAAQ,OAAO,EAAE,EACjB,MAAM,GAAG,EACT,OAAO,CAAC,cAAc,cAAc,EAAE;AAAA,QACzC,OAAO;AACN,uBAAa,uBAAuB,WAAW;AAAA,QAChD;AAEA,YAAI,MAAM,QAAQ,QAAQ,GAAG;AAC5B,cAAI,WAAW,WAAW,EAAG,YAAW,CAAC,QAAQ;AAAA,QAClD,OAAO;AACN,qBAAW,CAAC,QAAQ;AAAA,QACrB;AAGA,cAAM,iBAAa,yCAAuB,QAAQ,MAAM;AAExD,qBAAa,WAAW;AAAA,UACvB;AAAA,YACC;AAAA,YACA;AAAA,YACA;AAAA,YACA,KAAK,QAAQ;AAAA,YACb;AAAA,UACD;AAAA,QACD;AAAA,MACD,SAAS,KAAK;AACb,cAAM,QAAQ,gBAAgB,KAAK,MAAM,KAAK,GAAG,gBAAgB;AAEjE,YAAI,CAAC,KAAK,eAAe,EAAG,OAAM;AAClC,mBAAW,KAAK,EAAE,MAAM,EAAE,SAAS,MAAM,SAAS,OAAO,EAAE,GAAG,MAAM,EAAE,EAAE,CAAC;AAAA,MAC1E;AAAA,IACD,OAAO;AACN,UAAI,SAAS,6BAAW,eAAe;AACtC,YAAI,iBAAiB;AACrB,mBAAW,CAAC,OAAO,eAAe,KAAK,QAAQ,QAAQ,GAAG;AACzD,cAAI;AACH,kBAAM,EAAE,OAAO,OAAO,IAAI;AAC1B,6BAAiB,WAAW,OAAO,OAAO,MAAM;AAEhD,gBAAI;AACJ,gBAAK,SAAS,eAA0B,KAAK;AAC5C,2BAAa,eAAe,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;AAAA,YAC3D,OAAO;AACN,2BAAa,uBAAuB,gBAAgB,KAAK;AAAA,YAC1D;AAEA,kBAAM,YAA2B,CAAC;AAClC,uBAAW,aAAa,YAAY;AACnC,kBAAI,cAAc,GAAI;AACtB,oBAAM,YAAY,MAAM,WAAW,MAAM,SAAS,GAAG,CAAC;AAEtD,wBAAU,KAAK,QAAQ;AAAA,YACxB;AAEA,yBAAa,WAAW;AAAA,cACvB;AAAA,gBACC;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,KAAK,QAAQ;AAAA,gBACb,EAAE,MAAM,MAAM;AAAA,cACf;AAAA,YACD;AAAA,UACD,SAAS,KAAK;AACb,kBAAM,QAAQ,gBAAgB,KAAK,MAAM,KAAK,OAAO,CAAC,cAAc,CAAC;AAErE,gBAAI,CAAC,KAAK,eAAe,GAAG;AAC3B,yBAAW,QAAQ;AACnB,oBAAM;AAAA,YACP;AACA,uBAAW,KAAK,iBAAiB,QAAQ,KAAK,GAAG,OAAgB,KAAK,CAAC;AAAA,UACxE;AAAA,QACD;AAAA,MACD;AAEA,UAAI,SAAS,6BAAW,aAAa;AACpC,cAAM,WAAW,iBAAiB;AAElC,YAAI,iBAAiB;AACrB,mBAAW,CAAC,OAAO,eAAe,KAAK,QAAQ,QAAQ,GAAG;AACzD,cAAI;AACH,kBAAM,EAAE,OAAO,OAAO,IAAI;AAC1B,6BAAiB,WAAW,OAAO,OAAO,MAAM;AAEhD,gBAAI;AACJ,gBAAK,SAAS,eAA0B,KAAK;AAC5C,2BAAa,eAAe,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;AAAA,YAC3D,OAAO;AACN,2BAAa,uBAAuB,gBAAgB,KAAK;AAAA,YAC1D;AAEA,kBAAM,YAA2B,CAAC;AAClC,uBAAW,aAAa,YAAY;AACnC,kBAAI,cAAc,GAAI;AACtB,oBAAM,YAAY,MAAM,WAAW,MAAM,SAAS,GAAG,CAAC;AAEtD,wBAAU,KAAK,QAAQ;AAAA,YACxB;AAEA,yBAAa,WAAW;AAAA,cACvB;AAAA,gBACC;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,KAAK,QAAQ;AAAA,gBACb,EAAE,MAAM,MAAM;AAAA,cACf;AAAA,YACD;AAAA,UACD,SAAS,KAAK;AACb,kBAAM,QAAQ,gBAAgB,KAAK,MAAM,KAAK,OAAO,CAAC,cAAc,CAAC;AAErE,gBAAI,YAAY;AACf,oBAAM,WAAW,SAAS;AAC1B,yBAAW,QAAQ;AAAA,YACpB;AAEA,gBAAI,CAAC,KAAK,eAAe,EAAG,OAAM;AAClC,uBAAW,KAAK,iBAAiB,QAAQ,KAAK,GAAG,OAAgB,KAAK,CAAC;AAGvE,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,cAAM,WAAW,OAAO;AAAA,MACzB;AAEA,iBAAW,QAAQ;AAAA,IACpB;AAEA,WAAO;AAAA,EACR;AACD;AAEO,SAAS,gBACf,MACA,WACA,OACA,SACA,cACA,mBACwB;AACxB,MAAI,QAAQ,WAAW,EAAG,QAAO,CAAC,OAAO,YAAY;AAErD,MAAI,cAAc;AAElB,MAAI,sBAAsB,MAAM;AAC/B,kBAAc;AAAA,EACf;AAEA,MAAI,aAAa;AACjB,QAAM,SAAsB,CAAC;AAE7B,UAAQ,QAAQ,CAAC,QAAQ,UAAU;AAClC,QAAI,OAAO,cAAc,SAAS;AACjC,aAAO,YAAY;AAAA,IACpB;AAEA,QAAI,CAAC,KAAK,KAAK,MAAM,IAAI,EAAE,SAAS,OAAO,SAAS,GAAG;AACtD,YAAM,QAAQ,OAAO,OAAO,KAAK;AAEjC,UAAI,OAAO,MAAM,KAAK,GAAG;AACxB,cAAM,IAAI;AAAA,UACT;AAAA,UACA,qBAAqB,QAAQ,CAAC,mDAC7B,OAAO,KACR;AAAA,UACA;AAAA,YACC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,aAAO,QAAQ;AAAA,IAChB;AAEA,QAAI,mBAAmB;AACvB,QAAI,OAAO,cAAc,aAAa,OAAO,cAAc,eAAe;AACzE,yBAAmB;AACnB,aAAO,KAAK,OAAO,KAAK;AAAA,IACzB;AAEA,UAAM,WAAW,UAAU,QAAQ,SAAS,IAAI,KAAK,IAAI,WAAW;AAEpE,kBAAc,IAAI,oBAAoB,OAAO,MAAM,CAAC,IACnD,OAAO,SACR,GAAG,gBAAgB,GAAG,QAAQ;AAAA,EAC/B,CAAC;AAED,SAAO,CAAC,GAAG,KAAK,GAAG,UAAU,IAAI,aAAa,OAAO,GAAG,MAAM,CAAC;AAChE;AAEO,SAAS,aACf,OACA,OACA,cACwB;AACxB,MAAI,MAAM,WAAW,EAAG,QAAO,CAAC,OAAO,YAAY;AAEnD,MAAI,eAAe;AACnB,QAAM,SAAmB,CAAC;AAE1B,QAAM,QAAQ,CAAC,MAAM,UAAU;AAC9B,UAAM,UAAU,UAAU,MAAM,SAAS,IAAI,KAAK;AAElD,oBAAgB,IAAI,oBAAoB,KAAK,MAAM,CAAC,IAAI,KAAK,SAAS,GAAG,OAAO;AAAA,EACjF,CAAC;AAED,SAAO,CAAC,GAAG,KAAK,GAAG,YAAY,IAAI,aAAa,OAAO,GAAG,MAAM,CAAC;AAClE;AAEO,SAAS,2BACf,OACA,SACuB;AACvB,MAAI,CAAC,QAAS,QAAO,CAAC,GAAG,KAAK;AAE9B,QAAM,aAAmC,MAAM,IAAI,CAAC,SAAS;AAC5D,UAAM,UAAU,EAAE,GAAG,KAAK;AAC1B,UAAM,OAAO,OAAO,KAAK,QAAQ,IAAI;AAErC,eAAW,OAAO,MAAM;AACvB,UAAI,QAAQ,KAAK,GAAG,MAAM,IAAI;AAC7B,gBAAQ,KAAK,GAAG,IAAI;AAAA,MACrB;AAAA,IACD;AAEA,WAAO;AAAA,EACR,CAAC;AAED,SAAO;AACR;", "names": []}