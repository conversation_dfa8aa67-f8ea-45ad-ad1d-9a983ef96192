{"version": 3, "sources": ["../../../nodes/TravisCi/TravisCi.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tIDataObject,\n\tINodeExecutionData,\n\tINodeType,\n\tINodeTypeDescription,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes } from 'n8n-workflow';\n\nimport { buildFields, buildOperations } from './BuildDescription';\nimport { travisciApiRequest, travisciApiRequestAllItems } from './GenericFunctions';\n\nexport class <PERSON><PERSON>i implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'TravisCI',\n\t\tname: 'travisCi',\n\t\t// eslint-disable-next-line n8n-nodes-base/node-class-description-icon-not-svg\n\t\ticon: 'file:travisci.png',\n\t\tgroup: ['output'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume TravisCI API',\n\t\tdefaults: {\n\t\t\tname: 'TravisCI',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'travisCiApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Build',\n\t\t\t\t\t\tvalue: 'build',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'build',\n\t\t\t},\n\t\t\t...buildOperations,\n\t\t\t...buildFields,\n\t\t],\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: INodeExecutionData[] = [];\n\t\tconst length = items.length;\n\t\tconst qs: IDataObject = {};\n\t\tlet responseData;\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'build') {\n\t\t\t\t\t//https://developer.travis-ci.com/resource/build#find\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tconst buildId = this.getNodeParameter('buildId', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (additionalFields.include) {\n\t\t\t\t\t\t\tqs.include = additionalFields.include as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await travisciApiRequest.call(this, 'GET', `/build/${buildId}`, {}, qs);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.travis-ci.com/resource/builds#for_current_user\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tif (additionalFields.sortBy) {\n\t\t\t\t\t\t\tqs.sort_by = additionalFields.sortBy;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.sortBy && additionalFields.order) {\n\t\t\t\t\t\t\tqs.sort_by = `${additionalFields.sortBy}:${additionalFields.order}`;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.include) {\n\t\t\t\t\t\t\tqs.include = additionalFields.include;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await travisciApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'builds',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/builds',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tqs.limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = await travisciApiRequest.call(this, 'GET', '/builds', {}, qs);\n\t\t\t\t\t\t\tresponseData = responseData.builds;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.travis-ci.com/resource/build#cancel\n\t\t\t\t\tif (operation === 'cancel') {\n\t\t\t\t\t\tconst buildId = this.getNodeParameter('buildId', i) as string;\n\t\t\t\t\t\tresponseData = await travisciApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t`/build/${buildId}/cancel`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.travis-ci.com/resource/build#restart\n\t\t\t\t\tif (operation === 'restart') {\n\t\t\t\t\t\tconst buildId = this.getNodeParameter('buildId', i) as string;\n\t\t\t\t\t\tresponseData = await travisciApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t`/build/${buildId}/restart`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.travis-ci.com/resource/requests#create\n\t\t\t\t\tif (operation === 'trigger') {\n\t\t\t\t\t\tlet slug = this.getNodeParameter('slug', i) as string;\n\t\t\t\t\t\tconst branch = this.getNodeParameter('branch', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tslug = slug.replace(new RegExp(/\\//g), '%2F');\n\n\t\t\t\t\t\tconst request: IDataObject = {\n\t\t\t\t\t\t\tbranch,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (additionalFields.message) {\n\t\t\t\t\t\t\trequest.message = additionalFields.message as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.mergeMode) {\n\t\t\t\t\t\t\trequest.merge_mode = additionalFields.mergeMode as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await travisciApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t`/repo/${slug}/requests`,\n\t\t\t\t\t\t\tJSON.stringify({ request }),\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\tthis.helpers.returnJsonArray(responseData as IDataObject[]),\n\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t);\n\n\t\t\t\treturnData.push(...executionData);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\tconst executionErrorData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\t\tthis.helpers.returnJsonArray({ error: error.message }),\n\t\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t\t);\n\t\t\t\t\treturnData.push(...executionErrorData);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,0BAAoC;AAEpC,8BAA6C;AAC7C,8BAA+D;AAExD,MAAM,SAA8B;AAAA,EAApC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA;AAAA,MAEN,MAAM;AAAA,MACN,OAAO,CAAC,QAAQ;AAAA,MAChB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAAmC,CAAC;AAC1C,UAAM,SAAS,MAAM;AACrB,UAAM,KAAkB,CAAC;AACzB,QAAI;AACJ,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,YAAI,aAAa,SAAS;AAEzB,cAAI,cAAc,OAAO;AACxB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,iBAAiB,SAAS;AAC7B,iBAAG,UAAU,iBAAiB;AAAA,YAC/B;AAEA,2BAAe,MAAM,2CAAmB,KAAK,MAAM,OAAO,UAAU,OAAO,IAAI,CAAC,GAAG,EAAE;AAAA,UACtF;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,gBAAI,iBAAiB,QAAQ;AAC5B,iBAAG,UAAU,iBAAiB;AAAA,YAC/B;AAEA,gBAAI,iBAAiB,UAAU,iBAAiB,OAAO;AACtD,iBAAG,UAAU,GAAG,iBAAiB,MAAM,IAAI,iBAAiB,KAAK;AAAA,YAClE;AAEA,gBAAI,iBAAiB,SAAS;AAC7B,iBAAG,UAAU,iBAAiB;AAAA,YAC/B;AAEA,gBAAI,WAAW;AACd,6BAAe,MAAM,mDAA2B;AAAA,gBAC/C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,iBAAG,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC3C,6BAAe,MAAM,2CAAmB,KAAK,MAAM,OAAO,WAAW,CAAC,GAAG,EAAE;AAC3E,6BAAe,aAAa;AAAA,YAC7B;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,2BAAe,MAAM,2CAAmB;AAAA,cACvC;AAAA,cACA;AAAA,cACA,UAAU,OAAO;AAAA,cACjB,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,WAAW;AAC5B,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,2BAAe,MAAM,2CAAmB;AAAA,cACvC;AAAA,cACA;AAAA,cACA,UAAU,OAAO;AAAA,cACjB,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,WAAW;AAC5B,gBAAI,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC1C,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,mBAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,GAAG,KAAK;AAE5C,kBAAM,UAAuB;AAAA,cAC5B;AAAA,YACD;AAEA,gBAAI,iBAAiB,SAAS;AAC7B,sBAAQ,UAAU,iBAAiB;AAAA,YACpC;AAEA,gBAAI,iBAAiB,WAAW;AAC/B,sBAAQ,aAAa,iBAAiB;AAAA,YACvC;AAEA,2BAAe,MAAM,2CAAmB;AAAA,cACvC;AAAA,cACA;AAAA,cACA,SAAS,IAAI;AAAA,cACb,KAAK,UAAU,EAAE,QAAQ,CAAC;AAAA,YAC3B;AAAA,UACD;AAAA,QACD;AAEA,cAAM,gBAAgB,KAAK,QAAQ;AAAA,UAClC,KAAK,QAAQ,gBAAgB,YAA6B;AAAA,UAC1D,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,QACzB;AAEA,mBAAW,KAAK,GAAG,aAAa;AAAA,MACjC,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,gBAAM,qBAAqB,KAAK,QAAQ;AAAA,YACvC,KAAK,QAAQ,gBAAgB,EAAE,OAAO,MAAM,QAAQ,CAAC;AAAA,YACrD,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,UACzB;AACA,qBAAW,KAAK,GAAG,kBAAkB;AACrC;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AACA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": []}