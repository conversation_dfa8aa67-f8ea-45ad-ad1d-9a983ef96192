{"version": 3, "sources": ["../../../nodes/WooCommerce/WooCommerceTrigger.node.ts"], "sourcesContent": ["import { createHmac } from 'crypto';\nimport type {\n\tIHookFunctions,\n\tIWebhookFunctions,\n\tIDataObject,\n\tINodeType,\n\tINodeTypeDescription,\n\tIWebhookResponseData,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes } from 'n8n-workflow';\n\nimport { getAutomaticSecret, woocommerceApiRequest } from './GenericFunctions';\n\nexport class WooCommerceTrigger implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'WooCommerce Trigger',\n\t\tname: 'wooCommerceTrigger',\n\t\ticon: 'file:wooCommerce.svg',\n\t\tgroup: ['trigger'],\n\t\tversion: 1,\n\t\tdescription: 'Handle WooCommerce events via webhooks',\n\t\tdefaults: {\n\t\t\tname: 'WooCommerce Trigger',\n\t\t},\n\t\tinputs: [],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'wooCommerceApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\twebhooks: [\n\t\t\t{\n\t\t\t\tname: 'default',\n\t\t\t\thttpMethod: 'POST',\n\t\t\t\tresponseMode: 'onReceived',\n\t\t\t\tpath: 'webhook',\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Event',\n\t\t\t\tname: 'event',\n\t\t\t\ttype: 'options',\n\t\t\t\trequired: true,\n\t\t\t\tdefault: '',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'coupon.created',\n\t\t\t\t\t\tvalue: 'coupon.created',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'coupon.deleted',\n\t\t\t\t\t\tvalue: 'coupon.deleted',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'coupon.updated',\n\t\t\t\t\t\tvalue: 'coupon.updated',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'customer.created',\n\t\t\t\t\t\tvalue: 'customer.created',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'customer.deleted',\n\t\t\t\t\t\tvalue: 'customer.deleted',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'customer.updated',\n\t\t\t\t\t\tvalue: 'customer.updated',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'order.created',\n\t\t\t\t\t\tvalue: 'order.created',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'order.deleted',\n\t\t\t\t\t\tvalue: 'order.deleted',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'order.updated',\n\t\t\t\t\t\tvalue: 'order.updated',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'product.created',\n\t\t\t\t\t\tvalue: 'product.created',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'product.deleted',\n\t\t\t\t\t\tvalue: 'product.deleted',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'product.updated',\n\t\t\t\t\t\tvalue: 'product.updated',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdescription: 'Determines which resource events the webhook is triggered for',\n\t\t\t},\n\t\t],\n\t};\n\n\twebhookMethods = {\n\t\tdefault: {\n\t\t\tasync checkExists(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\tconst currentEvent = this.getNodeParameter('event') as string;\n\t\t\t\tconst endpoint = '/webhooks';\n\n\t\t\t\tconst webhooks = await woocommerceApiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\tendpoint,\n\t\t\t\t\t{},\n\t\t\t\t\t{ status: 'active', per_page: 100 },\n\t\t\t\t);\n\n\t\t\t\tfor (const webhook of webhooks) {\n\t\t\t\t\tif (\n\t\t\t\t\t\twebhook.status === 'active' &&\n\t\t\t\t\t\twebhook.delivery_url === webhookUrl &&\n\t\t\t\t\t\twebhook.topic === currentEvent\n\t\t\t\t\t) {\n\t\t\t\t\t\twebhookData.webhookId = webhook.id;\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tasync create(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst credentials = await this.getCredentials('wooCommerceApi');\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\tconst event = this.getNodeParameter('event') as string;\n\t\t\t\tconst secret = getAutomaticSecret(credentials);\n\t\t\t\tconst endpoint = '/webhooks';\n\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\tdelivery_url: webhookUrl,\n\t\t\t\t\ttopic: event,\n\t\t\t\t\tsecret,\n\t\t\t\t};\n\t\t\t\tconst { id } = await woocommerceApiRequest.call(this, 'POST', endpoint, body);\n\t\t\t\twebhookData.webhookId = id;\n\t\t\t\twebhookData.secret = secret;\n\t\t\t\treturn true;\n\t\t\t},\n\t\t\tasync delete(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\tconst endpoint = `/webhooks/${webhookData.webhookId}`;\n\t\t\t\ttry {\n\t\t\t\t\tawait woocommerceApiRequest.call(this, 'DELETE', endpoint, {}, { force: true });\n\t\t\t\t} catch (error) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tdelete webhookData.webhookId;\n\t\t\t\tdelete webhookData.secret;\n\t\t\t\treturn true;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync webhook(this: IWebhookFunctions): Promise<IWebhookResponseData> {\n\t\tconst req = this.getRequestObject();\n\t\tconst headerData = this.getHeaderData();\n\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\tif (headerData['x-wc-webhook-id'] === undefined) {\n\t\t\treturn {};\n\t\t}\n\n\t\tconst computedSignature = createHmac('sha256', webhookData.secret as string)\n\t\t\t.update(req.rawBody)\n\t\t\t.digest('base64');\n\t\tif (headerData['x-wc-webhook-signature'] !== computedSignature) {\n\t\t\t// Signature is not valid so ignore call\n\t\t\treturn {};\n\t\t}\n\t\treturn {\n\t\t\tworkflowData: [this.helpers.returnJsonArray(req.body as IDataObject)],\n\t\t};\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAS3B,0BAAoC;AAEpC,8BAA0D;AAEnD,MAAM,mBAAwC;AAAA,EAA9C;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,SAAS;AAAA,MACjB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,UAAU;AAAA,QACT;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,MAAM;AAAA,QACP;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS;AAAA,UACT,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,aAAa;AAAA,QACd;AAAA,MACD;AAAA,IACD;AAEA,0BAAiB;AAAA,MAChB,SAAS;AAAA,QACR,MAAM,cAAoD;AACzD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AACnD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,gBAAM,eAAe,KAAK,iBAAiB,OAAO;AAClD,gBAAM,WAAW;AAEjB,gBAAM,WAAW,MAAM,8CAAsB;AAAA,YAC5C;AAAA,YACA;AAAA,YACA;AAAA,YACA,CAAC;AAAA,YACD,EAAE,QAAQ,UAAU,UAAU,IAAI;AAAA,UACnC;AAEA,qBAAW,WAAW,UAAU;AAC/B,gBACC,QAAQ,WAAW,YACnB,QAAQ,iBAAiB,cACzB,QAAQ,UAAU,cACjB;AACD,0BAAY,YAAY,QAAQ;AAChC,qBAAO;AAAA,YACR;AAAA,UACD;AACA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,cAAc,MAAM,KAAK,eAAe,gBAAgB;AAC9D,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AACnD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,gBAAM,QAAQ,KAAK,iBAAiB,OAAO;AAC3C,gBAAM,aAAS,4CAAmB,WAAW;AAC7C,gBAAM,WAAW;AACjB,gBAAM,OAAoB;AAAA,YACzB,cAAc;AAAA,YACd,OAAO;AAAA,YACP;AAAA,UACD;AACA,gBAAM,EAAE,GAAG,IAAI,MAAM,8CAAsB,KAAK,MAAM,QAAQ,UAAU,IAAI;AAC5E,sBAAY,YAAY;AACxB,sBAAY,SAAS;AACrB,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,gBAAM,WAAW,aAAa,YAAY,SAAS;AACnD,cAAI;AACH,kBAAM,8CAAsB,KAAK,MAAM,UAAU,UAAU,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,UAC/E,SAAS,OAAO;AACf,mBAAO;AAAA,UACR;AACA,iBAAO,YAAY;AACnB,iBAAO,YAAY;AACnB,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAgE;AACrE,UAAM,MAAM,KAAK,iBAAiB;AAClC,UAAM,aAAa,KAAK,cAAc;AACtC,UAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,QAAI,WAAW,iBAAiB,MAAM,QAAW;AAChD,aAAO,CAAC;AAAA,IACT;AAEA,UAAM,wBAAoB,0BAAW,UAAU,YAAY,MAAgB,EACzE,OAAO,IAAI,OAAO,EAClB,OAAO,QAAQ;AACjB,QAAI,WAAW,wBAAwB,MAAM,mBAAmB;AAE/D,aAAO,CAAC;AAAA,IACT;AACA,WAAO;AAAA,MACN,cAAc,CAAC,KAAK,QAAQ,gBAAgB,IAAI,IAAmB,CAAC;AAAA,IACrE;AAAA,EACD;AACD;", "names": []}