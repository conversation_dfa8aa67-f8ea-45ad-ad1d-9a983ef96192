{"version": 3, "sources": ["../../../../../nodes/Airtable/v2/actions/versionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport { NodeConnectionTypes, type INodeTypeDescription } from 'n8n-workflow';\n\nimport * as base from './base/Base.resource';\nimport * as record from './record/Record.resource';\n\nexport const versionDescription: INodeTypeDescription = {\n\tdisplayName: 'Airtable',\n\tname: 'airtable',\n\ticon: 'file:airtable.svg',\n\tgroup: ['input'],\n\tversion: [2, 2.1],\n\tsubtitle: '={{ $parameter[\"operation\"] + \": \" + $parameter[\"resource\"] }}',\n\tdescription: 'Read, update, write and delete data from Airtable',\n\tdefaults: {\n\t\tname: 'Airtable',\n\t},\n\tinputs: [NodeConnectionTypes.Main],\n\toutputs: [NodeConnectionTypes.Main],\n\tcredentials: [\n\t\t{\n\t\t\tname: 'airtableTokenApi',\n\t\t\trequired: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tauthentication: ['airtableTokenApi'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\t{\n\t\t\tname: 'airtableOAuth2Api',\n\t\t\trequired: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tauthentication: ['airtableOAuth2Api'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t],\n\tproperties: [\n\t\t{\n\t\t\tdisplayName: 'Authentication',\n\t\t\tname: 'authentication',\n\t\t\ttype: 'options',\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Access Token',\n\t\t\t\t\tvalue: 'airtableTokenApi',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'OAuth2',\n\t\t\t\t\tvalue: 'airtableOAuth2Api',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'airtableTokenApi',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Base',\n\t\t\t\t\tvalue: 'base',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Record',\n\t\t\t\t\tvalue: 'record',\n\t\t\t\t},\n\t\t\t\t// {\n\t\t\t\t// \tname: 'Table',\n\t\t\t\t// \tvalue: 'table',\n\t\t\t\t// },\n\t\t\t],\n\t\t\tdefault: 'record',\n\t\t},\n\t\t...record.description,\n\t\t...base.description,\n\t],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAA+D;AAE/D,WAAsB;AACtB,aAAwB;AAEjB,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,OAAO;AAAA,EACf,SAAS,CAAC,GAAG,GAAG;AAAA,EAChB,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,aAAa;AAAA,IACZ;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,gBAAgB,CAAC,kBAAkB;AAAA,QACpC;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,gBAAgB,CAAC,mBAAmB;AAAA,QACrC;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA,YAAY;AAAA,IACX;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA,GAAG,OAAO;AAAA,IACV,GAAG,KAAK;AAAA,EACT;AACD;", "names": []}