{"version": 3, "sources": ["../../../../../nodes/QuickBooks/descriptions/Vendor/VendorAdditionalFieldsOptions.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const vendorAdditionalFieldsOptions: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Account Number',\n\t\tname: 'AcctNum',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Active',\n\t\tname: 'Active',\n\t\tdescription: 'Whether the employee is currently enabled for use by QuickBooks',\n\t\ttype: 'boolean',\n\t\tdefault: false,\n\t},\n\t{\n\t\tdisplayName: 'Balance',\n\t\tname: 'Balance',\n\t\tdescription: 'The balance reflecting any payments made against the transaction',\n\t\ttype: 'number',\n\t\tdefault: 0,\n\t},\n\t{\n\t\tdisplayName: 'Billing Address',\n\t\tname: 'BillAddr',\n\t\tplaceholder: 'Add Billing Address Fields',\n\t\ttype: 'fixedCollection',\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Details',\n\t\t\t\tname: 'details',\n\t\t\t\tvalues: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'City',\n\t\t\t\t\t\tname: 'City',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Line 1',\n\t\t\t\t\t\tname: 'Line1',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Postal Code',\n\t\t\t\t\t\tname: 'PostalCode',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Latitude',\n\t\t\t\t\t\tname: 'Lat',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Longitude',\n\t\t\t\t\t\tname: 'Long',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Country Subdivision Code',\n\t\t\t\t\t\tname: 'CountrySubDivisionCode',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Company Name',\n\t\tname: 'CompanyName',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Family Name',\n\t\tname: 'FamilyName',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Given Name',\n\t\tname: 'GivenName',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Primary Email Address',\n\t\tname: 'PrimaryEmailAddr',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Primary Phone',\n\t\tname: 'PrimaryPhone',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Print-On-Check Name',\n\t\tname: 'PrintOnCheckName',\n\t\tdescription: 'Name of the vendor as printed on a check',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Vendor 1099',\n\t\tname: 'Vendor1099',\n\t\tdescription:\n\t\t\t'Whether the vendor is an independent contractor, given a 1099-MISC form at the end of the year',\n\t\ttype: 'boolean',\n\t\tdefault: false,\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,gCAAmD;AAAA,EAC/D;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,QAAQ;AAAA,UACP;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aACC;AAAA,IACD,MAAM;AAAA,IACN,SAAS;AAAA,EACV;AACD;", "names": []}