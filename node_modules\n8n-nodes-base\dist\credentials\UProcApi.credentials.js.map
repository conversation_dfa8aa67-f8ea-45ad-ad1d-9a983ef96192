{"version": 3, "sources": ["../../credentials/UProcApi.credentials.ts"], "sourcesContent": ["import type {\n\tICredentialDataDecryptedObject,\n\tICredentialTestRequest,\n\tICredentialType,\n\tIHttpRequestOptions,\n\tINodeProperties,\n} from 'n8n-workflow';\n\nexport class UProcApi implements ICredentialType {\n\tname = 'uprocApi';\n\n\tdisplayName = 'uProc API';\n\n\tdocumentationUrl = 'uProc';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Email',\n\t\t\tname: 'email',\n\t\t\ttype: 'string',\n\t\t\tplaceholder: '<EMAIL>',\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'API Key',\n\t\t\tname: 'apiKey',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\tdefault: '',\n\t\t},\n\t];\n\n\tasync authenticate(\n\t\tcredentials: ICredentialDataDecryptedObject,\n\t\trequestOptions: IHttpRequestOptions,\n\t): Promise<IHttpRequestOptions> {\n\t\tconst token = Buffer.from(`${credentials.email}:${credentials.apiKey}`).toString('base64');\n\t\trequestOptions.headers = {\n\t\t\t...requestOptions.headers,\n\t\t\tAuthorization: `Basic ${token}`,\n\t\t};\n\t\treturn requestOptions;\n\t}\n\n\ttest: ICredentialTestRequest = {\n\t\trequest: {\n\t\t\tbaseURL: 'https://api.uproc.io/api/v2',\n\t\t\turl: '/profile',\n\t\t\tmethod: 'GET',\n\t\t},\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAQO,MAAM,SAAoC;AAAA,EAA1C;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,MACV;AAAA,IACD;AAcA,gBAA+B;AAAA,MAC9B,SAAS;AAAA,QACR,SAAS;AAAA,QACT,KAAK;AAAA,QACL,QAAQ;AAAA,MACT;AAAA,IACD;AAAA;AAAA,EAlBA,MAAM,aACL,aACA,gBAC+B;AAC/B,UAAM,QAAQ,OAAO,KAAK,GAAG,YAAY,KAAK,IAAI,YAAY,MAAM,EAAE,EAAE,SAAS,QAAQ;AACzF,mBAAe,UAAU;AAAA,MACxB,GAAG,eAAe;AAAA,MAClB,eAAe,SAAS,KAAK;AAAA,IAC9B;AACA,WAAO;AAAA,EACR;AASD;", "names": []}