{"version": 3, "sources": ["../../../../../../nodes/SeaTable/v2/actions/row/unlock.operation.ts"], "sourcesContent": ["import type { IDataObject, INodeExecutionData, IExecuteFunctions } from 'n8n-workflow';\n\nimport { seaTableApiRequest } from '../../GenericFunctions';\n\nexport async function execute(\n\tthis: IExecuteFunctions,\n\tindex: number,\n): Promise<INodeExecutionData[]> {\n\tconst tableName = this.getNodeParameter('tableName', index) as string;\n\tconst rowId = this.getNodeParameter('rowId', index) as string;\n\n\tconst responseData = await seaTableApiRequest.call(\n\t\tthis,\n\t\t{},\n\t\t'PUT',\n\t\t'/api-gateway/api/v2/dtables/{{dtable_uuid}}/unlock-rows/',\n\t\t{\n\t\t\ttable_name: tableName,\n\t\t\trow_ids: [rowId],\n\t\t},\n\t);\n\n\treturn this.helpers.returnJsonArray(responseData as IDataObject[]);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,8BAAmC;AAEnC,eAAsB,QAErB,OACgC;AAChC,QAAM,YAAY,KAAK,iBAAiB,aAAa,KAAK;AAC1D,QAAM,QAAQ,KAAK,iBAAiB,SAAS,KAAK;AAElD,QAAM,eAAe,MAAM,2CAAmB;AAAA,IAC7C;AAAA,IACA,CAAC;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,MACC,YAAY;AAAA,MACZ,SAAS,CAAC,KAAK;AAAA,IAChB;AAAA,EACD;AAEA,SAAO,KAAK,QAAQ,gBAAgB,YAA6B;AAClE;", "names": []}