{"version": 3, "sources": ["../../../nodes/Intercom/UserDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const userOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Create',\n\t\t\t\tvalue: 'create',\n\t\t\t\tdescription: 'Create a new user',\n\t\t\t\taction: 'Create a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'delete',\n\t\t\t\tdescription: 'Delete a user',\n\t\t\t\taction: 'Delete a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\tdescription: 'Get data of a user',\n\t\t\t\taction: 'Get a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Get data of many users',\n\t\t\t\taction: 'Get many users',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Update',\n\t\t\t\tvalue: 'update',\n\t\t\t\tdescription: 'Update a user',\n\t\t\t\taction: 'Update a user',\n\t\t\t},\n\t\t],\n\t\tdefault: 'create',\n\t},\n];\n\nexport const userFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 user:delete                                */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'ID',\n\t\tname: 'id',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['delete'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription: 'The Intercom defined ID representing the Lead',\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                  user:getAll                                 */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t\tmaxValue: 60,\n\t\t},\n\t\tdefault: 50,\n\t\tdescription: 'Max number of results to return',\n\t},\n\t{\n\t\tdisplayName: 'Filters',\n\t\tname: 'filters',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Filter',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Company ID',\n\t\t\t\tname: 'company_id',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Company ID representing the user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Email',\n\t\t\t\tname: 'email',\n\t\t\t\ttype: 'string',\n\t\t\t\tplaceholder: '<EMAIL>',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The email address of the user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Segment ID',\n\t\t\t\tname: 'segment_id',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Segment representing the user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Tag ID',\n\t\t\t\tname: 'tag_id',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Tag representing the user',\n\t\t\t},\n\t\t],\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                  user:get                                 */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Select By',\n\t\tname: 'selectBy',\n\t\ttype: 'options',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'ID',\n\t\t\t\tvalue: 'id',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The Intercom defined ID representing the Lead',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'User ID',\n\t\t\t\tvalue: 'userId',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Automatically generated identifier for the Lead',\n\t\t\t},\n\t\t],\n\t\tdefault: '',\n\t\tdescription: 'The property to select the user by',\n\t},\n\t{\n\t\tdisplayName: 'Value',\n\t\tname: 'value',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'View by value',\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 user:update                                */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Update By',\n\t\tname: 'updateBy',\n\t\ttype: 'options',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'ID',\n\t\t\t\tvalue: 'id',\n\t\t\t\tdescription: 'The Intercom defined ID representing the user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Email',\n\t\t\t\tvalue: 'email',\n\t\t\t\tdescription: 'The email address of user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'User ID',\n\t\t\t\tvalue: 'userId',\n\t\t\t\tdescription: 'Automatically generated identifier for the user',\n\t\t\t},\n\t\t],\n\t\tdefault: 'id',\n\t\tdescription: 'The property via which to query the user',\n\t},\n\t{\n\t\tdisplayName: 'Value',\n\t\tname: 'value',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'Value of the property to identify the user to update',\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 user:create                                */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Identifier Type',\n\t\tname: 'identifierType',\n\t\ttype: 'options',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'User ID',\n\t\t\t\tvalue: 'userId',\n\t\t\t\tdescription:\n\t\t\t\t\t'A unique string identifier for the user. It is required on creation if an email is not supplied.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Email',\n\t\t\t\tvalue: 'email',\n\t\t\t\tdescription:\n\t\t\t\t\t\"The user's email address. It is required on creation if a user_id is not supplied.\",\n\t\t\t},\n\t\t],\n\t\tdefault: '',\n\t\tdescription: 'Unique string identifier',\n\t},\n\t{\n\t\tdisplayName: 'Value',\n\t\tname: 'idValue',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'Unique string identifier value',\n\t},\n\t{\n\t\tdisplayName: 'JSON Parameters',\n\t\tname: 'jsonParameters',\n\t\ttype: 'boolean',\n\t\tdefault: false,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['create', 'update'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['create', 'update'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Avatar',\n\t\t\t\tname: 'avatar',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'An avatar image URL. note: the image URL needs to be https.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Company Names or IDs',\n\t\t\t\tname: 'companies',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getCompanies',\n\t\t\t\t},\n\t\t\t\tdefault: [],\n\t\t\t\tdescription:\n\t\t\t\t\t'Identifies the companies this user belongs to. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Email',\n\t\t\t\tname: 'email',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\t'/operation': ['update'],\n\t\t\t\t\t\t'/resource': ['user'],\n\t\t\t\t\t},\n\t\t\t\t\thide: {\n\t\t\t\t\t\t'/updateBy': ['email'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tplaceholder: '<EMAIL>',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Email of the user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Name',\n\t\t\t\tname: 'name',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: '',\n\t\t\t\tdescription: 'Name of the user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Phone',\n\t\t\t\tname: 'phone',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The phone number of the user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Session Count',\n\t\t\t\tname: 'sessionCount',\n\t\t\t\ttype: 'number',\n\t\t\t\tdefault: false,\n\t\t\t\toptions: [],\n\t\t\t\tdescription: 'How many sessions the user has recorded',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'User ID',\n\t\t\t\tname: 'userId',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\t'/operation': ['update'],\n\t\t\t\t\t\t'/resource': ['user'],\n\t\t\t\t\t},\n\t\t\t\t\thide: {\n\t\t\t\t\t\t'/updateBy': ['email', 'userId'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Email of the user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Unsubscribed From Emails',\n\t\t\t\tname: 'unsubscribedFromEmails',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tplaceholder: '',\n\t\t\t\tdescription: 'Whether the user is unsubscribed from emails',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Update Last Request At',\n\t\t\t\tname: 'updateLastRequestAt',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\toptions: [],\n\t\t\t\tdescription:\n\t\t\t\t\t'Whether to instruct Intercom to update the users last_request_at value to the current API service time in UTC',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'UTM Campaign',\n\t\t\t\tname: 'utmCampaign',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Identifies a specific product promotion or strategic campaign',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'UTM Content',\n\t\t\t\tname: 'utmContent',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Identifies what specifically was clicked to bring the user to the site',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'UTM Medium',\n\t\t\t\tname: 'utmMedium',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Identifies what type of link was used',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'UTM Source',\n\t\t\t\tname: 'utmSource',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'An avatar image URL. note: the image URL needs to be https.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'UTM Term',\n\t\t\t\tname: 'utmTerm',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Identifies search terms',\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Custom Attributes',\n\t\tname: 'customAttributesJson',\n\t\ttype: 'json',\n\t\ttypeOptions: {\n\t\t\talwaysOpenEditWindow: true,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create', 'update'],\n\t\t\t\tjsonParameters: [true],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription:\n\t\t\t'A hash of key/value pairs to represent custom data you want to attribute to a user',\n\t},\n\t{\n\t\tdisplayName: 'Custom Attributes',\n\t\tname: 'customAttributesUi',\n\t\ttype: 'fixedCollection',\n\t\tdefault: {},\n\t\tplaceholder: 'Add Attribute',\n\t\ttypeOptions: {\n\t\t\tmultipleValues: true,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create', 'update'],\n\t\t\t\tjsonParameters: [false],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'customAttributesValues',\n\t\t\t\tdisplayName: 'Attributes',\n\t\t\t\tvalues: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Name',\n\t\t\t\t\t\tname: 'name',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Value',\n\t\t\t\t\t\tname: 'value',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t\tdescription:\n\t\t\t'A hash of key/value pairs to represent custom data you want to attribute to a user',\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,iBAAoC;AAAA,EAChD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,aAAgC;AAAA;AAAA;AAAA;AAAA,EAI5C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,QACpB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aACC;AAAA,MACF;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,UAAU,QAAQ;AAAA,QAC9B,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,UAAU,QAAQ;AAAA,QAC9B,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,QACA,SAAS,CAAC;AAAA,QACV,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,gBAAgB;AAAA,UACf,MAAM;AAAA,YACL,cAAc,CAAC,QAAQ;AAAA,YACvB,aAAa,CAAC,MAAM;AAAA,UACrB;AAAA,UACA,MAAM;AAAA,YACL,aAAa,CAAC,OAAO;AAAA,UACtB;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,gBAAgB;AAAA,UACf,MAAM;AAAA,YACL,cAAc,CAAC,QAAQ;AAAA,YACvB,aAAa,CAAC,MAAM;AAAA,UACrB;AAAA,UACA,MAAM;AAAA,YACL,aAAa,CAAC,SAAS,QAAQ;AAAA,UAChC;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,CAAC;AAAA,QACV,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,sBAAsB;AAAA,IACvB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,UAAU,QAAQ;AAAA,QAC9B,gBAAgB,CAAC,IAAI;AAAA,MACtB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,IACV,aAAa;AAAA,IACb,aAAa;AAAA,MACZ,gBAAgB;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,UAAU,QAAQ;AAAA,QAC9B,gBAAgB,CAAC,KAAK;AAAA,MACvB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,UACP;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AACD;", "names": []}