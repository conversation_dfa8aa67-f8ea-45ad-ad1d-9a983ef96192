import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { InvokeModelRequestFilterSensitiveLog, InvokeModelResponseFilterSensitiveLog, } from "../models/models_0";
import { de_InvokeModelCommand, se_InvokeModelCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class InvokeModelCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockFrontendService", "InvokeModel", {})
    .n("BedrockRuntimeClient", "InvokeModelCommand")
    .f(InvokeModelRequestFilterSensitiveLog, InvokeModelResponseFilterSensitiveLog)
    .ser(se_InvokeModelCommand)
    .de(de_InvokeModelCommand)
    .build() {
}
