{"version": 3, "sources": ["../../../../nodes/Misp/descriptions/WarninglistDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const warninglistOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['warninglist'],\n\t\t\t},\n\t\t},\n\t\tnoDataExpression: true,\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\taction: 'Get a warninglist',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\taction: 'Get many warninglists',\n\t\t\t},\n\t\t],\n\t\tdefault: 'get',\n\t},\n];\n\nexport const warninglistFields: INodeProperties[] = [\n\t// ----------------------------------------\n\t//             warninglist: get\n\t// ----------------------------------------\n\t{\n\t\tdisplayName: 'Warninglist ID',\n\t\tname: 'warninglistId',\n\t\tdescription: 'Numeric ID of the warninglist',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['warninglist'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t},\n\n\t// ----------------------------------------\n\t//           warninglist: getAll\n\t// ----------------------------------------\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['warninglist'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdefault: 50,\n\t\tdescription: 'Max number of results to return',\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['warninglist'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,wBAA2C;AAAA,EACvD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,aAAa;AAAA,MACzB;AAAA,IACD;AAAA,IACA,kBAAkB;AAAA,IAClB,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,oBAAuC;AAAA;AAAA;AAAA;AAAA,EAInD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,aAAa;AAAA,QACxB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,aAAa;AAAA,QACxB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,MACZ,UAAU;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,aAAa;AAAA,QACxB,WAAW,CAAC,QAAQ;AAAA,QACpB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AACD;", "names": []}