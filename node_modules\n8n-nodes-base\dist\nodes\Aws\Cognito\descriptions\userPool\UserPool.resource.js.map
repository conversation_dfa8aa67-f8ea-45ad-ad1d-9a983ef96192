{"version": 3, "sources": ["../../../../../../nodes/Aws/Cognito/descriptions/userPool/UserPool.resource.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nimport * as get from './get.operation';\nimport { simplifyUserPool } from '../../helpers/utils';\n\nexport const description: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userPool'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\taction: 'Get user pool',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\theaders: {\n\t\t\t\t\t\t\t'X-Amz-Target': 'AWSCognitoIdentityProviderService.DescribeUserPool',\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [\n\t\t\t\t\t\t\tsimplifyUserPool,\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\ttype: 'rootProperty',\n\t\t\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\t\t\tproperty: 'UserPool',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t],\n\t\tdefault: 'get',\n\t},\n\n\t...get.description,\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,UAAqB;AACrB,mBAAiC;AAE1B,MAAM,cAAiC;AAAA,EAC7C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,UAAU;AAAA,MACtB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,SAAS;AAAA,cACR,gBAAgB;AAAA,YACjB;AAAA,UACD;AAAA,UACA,QAAQ;AAAA,YACP,aAAa;AAAA,cACZ;AAAA,cACA;AAAA,gBACC,MAAM;AAAA,gBACN,YAAY;AAAA,kBACX,UAAU;AAAA,gBACX;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EAEA,GAAG,IAAI;AACR;", "names": []}