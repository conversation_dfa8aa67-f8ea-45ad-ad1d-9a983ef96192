{"version": 3, "sources": ["../../../../nodes/SeaTable/v1/types.ts"], "sourcesContent": ["// ----------------------------------\n//         sea-table\n// ----------------------------------\n\nexport type TSeaTableServerVersion = '2.0.6';\nexport type TSeaTableServerEdition = 'enterprise edition';\n\n// ----------------------------------\n//         dtable\n// ----------------------------------\n\nimport type { ICredentialDataDecryptedObject } from 'n8n-workflow';\n\nimport type { IDtableMetadataColumn, IDtableMetadataTable, TDtableViewColumn } from './Interfaces';\n\nexport type TInheritColumnTypeTime = 'ctime' | 'mtime';\nexport type TInheritColumnTypeUser = 'creator' | 'last-modifier';\nexport type TColumnType =\n\t| 'text'\n\t| 'long-text'\n\t| 'number'\n\t| 'collaborator'\n\t| 'date'\n\t| 'duration'\n\t| 'single-select'\n\t| 'multiple-select'\n\t| 'email'\n\t| 'url'\n\t| 'rate'\n\t| 'checkbox'\n\t| 'formula'\n\t| TInheritColumnTypeTime\n\t| TInheritColumnTypeUser\n\t| 'auto-number';\n\ntype TImplementInheritColumnKey = '_seq';\nexport type TInheritColumnKey =\n\t| '_id'\n\t| '_creator'\n\t| '_ctime'\n\t| '_last_modifier'\n\t| '_mtime'\n\t| TImplementInheritColumnKey;\n\nexport type TColumnValue = undefined | boolean | number | string | string[] | null;\nexport type TColumnKey = TInheritColumnKey | string;\n\nexport type TDtableMetadataTables = readonly IDtableMetadataTable[];\nexport type TDtableMetadataColumns = readonly IDtableMetadataColumn[];\nexport type TDtableViewColumns = readonly TDtableViewColumn[];\n\n// ----------------------------------\n//         api\n// ----------------------------------\n\nexport type TEndpointVariableName = 'access_token' | 'dtable_uuid' | 'server';\n\n// Template Literal Types requires-ts-4.1.5 -- deferred\nexport type TMethod = 'GET' | 'POST';\ntype TEndpoint =\n\t| '/api/v2.1/dtable/app-access-token/'\n\t| '/dtable-server/api/v1/dtables/{{dtable_uuid}}/rows/';\nexport type TEndpointExpr = TEndpoint;\nexport type TEndpointResolvedExpr =\n\tTEndpoint; /* deferred: but already in use for header values, e.g. authentication */\n\nexport type TDateTimeFormat = 'YYYY-MM-DDTHH:mm:ss.SSSZ' /* moment.js */;\n\n// ----------------------------------\n//         node\n// ----------------------------------\n\nexport type TCredentials = ICredentialDataDecryptedObject | undefined;\n\nexport type TTriggerOperation = 'create' | 'update';\n\nexport type TOperation = 'append' | 'list' | 'metadata';\n\nexport type TLoadedResource = {\n\tname: string;\n};\nexport type TColumnsUiValues = Array<{\n\tcolumnName: string;\n\tcolumnValue: string;\n}>;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}