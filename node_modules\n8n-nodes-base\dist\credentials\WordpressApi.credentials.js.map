{"version": 3, "sources": ["../../credentials/WordpressApi.credentials.ts"], "sourcesContent": ["import type {\n\tIAuthenticateGeneric,\n\tICredentialTestRequest,\n\tICredentialType,\n\tINodeProperties,\n} from 'n8n-workflow';\n\nexport class WordpressApi implements ICredentialType {\n\tname = 'wordpressApi';\n\n\tdisplayName = 'Wordpress API';\n\n\tdocumentationUrl = 'wordpress';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Username',\n\t\t\tname: 'username',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Password',\n\t\t\tname: 'password',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: {\n\t\t\t\tpassword: true,\n\t\t\t},\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Wordpress URL',\n\t\t\tname: 'url',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\tplaceholder: 'https://example.com',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Ignore SSL Issues (Insecure)',\n\t\t\tname: 'allowUnauthorizedCerts',\n\t\t\ttype: 'boolean',\n\t\t\tdescription: 'Whether to connect even if SSL certificate validation is not possible',\n\t\t\tdefault: false,\n\t\t},\n\t];\n\n\tauthenticate: IAuthenticateGeneric = {\n\t\ttype: 'generic',\n\t\tproperties: {\n\t\t\tauth: {\n\t\t\t\tusername: '={{$credentials.username}}',\n\t\t\t\tpassword: '={{$credentials.password}}',\n\t\t\t},\n\t\t},\n\t};\n\n\ttest: ICredentialTestRequest = {\n\t\trequest: {\n\t\t\tbaseURL: '={{$credentials?.url}}/wp-json/wp/v2',\n\t\t\turl: '/users',\n\t\t\tmethod: 'GET',\n\t\t\tskipSslCertificateValidation: '={{$credentials.allowUnauthorizedCerts}}',\n\t\t},\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOO,MAAM,aAAwC;AAAA,EAA9C;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,MACV;AAAA,IACD;AAEA,wBAAqC;AAAA,MACpC,MAAM;AAAA,MACN,YAAY;AAAA,QACX,MAAM;AAAA,UACL,UAAU;AAAA,UACV,UAAU;AAAA,QACX;AAAA,MACD;AAAA,IACD;AAEA,gBAA+B;AAAA,MAC9B,SAAS;AAAA,QACR,SAAS;AAAA,QACT,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,8BAA8B;AAAA,MAC/B;AAAA,IACD;AAAA;AACD;", "names": []}