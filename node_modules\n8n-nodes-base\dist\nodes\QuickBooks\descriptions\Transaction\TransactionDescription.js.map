{"version": 3, "sources": ["../../../../../nodes/QuickBooks/descriptions/Transaction/TransactionDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nimport {\n\tGROUP_BY_OPTIONS,\n\tPAYMENT_METHODS,\n\tPREDEFINED_DATE_RANGES,\n\tSOURCE_ACCOUNT_TYPES,\n\tTRANSACTION_REPORT_COLUMNS,\n\tTRANSACTION_TYPES,\n} from './constants';\nimport { toDisplayName, toOptions } from '../../GenericFunctions';\n\nexport const transactionOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdefault: 'getReport',\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Get Report',\n\t\t\t\tvalue: 'getReport',\n\t\t\t\taction: 'Get a report',\n\t\t\t},\n\t\t],\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transaction'],\n\t\t\t},\n\t\t},\n\t},\n];\n\nexport const transactionFields: INodeProperties[] = [\n\t// ----------------------------------\n\t//       transaction: getReport\n\t// ----------------------------------\n\t{\n\t\tdisplayName: 'Simplify',\n\t\tname: 'simple',\n\t\ttype: 'boolean',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transaction'],\n\t\t\t\toperation: ['getReport'],\n\t\t\t},\n\t\t},\n\t\tdefault: true,\n\t\tdescription: 'Whether to return a simplified version of the response instead of the raw data',\n\t},\n\t{\n\t\tdisplayName: 'Filters',\n\t\tname: 'filters',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['transaction'],\n\t\t\t\toperation: ['getReport'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Accounts Payable Paid',\n\t\t\t\tname: 'appaid',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'All',\n\t\t\t\toptions: ['All', 'Paid', 'Unpaid'].map(toOptions),\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Accounts Receivable Paid',\n\t\t\t\tname: 'arpaid',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'All',\n\t\t\t\toptions: ['All', 'Paid', 'Unpaid'].map(toOptions),\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Cleared Status',\n\t\t\t\tname: 'cleared',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'Reconciled',\n\t\t\t\toptions: ['Cleared', 'Uncleared', 'Reconciled', 'Deposited'].map(toOptions),\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Columns',\n\t\t\t\tname: 'columns',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: 'Columns to return',\n\t\t\t\toptions: TRANSACTION_REPORT_COLUMNS,\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Customer Names or IDs',\n\t\t\t\tname: 'customer',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription:\n\t\t\t\t\t'Customer to filter results by. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getCustomers',\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Date Range (Custom)',\n\t\t\t\tname: 'dateRangeCustom',\n\t\t\t\tplaceholder: 'Add Date Range',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Date Range Properties',\n\t\t\t\t\t\tname: 'dateRangeCustomProperties',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Start Date',\n\t\t\t\t\t\t\t\tname: 'start_date',\n\t\t\t\t\t\t\t\ttype: 'dateTime',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription: 'Start date of the date range to filter results by',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'End Date',\n\t\t\t\t\t\t\t\tname: 'end_date',\n\t\t\t\t\t\t\t\ttype: 'dateTime',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription: 'End date of the date range to filter results by',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Date Range (Predefined)',\n\t\t\t\tname: 'date_macro',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'This Month',\n\t\t\t\tdescription: 'Predefined date range to filter results by',\n\t\t\t\toptions: PREDEFINED_DATE_RANGES.map(toOptions),\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Date Range for Creation Date (Custom)',\n\t\t\t\tname: 'dateRangeCreationCustom',\n\t\t\t\tplaceholder: 'Add Creation Date Range',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Creation Date Range Properties',\n\t\t\t\t\t\tname: 'dateRangeCreationCustomProperties',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Start Creation Date',\n\t\t\t\t\t\t\t\tname: 'start_createdate',\n\t\t\t\t\t\t\t\ttype: 'dateTime',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription: 'Start date of the account creation date range to filter results by',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'End Creation Date',\n\t\t\t\t\t\t\t\tname: 'end_createdate',\n\t\t\t\t\t\t\t\ttype: 'dateTime',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription: 'End date of the account creation date range to filter results by',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Date Range for Creation Date (Predefined)',\n\t\t\t\tname: 'createdate_macro',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'This Month',\n\t\t\t\toptions: PREDEFINED_DATE_RANGES.map(toOptions),\n\t\t\t\tdescription: 'Predefined report account creation date range',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Date Range for Due Date (Custom)',\n\t\t\t\tname: 'dateRangeDueCustom',\n\t\t\t\tplaceholder: 'Add Due Date Range',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Due Date Range Properties',\n\t\t\t\t\t\tname: 'dateRangeDueCustomProperties',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Start Due Date',\n\t\t\t\t\t\t\t\tname: 'start_duedate',\n\t\t\t\t\t\t\t\ttype: 'dateTime',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription: 'Start date of the due date range to filter results by',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'End Due Date',\n\t\t\t\t\t\t\t\tname: 'end_duedate',\n\t\t\t\t\t\t\t\ttype: 'dateTime',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription: 'End date of the due date range to filter results by',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Date Range for Due Date (Predefined)',\n\t\t\t\tname: 'duedate_macro',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'This Month',\n\t\t\t\tdescription: 'Predefined due date range to filter results by',\n\t\t\t\toptions: PREDEFINED_DATE_RANGES.map(toOptions),\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Date Range for Modification Date (Custom)',\n\t\t\t\tname: 'dateRangeModificationCustom',\n\t\t\t\tplaceholder: 'Add Modification Date Range',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Modification Date Range Properties',\n\t\t\t\t\t\tname: 'dateRangeModificationCustomProperties',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Start Modification Date',\n\t\t\t\t\t\t\t\tname: 'start_moddate',\n\t\t\t\t\t\t\t\ttype: 'dateTime',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t\t'Start date of the account modification date range to filter results by',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'End Modification Date',\n\t\t\t\t\t\t\t\tname: 'end_moddate',\n\t\t\t\t\t\t\t\ttype: 'dateTime',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription: 'End date of the account modification date range to filter results by',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Date Range for Modification Date (Predefined)',\n\t\t\t\tname: 'moddate_macro',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'This Month',\n\t\t\t\tdescription: 'Predefined account modifiction date range to filter results by',\n\t\t\t\toptions: PREDEFINED_DATE_RANGES.map(toOptions),\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Department Names or IDs',\n\t\t\t\tname: 'department',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription:\n\t\t\t\t\t'Department to filter results by. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getDepartments',\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Document Number',\n\t\t\t\tname: 'docnum',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Transaction document number to filter results by',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Group By',\n\t\t\t\tname: 'group_by',\n\t\t\t\tdefault: 'Account',\n\t\t\t\ttype: 'options',\n\t\t\t\tdescription: 'Transaction field to group results by',\n\t\t\t\toptions: GROUP_BY_OPTIONS.map(toOptions),\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Memo Names or IDs',\n\t\t\t\tname: 'memo',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription:\n\t\t\t\t\t'Memo to filter results by. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getMemos',\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Payment Method',\n\t\t\t\tname: 'payment_Method',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'Cash',\n\t\t\t\tdescription: 'Payment method to filter results by',\n\t\t\t\toptions: PAYMENT_METHODS.map(toOptions),\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Printed Status',\n\t\t\t\tname: 'printed',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'Printed',\n\t\t\t\tdescription: 'Printed state to filter results by',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Printed',\n\t\t\t\t\t\tvalue: 'Printed',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'To Be Printed',\n\t\t\t\t\t\tvalue: 'To_be_printed',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Quick Zoom URL',\n\t\t\t\tname: 'qzurl',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: true,\n\t\t\t\tdescription: 'Whether Quick Zoom URL information should be generated',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Sort By',\n\t\t\t\tname: 'sort_by',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'account_name',\n\t\t\t\tdescription: 'Column to sort results by',\n\t\t\t\toptions: TRANSACTION_REPORT_COLUMNS,\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Sort Order',\n\t\t\t\tname: 'sort_order',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'Ascend',\n\t\t\t\toptions: ['Ascend', 'Descend'].map(toOptions),\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Source Account Type',\n\t\t\t\tname: 'source_account_type',\n\t\t\t\tdefault: 'Bank',\n\t\t\t\ttype: 'options',\n\t\t\t\tdescription: 'Account type to filter results by',\n\t\t\t\toptions: SOURCE_ACCOUNT_TYPES.map(toOptions).map(toDisplayName),\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Term Names or IDs',\n\t\t\t\tname: 'term',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription:\n\t\t\t\t\t'Term to filter results by. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getTerms',\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Transaction Amount',\n\t\t\t\tname: 'bothamount',\n\t\t\t\ttype: 'number',\n\t\t\t\tdefault: 0,\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tnumberPrecision: 2,\n\t\t\t\t},\n\t\t\t\tdescription: 'Monetary amount to filter results by',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Transaction Type',\n\t\t\t\tname: 'transaction_type',\n\t\t\t\ttype: 'options',\n\t\t\t\tdefault: 'CreditCardCharge',\n\t\t\t\tdescription: 'Transaction type to filter results by',\n\t\t\t\toptions: TRANSACTION_TYPES.map(toOptions).map(toDisplayName),\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Vendor Names or IDs',\n\t\t\t\tname: 'vendor',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\tdefault: [],\n\t\t\t\tdescription:\n\t\t\t\t\t'Vendor to filter results by. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getVendors',\n\t\t\t\t},\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,uBAOO;AACP,8BAAyC;AAElC,MAAM,wBAA2C;AAAA,EACvD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,aAAa;AAAA,MACzB;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,oBAAuC;AAAA;AAAA;AAAA;AAAA,EAInD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,aAAa;AAAA,QACxB,WAAW,CAAC,WAAW;AAAA,MACxB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,aAAa;AAAA,QACxB,WAAW,CAAC,WAAW;AAAA,MACxB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,CAAC,OAAO,QAAQ,QAAQ,EAAE,IAAI,iCAAS;AAAA,MACjD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,CAAC,OAAO,QAAQ,QAAQ,EAAE,IAAI,iCAAS;AAAA,MACjD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,CAAC,WAAW,aAAa,cAAc,WAAW,EAAE,IAAI,iCAAS;AAAA,MAC3E;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aACC;AAAA,QACD,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,SAAS;AAAA,UACR;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS,wCAAuB,IAAI,iCAAS;AAAA,MAC9C;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,SAAS;AAAA,UACR;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,wCAAuB,IAAI,iCAAS;AAAA,QAC7C,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,SAAS;AAAA,UACR;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS,wCAAuB,IAAI,iCAAS;AAAA,MAC9C;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,SAAS;AAAA,UACR;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aACC;AAAA,cACF;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS,wCAAuB,IAAI,iCAAS;AAAA,MAC9C;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aACC;AAAA,QACD,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS,kCAAiB,IAAI,iCAAS;AAAA,MACxC;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aACC;AAAA,QACD,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS,iCAAgB,IAAI,iCAAS;AAAA,MACvC;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,CAAC,UAAU,SAAS,EAAE,IAAI,iCAAS;AAAA,MAC7C;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS,sCAAqB,IAAI,iCAAS,EAAE,IAAI,qCAAa;AAAA,MAC/D;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aACC;AAAA,QACD,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,UACZ,iBAAiB;AAAA,QAClB;AAAA,QACA,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS,mCAAkB,IAAI,iCAAS,EAAE,IAAI,qCAAa;AAAA,MAC5D;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aACC;AAAA,QACD,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;", "names": []}