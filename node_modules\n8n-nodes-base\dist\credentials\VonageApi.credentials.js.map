{"version": 3, "sources": ["../../credentials/VonageApi.credentials.ts"], "sourcesContent": ["import type { ICredentialType, INodeProperties } from 'n8n-workflow';\n\nexport class VonageApi implements ICredentialType {\n\tname = 'vonageApi';\n\n\tdisplayName = 'Vonage API';\n\n\tdocumentationUrl = 'vonage';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'API Key',\n\t\t\tname: 'api<PERSON><PERSON>',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'API Secret',\n\t\t\tname: 'apiSecret',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\tdefault: '',\n\t\t},\n\t];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,UAAqC;AAAA,EAA3C;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,MACV;AAAA,IACD;AAAA;AACD;", "names": []}