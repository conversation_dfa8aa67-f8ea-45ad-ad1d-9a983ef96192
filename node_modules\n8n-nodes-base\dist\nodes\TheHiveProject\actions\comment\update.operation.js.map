{"version": 3, "sources": ["../../../../../nodes/TheHiveProject/actions/comment/update.operation.ts"], "sourcesContent": ["import type {\n\tIDataObject,\n\tIExecuteFunctions,\n\tINodeExecutionData,\n\tINodeProperties,\n} from 'n8n-workflow';\n\nimport { updateDisplayOptions, wrapData } from '@utils/utilities';\n\nimport { commentRLC } from '../../descriptions';\nimport { theHiveApiRequest } from '../../transport';\n\nconst properties: INodeProperties[] = [\n\tcommentRLC,\n\t{\n\t\tdisplayName: 'Message',\n\t\tname: 'message',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\trows: 2,\n\t\t},\n\t},\n];\n\nconst displayOptions = {\n\tshow: {\n\t\tresource: ['comment'],\n\t\toperation: ['update'],\n\t},\n};\n\nexport const description = updateDisplayOptions(displayOptions, properties);\n\nexport async function execute(this: IExecuteFunctions, i: number): Promise<INodeExecutionData[]> {\n\tlet responseData: IDataObject | IDataObject[] = [];\n\n\tconst commentId = this.getNodeParameter('commentId', i, '', { extractValue: true }) as string;\n\tconst message = this.getNodeParameter('message', i) as string;\n\n\tconst body: IDataObject = {\n\t\tmessage,\n\t};\n\n\tresponseData = await theHiveApiRequest.call(this, 'PATCH', `/v1/comment/${commentId}`, body);\n\n\tconst executionData = this.helpers.constructExecutionMetaData(wrapData(responseData), {\n\t\titemData: { item: i },\n\t});\n\n\treturn executionData;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,uBAA+C;AAE/C,0BAA2B;AAC3B,uBAAkC;AAElC,MAAM,aAAgC;AAAA,EACrC;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,MAAM;AAAA,IACP;AAAA,EACD;AACD;AAEA,MAAM,iBAAiB;AAAA,EACtB,MAAM;AAAA,IACL,UAAU,CAAC,SAAS;AAAA,IACpB,WAAW,CAAC,QAAQ;AAAA,EACrB;AACD;AAEO,MAAM,kBAAc,uCAAqB,gBAAgB,UAAU;AAE1E,eAAsB,QAAiC,GAA0C;AAChG,MAAI,eAA4C,CAAC;AAEjD,QAAM,YAAY,KAAK,iBAAiB,aAAa,GAAG,IAAI,EAAE,cAAc,KAAK,CAAC;AAClF,QAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,QAAM,OAAoB;AAAA,IACzB;AAAA,EACD;AAEA,iBAAe,MAAM,mCAAkB,KAAK,MAAM,SAAS,eAAe,SAAS,IAAI,IAAI;AAE3F,QAAM,gBAAgB,KAAK,QAAQ,+BAA2B,2BAAS,YAAY,GAAG;AAAA,IACrF,UAAU,EAAE,MAAM,EAAE;AAAA,EACrB,CAAC;AAED,SAAO;AACR;", "names": []}