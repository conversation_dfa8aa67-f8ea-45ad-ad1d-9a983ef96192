{"version": 3, "sources": ["../../../nodes/Typeform/TypeformTrigger.node.ts"], "sourcesContent": ["import type {\n\tIHookFunctions,\n\tIWebhookFunctions,\n\tICredentialsDecrypted,\n\tICredentialTestFunctions,\n\tIDataObject,\n\tINodeCredentialTestResult,\n\tINodeType,\n\tINodeTypeDescription,\n\tIWebhookResponseData,\n\tJsonObject,\n} from 'n8n-workflow';\nimport { NodeApiError, NodeConnectionTypes, randomString } from 'n8n-workflow';\n\nimport type {\n\tITypeformAnswer,\n\tITypeformAnswerField,\n\tITypeformDefinition,\n} from './GenericFunctions';\nimport { apiRequest, getForms } from './GenericFunctions';\n\nexport class TypeformTrigger implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Typeform Trigger',\n\t\tname: 'typeformTrigger',\n\t\ticon: { light: 'file:typeform.svg', dark: 'file:typeform.dark.svg' },\n\t\tgroup: ['trigger'],\n\t\tversion: [1, 1.1],\n\t\tsubtitle: '=Form ID: {{$parameter[\"formId\"]}}',\n\t\tdescription: 'Starts the workflow on a Typeform form submission',\n\t\tdefaults: {\n\t\t\tname: 'Typeform Trigger',\n\t\t},\n\t\tinputs: [],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'typeform<PERSON><PERSON>',\n\t\t\t\trequired: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tauthentication: ['accessToken'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttestedBy: 'testTypeformTokenAuth',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'typeformOAuth2Api',\n\t\t\t\trequired: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tauthentication: ['oAuth2'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t],\n\t\twebhooks: [\n\t\t\t{\n\t\t\t\tname: 'default',\n\t\t\t\thttpMethod: 'POST',\n\t\t\t\tresponseMode: 'onReceived',\n\t\t\t\tpath: 'webhook',\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Authentication',\n\t\t\t\tname: 'authentication',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Access Token',\n\t\t\t\t\t\tvalue: 'accessToken',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'OAuth2',\n\t\t\t\t\t\tvalue: 'oAuth2',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'accessToken',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Form Name or ID',\n\t\t\t\tname: 'formId',\n\t\t\t\ttype: 'options',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getForms',\n\t\t\t\t},\n\t\t\t\toptions: [],\n\t\t\t\tdefault: '',\n\t\t\t\trequired: true,\n\t\t\t\tdescription:\n\t\t\t\t\t'Form which should trigger workflow on submission. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Simplify Answers',\n\t\t\t\tname: 'simplifyAnswers',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: true,\n\n\t\t\t\tdescription:\n\t\t\t\t\t'Whether to convert the answers to a key:value pair (\"FIELD_TITLE\":\"USER_ANSER\") to be easily processable',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Only Answers',\n\t\t\t\tname: 'onlyAnswers',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: true,\n\t\t\t\tdescription: 'Whether to return only the answers of the form and not any of the other data',\n\t\t\t},\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\tgetForms,\n\t\t},\n\t\tcredentialTest: {\n\t\t\tasync testTypeformTokenAuth(\n\t\t\t\tthis: ICredentialTestFunctions,\n\t\t\t\tcredential: ICredentialsDecrypted,\n\t\t\t): Promise<INodeCredentialTestResult> {\n\t\t\t\tconst credentials = credential.data;\n\n\t\t\t\tconst options = {\n\t\t\t\t\theaders: {\n\t\t\t\t\t\tauthorization: `bearer ${credentials!.accessToken}`,\n\t\t\t\t\t},\n\t\t\t\t\turi: 'https://api.typeform.com/workspaces',\n\t\t\t\t\tjson: true,\n\t\t\t\t};\n\t\t\t\ttry {\n\t\t\t\t\tconst response = await this.helpers.request(options);\n\t\t\t\t\tif (!response.items) {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tstatus: 'Error',\n\t\t\t\t\t\t\tmessage: 'Token is not valid.',\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tstatus: 'Error',\n\t\t\t\t\t\tmessage: `Token is not valid; ${err.message}`,\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\treturn {\n\t\t\t\t\tstatus: 'OK',\n\t\t\t\t\tmessage: 'Authentication successful!',\n\t\t\t\t};\n\t\t\t},\n\t\t},\n\t};\n\n\twebhookMethods = {\n\t\tdefault: {\n\t\t\tasync checkExists(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\n\t\t\t\tconst formId = this.getNodeParameter('formId') as string;\n\n\t\t\t\tconst endpoint = `forms/${formId}/webhooks`;\n\n\t\t\t\tconst { items } = await apiRequest.call(this, 'GET', endpoint, {});\n\n\t\t\t\tfor (const item of items) {\n\t\t\t\t\tif (item.form_id === formId && item.url === webhookUrl) {\n\t\t\t\t\t\twebhookData.webhookId = item.tag;\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tasync create(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\n\t\t\t\tconst formId = this.getNodeParameter('formId') as string;\n\t\t\t\tconst webhookId = 'n8n-' + randomString(10).toLowerCase();\n\n\t\t\t\tconst endpoint = `forms/${formId}/webhooks/${webhookId}`;\n\n\t\t\t\t// TODO: Add HMAC-validation once either the JSON data can be used for it or there is a way to access the binary-payload-data\n\t\t\t\tconst body = {\n\t\t\t\t\turl: webhookUrl,\n\t\t\t\t\tenabled: true,\n\t\t\t\t\tverify_ssl: true,\n\t\t\t\t};\n\n\t\t\t\tawait apiRequest.call(this, 'PUT', endpoint, body);\n\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\twebhookData.webhookId = webhookId;\n\n\t\t\t\treturn true;\n\t\t\t},\n\t\t\tasync delete(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst formId = this.getNodeParameter('formId') as string;\n\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\n\t\t\t\tif (webhookData.webhookId !== undefined) {\n\t\t\t\t\tconst endpoint = `forms/${formId}/webhooks/${webhookData.webhookId}`;\n\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst body = {};\n\t\t\t\t\t\tawait apiRequest.call(this, 'DELETE', endpoint, body);\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\t// Remove from the static workflow data so that it is clear\n\t\t\t\t\t// that no webhooks are registered anymore\n\t\t\t\t\tdelete webhookData.webhookId;\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync webhook(this: IWebhookFunctions): Promise<IWebhookResponseData> {\n\t\tconst version = this.getNode().typeVersion;\n\t\tconst bodyData = this.getBodyData();\n\n\t\tconst simplifyAnswers = this.getNodeParameter('simplifyAnswers') as boolean;\n\t\tconst onlyAnswers = this.getNodeParameter('onlyAnswers') as boolean;\n\n\t\tif (\n\t\t\tbodyData.form_response === undefined ||\n\t\t\t(bodyData.form_response as IDataObject).definition === undefined ||\n\t\t\t(bodyData.form_response as IDataObject).answers === undefined\n\t\t) {\n\t\t\tthrow new NodeApiError(this.getNode(), bodyData as JsonObject, {\n\t\t\t\tmessage: 'Expected definition/answers data is missing!',\n\t\t\t});\n\t\t}\n\n\t\tconst answers = (bodyData.form_response as IDataObject).answers as ITypeformAnswer[];\n\n\t\t// Some fields contain lower level fields of which we are only interested of the values\n\t\tconst subValueKeys = ['label', 'labels'];\n\n\t\tif (simplifyAnswers) {\n\t\t\t// Convert the answers to simple key -> value pairs\n\t\t\tconst definition = (bodyData.form_response as IDataObject).definition as ITypeformDefinition;\n\n\t\t\t// Create a dictionary to get the field title by its ID\n\t\t\tconst definitionsById: { [key: string]: string } = {};\n\t\t\tfor (const field of definition.fields) {\n\t\t\t\tdefinitionsById[field.id] = field.title.replace(/\\{\\{/g, '[').replace(/\\}\\}/g, ']');\n\t\t\t}\n\n\t\t\t// Convert the answers to key -> value pair\n\t\t\tconst convertedAnswers: IDataObject = {};\n\t\t\tfor (const answer of answers) {\n\t\t\t\tlet value = answer[answer.type];\n\t\t\t\tif (typeof value === 'object') {\n\t\t\t\t\tfor (const key of subValueKeys) {\n\t\t\t\t\t\tif ((value as IDataObject)[key] !== undefined) {\n\t\t\t\t\t\t\tvalue = (value as ITypeformAnswerField)[key];\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconvertedAnswers[definitionsById[answer.field.id]] = value;\n\t\t\t}\n\n\t\t\tif (onlyAnswers) {\n\t\t\t\t// Only the answers should be returned so do it directly\n\t\t\t\treturn {\n\t\t\t\t\tworkflowData: [this.helpers.returnJsonArray([convertedAnswers])],\n\t\t\t\t};\n\t\t\t} else {\n\t\t\t\t// All data should be returned but the answers should still be\n\t\t\t\t// converted to key -> value pair so overwrite the answers.\n\t\t\t\t(bodyData.form_response as IDataObject).answers = convertedAnswers;\n\t\t\t}\n\t\t}\n\n\t\tif (onlyAnswers) {\n\t\t\t// Return only the answers\n\t\t\tif (version >= 1.1) {\n\t\t\t\treturn {\n\t\t\t\t\tworkflowData: [\n\t\t\t\t\t\tthis.helpers.returnJsonArray([\n\t\t\t\t\t\t\tanswers.reduce(\n\t\t\t\t\t\t\t\t(acc, answer) => {\n\t\t\t\t\t\t\t\t\tacc[answer.field.id] = answer;\n\t\t\t\t\t\t\t\t\treturn acc;\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t{} as Record<string, ITypeformAnswer>,\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\t]),\n\t\t\t\t\t],\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\tworkflowData: [this.helpers.returnJsonArray([answers as unknown as IDataObject])],\n\t\t\t};\n\t\t} else {\n\t\t\t// Return all the data that got received\n\t\t\treturn {\n\t\t\t\tworkflowData: [this.helpers.returnJsonArray([bodyData])],\n\t\t\t};\n\t\t}\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,0BAAgE;AAOhE,8BAAqC;AAE9B,MAAM,gBAAqC;AAAA,EAA3C;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM,EAAE,OAAO,qBAAqB,MAAM,yBAAyB;AAAA,MACnE,OAAO,CAAC,SAAS;AAAA,MACjB,SAAS,CAAC,GAAG,GAAG;AAAA,MAChB,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,gBAAgB,CAAC,aAAa;AAAA,YAC/B;AAAA,UACD;AAAA,UACA,UAAU;AAAA,QACX;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,gBAAgB,CAAC,QAAQ;AAAA,YAC1B;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA,UAAU;AAAA,QACT;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,MAAM;AAAA,QACP;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,YACZ,mBAAmB;AAAA,UACpB;AAAA,UACA,SAAS,CAAC;AAAA,UACV,SAAS;AAAA,UACT,UAAU;AAAA,UACV,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UAET,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,MACD;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA,QACZ;AAAA,MACD;AAAA,MACA,gBAAgB;AAAA,QACf,MAAM,sBAEL,YACqC;AACrC,gBAAM,cAAc,WAAW;AAE/B,gBAAM,UAAU;AAAA,YACf,SAAS;AAAA,cACR,eAAe,UAAU,YAAa,WAAW;AAAA,YAClD;AAAA,YACA,KAAK;AAAA,YACL,MAAM;AAAA,UACP;AACA,cAAI;AACH,kBAAM,WAAW,MAAM,KAAK,QAAQ,QAAQ,OAAO;AACnD,gBAAI,CAAC,SAAS,OAAO;AACpB,qBAAO;AAAA,gBACN,QAAQ;AAAA,gBACR,SAAS;AAAA,cACV;AAAA,YACD;AAAA,UACD,SAAS,KAAK;AACb,mBAAO;AAAA,cACN,QAAQ;AAAA,cACR,SAAS,uBAAuB,IAAI,OAAO;AAAA,YAC5C;AAAA,UACD;AAEA,iBAAO;AAAA,YACN,QAAQ;AAAA,YACR,SAAS;AAAA,UACV;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,0BAAiB;AAAA,MAChB,SAAS;AAAA,QACR,MAAM,cAAoD;AACzD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AAEnD,gBAAM,SAAS,KAAK,iBAAiB,QAAQ;AAE7C,gBAAM,WAAW,SAAS,MAAM;AAEhC,gBAAM,EAAE,MAAM,IAAI,MAAM,mCAAW,KAAK,MAAM,OAAO,UAAU,CAAC,CAAC;AAEjE,qBAAW,QAAQ,OAAO;AACzB,gBAAI,KAAK,YAAY,UAAU,KAAK,QAAQ,YAAY;AACvD,0BAAY,YAAY,KAAK;AAC7B,qBAAO;AAAA,YACR;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AAEnD,gBAAM,SAAS,KAAK,iBAAiB,QAAQ;AAC7C,gBAAM,YAAY,aAAS,kCAAa,EAAE,EAAE,YAAY;AAExD,gBAAM,WAAW,SAAS,MAAM,aAAa,SAAS;AAGtD,gBAAM,OAAO;AAAA,YACZ,KAAK;AAAA,YACL,SAAS;AAAA,YACT,YAAY;AAAA,UACb;AAEA,gBAAM,mCAAW,KAAK,MAAM,OAAO,UAAU,IAAI;AAEjD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,sBAAY,YAAY;AAExB,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,SAAS,KAAK,iBAAiB,QAAQ;AAE7C,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AAErD,cAAI,YAAY,cAAc,QAAW;AACxC,kBAAM,WAAW,SAAS,MAAM,aAAa,YAAY,SAAS;AAElE,gBAAI;AACH,oBAAM,OAAO,CAAC;AACd,oBAAM,mCAAW,KAAK,MAAM,UAAU,UAAU,IAAI;AAAA,YACrD,SAAS,OAAO;AACf,qBAAO;AAAA,YACR;AAGA,mBAAO,YAAY;AAAA,UACpB;AAEA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAgE;AACrE,UAAM,UAAU,KAAK,QAAQ,EAAE;AAC/B,UAAM,WAAW,KAAK,YAAY;AAElC,UAAM,kBAAkB,KAAK,iBAAiB,iBAAiB;AAC/D,UAAM,cAAc,KAAK,iBAAiB,aAAa;AAEvD,QACC,SAAS,kBAAkB,UAC1B,SAAS,cAA8B,eAAe,UACtD,SAAS,cAA8B,YAAY,QACnD;AACD,YAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,UAAwB;AAAA,QAC9D,SAAS;AAAA,MACV,CAAC;AAAA,IACF;AAEA,UAAM,UAAW,SAAS,cAA8B;AAGxD,UAAM,eAAe,CAAC,SAAS,QAAQ;AAEvC,QAAI,iBAAiB;AAEpB,YAAM,aAAc,SAAS,cAA8B;AAG3D,YAAM,kBAA6C,CAAC;AACpD,iBAAW,SAAS,WAAW,QAAQ;AACtC,wBAAgB,MAAM,EAAE,IAAI,MAAM,MAAM,QAAQ,SAAS,GAAG,EAAE,QAAQ,SAAS,GAAG;AAAA,MACnF;AAGA,YAAM,mBAAgC,CAAC;AACvC,iBAAW,UAAU,SAAS;AAC7B,YAAI,QAAQ,OAAO,OAAO,IAAI;AAC9B,YAAI,OAAO,UAAU,UAAU;AAC9B,qBAAW,OAAO,cAAc;AAC/B,gBAAK,MAAsB,GAAG,MAAM,QAAW;AAC9C,sBAAS,MAA+B,GAAG;AAC3C;AAAA,YACD;AAAA,UACD;AAAA,QACD;AACA,yBAAiB,gBAAgB,OAAO,MAAM,EAAE,CAAC,IAAI;AAAA,MACtD;AAEA,UAAI,aAAa;AAEhB,eAAO;AAAA,UACN,cAAc,CAAC,KAAK,QAAQ,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAAA,QAChE;AAAA,MACD,OAAO;AAGN,QAAC,SAAS,cAA8B,UAAU;AAAA,MACnD;AAAA,IACD;AAEA,QAAI,aAAa;AAEhB,UAAI,WAAW,KAAK;AACnB,eAAO;AAAA,UACN,cAAc;AAAA,YACb,KAAK,QAAQ,gBAAgB;AAAA,cAC5B,QAAQ;AAAA,gBACP,CAAC,KAAK,WAAW;AAChB,sBAAI,OAAO,MAAM,EAAE,IAAI;AACvB,yBAAO;AAAA,gBACR;AAAA,gBACA,CAAC;AAAA,cACF;AAAA,YACD,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,QACN,cAAc,CAAC,KAAK,QAAQ,gBAAgB,CAAC,OAAiC,CAAC,CAAC;AAAA,MACjF;AAAA,IACD,OAAO;AAEN,aAAO;AAAA,QACN,cAAc,CAAC,KAAK,QAAQ,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AAAA,MACxD;AAAA,IACD;AAAA,EACD;AACD;", "names": []}