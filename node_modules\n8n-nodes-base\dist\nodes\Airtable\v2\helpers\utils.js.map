{"version": 3, "sources": ["../../../../../nodes/Airtable/v2/helpers/utils.ts"], "sourcesContent": ["import set from 'lodash/set';\nimport { ApplicationError, type IDataObject, type NodeApiError } from 'n8n-workflow';\n\nimport type { UpdateRecord } from './interfaces';\n\nexport function removeIgnored(data: IDataObject, ignore: string | string[]) {\n\tif (ignore) {\n\t\tlet ignoreFields: string[] = [];\n\n\t\tif (typeof ignore === 'string') {\n\t\t\tignoreFields = ignore.split(',').map((field) => field.trim());\n\t\t} else {\n\t\t\tignoreFields = ignore;\n\t\t}\n\n\t\tconst newData: IDataObject = {};\n\n\t\tfor (const field of Object.keys(data)) {\n\t\t\tif (!ignoreFields.includes(field)) {\n\t\t\t\tnewData[field] = data[field];\n\t\t\t}\n\t\t}\n\n\t\treturn newData;\n\t} else {\n\t\treturn data;\n\t}\n}\n\nexport function findMatches(\n\tdata: UpdateRecord[],\n\tkeys: string[],\n\tfields: IDataObject,\n\tupdateAll?: boolean,\n) {\n\tif (updateAll) {\n\t\tconst matches = data.filter((record) => {\n\t\t\tfor (const key of keys) {\n\t\t\t\tif (record.fields[key] !== fields[key]) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t});\n\n\t\tif (!matches?.length) {\n\t\t\tthrow new ApplicationError('No records match provided keys', { level: 'warning' });\n\t\t}\n\n\t\treturn matches;\n\t} else {\n\t\tconst match = data.find((record) => {\n\t\t\tfor (const key of keys) {\n\t\t\t\tif (record.fields[key] !== fields[key]) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t});\n\n\t\tif (!match) {\n\t\t\tthrow new ApplicationError('Record matching provided keys was not found', {\n\t\t\t\tlevel: 'warning',\n\t\t\t});\n\t\t}\n\n\t\treturn [match];\n\t}\n}\n\nexport function processAirtableError(error: NodeApiError, id?: string, itemIndex?: number) {\n\tif (error.description === 'NOT_FOUND' && id) {\n\t\terror.description = `${id} is not a valid Record ID`;\n\t}\n\tif (error.description?.includes('You must provide an array of up to 10 record objects') && id) {\n\t\terror.description = `${id} is not a valid Record ID`;\n\t}\n\n\tif (itemIndex !== undefined) {\n\t\tset(error, 'context.itemIndex', itemIndex);\n\t}\n\n\treturn error;\n}\n\nexport const flattenOutput = (record: IDataObject) => {\n\tconst { fields, ...rest } = record;\n\treturn {\n\t\t...rest,\n\t\t...(fields as IDataObject),\n\t};\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAgB;AAChB,0BAAsE;AAI/D,SAAS,cAAc,MAAmB,QAA2B;AAC3E,MAAI,QAAQ;AACX,QAAI,eAAyB,CAAC;AAE9B,QAAI,OAAO,WAAW,UAAU;AAC/B,qBAAe,OAAO,MAAM,GAAG,EAAE,IAAI,CAAC,UAAU,MAAM,KAAK,CAAC;AAAA,IAC7D,OAAO;AACN,qBAAe;AAAA,IAChB;AAEA,UAAM,UAAuB,CAAC;AAE9B,eAAW,SAAS,OAAO,KAAK,IAAI,GAAG;AACtC,UAAI,CAAC,aAAa,SAAS,KAAK,GAAG;AAClC,gBAAQ,KAAK,IAAI,KAAK,KAAK;AAAA,MAC5B;AAAA,IACD;AAEA,WAAO;AAAA,EACR,OAAO;AACN,WAAO;AAAA,EACR;AACD;AAEO,SAAS,YACf,MACA,MACA,QACA,WACC;AACD,MAAI,WAAW;AACd,UAAM,UAAU,KAAK,OAAO,CAAC,WAAW;AACvC,iBAAW,OAAO,MAAM;AACvB,YAAI,OAAO,OAAO,GAAG,MAAM,OAAO,GAAG,GAAG;AACvC,iBAAO;AAAA,QACR;AAAA,MACD;AACA,aAAO;AAAA,IACR,CAAC;AAED,QAAI,CAAC,SAAS,QAAQ;AACrB,YAAM,IAAI,qCAAiB,kCAAkC,EAAE,OAAO,UAAU,CAAC;AAAA,IAClF;AAEA,WAAO;AAAA,EACR,OAAO;AACN,UAAM,QAAQ,KAAK,KAAK,CAAC,WAAW;AACnC,iBAAW,OAAO,MAAM;AACvB,YAAI,OAAO,OAAO,GAAG,MAAM,OAAO,GAAG,GAAG;AACvC,iBAAO;AAAA,QACR;AAAA,MACD;AACA,aAAO;AAAA,IACR,CAAC;AAED,QAAI,CAAC,OAAO;AACX,YAAM,IAAI,qCAAiB,+CAA+C;AAAA,QACzE,OAAO;AAAA,MACR,CAAC;AAAA,IACF;AAEA,WAAO,CAAC,KAAK;AAAA,EACd;AACD;AAEO,SAAS,qBAAqB,OAAqB,IAAa,WAAoB;AAC1F,MAAI,MAAM,gBAAgB,eAAe,IAAI;AAC5C,UAAM,cAAc,GAAG,EAAE;AAAA,EAC1B;AACA,MAAI,MAAM,aAAa,SAAS,sDAAsD,KAAK,IAAI;AAC9F,UAAM,cAAc,GAAG,EAAE;AAAA,EAC1B;AAEA,MAAI,cAAc,QAAW;AAC5B,mBAAAA,SAAI,OAAO,qBAAqB,SAAS;AAAA,EAC1C;AAEA,SAAO;AACR;AAEO,MAAM,gBAAgB,CAAC,WAAwB;AACrD,QAAM,EAAE,QAAQ,GAAG,KAAK,IAAI;AAC5B,SAAO;AAAA,IACN,GAAG;AAAA,IACH,GAAI;AAAA,EACL;AACD;", "names": ["set"]}