{"version": 3, "sources": ["../../../../../nodes/Files/ReadWriteFile/helpers/utils.ts"], "sourcesContent": ["import type { IDataObject, IExecuteFunctions } from 'n8n-workflow';\nimport { NodeOperationError } from 'n8n-workflow';\n\nexport function errorMapper(\n\tthis: IExecuteFunctions,\n\terror: Error,\n\titemIndex: number,\n\tcontext?: IDataObject,\n) {\n\tlet message;\n\tlet description;\n\n\tif (error.message.includes('Cannot create a string longer than')) {\n\t\tmessage = 'The file is too large';\n\t\tdescription =\n\t\t\t'The binary file you are attempting to read exceeds 512MB, which is limit when using default binary data mode, try using the filesystem binary mode. More information <a href=\"https://docs.n8n.io/hosting/scaling/binary-data/\" target=\"_blank\">here</a>.';\n\t} else if (error.message.includes('EACCES') && context?.operation === 'read') {\n\t\tconst path =\n\t\t\t((error as unknown as IDataObject).path as string) || (context?.filePath as string);\n\t\tmessage = `You don't have the permissions to access ${path}`;\n\t\tdescription =\n\t\t\t\"Verify that the path specified in 'File(s) Selector' is correct, or change the file(s) permissions if needed\";\n\t} else if (error.message.includes('EACCES') && context?.operation === 'write') {\n\t\tconst path =\n\t\t\t((error as unknown as IDataObject).path as string) || (context?.filePath as string);\n\t\tmessage = `You don't have the permissions to write the file ${path}`;\n\t\tdescription =\n\t\t\t\"Specify another destination folder in 'File Path and Name', or change the permissions of the parent folder\";\n\t}\n\n\treturn new NodeOperationError(this.getNode(), error, { itemIndex, message, description });\n}\n\nexport function escapeSpecialCharacters(str: string) {\n\t// Escape parentheses\n\tstr = str.replace(/[()]/g, '\\\\$&');\n\n\treturn str;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAAmC;AAE5B,SAAS,YAEf,OACA,WACA,SACC;AACD,MAAI;AACJ,MAAI;AAEJ,MAAI,MAAM,QAAQ,SAAS,oCAAoC,GAAG;AACjE,cAAU;AACV,kBACC;AAAA,EACF,WAAW,MAAM,QAAQ,SAAS,QAAQ,KAAK,SAAS,cAAc,QAAQ;AAC7E,UAAM,OACH,MAAiC,QAAoB,SAAS;AACjE,cAAU,4CAA4C,IAAI;AAC1D,kBACC;AAAA,EACF,WAAW,MAAM,QAAQ,SAAS,QAAQ,KAAK,SAAS,cAAc,SAAS;AAC9E,UAAM,OACH,MAAiC,QAAoB,SAAS;AACjE,cAAU,oDAAoD,IAAI;AAClE,kBACC;AAAA,EACF;AAEA,SAAO,IAAI,uCAAmB,KAAK,QAAQ,GAAG,OAAO,EAAE,WAAW,SAAS,YAAY,CAAC;AACzF;AAEO,SAAS,wBAAwB,KAAa;AAEpD,QAAM,IAAI,QAAQ,SAAS,MAAM;AAEjC,SAAO;AACR;", "names": []}