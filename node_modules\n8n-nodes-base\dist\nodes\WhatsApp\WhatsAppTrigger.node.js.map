{"version": 3, "sources": ["../../../nodes/WhatsApp/WhatsAppTrigger.node.ts"], "sourcesContent": ["import { createHmac } from 'crypto';\nimport {\n\tNodeOperationError,\n\ttype IDataObject,\n\ttype IHookFunctions,\n\ttype INodeType,\n\ttype INodeTypeDescription,\n\ttype IWebhookFunctions,\n\ttype IWebhookResponseData,\n\tNodeConnectionTypes,\n} from 'n8n-workflow';\n\nimport {\n\tappWebhookSubscriptionCreate,\n\tappWebhookSubscriptionDelete,\n\tappWebhookSubscriptionList,\n} from './GenericFunctions';\nimport type { WhatsAppPageEvent } from './types';\n\nexport const filterStatuses = (\n\tevents: Array<{ statuses?: Array<{ status: string }> }>,\n\tallowedStatuses: string[] | undefined,\n) => {\n\tif (!allowedStatuses) return events;\n\n\t// If allowedStatuses is empty filter out events with statuses\n\tif (!allowedStatuses.length) {\n\t\treturn events.filter((event) => (event?.statuses ? false : true));\n\t}\n\n\t// If 'all' is not in allowedStatuses, return only events with allowed status\n\tif (!allowedStatuses.includes('all')) {\n\t\treturn events.filter((event) => {\n\t\t\tconst statuses = event.statuses;\n\t\t\tif (statuses?.length) {\n\t\t\t\treturn statuses.some((status) => allowedStatuses.includes(status.status));\n\t\t\t}\n\t\t\treturn true;\n\t\t});\n\t}\n\n\treturn events;\n};\n\nexport class WhatsAppTrigger implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'WhatsApp Trigger',\n\t\tname: 'whatsAppTrigger',\n\t\ticon: 'file:whatsapp.svg',\n\t\tgroup: ['trigger'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"event\"]}}',\n\t\tdescription: 'Handle WhatsApp events via webhooks',\n\t\tdefaults: {\n\t\t\tname: 'WhatsApp Trigger',\n\t\t},\n\t\tinputs: [],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'whatsAppTriggerApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\twebhooks: [\n\t\t\t{\n\t\t\t\tname: 'setup',\n\t\t\t\thttpMethod: 'GET',\n\t\t\t\tresponseMode: 'onReceived',\n\t\t\t\tpath: 'webhook',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'default',\n\t\t\t\thttpMethod: 'POST',\n\t\t\t\tresponseMode: 'onReceived',\n\t\t\t\tpath: 'webhook',\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName:\n\t\t\t\t\t'Due to Facebook API limitations, you can use just one WhatsApp trigger for each Facebook App',\n\t\t\t\tname: 'whatsAppNotice',\n\t\t\t\ttype: 'notice',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Trigger On',\n\t\t\t\tname: 'updates',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\trequired: true,\n\t\t\t\tdefault: [],\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Account Review Update',\n\t\t\t\t\t\tvalue: 'account_review_update',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Account Update',\n\t\t\t\t\t\tvalue: 'account_update',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Business Capability Update',\n\t\t\t\t\t\tvalue: 'business_capability_update',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Message Template Quality Update',\n\t\t\t\t\t\tvalue: 'message_template_quality_update',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Message Template Status Update',\n\t\t\t\t\t\tvalue: 'message_template_status_update',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Messages',\n\t\t\t\t\t\tvalue: 'messages',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Phone Number Name Update',\n\t\t\t\t\t\tvalue: 'phone_number_name_update',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Phone Number Quality Update',\n\t\t\t\t\t\tvalue: 'phone_number_quality_update',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Security',\n\t\t\t\t\t\tvalue: 'security',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Template Category Update',\n\t\t\t\t\t\tvalue: 'template_category_update',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Options',\n\t\t\t\tname: 'options',\n\t\t\t\ttype: 'collection',\n\t\t\t\tdefault: {},\n\t\t\t\tplaceholder: 'Add option',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\t// https://developers.facebook.com/docs/whatsapp/cloud-api/webhooks/payload-examples#message-status-updates\n\t\t\t\t\t\tdisplayName: 'Receive Message Status Updates',\n\t\t\t\t\t\tname: 'messageStatusUpdates',\n\t\t\t\t\t\ttype: 'multiOptions',\n\t\t\t\t\t\tdefault: ['all'],\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'WhatsApp sends notifications to the Trigger when the status of a message changes (for example from Sent to Delivered and from Delivered to Read). To avoid multiple executions for one WhatsApp message, you can set the Trigger to execute only on selected message status updates.',\n\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'All',\n\t\t\t\t\t\t\t\tvalue: 'all',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'Deleted',\n\t\t\t\t\t\t\t\tvalue: 'deleted',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'Delivered',\n\t\t\t\t\t\t\t\tvalue: 'delivered',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'Failed',\n\t\t\t\t\t\t\t\tvalue: 'failed',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'Read',\n\t\t\t\t\t\t\t\tvalue: 'read',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'Sent',\n\t\t\t\t\t\t\t\tvalue: 'sent',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t};\n\n\twebhookMethods = {\n\t\tdefault: {\n\t\t\tasync checkExists(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default') as string;\n\t\t\t\tconst credentials = await this.getCredentials('whatsAppTriggerApi');\n\t\t\t\tconst updates = this.getNodeParameter('updates', []) as IDataObject[];\n\t\t\t\tconst subscribedEvents = updates.sort().join(',');\n\t\t\t\tconst appId = credentials.clientId as string;\n\n\t\t\t\tconst webhooks = await appWebhookSubscriptionList.call(this, appId);\n\n\t\t\t\tconst subscription = webhooks.find(\n\t\t\t\t\t(webhook) =>\n\t\t\t\t\t\twebhook.object === 'whatsapp_business_account' &&\n\t\t\t\t\t\twebhook.fields\n\t\t\t\t\t\t\t.map((x) => x.name)\n\t\t\t\t\t\t\t.sort()\n\t\t\t\t\t\t\t.join(',') === subscribedEvents &&\n\t\t\t\t\t\twebhook.active,\n\t\t\t\t);\n\n\t\t\t\tif (!subscription) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\tif (subscription.callback_url !== webhookUrl) {\n\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t`The WhatsApp App ID ${appId} already has a webhook subscription. Delete it or use another App before executing the trigger. Due to WhatsApp API limitations, you can have just one trigger per App.`,\n\t\t\t\t\t\t{ level: 'warning' },\n\t\t\t\t\t);\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\tsubscription?.fields\n\t\t\t\t\t\t.map((x) => x.name)\n\t\t\t\t\t\t.sort()\n\t\t\t\t\t\t.join(',') !== subscribedEvents\n\t\t\t\t) {\n\t\t\t\t\tawait appWebhookSubscriptionDelete.call(this, appId, 'whatsapp_business_account');\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\t\t\t},\n\t\t\tasync create(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default') as string;\n\t\t\t\tconst credentials = await this.getCredentials('whatsAppTriggerApi');\n\t\t\t\tconst appId = credentials.clientId as string;\n\t\t\t\tconst updates = this.getNodeParameter('updates', []) as IDataObject[];\n\t\t\t\tconst verifyToken = this.getNode().id;\n\n\t\t\t\tawait appWebhookSubscriptionCreate.call(this, appId, {\n\t\t\t\t\tobject: 'whatsapp_business_account',\n\t\t\t\t\tcallback_url: webhookUrl,\n\t\t\t\t\tverify_token: verifyToken,\n\t\t\t\t\tfields: JSON.stringify(updates),\n\t\t\t\t\tinclude_values: true,\n\t\t\t\t});\n\n\t\t\t\treturn true;\n\t\t\t},\n\t\t\tasync delete(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst credentials = await this.getCredentials('whatsAppTriggerApi');\n\t\t\t\tconst appId = credentials.clientId as string;\n\n\t\t\t\tawait appWebhookSubscriptionDelete.call(this, appId, 'whatsapp_business_account');\n\n\t\t\t\treturn true;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync webhook(this: IWebhookFunctions): Promise<IWebhookResponseData> {\n\t\tconst bodyData = this.getBodyData() as unknown as WhatsAppPageEvent;\n\t\tconst query = this.getQueryData() as IDataObject;\n\t\tconst res = this.getResponseObject();\n\t\tconst req = this.getRequestObject();\n\t\tconst headerData = this.getHeaderData() as IDataObject;\n\t\tconst credentials = await this.getCredentials('whatsAppTriggerApi');\n\n\t\t// Check if we're getting facebook's challenge request (https://developers.facebook.com/docs/graph-api/webhooks/getting-started)\n\t\tif (this.getWebhookName() === 'setup') {\n\t\t\tif (query['hub.challenge']) {\n\t\t\t\tif (this.getNode().id !== query['hub.verify_token']) {\n\t\t\t\t\treturn {};\n\t\t\t\t}\n\n\t\t\t\tres.status(200).send(query['hub.challenge']).end();\n\n\t\t\t\treturn { noWebhookResponse: true };\n\t\t\t}\n\t\t}\n\n\t\tconst computedSignature = createHmac('sha256', credentials.clientSecret as string)\n\t\t\t.update(req.rawBody)\n\t\t\t.digest('hex');\n\t\tif (headerData['x-hub-signature-256'] !== `sha256=${computedSignature}`) {\n\t\t\treturn {};\n\t\t}\n\n\t\tif (bodyData.object !== 'whatsapp_business_account') {\n\t\t\treturn {};\n\t\t}\n\n\t\tconst events = await Promise.all(\n\t\t\tbodyData.entry\n\t\t\t\t.map((entry) => entry.changes)\n\t\t\t\t.flat()\n\t\t\t\t.map((change) => ({ ...change.value, field: change.field })),\n\t\t);\n\n\t\tconst options = this.getNodeParameter('options', {}) as { messageStatusUpdates?: string[] };\n\n\t\tconst returnData = filterStatuses(events, options.messageStatusUpdates);\n\n\t\tif (returnData.length === 0) return {};\n\n\t\treturn {\n\t\t\tworkflowData: [this.helpers.returnJsonArray(returnData)],\n\t\t};\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAC3B,0BASO;AAEP,8BAIO;AAGA,MAAM,iBAAiB,CAC7B,QACA,oBACI;AACJ,MAAI,CAAC,gBAAiB,QAAO;AAG7B,MAAI,CAAC,gBAAgB,QAAQ;AAC5B,WAAO,OAAO,OAAO,CAAC,UAAW,OAAO,WAAW,QAAQ,IAAK;AAAA,EACjE;AAGA,MAAI,CAAC,gBAAgB,SAAS,KAAK,GAAG;AACrC,WAAO,OAAO,OAAO,CAAC,UAAU;AAC/B,YAAM,WAAW,MAAM;AACvB,UAAI,UAAU,QAAQ;AACrB,eAAO,SAAS,KAAK,CAAC,WAAW,gBAAgB,SAAS,OAAO,MAAM,CAAC;AAAA,MACzE;AACA,aAAO;AAAA,IACR,CAAC;AAAA,EACF;AAEA,SAAO;AACR;AAEO,MAAM,gBAAqC;AAAA,EAA3C;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,SAAS;AAAA,MACjB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,UAAU;AAAA,QACT;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,MAAM;AAAA,QACP;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,MAAM;AAAA,QACP;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aACC;AAAA,UACD,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,CAAC;AAAA,UACV,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,QACD;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS,CAAC;AAAA,UACV,aAAa;AAAA,UACb,SAAS;AAAA,YACR;AAAA;AAAA,cAEC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS,CAAC,KAAK;AAAA,cACf,aACC;AAAA,cACD,SAAS;AAAA,gBACR;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,gBACA;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,gBACA;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,gBACA;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,gBACA;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,gBACA;AAAA,kBACC,MAAM;AAAA,kBACN,OAAO;AAAA,gBACR;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,0BAAiB;AAAA,MAChB,SAAS;AAAA,QACR,MAAM,cAAoD;AACzD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AACnD,gBAAM,cAAc,MAAM,KAAK,eAAe,oBAAoB;AAClE,gBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC,CAAC;AACnD,gBAAM,mBAAmB,QAAQ,KAAK,EAAE,KAAK,GAAG;AAChD,gBAAM,QAAQ,YAAY;AAE1B,gBAAM,WAAW,MAAM,mDAA2B,KAAK,MAAM,KAAK;AAElE,gBAAM,eAAe,SAAS;AAAA,YAC7B,CAAC,YACA,QAAQ,WAAW,+BACnB,QAAQ,OACN,IAAI,CAAC,MAAM,EAAE,IAAI,EACjB,KAAK,EACL,KAAK,GAAG,MAAM,oBAChB,QAAQ;AAAA,UACV;AAEA,cAAI,CAAC,cAAc;AAClB,mBAAO;AAAA,UACR;AAEA,cAAI,aAAa,iBAAiB,YAAY;AAC7C,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,uBAAuB,KAAK;AAAA,cAC5B,EAAE,OAAO,UAAU;AAAA,YACpB;AAAA,UACD;AAEA,cACC,cAAc,OACZ,IAAI,CAAC,MAAM,EAAE,IAAI,EACjB,KAAK,EACL,KAAK,GAAG,MAAM,kBACf;AACD,kBAAM,qDAA6B,KAAK,MAAM,OAAO,2BAA2B;AAChF,mBAAO;AAAA,UACR;AAEA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AACnD,gBAAM,cAAc,MAAM,KAAK,eAAe,oBAAoB;AAClE,gBAAM,QAAQ,YAAY;AAC1B,gBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC,CAAC;AACnD,gBAAM,cAAc,KAAK,QAAQ,EAAE;AAEnC,gBAAM,qDAA6B,KAAK,MAAM,OAAO;AAAA,YACpD,QAAQ;AAAA,YACR,cAAc;AAAA,YACd,cAAc;AAAA,YACd,QAAQ,KAAK,UAAU,OAAO;AAAA,YAC9B,gBAAgB;AAAA,UACjB,CAAC;AAED,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,cAAc,MAAM,KAAK,eAAe,oBAAoB;AAClE,gBAAM,QAAQ,YAAY;AAE1B,gBAAM,qDAA6B,KAAK,MAAM,OAAO,2BAA2B;AAEhF,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAgE;AACrE,UAAM,WAAW,KAAK,YAAY;AAClC,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,MAAM,KAAK,kBAAkB;AACnC,UAAM,MAAM,KAAK,iBAAiB;AAClC,UAAM,aAAa,KAAK,cAAc;AACtC,UAAM,cAAc,MAAM,KAAK,eAAe,oBAAoB;AAGlE,QAAI,KAAK,eAAe,MAAM,SAAS;AACtC,UAAI,MAAM,eAAe,GAAG;AAC3B,YAAI,KAAK,QAAQ,EAAE,OAAO,MAAM,kBAAkB,GAAG;AACpD,iBAAO,CAAC;AAAA,QACT;AAEA,YAAI,OAAO,GAAG,EAAE,KAAK,MAAM,eAAe,CAAC,EAAE,IAAI;AAEjD,eAAO,EAAE,mBAAmB,KAAK;AAAA,MAClC;AAAA,IACD;AAEA,UAAM,wBAAoB,0BAAW,UAAU,YAAY,YAAsB,EAC/E,OAAO,IAAI,OAAO,EAClB,OAAO,KAAK;AACd,QAAI,WAAW,qBAAqB,MAAM,UAAU,iBAAiB,IAAI;AACxE,aAAO,CAAC;AAAA,IACT;AAEA,QAAI,SAAS,WAAW,6BAA6B;AACpD,aAAO,CAAC;AAAA,IACT;AAEA,UAAM,SAAS,MAAM,QAAQ;AAAA,MAC5B,SAAS,MACP,IAAI,CAAC,UAAU,MAAM,OAAO,EAC5B,KAAK,EACL,IAAI,CAAC,YAAY,EAAE,GAAG,OAAO,OAAO,OAAO,OAAO,MAAM,EAAE;AAAA,IAC7D;AAEA,UAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC,CAAC;AAEnD,UAAM,aAAa,eAAe,QAAQ,QAAQ,oBAAoB;AAEtE,QAAI,WAAW,WAAW,EAAG,QAAO,CAAC;AAErC,WAAO;AAAA,MACN,cAAc,CAAC,KAAK,QAAQ,gBAAgB,UAAU,CAAC;AAAA,IACxD;AAAA,EACD;AACD;", "names": []}