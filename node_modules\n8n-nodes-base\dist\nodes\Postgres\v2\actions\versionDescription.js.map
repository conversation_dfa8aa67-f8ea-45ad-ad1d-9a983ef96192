{"version": 3, "sources": ["../../../../../nodes/Postgres/v2/actions/versionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport { NodeConnectionTypes, type INodeTypeDescription } from 'n8n-workflow';\n\nimport * as database from './database/Database.resource';\n\nexport const versionDescription: INodeTypeDescription = {\n\tdisplayName: 'Postgres',\n\tname: 'postgres',\n\ticon: 'file:postgres.svg',\n\tgroup: ['input'],\n\tversion: [2, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6],\n\tsubtitle: '={{ $parameter[\"operation\"] }}',\n\tdescription: 'Get, add and update data in Postgres',\n\tdefaults: {\n\t\tname: 'Postgres',\n\t},\n\tinputs: [NodeConnectionTypes.Main],\n\toutputs: [NodeConnectionTypes.Main],\n\tusableAsTool: true,\n\tcredentials: [\n\t\t{\n\t\t\tname: 'postgres',\n\t\t\trequired: true,\n\t\t\ttestedBy: 'postgresConnectionTest',\n\t\t},\n\t],\n\tproperties: [\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'hidden',\n\t\t\tnoDataExpression: true,\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Database',\n\t\t\t\t\tvalue: 'database',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'database',\n\t\t},\n\t\t...database.description,\n\t],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAA+D;AAE/D,eAA0B;AAEnB,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,OAAO;AAAA,EACf,SAAS,CAAC,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACzC,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,cAAc;AAAA,EACd,aAAa;AAAA,IACZ;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAAA,EACD;AAAA,EACA,YAAY;AAAA,IACX;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA,GAAG,SAAS;AAAA,EACb;AACD;", "names": []}