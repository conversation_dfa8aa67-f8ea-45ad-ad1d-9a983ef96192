{"version": 3, "sources": ["../../../nodes/Segment/TrackDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const trackOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['track'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Event',\n\t\t\t\tvalue: 'event',\n\t\t\t\tdescription:\n\t\t\t\t\t'Record the actions your users perform. Every action triggers an event, which can also have associated properties.',\n\t\t\t\taction: 'Track an event',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Page',\n\t\t\t\tvalue: 'page',\n\t\t\t\tdescription:\n\t\t\t\t\t'Record page views on your website, along with optional extra information about the page being viewed',\n\t\t\t\taction: 'Track a page',\n\t\t\t},\n\t\t],\n\t\tdefault: 'event',\n\t},\n];\n\nexport const trackFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                                track:event                                 */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'User ID',\n\t\tname: 'userId',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['track'],\n\t\t\t\toperation: ['event'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Event',\n\t\tname: 'event',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['track'],\n\t\t\t\toperation: ['event'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'Name of the action that a user has performed',\n\t\trequired: true,\n\t},\n\t{\n\t\tdisplayName: 'Context',\n\t\tname: 'context',\n\t\tplaceholder: 'Add Context',\n\t\ttype: 'fixedCollection',\n\t\ttypeOptions: {\n\t\t\tmultipleValues: false,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['track'],\n\t\t\t\toperation: ['event'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'contextUi',\n\t\t\t\tdisplayName: 'Context',\n\t\t\t\tvalues: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Active',\n\t\t\t\t\t\tname: 'active',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\tdescription: 'Whether a user is active',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'IP',\n\t\t\t\t\t\tname: 'ip',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription: 'Current user’s IP address',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Locale',\n\t\t\t\t\t\tname: 'locate',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription: 'Locale string for the current user, for example en-US',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Page',\n\t\t\t\t\t\tname: 'page',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Dictionary of information about the current page in the browser, containing hash, path, referrer, search, title and URL',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Timezone',\n\t\t\t\t\t\tname: 'timezone',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Timezones are sent as tzdata strings to add user timezone information which might be stripped from the timestamp, for example America/New_York',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'App',\n\t\t\t\t\t\tname: 'app',\n\t\t\t\t\t\tplaceholder: 'Add App',\n\t\t\t\t\t\ttype: 'fixedCollection',\n\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\tmultipleValues: false,\n\t\t\t\t\t\t},\n\t\t\t\t\t\tdefault: {},\n\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'appUi',\n\t\t\t\t\t\t\t\tdisplayName: 'App',\n\t\t\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Name',\n\t\t\t\t\t\t\t\t\t\tname: 'name',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Version',\n\t\t\t\t\t\t\t\t\t\tname: 'version',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Build',\n\t\t\t\t\t\t\t\t\t\tname: 'build',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Campaign',\n\t\t\t\t\t\tname: 'campaign',\n\t\t\t\t\t\tplaceholder: 'Campaign App',\n\t\t\t\t\t\ttype: 'fixedCollection',\n\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\tmultipleValues: false,\n\t\t\t\t\t\t},\n\t\t\t\t\t\tdefault: {},\n\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'campaignUi',\n\t\t\t\t\t\t\t\tdisplayName: 'Campaign',\n\t\t\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Name',\n\t\t\t\t\t\t\t\t\t\tname: 'name',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Source',\n\t\t\t\t\t\t\t\t\t\tname: 'source',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Medium',\n\t\t\t\t\t\t\t\t\t\tname: 'medium',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Term',\n\t\t\t\t\t\t\t\t\t\tname: 'term',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Content',\n\t\t\t\t\t\t\t\t\t\tname: 'content',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Device',\n\t\t\t\t\t\tname: 'device',\n\t\t\t\t\t\tplaceholder: 'Add Device',\n\t\t\t\t\t\ttype: 'fixedCollection',\n\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\tmultipleValues: false,\n\t\t\t\t\t\t},\n\t\t\t\t\t\tdefault: {},\n\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'deviceUi',\n\t\t\t\t\t\t\t\tdisplayName: 'Device',\n\t\t\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'ID',\n\t\t\t\t\t\t\t\t\t\tname: 'id',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Manufacturer',\n\t\t\t\t\t\t\t\t\t\tname: 'manufacturer',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Model',\n\t\t\t\t\t\t\t\t\t\tname: 'model',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Name',\n\t\t\t\t\t\t\t\t\t\tname: 'name',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Type',\n\t\t\t\t\t\t\t\t\t\tname: 'type',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Version',\n\t\t\t\t\t\t\t\t\t\tname: 'version',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Integration',\n\t\tname: 'integrations',\n\t\tplaceholder: 'Add Integration',\n\t\ttype: 'fixedCollection',\n\t\ttypeOptions: {\n\t\t\tmultipleValues: false,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['track'],\n\t\t\t\toperation: ['event'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'integrationsUi',\n\t\t\t\tdisplayName: 'Integration',\n\t\t\t\tvalues: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'All',\n\t\t\t\t\t\tname: 'all',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: true,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Salesforce',\n\t\t\t\t\t\tname: 'salesforce',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Properties',\n\t\tname: 'properties',\n\t\tplaceholder: 'Add Properties',\n\t\ttype: 'fixedCollection',\n\t\ttypeOptions: {\n\t\t\tmultipleValues: true,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['track'],\n\t\t\t\toperation: ['event'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'propertiesUi',\n\t\t\t\tdisplayName: 'Property',\n\t\t\t\tvalues: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Key',\n\t\t\t\t\t\tname: 'key',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Value',\n\t\t\t\t\t\tname: 'value',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                track:page                                  */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'User ID',\n\t\tname: 'userId',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['track'],\n\t\t\t\toperation: ['page'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Name',\n\t\tname: 'name',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['track'],\n\t\t\t\toperation: ['page'],\n\t\t\t},\n\t\t},\n\t\tdescription:\n\t\t\t'Name of the page For example, most sites have a “Signup” page that can be useful to tag, so you can see users as they move through your funnel',\n\t},\n\t{\n\t\tdisplayName: 'Context',\n\t\tname: 'context',\n\t\tplaceholder: 'Add Context',\n\t\ttype: 'fixedCollection',\n\t\ttypeOptions: {\n\t\t\tmultipleValues: false,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['track'],\n\t\t\t\toperation: ['page'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'contextUi',\n\t\t\t\tdisplayName: 'Context',\n\t\t\t\tvalues: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Active',\n\t\t\t\t\t\tname: 'active',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\tdescription: 'Whether a user is active',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'IP',\n\t\t\t\t\t\tname: 'ip',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription: 'Current user’s IP address',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Locale',\n\t\t\t\t\t\tname: 'locate',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription: 'Locale string for the current user, for example en-US',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Page',\n\t\t\t\t\t\tname: 'page',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Dictionary of information about the current page in the browser, containing hash, path, referrer, search, title and URL',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Timezone',\n\t\t\t\t\t\tname: 'timezone',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Timezones are sent as tzdata strings to add user timezone information which might be stripped from the timestamp, for example America/New_York',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'App',\n\t\t\t\t\t\tname: 'app',\n\t\t\t\t\t\tplaceholder: 'Add App',\n\t\t\t\t\t\ttype: 'fixedCollection',\n\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\tmultipleValues: false,\n\t\t\t\t\t\t},\n\t\t\t\t\t\tdefault: {},\n\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'appUi',\n\t\t\t\t\t\t\t\tdisplayName: 'App',\n\t\t\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Name',\n\t\t\t\t\t\t\t\t\t\tname: 'name',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Version',\n\t\t\t\t\t\t\t\t\t\tname: 'version',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Build',\n\t\t\t\t\t\t\t\t\t\tname: 'build',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Campaign',\n\t\t\t\t\t\tname: 'campaign',\n\t\t\t\t\t\tplaceholder: 'Campaign App',\n\t\t\t\t\t\ttype: 'fixedCollection',\n\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\tmultipleValues: false,\n\t\t\t\t\t\t},\n\t\t\t\t\t\tdefault: {},\n\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'campaignUi',\n\t\t\t\t\t\t\t\tdisplayName: 'Campaign',\n\t\t\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Name',\n\t\t\t\t\t\t\t\t\t\tname: 'name',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Source',\n\t\t\t\t\t\t\t\t\t\tname: 'source',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Medium',\n\t\t\t\t\t\t\t\t\t\tname: 'medium',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Term',\n\t\t\t\t\t\t\t\t\t\tname: 'term',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Content',\n\t\t\t\t\t\t\t\t\t\tname: 'content',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Device',\n\t\t\t\t\t\tname: 'device',\n\t\t\t\t\t\tplaceholder: 'Add Device',\n\t\t\t\t\t\ttype: 'fixedCollection',\n\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\tmultipleValues: false,\n\t\t\t\t\t\t},\n\t\t\t\t\t\tdefault: {},\n\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: 'deviceUi',\n\t\t\t\t\t\t\t\tdisplayName: 'Device',\n\t\t\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'ID',\n\t\t\t\t\t\t\t\t\t\tname: 'id',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Manufacturer',\n\t\t\t\t\t\t\t\t\t\tname: 'manufacturer',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Model',\n\t\t\t\t\t\t\t\t\t\tname: 'model',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Name',\n\t\t\t\t\t\t\t\t\t\tname: 'name',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Type',\n\t\t\t\t\t\t\t\t\t\tname: 'type',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdisplayName: 'Version',\n\t\t\t\t\t\t\t\t\t\tname: 'version',\n\t\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Integration',\n\t\tname: 'integrations',\n\t\tplaceholder: 'Add Integration',\n\t\ttype: 'fixedCollection',\n\t\ttypeOptions: {\n\t\t\tmultipleValues: false,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['track'],\n\t\t\t\toperation: ['page'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'integrationsUi',\n\t\t\t\tdisplayName: 'Integration',\n\t\t\t\tvalues: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'All',\n\t\t\t\t\t\tname: 'all',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: true,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Salesforce',\n\t\t\t\t\t\tname: 'salesforce',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Properties',\n\t\tname: 'properties',\n\t\tplaceholder: 'Add Properties',\n\t\ttype: 'fixedCollection',\n\t\ttypeOptions: {\n\t\t\tmultipleValues: true,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['track'],\n\t\t\t\toperation: ['page'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'propertiesUi',\n\t\t\t\tdisplayName: 'Property',\n\t\t\t\tvalues: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Key',\n\t\t\t\t\t\tname: 'key',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Value',\n\t\t\t\t\t\tname: 'value',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,kBAAqC;AAAA,EACjD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aACC;AAAA,QACD,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aACC;AAAA,QACD,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,cAAiC;AAAA;AAAA;AAAA;AAAA,EAI7C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,QAClB,WAAW,CAAC,OAAO;AAAA,MACpB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,QAClB,WAAW,CAAC,OAAO;AAAA,MACpB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,IACb,UAAU;AAAA,EACX;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,gBAAgB;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,QAClB,WAAW,CAAC,OAAO;AAAA,MACpB;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,UACP;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,aAAa;AAAA,UACd;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,aAAa;AAAA,UACd;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,aAAa;AAAA,UACd;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,aACC;AAAA,UACF;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,aACC;AAAA,UACF;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,aAAa;AAAA,YACb,MAAM;AAAA,YACN,aAAa;AAAA,cACZ,gBAAgB;AAAA,YACjB;AAAA,YACA,SAAS,CAAC;AAAA,YACV,SAAS;AAAA,cACR;AAAA,gBACC,MAAM;AAAA,gBACN,aAAa;AAAA,gBACb,QAAQ;AAAA,kBACP;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,aAAa;AAAA,YACb,MAAM;AAAA,YACN,aAAa;AAAA,cACZ,gBAAgB;AAAA,YACjB;AAAA,YACA,SAAS,CAAC;AAAA,YACV,SAAS;AAAA,cACR;AAAA,gBACC,MAAM;AAAA,gBACN,aAAa;AAAA,gBACb,QAAQ;AAAA,kBACP;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,aAAa;AAAA,YACb,MAAM;AAAA,YACN,aAAa;AAAA,cACZ,gBAAgB;AAAA,YACjB;AAAA,YACA,SAAS,CAAC;AAAA,YACV,SAAS;AAAA,cACR;AAAA,gBACC,MAAM;AAAA,gBACN,aAAa;AAAA,gBACb,QAAQ;AAAA,kBACP;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,gBAAgB;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,QAClB,WAAW,CAAC,OAAO;AAAA,MACpB;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,UACP;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,gBAAgB;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,QAClB,WAAW,CAAC,OAAO;AAAA,MACpB;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,UACP;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,QAClB,WAAW,CAAC,MAAM;AAAA,MACnB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,QAClB,WAAW,CAAC,MAAM;AAAA,MACnB;AAAA,IACD;AAAA,IACA,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,gBAAgB;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,QAClB,WAAW,CAAC,MAAM;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,UACP;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,aAAa;AAAA,UACd;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,aAAa;AAAA,UACd;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,aAAa;AAAA,UACd;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,aACC;AAAA,UACF;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,aACC;AAAA,UACF;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,aAAa;AAAA,YACb,MAAM;AAAA,YACN,aAAa;AAAA,cACZ,gBAAgB;AAAA,YACjB;AAAA,YACA,SAAS,CAAC;AAAA,YACV,SAAS;AAAA,cACR;AAAA,gBACC,MAAM;AAAA,gBACN,aAAa;AAAA,gBACb,QAAQ;AAAA,kBACP;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,aAAa;AAAA,YACb,MAAM;AAAA,YACN,aAAa;AAAA,cACZ,gBAAgB;AAAA,YACjB;AAAA,YACA,SAAS,CAAC;AAAA,YACV,SAAS;AAAA,cACR;AAAA,gBACC,MAAM;AAAA,gBACN,aAAa;AAAA,gBACb,QAAQ;AAAA,kBACP;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,aAAa;AAAA,YACb,MAAM;AAAA,YACN,aAAa;AAAA,cACZ,gBAAgB;AAAA,YACjB;AAAA,YACA,SAAS,CAAC;AAAA,YACV,SAAS;AAAA,cACR;AAAA,gBACC,MAAM;AAAA,gBACN,aAAa;AAAA,gBACb,QAAQ;AAAA,kBACP;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,kBACA;AAAA,oBACC,aAAa;AAAA,oBACb,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS;AAAA,kBACV;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,gBAAgB;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,QAClB,WAAW,CAAC,MAAM;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,UACP;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,gBAAgB;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,QAClB,WAAW,CAAC,MAAM;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,UACP;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;", "names": []}