{"version": 3, "sources": ["../../../nodes/N8n/WorkflowLocator.ts"], "sourcesContent": ["import type { ILoadOptionsFunctions, INodeListSearchResult, INodeProperties } from 'n8n-workflow';\n\nimport { apiRequestAllItems } from './GenericFunctions';\n\ntype DataItemsResponse<T> = {\n\tdata: T[];\n};\n\ninterface PartialWorkflow {\n\tid: number;\n\tname: string;\n}\n\n/**\n * A helper to populate workflow lists. It does a pseudo-search by\n * listing available workflows and matching with the specified query.\n */\nexport async function searchWorkflows(\n\tthis: ILoadOptionsFunctions,\n\tquery?: string,\n): Promise<INodeListSearchResult> {\n\tconst searchResults = (await apiRequestAllItems.call(\n\t\tthis,\n\t\t'GET',\n\t\t'workflows',\n\t\t{},\n\t)) as DataItemsResponse<PartialWorkflow>;\n\n\t// Map the workflows list against a simple name/id filter, and sort\n\t// with the latest on top.\n\tconst workflows = (searchResults as unknown as PartialWorkflow[])\n\t\t.map((w: PartialWorkflow) => ({\n\t\t\tname: `${w.name} (#${w.id})`,\n\t\t\tvalue: w.id,\n\t\t}))\n\t\t.filter(\n\t\t\t(w) =>\n\t\t\t\t!query ||\n\t\t\t\tw.name.toLowerCase().includes(query.toLowerCase()) ||\n\t\t\t\tw.value?.toString() === query,\n\t\t)\n\t\t.sort((a, b) => b.value - a.value);\n\n\treturn {\n\t\tresults: workflows,\n\t};\n}\n\n/**\n * A resourceLocator to enable looking up workflows by their ID.\n * This object can be used as a base and then extended as needed.\n */\nexport const workflowIdLocator: INodeProperties = {\n\tdisplayName: 'Workflow',\n\tname: 'workflowId',\n\ttype: 'resourceLocator',\n\tdefault: { mode: 'list', value: '' },\n\tdescription: 'Workflow to filter the executions by',\n\tmodes: [\n\t\t{\n\t\t\tdisplayName: 'From List',\n\t\t\tname: 'list',\n\t\t\ttype: 'list',\n\t\t\tplaceholder: 'Select a Workflow...',\n\t\t\tinitType: 'workflow',\n\t\t\ttypeOptions: {\n\t\t\t\tsearchListMethod: 'searchWorkflows',\n\t\t\t\tsearchFilterRequired: false,\n\t\t\t\tsearchable: true,\n\t\t\t},\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'By URL',\n\t\t\tname: 'url',\n\t\t\ttype: 'string',\n\t\t\tplaceholder: 'https://myinstance.app.n8n.cloud/workflow/1',\n\t\t\tvalidation: [\n\t\t\t\t{\n\t\t\t\t\ttype: 'regex',\n\t\t\t\t\tproperties: {\n\t\t\t\t\t\tregex: '.*/workflow/([0-9a-zA-Z]{1,})',\n\t\t\t\t\t\terrorMessage: 'Not a valid Workflow URL',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t],\n\t\t\textractValue: {\n\t\t\t\ttype: 'regex',\n\t\t\t\tregex: '.*/workflow/([0-9a-zA-Z]{1,})',\n\t\t\t},\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'ID',\n\t\t\tname: 'id',\n\t\t\ttype: 'string',\n\t\t\tvalidation: [\n\t\t\t\t{\n\t\t\t\t\ttype: 'regex',\n\t\t\t\t\tproperties: {\n\t\t\t\t\t\tregex: '[0-9a-zA-Z]{1,}',\n\t\t\t\t\t\terrorMessage: 'Not a valid Workflow ID',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t],\n\t\t\tplaceholder: '1',\n\t\t},\n\t],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,8BAAmC;AAenC,eAAsB,gBAErB,OACiC;AACjC,QAAM,gBAAiB,MAAM,2CAAmB;AAAA,IAC/C;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC;AAAA,EACF;AAIA,QAAM,YAAa,cACjB,IAAI,CAAC,OAAwB;AAAA,IAC7B,MAAM,GAAG,EAAE,IAAI,MAAM,EAAE,EAAE;AAAA,IACzB,OAAO,EAAE;AAAA,EACV,EAAE,EACD;AAAA,IACA,CAAC,MACA,CAAC,SACD,EAAE,KAAK,YAAY,EAAE,SAAS,MAAM,YAAY,CAAC,KACjD,EAAE,OAAO,SAAS,MAAM;AAAA,EAC1B,EACC,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK;AAElC,SAAO;AAAA,IACN,SAAS;AAAA,EACV;AACD;AAMO,MAAM,oBAAqC;AAAA,EACjD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS,EAAE,MAAM,QAAQ,OAAO,GAAG;AAAA,EACnC,aAAa;AAAA,EACb,OAAO;AAAA,IACN;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,UAAU;AAAA,MACV,aAAa;AAAA,QACZ,kBAAkB;AAAA,QAClB,sBAAsB;AAAA,QACtB,YAAY;AAAA,MACb;AAAA,IACD;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,YAAY;AAAA,QACX;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,YACX,OAAO;AAAA,YACP,cAAc;AAAA,UACf;AAAA,QACD;AAAA,MACD;AAAA,MACA,cAAc;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,QACX;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,YACX,OAAO;AAAA,YACP,cAAc;AAAA,UACf;AAAA,QACD;AAAA,MACD;AAAA,MACA,aAAa;AAAA,IACd;AAAA,EACD;AACD;", "names": []}