{"version": 3, "sources": ["../../../../nodes/Zammad/descriptions/UserDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const userDescription: INodeProperties[] = [\n\t// ----------------------------------\n\t//           operations\n\t// ----------------------------------\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Create',\n\t\t\t\tvalue: 'create',\n\t\t\t\tdescription: 'Create a user',\n\t\t\t\taction: 'Create a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'delete',\n\t\t\t\tdescription: 'Delete a user',\n\t\t\t\taction: 'Delete a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\tdescription: 'Retrieve a user',\n\t\t\t\taction: 'Get a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Retrieve many users',\n\t\t\t\taction: 'Get many users',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Self',\n\t\t\t\tvalue: 'getSelf',\n\t\t\t\tdescription: 'Retrieve currently logged-in user',\n\t\t\t\taction: 'Get currently logged-in user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Update',\n\t\t\t\tvalue: 'update',\n\t\t\t\tdescription: 'Update a user',\n\t\t\t\taction: 'Update a user',\n\t\t\t},\n\t\t],\n\t\tdefault: 'create',\n\t},\n\n\t// ----------------------------------\n\t//             fields\n\t// ----------------------------------\n\t{\n\t\tdisplayName: 'First Name',\n\t\tname: 'firstname',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\tplaceholder: 'John',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Last Name',\n\t\tname: 'lastname',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\tplaceholder: 'Smith',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'User ID',\n\t\tname: 'id',\n\t\ttype: 'string',\n\t\tdescription:\n\t\t\t'User to update. Specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'User ID',\n\t\tname: 'id',\n\t\ttype: 'string',\n\t\tdescription:\n\t\t\t'User to delete. Specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['delete'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'User ID',\n\t\tname: 'id',\n\t\ttype: 'string',\n\t\tdescription:\n\t\t\t'User to retrieve. Specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\ttype: 'collection',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\tplaceholder: 'Add Field',\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Active',\n\t\t\t\tname: 'active',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: true,\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Address',\n\t\t\t\tname: 'addressUi',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\tplaceholder: 'Add Address',\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Address Details',\n\t\t\t\t\t\tname: 'addressDetails',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'City',\n\t\t\t\t\t\t\t\tname: 'city',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tplaceholder: 'Berlin',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Country',\n\t\t\t\t\t\t\t\tname: 'country',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tplaceholder: 'Germany',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Street & Number',\n\t\t\t\t\t\t\t\tname: 'address',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tplaceholder: 'Borsigstr. 27',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Zip Code',\n\t\t\t\t\t\t\t\tname: 'zip',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tplaceholder: '10115',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Custom Fields',\n\t\t\t\tname: 'customFieldsUi',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\tdefault: {},\n\t\t\t\tplaceholder: 'Add Custom Field',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'customFieldPairs',\n\t\t\t\t\t\tdisplayName: 'Custom Field',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Field Name or ID',\n\t\t\t\t\t\t\t\tname: 'name',\n\t\t\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\t\t\tloadOptionsMethod: 'loadUserCustomFields',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t\t'Name of the custom field to set. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Field Value',\n\t\t\t\t\t\t\t\tname: 'value',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription: 'Value to set on the custom field',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Department',\n\t\t\t\tname: 'department',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: 'Finance',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Email Address',\n\t\t\t\tname: 'email',\n\t\t\t\ttype: 'string',\n\t\t\t\tplaceholder: '<EMAIL>',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Fax',\n\t\t\t\tname: 'fax',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: '+49 30 901820',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Notes',\n\t\t\t\tname: 'note',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Organization Name or ID',\n\t\t\t\tname: 'organization',\n\t\t\t\ttype: 'options',\n\t\t\t\tdescription:\n\t\t\t\t\t'Name of the organization to assign to the user. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\tdefault: '',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'loadOrganizations',\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Phone (Landline)',\n\t\t\t\tname: 'phone',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: '+49 30 901820',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Phone (Mobile)',\n\t\t\t\tname: 'mobile',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: '+49 1522 3433333',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Verified',\n\t\t\t\tname: 'verified',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user has been verified',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'VIP',\n\t\t\t\tname: 'vip',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user is a Very Important Person',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Website',\n\t\t\t\tname: 'web',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: 'https://n8n.io',\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Update Fields',\n\t\tname: 'updateFields',\n\t\ttype: 'collection',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['update'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\tplaceholder: 'Add Field',\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Active',\n\t\t\t\tname: 'active',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: true,\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Address',\n\t\t\t\tname: 'addressUi',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\tplaceholder: 'Add Address',\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Address Details',\n\t\t\t\t\t\tname: 'addressDetails',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'City',\n\t\t\t\t\t\t\t\tname: 'city',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tplaceholder: 'Berlin',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Country',\n\t\t\t\t\t\t\t\tname: 'country',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tplaceholder: 'Germany',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Street & Number',\n\t\t\t\t\t\t\t\tname: 'address',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tplaceholder: 'Borsigstr. 27',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Zip Code',\n\t\t\t\t\t\t\t\tname: 'zip',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tplaceholder: '10115',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Custom Fields',\n\t\t\t\tname: 'customFieldsUi',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\tdefault: {},\n\t\t\t\tplaceholder: 'Add Custom Field',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'customFieldPairs',\n\t\t\t\t\t\tdisplayName: 'Custom Field',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Field Name or ID',\n\t\t\t\t\t\t\t\tname: 'name',\n\t\t\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\t\t\tloadOptionsMethod: 'loadUserCustomFields',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t\t'Name of the custom field to set. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Field Value',\n\t\t\t\t\t\t\t\tname: 'value',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription: 'Value to set on the custom field',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Department',\n\t\t\t\tname: 'department',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: 'Finance',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Email Address',\n\t\t\t\tname: 'email',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: '<EMAIL>',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Fax',\n\t\t\t\tname: 'fax',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: '+49 30 901820',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'First Name',\n\t\t\t\tname: 'firstname',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: 'John',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Last Name',\n\t\t\t\tname: 'lastname',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: 'Smith',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Notes',\n\t\t\t\tname: 'note',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Organization Name or ID',\n\t\t\t\tname: 'organization',\n\t\t\t\ttype: 'options',\n\t\t\t\tdescription:\n\t\t\t\t\t'Name of the organization to assign to the user. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\tdefault: '',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'loadOrganizationNames',\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Phone (Landline)',\n\t\t\t\tname: 'phone',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: '+49 30 901820',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Phone (Mobile)',\n\t\t\t\tname: 'mobile',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: '+49 1522 3433333',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Verified',\n\t\t\t\tname: 'verified',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user has been verified',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'VIP',\n\t\t\t\tname: 'vip',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether the user is a Very Important Person',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Website',\n\t\t\t\tname: 'web',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tplaceholder: 'https://n8n.io',\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Query',\n\t\tname: 'query',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['search'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdefault: 50,\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['search'],\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'Max number of results to return',\n\t},\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdefault: 50,\n\t\tdescription: 'Max number of results to return',\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Filters',\n\t\tname: 'filters',\n\t\ttype: 'collection',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\tplaceholder: 'Add Filter',\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Query',\n\t\t\t\tname: 'query',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Query to filter results by',\n\t\t\t\tplaceholder: 'user.firstname:john',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Sort',\n\t\t\t\tname: 'sortUi',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\tplaceholder: 'Add Sort Options',\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Sort Options',\n\t\t\t\t\t\tname: 'sortDetails',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Sort Key Name or ID',\n\t\t\t\t\t\t\t\tname: 'sort_by',\n\t\t\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\t\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\t\t\tloadOptionsMethod: 'loadUserFields',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Sort Order',\n\t\t\t\t\t\t\t\tname: 'order_by',\n\t\t\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Ascending',\n\t\t\t\t\t\t\t\t\t\tvalue: 'asc',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tname: 'Descending',\n\t\t\t\t\t\t\t\t\t\tvalue: 'desc',\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t\tdefault: 'asc',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,kBAAqC;AAAA;AAAA;AAAA;AAAA,EAIjD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aACC;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aACC;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aACC;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,aAAa;AAAA,IACb,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS,CAAC;AAAA,QACV,SAAS;AAAA,UACR;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,aAAa;AAAA,kBACZ,mBAAmB;AAAA,gBACpB;AAAA,gBACA,SAAS;AAAA,gBACT,aACC;AAAA,cACF;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aACC;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,aAAa;AAAA,IACb,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS,CAAC;AAAA,QACV,SAAS;AAAA,UACR;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,aAAa;AAAA,kBACZ,mBAAmB;AAAA,gBACpB;AAAA,gBACA,SAAS;AAAA,gBACT,aACC;AAAA,cACF;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aACC;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,MACZ,UAAU;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,QACpB,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,MACZ,UAAU;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,QACpB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,aAAa;AAAA,IACb,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS,CAAC;AAAA,QACV,SAAS;AAAA,UACR;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,aACC;AAAA,gBACD,aAAa;AAAA,kBACZ,mBAAmB;AAAA,gBACpB;AAAA,gBACA,SAAS;AAAA,cACV;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,kBACR;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,kBACA;AAAA,oBACC,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AAAA,gBACD;AAAA,gBACA,SAAS;AAAA,cACV;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;", "names": []}