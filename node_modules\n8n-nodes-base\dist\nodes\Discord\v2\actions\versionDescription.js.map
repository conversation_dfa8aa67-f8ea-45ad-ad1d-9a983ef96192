{"version": 3, "sources": ["../../../../../nodes/Discord/v2/actions/versionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport { NodeConnectionTypes, type INodeTypeDescription } from 'n8n-workflow';\n\nimport * as channel from './channel';\nimport * as member from './member';\nimport * as message from './message';\nimport * as webhook from './webhook';\nimport { sendAndWaitWebhooksDescription } from '../../../../utils/sendAndWait/descriptions';\n\nexport const versionDescription: INodeTypeDescription = {\n\tdisplayName: 'Discord',\n\tname: 'discord',\n\ticon: 'file:discord.svg',\n\tgroup: ['output'],\n\tversion: 2,\n\tsubtitle: '={{ $parameter[\"operation\"] + \": \" + $parameter[\"resource\"] }}',\n\tdescription: 'Sends data to Discord',\n\tdefaults: {\n\t\tname: 'Discord',\n\t},\n\tinputs: [NodeConnectionTypes.Main],\n\toutputs: [NodeConnectionTypes.Main],\n\twebhooks: sendAndWaitWebhooksDescription,\n\tcredentials: [\n\t\t{\n\t\t\tname: 'discordBot<PERSON><PERSON>',\n\t\t\trequired: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tauthentication: ['botToken'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\t{\n\t\t\tname: 'discordOAuth2Api',\n\t\t\trequired: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tauthentication: ['oAuth2'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\t{\n\t\t\tname: 'discordWebhookApi',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tauthentication: ['webhook'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t],\n\tproperties: [\n\t\t{\n\t\t\tdisplayName: 'Connection Type',\n\t\t\tname: 'authentication',\n\t\t\ttype: 'options',\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Bot Token',\n\t\t\t\t\tvalue: 'botToken',\n\t\t\t\t\tdescription: 'Manage messages, channels, and members on a server',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'OAuth2',\n\t\t\t\t\tvalue: 'oAuth2',\n\t\t\t\t\tdescription: \"Same features as 'Bot Token' with easier Bot installation\",\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Webhook',\n\t\t\t\t\tvalue: 'webhook',\n\t\t\t\t\tdescription: 'Send messages to a specific channel',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'botToken',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Channel',\n\t\t\t\t\tvalue: 'channel',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Message',\n\t\t\t\t\tvalue: 'message',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Member',\n\t\t\t\t\tvalue: 'member',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'channel',\n\t\t\tdisplayOptions: {\n\t\t\t\thide: {\n\t\t\t\t\tauthentication: ['webhook'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\n\t\t...message.description,\n\t\t...channel.description,\n\t\t...member.description,\n\t\t...webhook.description,\n\t],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAA+D;AAE/D,cAAyB;AACzB,aAAwB;AACxB,cAAyB;AACzB,cAAyB;AACzB,0BAA+C;AAExC,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,QAAQ;AAAA,EAChB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,UAAU;AAAA,EACV,aAAa;AAAA,IACZ;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,gBAAgB,CAAC,UAAU;AAAA,QAC5B;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,gBAAgB,CAAC,QAAQ;AAAA,QAC1B;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,gBAAgB,CAAC,SAAS;AAAA,QAC3B;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA,YAAY;AAAA,IACX;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,MACT,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,gBAAgB,CAAC,SAAS;AAAA,QAC3B;AAAA,MACD;AAAA,IACD;AAAA,IAEA,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,IACX,GAAG,OAAO;AAAA,IACV,GAAG,QAAQ;AAAA,EACZ;AACD;", "names": []}