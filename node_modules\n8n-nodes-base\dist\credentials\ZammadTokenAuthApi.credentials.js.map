{"version": 3, "sources": ["../../credentials/ZammadTokenAuthApi.credentials.ts"], "sourcesContent": ["import type { ICredentialType, INodeProperties } from 'n8n-workflow';\n\nexport class ZammadTokenAuthApi implements ICredentialType {\n\tname = 'zammadTokenAuthApi';\n\n\tdisplayName = 'Zammad Token Auth API';\n\n\tdocumentationUrl = 'zammad';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Base URL',\n\t\t\tname: 'baseUrl',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\tplaceholder: 'https://n8n-helpdesk.zammad.com',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Access Token',\n\t\t\tname: 'accessToken',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: {\n\t\t\t\tpassword: true,\n\t\t\t},\n\t\t\tdefault: '',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Ignore SSL Issues (Insecure)',\n\t\t\tname: 'allowUnauthorizedCerts',\n\t\t\ttype: 'boolean',\n\t\t\tdescription: 'Whether to connect even if SSL certificate validation is not possible',\n\t\t\tdefault: false,\n\t\t},\n\t];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,mBAA8C;AAAA,EAApD;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,UAAU;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,SAAS;AAAA,QACT,UAAU;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,MACV;AAAA,IACD;AAAA;AACD;", "names": []}