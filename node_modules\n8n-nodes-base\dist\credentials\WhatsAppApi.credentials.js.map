{"version": 3, "sources": ["../../credentials/WhatsAppApi.credentials.ts"], "sourcesContent": ["import type {\n\tIAuthenticateGeneric,\n\tICredentialTestRequest,\n\tICredentialType,\n\tINodeProperties,\n} from 'n8n-workflow';\n\nexport class WhatsAppApi implements ICredentialType {\n\tname = 'whatsAppApi';\n\n\tdisplayName = 'WhatsApp API';\n\n\tdocumentationUrl = 'whatsApp';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Access Token',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\tname: 'accessToken',\n\t\t\tdefault: '',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Business Account ID',\n\t\t\ttype: 'string',\n\t\t\tname: 'businessAccountId',\n\t\t\tdefault: '',\n\t\t\trequired: true,\n\t\t},\n\t];\n\n\tauthenticate: IAuthenticateGeneric = {\n\t\ttype: 'generic',\n\t\tproperties: {\n\t\t\theaders: {\n\t\t\t\tAuthorization: '=Bearer {{$credentials.accessToken}}',\n\t\t\t},\n\t\t},\n\t};\n\n\ttest: ICredentialTestRequest = {\n\t\trequest: {\n\t\t\tbaseURL: 'https://graph.facebook.com/v13.0',\n\t\t\turl: '/',\n\t\t\tignoreHttpStatusErrors: true,\n\t\t},\n\t\trules: [\n\t\t\t{\n\t\t\t\ttype: 'responseSuccessBody',\n\t\t\t\tproperties: {\n\t\t\t\t\tkey: 'error.type',\n\t\t\t\t\tvalue: 'OAuthException',\n\t\t\t\t\tmessage: 'Invalid access token',\n\t\t\t\t},\n\t\t\t},\n\t\t],\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOO,MAAM,YAAuC;AAAA,EAA7C;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACX;AAAA,IACD;AAEA,wBAAqC;AAAA,MACpC,MAAM;AAAA,MACN,YAAY;AAAA,QACX,SAAS;AAAA,UACR,eAAe;AAAA,QAChB;AAAA,MACD;AAAA,IACD;AAEA,gBAA+B;AAAA,MAC9B,SAAS;AAAA,QACR,SAAS;AAAA,QACT,KAAK;AAAA,QACL,wBAAwB;AAAA,MACzB;AAAA,MACA,OAAO;AAAA,QACN;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,YACX,KAAK;AAAA,YACL,OAAO;AAAA,YACP,SAAS;AAAA,UACV;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA;AACD;", "names": []}