{"version": 3, "sources": ["../../../../../../nodes/Google/Analytics/v2/helpers/utils.ts"], "sourcesContent": ["import { DateTime } from 'luxon';\nimport type {\n\tIExecuteFunctions,\n\tILoadOptionsFunctions,\n\tIDataObject,\n\tINodeListSearchItems,\n\tINodePropertyOptions,\n} from 'n8n-workflow';\nimport { NodeOperationError } from 'n8n-workflow';\n\n// tslint:disable-next-line:no-any\nexport function simplify(responseData: any | [any]) {\n\tconst returnData = [];\n\tfor (const {\n\t\tcolumnHeader: { dimensions, metricHeader },\n\t\tdata: { rows },\n\t} of responseData) {\n\t\tif (rows === undefined) {\n\t\t\t// Do not error if there is no data\n\t\t\tcontinue;\n\t\t}\n\t\tconst metrics = metricHeader.metricHeaderEntries.map((entry: { name: string }) => entry.name);\n\t\tfor (const row of rows) {\n\t\t\tconst rowDimensions: IDataObject = {};\n\t\t\tconst rowMetrics: IDataObject = {};\n\t\t\tif (dimensions) {\n\t\t\t\tfor (let i = 0; i < dimensions.length; i++) {\n\t\t\t\t\trowDimensions[dimensions[i]] = row.dimensions[i];\n\t\t\t\t\tfor (const [index, metric] of metrics.entries()) {\n\t\t\t\t\t\trowMetrics[metric] = row.metrics[0].values[index];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tfor (const [index, metric] of metrics.entries()) {\n\t\t\t\t\trowMetrics[metric] = row.metrics[0].values[index];\n\t\t\t\t}\n\t\t\t}\n\t\t\treturnData.push({ ...rowDimensions, ...rowMetrics });\n\t\t}\n\t}\n\n\treturn returnData;\n}\n\n// tslint:disable-next-line:no-any\nexport function merge(responseData: [any]) {\n\tconst response: { columnHeader: IDataObject; data: { rows: [] } } = {\n\t\tcolumnHeader: responseData[0].columnHeader,\n\t\tdata: responseData[0].data,\n\t};\n\tconst allRows = [];\n\tfor (const {\n\t\tdata: { rows },\n\t} of responseData) {\n\t\tallRows.push(...(rows as IDataObject[]));\n\t}\n\tresponse.data.rows = allRows as [];\n\treturn [response];\n}\n\nexport function simplifyGA4(response: IDataObject) {\n\tif (!response.rows) return [];\n\tconst dimensionHeaders = ((response.dimensionHeaders as IDataObject[]) || []).map(\n\t\t(header) => header.name as string,\n\t);\n\tconst metricHeaders = ((response.metricHeaders as IDataObject[]) || []).map(\n\t\t(header) => header.name as string,\n\t);\n\tconst returnData: IDataObject[] = [];\n\n\t(response.rows as IDataObject[]).forEach((row) => {\n\t\tif (!row) return;\n\t\tconst rowDimensions: IDataObject = {};\n\t\tconst rowMetrics: IDataObject = {};\n\t\tdimensionHeaders.forEach((dimension, index) => {\n\t\t\trowDimensions[dimension] = (row.dimensionValues as IDataObject[])[index].value;\n\t\t});\n\t\tmetricHeaders.forEach((metric, index) => {\n\t\t\trowMetrics[metric] = (row.metricValues as IDataObject[])[index].value;\n\t\t});\n\t\treturnData.push({ ...rowDimensions, ...rowMetrics });\n\t});\n\n\treturn returnData;\n}\n\nexport function processFilters(expression: IDataObject): IDataObject[] {\n\tconst processedFilters: IDataObject[] = [];\n\n\tObject.entries(expression).forEach((entry) => {\n\t\tconst [filterType, filters] = entry;\n\n\t\t(filters as IDataObject[]).forEach((filter) => {\n\t\t\tlet fieldName = '';\n\t\t\tswitch (filter.listName) {\n\t\t\t\tcase 'other':\n\t\t\t\t\tfieldName = filter.name as string;\n\t\t\t\t\tdelete filter.name;\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'custom':\n\t\t\t\t\tfieldName = filter.name as string;\n\t\t\t\t\tdelete filter.name;\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\tfieldName = filter.listName as string;\n\t\t\t}\n\t\t\tdelete filter.listName;\n\n\t\t\tif (filterType === 'inListFilter') {\n\t\t\t\tfilter.values = (filter.values as string).split(',');\n\t\t\t}\n\n\t\t\tif (filterType === 'numericFilter') {\n\t\t\t\tfilter.value = {\n\t\t\t\t\t[filter.valueType as string]: filter.value,\n\t\t\t\t};\n\t\t\t\tdelete filter.valueType;\n\t\t\t}\n\n\t\t\tif (filterType === 'betweenFilter') {\n\t\t\t\tfilter.fromValue = {\n\t\t\t\t\t[filter.valueType as string]: filter.fromValue,\n\t\t\t\t};\n\t\t\t\tfilter.toValue = {\n\t\t\t\t\t[filter.valueType as string]: filter.toValue,\n\t\t\t\t};\n\t\t\t\tdelete filter.valueType;\n\t\t\t}\n\n\t\t\tprocessedFilters.push({\n\t\t\t\tfilter: {\n\t\t\t\t\tfieldName,\n\t\t\t\t\t[filterType]: filter,\n\t\t\t\t},\n\t\t\t});\n\t\t});\n\t});\n\n\treturn processedFilters;\n}\n\nexport function prepareDateRange(\n\tthis: IExecuteFunctions | ILoadOptionsFunctions,\n\tperiod: string,\n\titemIndex: number,\n) {\n\tconst dateRanges: IDataObject[] = [];\n\n\tswitch (period) {\n\t\tcase 'today':\n\t\t\tdateRanges.push({\n\t\t\t\tstartDate: DateTime.local().startOf('day').toISODate(),\n\t\t\t\tendDate: DateTime.now().toISODate(),\n\t\t\t});\n\t\t\tbreak;\n\t\tcase 'yesterday':\n\t\t\tdateRanges.push({\n\t\t\t\tstartDate: DateTime.local().startOf('day').minus({ days: 1 }).toISODate(),\n\t\t\t\tendDate: DateTime.local().endOf('day').minus({ days: 1 }).toISODate(),\n\t\t\t});\n\t\t\tbreak;\n\t\tcase 'lastCalendarWeek':\n\t\t\tconst begginingOfLastWeek = DateTime.local().startOf('week').minus({ weeks: 1 }).toISODate();\n\t\t\tconst endOfLastWeek = DateTime.local().endOf('week').minus({ weeks: 1 }).toISODate();\n\t\t\tdateRanges.push({\n\t\t\t\tstartDate: begginingOfLastWeek,\n\t\t\t\tendDate: endOfLastWeek,\n\t\t\t});\n\t\t\tbreak;\n\t\tcase 'lastCalendarMonth':\n\t\t\tconst begginingOfLastMonth = DateTime.local()\n\t\t\t\t.startOf('month')\n\t\t\t\t.minus({ months: 1 })\n\t\t\t\t.toISODate();\n\t\t\tconst endOfLastMonth = DateTime.local().endOf('month').minus({ months: 1 }).toISODate();\n\t\t\tdateRanges.push({\n\t\t\t\tstartDate: begginingOfLastMonth,\n\t\t\t\tendDate: endOfLastMonth,\n\t\t\t});\n\t\t\tbreak;\n\t\tcase 'last7days':\n\t\t\tdateRanges.push({\n\t\t\t\tstartDate: DateTime.now().minus({ days: 7 }).toISODate(),\n\t\t\t\tendDate: DateTime.now().toISODate(),\n\t\t\t});\n\t\t\tbreak;\n\t\tcase 'last30days':\n\t\t\tdateRanges.push({\n\t\t\t\tstartDate: DateTime.now().minus({ days: 30 }).toISODate(),\n\t\t\t\tendDate: DateTime.now().toISODate(),\n\t\t\t});\n\t\t\tbreak;\n\t\tcase 'custom':\n\t\t\tconst start = DateTime.fromISO(this.getNodeParameter('startDate', itemIndex, '') as string);\n\t\t\tconst end = DateTime.fromISO(this.getNodeParameter('endDate', itemIndex, '') as string);\n\n\t\t\tif (start > end) {\n\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t`Parameter Start: ${start.toISO()} cannot be after End: ${end.toISO()}`,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tdateRanges.push({\n\t\t\t\tstartDate: start.toISODate(),\n\t\t\t\tendDate: end.toISODate(),\n\t\t\t});\n\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tthrow new NodeOperationError(\n\t\t\t\tthis.getNode(),\n\t\t\t\t`The period '${period}' is not supported, to specify own period use 'custom' option`,\n\t\t\t);\n\t}\n\n\treturn dateRanges;\n}\n\nexport const defaultStartDate = () => DateTime.now().startOf('day').minus({ days: 8 }).toISO();\n\nexport const defaultEndDate = () => DateTime.now().startOf('day').minus({ days: 1 }).toISO();\n\nexport function checkDuplicates(\n\tthis: IExecuteFunctions,\n\tdata: IDataObject[],\n\tkey: string,\n\ttype: string,\n) {\n\tconst fields = data.map((item) => item[key] as string);\n\tconst duplicates = fields.filter((field, i) => fields.indexOf(field) !== i);\n\tconst unique = Array.from(new Set(duplicates));\n\tif (unique.length) {\n\t\tthrow new NodeOperationError(\n\t\t\tthis.getNode(),\n\t\t\t`A ${type} is specified more than once (${unique.join(', ')})`,\n\t\t);\n\t}\n}\n\nexport function sortLoadOptions(data: INodePropertyOptions[] | INodeListSearchItems[]) {\n\tconst returnData = [...data];\n\treturnData.sort((a, b) => {\n\t\tconst aName = a.name.toLowerCase();\n\t\tconst bName = b.name.toLowerCase();\n\t\tif (aName < bName) {\n\t\t\treturn -1;\n\t\t}\n\t\tif (aName > bName) {\n\t\t\treturn 1;\n\t\t}\n\t\treturn 0;\n\t});\n\n\treturn returnData;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAyB;AAQzB,0BAAmC;AAG5B,SAAS,SAAS,cAA2B;AACnD,QAAM,aAAa,CAAC;AACpB,aAAW;AAAA,IACV,cAAc,EAAE,YAAY,aAAa;AAAA,IACzC,MAAM,EAAE,KAAK;AAAA,EACd,KAAK,cAAc;AAClB,QAAI,SAAS,QAAW;AAEvB;AAAA,IACD;AACA,UAAM,UAAU,aAAa,oBAAoB,IAAI,CAAC,UAA4B,MAAM,IAAI;AAC5F,eAAW,OAAO,MAAM;AACvB,YAAM,gBAA6B,CAAC;AACpC,YAAM,aAA0B,CAAC;AACjC,UAAI,YAAY;AACf,iBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC3C,wBAAc,WAAW,CAAC,CAAC,IAAI,IAAI,WAAW,CAAC;AAC/C,qBAAW,CAAC,OAAO,MAAM,KAAK,QAAQ,QAAQ,GAAG;AAChD,uBAAW,MAAM,IAAI,IAAI,QAAQ,CAAC,EAAE,OAAO,KAAK;AAAA,UACjD;AAAA,QACD;AAAA,MACD,OAAO;AACN,mBAAW,CAAC,OAAO,MAAM,KAAK,QAAQ,QAAQ,GAAG;AAChD,qBAAW,MAAM,IAAI,IAAI,QAAQ,CAAC,EAAE,OAAO,KAAK;AAAA,QACjD;AAAA,MACD;AACA,iBAAW,KAAK,EAAE,GAAG,eAAe,GAAG,WAAW,CAAC;AAAA,IACpD;AAAA,EACD;AAEA,SAAO;AACR;AAGO,SAAS,MAAM,cAAqB;AAC1C,QAAM,WAA8D;AAAA,IACnE,cAAc,aAAa,CAAC,EAAE;AAAA,IAC9B,MAAM,aAAa,CAAC,EAAE;AAAA,EACvB;AACA,QAAM,UAAU,CAAC;AACjB,aAAW;AAAA,IACV,MAAM,EAAE,KAAK;AAAA,EACd,KAAK,cAAc;AAClB,YAAQ,KAAK,GAAI,IAAsB;AAAA,EACxC;AACA,WAAS,KAAK,OAAO;AACrB,SAAO,CAAC,QAAQ;AACjB;AAEO,SAAS,YAAY,UAAuB;AAClD,MAAI,CAAC,SAAS,KAAM,QAAO,CAAC;AAC5B,QAAM,oBAAqB,SAAS,oBAAsC,CAAC,GAAG;AAAA,IAC7E,CAAC,WAAW,OAAO;AAAA,EACpB;AACA,QAAM,iBAAkB,SAAS,iBAAmC,CAAC,GAAG;AAAA,IACvE,CAAC,WAAW,OAAO;AAAA,EACpB;AACA,QAAM,aAA4B,CAAC;AAEnC,EAAC,SAAS,KAAuB,QAAQ,CAAC,QAAQ;AACjD,QAAI,CAAC,IAAK;AACV,UAAM,gBAA6B,CAAC;AACpC,UAAM,aAA0B,CAAC;AACjC,qBAAiB,QAAQ,CAAC,WAAW,UAAU;AAC9C,oBAAc,SAAS,IAAK,IAAI,gBAAkC,KAAK,EAAE;AAAA,IAC1E,CAAC;AACD,kBAAc,QAAQ,CAAC,QAAQ,UAAU;AACxC,iBAAW,MAAM,IAAK,IAAI,aAA+B,KAAK,EAAE;AAAA,IACjE,CAAC;AACD,eAAW,KAAK,EAAE,GAAG,eAAe,GAAG,WAAW,CAAC;AAAA,EACpD,CAAC;AAED,SAAO;AACR;AAEO,SAAS,eAAe,YAAwC;AACtE,QAAM,mBAAkC,CAAC;AAEzC,SAAO,QAAQ,UAAU,EAAE,QAAQ,CAAC,UAAU;AAC7C,UAAM,CAAC,YAAY,OAAO,IAAI;AAE9B,IAAC,QAA0B,QAAQ,CAAC,WAAW;AAC9C,UAAI,YAAY;AAChB,cAAQ,OAAO,UAAU;AAAA,QACxB,KAAK;AACJ,sBAAY,OAAO;AACnB,iBAAO,OAAO;AACd;AAAA,QACD,KAAK;AACJ,sBAAY,OAAO;AACnB,iBAAO,OAAO;AACd;AAAA,QACD;AACC,sBAAY,OAAO;AAAA,MACrB;AACA,aAAO,OAAO;AAEd,UAAI,eAAe,gBAAgB;AAClC,eAAO,SAAU,OAAO,OAAkB,MAAM,GAAG;AAAA,MACpD;AAEA,UAAI,eAAe,iBAAiB;AACnC,eAAO,QAAQ;AAAA,UACd,CAAC,OAAO,SAAmB,GAAG,OAAO;AAAA,QACtC;AACA,eAAO,OAAO;AAAA,MACf;AAEA,UAAI,eAAe,iBAAiB;AACnC,eAAO,YAAY;AAAA,UAClB,CAAC,OAAO,SAAmB,GAAG,OAAO;AAAA,QACtC;AACA,eAAO,UAAU;AAAA,UAChB,CAAC,OAAO,SAAmB,GAAG,OAAO;AAAA,QACtC;AACA,eAAO,OAAO;AAAA,MACf;AAEA,uBAAiB,KAAK;AAAA,QACrB,QAAQ;AAAA,UACP;AAAA,UACA,CAAC,UAAU,GAAG;AAAA,QACf;AAAA,MACD,CAAC;AAAA,IACF,CAAC;AAAA,EACF,CAAC;AAED,SAAO;AACR;AAEO,SAAS,iBAEf,QACA,WACC;AACD,QAAM,aAA4B,CAAC;AAEnC,UAAQ,QAAQ;AAAA,IACf,KAAK;AACJ,iBAAW,KAAK;AAAA,QACf,WAAW,sBAAS,MAAM,EAAE,QAAQ,KAAK,EAAE,UAAU;AAAA,QACrD,SAAS,sBAAS,IAAI,EAAE,UAAU;AAAA,MACnC,CAAC;AACD;AAAA,IACD,KAAK;AACJ,iBAAW,KAAK;AAAA,QACf,WAAW,sBAAS,MAAM,EAAE,QAAQ,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,UAAU;AAAA,QACxE,SAAS,sBAAS,MAAM,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,UAAU;AAAA,MACrE,CAAC;AACD;AAAA,IACD,KAAK;AACJ,YAAM,sBAAsB,sBAAS,MAAM,EAAE,QAAQ,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,UAAU;AAC3F,YAAM,gBAAgB,sBAAS,MAAM,EAAE,MAAM,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,UAAU;AACnF,iBAAW,KAAK;AAAA,QACf,WAAW;AAAA,QACX,SAAS;AAAA,MACV,CAAC;AACD;AAAA,IACD,KAAK;AACJ,YAAM,uBAAuB,sBAAS,MAAM,EAC1C,QAAQ,OAAO,EACf,MAAM,EAAE,QAAQ,EAAE,CAAC,EACnB,UAAU;AACZ,YAAM,iBAAiB,sBAAS,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,UAAU;AACtF,iBAAW,KAAK;AAAA,QACf,WAAW;AAAA,QACX,SAAS;AAAA,MACV,CAAC;AACD;AAAA,IACD,KAAK;AACJ,iBAAW,KAAK;AAAA,QACf,WAAW,sBAAS,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,UAAU;AAAA,QACvD,SAAS,sBAAS,IAAI,EAAE,UAAU;AAAA,MACnC,CAAC;AACD;AAAA,IACD,KAAK;AACJ,iBAAW,KAAK;AAAA,QACf,WAAW,sBAAS,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,UAAU;AAAA,QACxD,SAAS,sBAAS,IAAI,EAAE,UAAU;AAAA,MACnC,CAAC;AACD;AAAA,IACD,KAAK;AACJ,YAAM,QAAQ,sBAAS,QAAQ,KAAK,iBAAiB,aAAa,WAAW,EAAE,CAAW;AAC1F,YAAM,MAAM,sBAAS,QAAQ,KAAK,iBAAiB,WAAW,WAAW,EAAE,CAAW;AAEtF,UAAI,QAAQ,KAAK;AAChB,cAAM,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,oBAAoB,MAAM,MAAM,CAAC,yBAAyB,IAAI,MAAM,CAAC;AAAA,QACtE;AAAA,MACD;AAEA,iBAAW,KAAK;AAAA,QACf,WAAW,MAAM,UAAU;AAAA,QAC3B,SAAS,IAAI,UAAU;AAAA,MACxB,CAAC;AAED;AAAA,IACD;AACC,YAAM,IAAI;AAAA,QACT,KAAK,QAAQ;AAAA,QACb,eAAe,MAAM;AAAA,MACtB;AAAA,EACF;AAEA,SAAO;AACR;AAEO,MAAM,mBAAmB,MAAM,sBAAS,IAAI,EAAE,QAAQ,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM;AAEtF,MAAM,iBAAiB,MAAM,sBAAS,IAAI,EAAE,QAAQ,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM;AAEpF,SAAS,gBAEf,MACA,KACA,MACC;AACD,QAAM,SAAS,KAAK,IAAI,CAAC,SAAS,KAAK,GAAG,CAAW;AACrD,QAAM,aAAa,OAAO,OAAO,CAAC,OAAO,MAAM,OAAO,QAAQ,KAAK,MAAM,CAAC;AAC1E,QAAM,SAAS,MAAM,KAAK,IAAI,IAAI,UAAU,CAAC;AAC7C,MAAI,OAAO,QAAQ;AAClB,UAAM,IAAI;AAAA,MACT,KAAK,QAAQ;AAAA,MACb,KAAK,IAAI,iCAAiC,OAAO,KAAK,IAAI,CAAC;AAAA,IAC5D;AAAA,EACD;AACD;AAEO,SAAS,gBAAgB,MAAuD;AACtF,QAAM,aAAa,CAAC,GAAG,IAAI;AAC3B,aAAW,KAAK,CAAC,GAAG,MAAM;AACzB,UAAM,QAAQ,EAAE,KAAK,YAAY;AACjC,UAAM,QAAQ,EAAE,KAAK,YAAY;AACjC,QAAI,QAAQ,OAAO;AAClB,aAAO;AAAA,IACR;AACA,QAAI,QAAQ,OAAO;AAClB,aAAO;AAAA,IACR;AACA,WAAO;AAAA,EACR,CAAC;AAED,SAAO;AACR;", "names": []}