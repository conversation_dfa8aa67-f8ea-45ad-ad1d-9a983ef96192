{"version": 3, "sources": ["../../../../../nodes/TheHiveProject/actions/task/update.operation.ts"], "sourcesContent": ["import set from 'lodash/set';\nimport type {\n\tIDataObject,\n\tIExecuteFunctions,\n\tINodeExecutionData,\n\tINodeProperties,\n} from 'n8n-workflow';\nimport { NodeOperationError } from 'n8n-workflow';\n\nimport { updateDisplayOptions, wrapData } from '@utils/utilities';\n\nimport { fixFieldType, prepareInputItem } from '../../helpers/utils';\nimport { theHiveApiRequest } from '../../transport';\n\nconst properties: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Fields',\n\t\tname: 'taskUpdateFields',\n\t\ttype: 'resourceMapper',\n\t\tdefault: {\n\t\t\tmappingMode: 'defineBelow',\n\t\t\tvalue: null,\n\t\t},\n\t\tnoDataExpression: true,\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tresourceMapper: {\n\t\t\t\tresourceMapperMethod: 'getTaskUpdateFields',\n\t\t\t\tmode: 'update',\n\t\t\t\tvaluesLabel: 'Fields',\n\t\t\t\taddAllFields: true,\n\t\t\t\tmultiKeyMatch: true,\n\t\t\t},\n\t\t},\n\t},\n];\n\nconst displayOptions = {\n\tshow: {\n\t\tresource: ['task'],\n\t\toperation: ['update'],\n\t},\n};\n\nexport const description = updateDisplayOptions(displayOptions, properties);\n\nexport async function execute(\n\tthis: IExecuteFunctions,\n\ti: number,\n\titem: INodeExecutionData,\n): Promise<INodeExecutionData[]> {\n\tlet body: IDataObject = {};\n\tlet updated = 1;\n\n\tconst dataMode = this.getNodeParameter('taskUpdateFields.mappingMode', i) as string;\n\n\tif (dataMode === 'autoMapInputData') {\n\t\tconst schema = this.getNodeParameter('taskUpdateFields.schema', i) as IDataObject[];\n\t\tbody = prepareInputItem(item.json, schema, i);\n\t}\n\n\tif (dataMode === 'defineBelow') {\n\t\tconst taskUpdateFields = this.getNodeParameter('taskUpdateFields.value', i, []) as IDataObject;\n\t\tbody = taskUpdateFields;\n\t}\n\n\tbody = fixFieldType(body);\n\n\tconst fieldsToMatchOn = this.getNodeParameter('taskUpdateFields.matchingColumns', i) as string[];\n\n\tconst updateBody: IDataObject = {};\n\tconst matchFields: IDataObject = {};\n\tconst { id } = body; // id would be used if matching on id, also we need to remove it from the body\n\n\tfor (const field of Object.keys(body)) {\n\t\tif (fieldsToMatchOn.includes(field)) {\n\t\t\t// if field is in fieldsToMatchOn, we need to exclude it from the updateBody, as values used for matching should not be updated\n\t\t\tmatchFields[field] = body[field];\n\t\t} else {\n\t\t\t// use set to construct the updateBody, as it allows to process customFields.fieldName\n\t\t\t// if customFields provided under customFields property, it will be send as is\n\t\t\tset(updateBody, field, body[field]);\n\t\t}\n\t}\n\n\tif (fieldsToMatchOn.includes('id')) {\n\t\tawait theHiveApiRequest.call(this, 'PATCH', `/v1/task/${id}`, body);\n\t} else {\n\t\tconst filter = {\n\t\t\t_name: 'filter',\n\t\t\t_and: fieldsToMatchOn.map((field) => ({\n\t\t\t\t_eq: {\n\t\t\t\t\t_field: field,\n\t\t\t\t\t_value: matchFields[field],\n\t\t\t\t},\n\t\t\t})),\n\t\t};\n\n\t\tconst queryBody = {\n\t\t\tquery: [\n\t\t\t\t{\n\t\t\t\t\t_name: 'listTask',\n\t\t\t\t},\n\t\t\t\tfilter,\n\t\t\t],\n\t\t};\n\n\t\tconst matches = (await theHiveApiRequest.call(\n\t\t\tthis,\n\t\t\t'POST',\n\t\t\t'/v1/query',\n\t\t\tqueryBody,\n\t\t)) as IDataObject[];\n\n\t\tif (!matches.length) {\n\t\t\tthrow new NodeOperationError(this.getNode(), 'No matching alerts found');\n\t\t}\n\t\tconst ids = matches.map((match) => match._id);\n\t\tupdated = ids.length;\n\n\t\tupdateBody.ids = ids;\n\n\t\tawait theHiveApiRequest.call(this, 'PATCH', '/v1/task/_bulk', updateBody);\n\t}\n\n\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\twrapData({ success: true, updated }),\n\t\t{\n\t\t\titemData: { item: i },\n\t\t},\n\t);\n\n\treturn executionData;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAgB;AAOhB,0BAAmC;AAEnC,uBAA+C;AAE/C,mBAA+C;AAC/C,uBAAkC;AAElC,MAAM,aAAgC;AAAA,EACrC;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,MACR,aAAa;AAAA,MACb,OAAO;AAAA,IACR;AAAA,IACA,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,gBAAgB;AAAA,QACf,sBAAsB;AAAA,QACtB,MAAM;AAAA,QACN,aAAa;AAAA,QACb,cAAc;AAAA,QACd,eAAe;AAAA,MAChB;AAAA,IACD;AAAA,EACD;AACD;AAEA,MAAM,iBAAiB;AAAA,EACtB,MAAM;AAAA,IACL,UAAU,CAAC,MAAM;AAAA,IACjB,WAAW,CAAC,QAAQ;AAAA,EACrB;AACD;AAEO,MAAM,kBAAc,uCAAqB,gBAAgB,UAAU;AAE1E,eAAsB,QAErB,GACA,MACgC;AAChC,MAAI,OAAoB,CAAC;AACzB,MAAI,UAAU;AAEd,QAAM,WAAW,KAAK,iBAAiB,gCAAgC,CAAC;AAExE,MAAI,aAAa,oBAAoB;AACpC,UAAM,SAAS,KAAK,iBAAiB,2BAA2B,CAAC;AACjE,eAAO,+BAAiB,KAAK,MAAM,QAAQ,CAAC;AAAA,EAC7C;AAEA,MAAI,aAAa,eAAe;AAC/B,UAAM,mBAAmB,KAAK,iBAAiB,0BAA0B,GAAG,CAAC,CAAC;AAC9E,WAAO;AAAA,EACR;AAEA,aAAO,2BAAa,IAAI;AAExB,QAAM,kBAAkB,KAAK,iBAAiB,oCAAoC,CAAC;AAEnF,QAAM,aAA0B,CAAC;AACjC,QAAM,cAA2B,CAAC;AAClC,QAAM,EAAE,GAAG,IAAI;AAEf,aAAW,SAAS,OAAO,KAAK,IAAI,GAAG;AACtC,QAAI,gBAAgB,SAAS,KAAK,GAAG;AAEpC,kBAAY,KAAK,IAAI,KAAK,KAAK;AAAA,IAChC,OAAO;AAGN,qBAAAA,SAAI,YAAY,OAAO,KAAK,KAAK,CAAC;AAAA,IACnC;AAAA,EACD;AAEA,MAAI,gBAAgB,SAAS,IAAI,GAAG;AACnC,UAAM,mCAAkB,KAAK,MAAM,SAAS,YAAY,EAAE,IAAI,IAAI;AAAA,EACnE,OAAO;AACN,UAAM,SAAS;AAAA,MACd,OAAO;AAAA,MACP,MAAM,gBAAgB,IAAI,CAAC,WAAW;AAAA,QACrC,KAAK;AAAA,UACJ,QAAQ;AAAA,UACR,QAAQ,YAAY,KAAK;AAAA,QAC1B;AAAA,MACD,EAAE;AAAA,IACH;AAEA,UAAM,YAAY;AAAA,MACjB,OAAO;AAAA,QACN;AAAA,UACC,OAAO;AAAA,QACR;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAEA,UAAM,UAAW,MAAM,mCAAkB;AAAA,MACxC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAEA,QAAI,CAAC,QAAQ,QAAQ;AACpB,YAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,0BAA0B;AAAA,IACxE;AACA,UAAM,MAAM,QAAQ,IAAI,CAAC,UAAU,MAAM,GAAG;AAC5C,cAAU,IAAI;AAEd,eAAW,MAAM;AAEjB,UAAM,mCAAkB,KAAK,MAAM,SAAS,kBAAkB,UAAU;AAAA,EACzE;AAEA,QAAM,gBAAgB,KAAK,QAAQ;AAAA,QAClC,2BAAS,EAAE,SAAS,MAAM,QAAQ,CAAC;AAAA,IACnC;AAAA,MACC,UAAU,EAAE,MAAM,EAAE;AAAA,IACrB;AAAA,EACD;AAEA,SAAO;AACR;", "names": ["set"]}