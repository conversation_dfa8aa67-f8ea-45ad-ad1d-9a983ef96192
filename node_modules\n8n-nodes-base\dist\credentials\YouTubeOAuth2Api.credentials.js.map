{"version": 3, "sources": ["../../credentials/YouTubeOAuth2Api.credentials.ts"], "sourcesContent": ["import type { ICredentialType, INodeProperties, Icon } from 'n8n-workflow';\n\n//https://developers.google.com/youtube/v3/guides/auth/client-side-web-apps#identify-access-scopes\nconst scopes = [\n\t'https://www.googleapis.com/auth/youtube',\n\t'https://www.googleapis.com/auth/youtubepartner',\n\t'https://www.googleapis.com/auth/youtube.force-ssl',\n\t'https://www.googleapis.com/auth/youtube.upload',\n\t'https://www.googleapis.com/auth/youtubepartner-channel-audit',\n];\n\nexport class YouTubeOAuth2Api implements ICredentialType {\n\tname = 'youTubeOAuth2Api';\n\n\ticon: Icon = 'node:n8n-nodes-base.youTube';\n\n\textends = ['googleOAuth2Api'];\n\n\tdisplayName = 'YouTube OAuth2 API';\n\n\tdocumentationUrl = 'google';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Scope',\n\t\t\tname: 'scope',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: scopes.join(' '),\n\t\t},\n\t];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,MAAM,SAAS;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEO,MAAM,iBAA4C;AAAA,EAAlD;AACN,gBAAO;AAEP,gBAAa;AAEb,mBAAU,CAAC,iBAAiB;AAE5B,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,OAAO,KAAK,GAAG;AAAA,MACzB;AAAA,IACD;AAAA;AACD;", "names": []}