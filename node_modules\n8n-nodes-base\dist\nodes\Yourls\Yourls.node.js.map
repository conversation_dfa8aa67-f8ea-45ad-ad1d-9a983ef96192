{"version": 3, "sources": ["../../../nodes/Yourls/Yourls.node.ts"], "sourcesContent": ["import {\n\ttype IExecuteFunctions,\n\ttype IDataObject,\n\ttype INodeExecutionData,\n\ttype INodeType,\n\ttype INodeTypeDescription,\n\tNodeConnectionTypes,\n} from 'n8n-workflow';\n\nimport { yourlsApiRequest } from './GenericFunctions';\nimport { urlFields, urlOperations } from './UrlDescription';\n\nexport class Yourls implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Yourls',\n\t\tname: 'yourls',\n\t\t// eslint-disable-next-line n8n-nodes-base/node-class-description-icon-not-svg\n\t\ticon: 'file:yourls.png',\n\t\tgroup: ['input'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume Yourls API',\n\t\tdefaults: {\n\t\t\tname: 'Yourls',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'yourls<PERSON><PERSON>',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'URL',\n\t\t\t\t\t\tvalue: 'url',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'url',\n\t\t\t},\n\t\t\t...urlOperations,\n\t\t\t...urlFields,\n\t\t],\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: IDataObject[] = [];\n\t\tconst length = items.length;\n\t\tconst qs: IDataObject = {};\n\t\tlet responseData;\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'url') {\n\t\t\t\t\tif (operation === 'shorten') {\n\t\t\t\t\t\tconst url = this.getNodeParameter('url', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tqs.url = url;\n\t\t\t\t\t\tqs.action = 'shorturl';\n\t\t\t\t\t\tObject.assign(qs, additionalFields);\n\t\t\t\t\t\tresponseData = await yourlsApiRequest.call(this, 'GET', {}, qs);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (operation === 'expand') {\n\t\t\t\t\t\tconst shortUrl = this.getNodeParameter('shortUrl', i) as string;\n\t\t\t\t\t\tqs.shorturl = shortUrl;\n\t\t\t\t\t\tqs.action = 'expand';\n\t\t\t\t\t\tresponseData = await yourlsApiRequest.call(this, 'GET', {}, qs);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (operation === 'stats') {\n\t\t\t\t\t\tconst shortUrl = this.getNodeParameter('shortUrl', i) as string;\n\t\t\t\t\t\tqs.shorturl = shortUrl;\n\t\t\t\t\t\tqs.action = 'url-stats';\n\t\t\t\t\t\tresponseData = await yourlsApiRequest.call(this, 'GET', {}, qs);\n\t\t\t\t\t\tresponseData = responseData.link;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (Array.isArray(responseData)) {\n\t\t\t\t\treturnData.push.apply(returnData, responseData as IDataObject[]);\n\t\t\t\t} else {\n\t\t\t\t\treturnData.push(responseData as IDataObject);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ error: error.message });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\t\treturn [this.helpers.returnJsonArray(returnData)];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAOO;AAEP,8BAAiC;AACjC,4BAAyC;AAElC,MAAM,OAA4B;AAAA,EAAlC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA;AAAA,MAEN,MAAM;AAAA,MACN,OAAO,CAAC,OAAO;AAAA,MACf,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAA4B,CAAC;AACnC,UAAM,SAAS,MAAM;AACrB,UAAM,KAAkB,CAAC;AACzB,QAAI;AACJ,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,YAAI,aAAa,OAAO;AACvB,cAAI,cAAc,WAAW;AAC5B,kBAAM,MAAM,KAAK,iBAAiB,OAAO,CAAC;AAC1C,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,eAAG,MAAM;AACT,eAAG,SAAS;AACZ,mBAAO,OAAO,IAAI,gBAAgB;AAClC,2BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,CAAC,GAAG,EAAE;AAAA,UAC/D;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,eAAG,WAAW;AACd,eAAG,SAAS;AACZ,2BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,CAAC,GAAG,EAAE;AAAA,UAC/D;AAEA,cAAI,cAAc,SAAS;AAC1B,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,eAAG,WAAW;AACd,eAAG,SAAS;AACZ,2BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,CAAC,GAAG,EAAE;AAC9D,2BAAe,aAAa;AAAA,UAC7B;AAAA,QACD;AACA,YAAI,MAAM,QAAQ,YAAY,GAAG;AAChC,qBAAW,KAAK,MAAM,YAAY,YAA6B;AAAA,QAChE,OAAO;AACN,qBAAW,KAAK,YAA2B;AAAA,QAC5C;AAAA,MACD,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,OAAO,MAAM,QAAQ,CAAC;AACxC;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AACA,WAAO,CAAC,KAAK,QAAQ,gBAAgB,UAAU,CAAC;AAAA,EACjD;AACD;", "names": []}