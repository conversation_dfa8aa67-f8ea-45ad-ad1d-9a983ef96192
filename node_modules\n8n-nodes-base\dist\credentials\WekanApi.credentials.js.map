{"version": 3, "sources": ["../../credentials/WekanApi.credentials.ts"], "sourcesContent": ["import type {\n\tIAuthenticateGeneric,\n\tICredentialDataDecryptedObject,\n\tICredentialTestRequest,\n\tICredentialType,\n\tIHttpRequestHelper,\n\tINodeProperties,\n} from 'n8n-workflow';\n\nexport class Wekan<PERSON><PERSON> implements ICredentialType {\n\tname = 'wekan<PERSON><PERSON>';\n\n\tdisplayName = 'Wekan API';\n\n\tdocumentationUrl = 'wekan';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Username',\n\t\t\tname: 'username',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Password',\n\t\t\tname: 'password',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: {\n\t\t\t\tpassword: true,\n\t\t\t},\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'URL',\n\t\t\tname: 'url',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\tplaceholder: 'https://wekan.yourdomain.com',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Session Token',\n\t\t\tname: 'token',\n\t\t\ttype: 'hidden',\n\n\t\t\ttypeOptions: {\n\t\t\t\texpirable: true,\n\t\t\t},\n\t\t\tdefault: '',\n\t\t},\n\t];\n\n\tasync preAuthentication(this: IHttpRequestHelper, credentials: ICredentialDataDecryptedObject) {\n\t\tconst url = credentials.url as string;\n\t\tconst { token } = (await this.helpers.httpRequest({\n\t\t\tmethod: 'POST',\n\t\t\turl: `${url.endsWith('/') ? url.slice(0, -1) : url}/users/login`,\n\t\t\tbody: {\n\t\t\t\tusername: credentials.username,\n\t\t\t\tpassword: credentials.password,\n\t\t\t},\n\t\t})) as { token: string };\n\t\treturn { token };\n\t}\n\n\tauthenticate: IAuthenticateGeneric = {\n\t\ttype: 'generic',\n\t\tproperties: {\n\t\t\theaders: {\n\t\t\t\tAuthorization: '=Bearer {{$credentials.token}}',\n\t\t\t},\n\t\t},\n\t};\n\n\ttest: ICredentialTestRequest = {\n\t\trequest: {\n\t\t\tbaseURL: '={{$credentials.url.replace(new RegExp(\"/$\"), \"\")}}',\n\t\t\turl: '/api/user',\n\t\t},\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AASO,MAAM,SAAoC;AAAA,EAA1C;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QAEN,aAAa;AAAA,UACZ,WAAW;AAAA,QACZ;AAAA,QACA,SAAS;AAAA,MACV;AAAA,IACD;AAeA,wBAAqC;AAAA,MACpC,MAAM;AAAA,MACN,YAAY;AAAA,QACX,SAAS;AAAA,UACR,eAAe;AAAA,QAChB;AAAA,MACD;AAAA,IACD;AAEA,gBAA+B;AAAA,MAC9B,SAAS;AAAA,QACR,SAAS;AAAA,QACT,KAAK;AAAA,MACN;AAAA,IACD;AAAA;AAAA,EA3BA,MAAM,kBAA4C,aAA6C;AAC9F,UAAM,MAAM,YAAY;AACxB,UAAM,EAAE,MAAM,IAAK,MAAM,KAAK,QAAQ,YAAY;AAAA,MACjD,QAAQ;AAAA,MACR,KAAK,GAAG,IAAI,SAAS,GAAG,IAAI,IAAI,MAAM,GAAG,EAAE,IAAI,GAAG;AAAA,MAClD,MAAM;AAAA,QACL,UAAU,YAAY;AAAA,QACtB,UAAU,YAAY;AAAA,MACvB;AAAA,IACD,CAAC;AACD,WAAO,EAAE,MAAM;AAAA,EAChB;AAiBD;", "names": []}