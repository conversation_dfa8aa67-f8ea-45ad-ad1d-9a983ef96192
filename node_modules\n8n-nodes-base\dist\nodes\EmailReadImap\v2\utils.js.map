{"version": 3, "sources": ["../../../../nodes/EmailReadImap/v2/utils.ts"], "sourcesContent": ["import { getParts, type ImapSimple, type Message, type MessagePart } from '@n8n/imap';\nimport { find } from 'lodash';\nimport { simpleParser, type Source as ParserSource } from 'mailparser';\nimport {\n\ttype IBinaryData,\n\ttype INodeExecutionData,\n\ttype IDataObject,\n\ttype ITriggerFunctions,\n\tNodeOperationError,\n\ttype IBinaryKeyData,\n} from 'n8n-workflow';\n\nasync function parseRawEmail(\n\tthis: ITriggerFunctions,\n\tmessageEncoded: ParserSource,\n\tdataPropertyNameDownload: string,\n): Promise<INodeExecutionData> {\n\tconst responseData = await simpleParser(messageEncoded);\n\tconst headers: IDataObject = {};\n\tconst additionalData: IDataObject = {};\n\n\tfor (const header of responseData.headerLines) {\n\t\theaders[header.key] = header.line;\n\t}\n\n\tadditionalData.headers = headers;\n\tadditionalData.headerLines = undefined;\n\n\tconst binaryData: IBinaryKeyData = {};\n\tif (responseData.attachments) {\n\t\tfor (let i = 0; i < responseData.attachments.length; i++) {\n\t\t\tconst attachment = responseData.attachments[i];\n\t\t\tbinaryData[`${dataPropertyNameDownload}${i}`] = await this.helpers.prepareBinaryData(\n\t\t\t\tattachment.content,\n\t\t\t\tattachment.filename,\n\t\t\t\tattachment.contentType,\n\t\t\t);\n\t\t}\n\n\t\tadditionalData.attachments = undefined;\n\t}\n\n\treturn {\n\t\tjson: { ...responseData, ...additionalData },\n\t\tbinary: Object.keys(binaryData).length ? binaryData : undefined,\n\t} as INodeExecutionData;\n}\n\nexport async function getNewEmails(\n\tthis: ITriggerFunctions,\n\timapConnection: ImapSimple,\n\tsearchCriteria: Array<string | string[]>,\n\tstaticData: IDataObject,\n\tpostProcessAction: string,\n\tgetText: (parts: MessagePart[], message: Message, subtype: string) => Promise<string>,\n\tgetAttachment: (\n\t\timapConnection: ImapSimple,\n\t\tparts: MessagePart[],\n\t\tmessage: Message,\n\t) => Promise<IBinaryData[]>,\n): Promise<INodeExecutionData[]> {\n\tconst format = this.getNodeParameter('format', 0) as string;\n\n\tlet fetchOptions = {};\n\n\tif (format === 'simple' || format === 'raw') {\n\t\tfetchOptions = {\n\t\t\tbodies: ['TEXT', 'HEADER'],\n\t\t\tmarkSeen: false,\n\t\t\tstruct: true,\n\t\t};\n\t} else if (format === 'resolved') {\n\t\tfetchOptions = {\n\t\t\tbodies: [''],\n\t\t\tmarkSeen: false,\n\t\t\tstruct: true,\n\t\t};\n\t}\n\n\tconst results = await imapConnection.search(searchCriteria, fetchOptions);\n\n\tconst newEmails: INodeExecutionData[] = [];\n\tlet newEmail: INodeExecutionData;\n\tlet attachments: IBinaryData[];\n\tlet propertyName: string;\n\n\t// All properties get by default moved to metadata except the ones\n\t// which are defined here which get set on the top level.\n\tconst topLevelProperties = ['cc', 'date', 'from', 'subject', 'to'];\n\n\tif (format === 'resolved') {\n\t\tconst dataPropertyAttachmentsPrefixName = this.getNodeParameter(\n\t\t\t'dataPropertyAttachmentsPrefixName',\n\t\t) as string;\n\n\t\tfor (const message of results) {\n\t\t\tif (\n\t\t\t\tstaticData.lastMessageUid !== undefined &&\n\t\t\t\tmessage.attributes.uid <= (staticData.lastMessageUid as number)\n\t\t\t) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tif (\n\t\t\t\tstaticData.lastMessageUid === undefined ||\n\t\t\t\t(staticData.lastMessageUid as number) < message.attributes.uid\n\t\t\t) {\n\t\t\t\tstaticData.lastMessageUid = message.attributes.uid;\n\t\t\t}\n\t\t\tconst part = find(message.parts, { which: '' });\n\n\t\t\tif (part === undefined) {\n\t\t\t\tthrow new NodeOperationError(this.getNode(), 'Email part could not be parsed.');\n\t\t\t}\n\t\t\tconst parsedEmail = await parseRawEmail.call(\n\t\t\t\tthis,\n\t\t\t\tpart.body as Buffer,\n\t\t\t\tdataPropertyAttachmentsPrefixName,\n\t\t\t);\n\n\t\t\tnewEmails.push(parsedEmail);\n\t\t}\n\t} else if (format === 'simple') {\n\t\tconst downloadAttachments = this.getNodeParameter('downloadAttachments') as boolean;\n\n\t\tlet dataPropertyAttachmentsPrefixName = '';\n\t\tif (downloadAttachments) {\n\t\t\tdataPropertyAttachmentsPrefixName = this.getNodeParameter(\n\t\t\t\t'dataPropertyAttachmentsPrefixName',\n\t\t\t) as string;\n\t\t}\n\n\t\tfor (const message of results) {\n\t\t\tif (\n\t\t\t\tstaticData.lastMessageUid !== undefined &&\n\t\t\t\tmessage.attributes.uid <= (staticData.lastMessageUid as number)\n\t\t\t) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tif (\n\t\t\t\tstaticData.lastMessageUid === undefined ||\n\t\t\t\t(staticData.lastMessageUid as number) < message.attributes.uid\n\t\t\t) {\n\t\t\t\tstaticData.lastMessageUid = message.attributes.uid;\n\t\t\t}\n\t\t\tconst parts = getParts(message.attributes.struct as IDataObject[]);\n\n\t\t\tnewEmail = {\n\t\t\t\tjson: {\n\t\t\t\t\ttextHtml: await getText(parts, message, 'html'),\n\t\t\t\t\ttextPlain: await getText(parts, message, 'plain'),\n\t\t\t\t\tmetadata: {} as IDataObject,\n\t\t\t\t\tattributes: {\n\t\t\t\t\t\tuid: message.attributes.uid,\n\t\t\t\t\t} as IDataObject,\n\t\t\t\t},\n\t\t\t};\n\n\t\t\tconst messageHeader = message.parts.filter((part) => part.which === 'HEADER');\n\n\t\t\tconst messageBody = messageHeader[0].body as Record<string, string[]>;\n\t\t\tfor (propertyName of Object.keys(messageBody)) {\n\t\t\t\tif (messageBody[propertyName].length) {\n\t\t\t\t\tif (topLevelProperties.includes(propertyName)) {\n\t\t\t\t\t\tnewEmail.json[propertyName] = messageBody[propertyName][0];\n\t\t\t\t\t} else {\n\t\t\t\t\t\t(newEmail.json.metadata as IDataObject)[propertyName] = messageBody[propertyName][0];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (downloadAttachments) {\n\t\t\t\t// Get attachments and add them if any get found\n\t\t\t\tattachments = await getAttachment(imapConnection, parts, message);\n\t\t\t\tif (attachments.length) {\n\t\t\t\t\tnewEmail.binary = {};\n\t\t\t\t\tfor (let i = 0; i < attachments.length; i++) {\n\t\t\t\t\t\tnewEmail.binary[`${dataPropertyAttachmentsPrefixName}${i}`] = attachments[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tnewEmails.push(newEmail);\n\t\t}\n\t} else if (format === 'raw') {\n\t\tfor (const message of results) {\n\t\t\tif (\n\t\t\t\tstaticData.lastMessageUid !== undefined &&\n\t\t\t\tmessage.attributes.uid <= (staticData.lastMessageUid as number)\n\t\t\t) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tif (\n\t\t\t\tstaticData.lastMessageUid === undefined ||\n\t\t\t\t(staticData.lastMessageUid as number) < message.attributes.uid\n\t\t\t) {\n\t\t\t\tstaticData.lastMessageUid = message.attributes.uid;\n\t\t\t}\n\t\t\tconst part = find(message.parts, { which: 'TEXT' });\n\n\t\t\tif (part === undefined) {\n\t\t\t\tthrow new NodeOperationError(this.getNode(), 'Email part could not be parsed.');\n\t\t\t}\n\t\t\t// Return base64 string\n\t\t\tnewEmail = {\n\t\t\t\tjson: {\n\t\t\t\t\traw: part.body as string,\n\t\t\t\t},\n\t\t\t};\n\n\t\t\tnewEmails.push(newEmail);\n\t\t}\n\t}\n\n\t// only mark messages as seen once processing has finished\n\tif (postProcessAction === 'read') {\n\t\tconst uidList = results.map((e) => e.attributes.uid);\n\t\tif (uidList.length > 0) {\n\t\t\tawait imapConnection.addFlags(uidList, '\\\\SEEN');\n\t\t}\n\t}\n\treturn newEmails;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAA0E;AAC1E,oBAAqB;AACrB,wBAA0D;AAC1D,0BAOO;AAEP,eAAe,cAEd,gBACA,0BAC8B;AAC9B,QAAM,eAAe,UAAM,gCAAa,cAAc;AACtD,QAAM,UAAuB,CAAC;AAC9B,QAAM,iBAA8B,CAAC;AAErC,aAAW,UAAU,aAAa,aAAa;AAC9C,YAAQ,OAAO,GAAG,IAAI,OAAO;AAAA,EAC9B;AAEA,iBAAe,UAAU;AACzB,iBAAe,cAAc;AAE7B,QAAM,aAA6B,CAAC;AACpC,MAAI,aAAa,aAAa;AAC7B,aAAS,IAAI,GAAG,IAAI,aAAa,YAAY,QAAQ,KAAK;AACzD,YAAM,aAAa,aAAa,YAAY,CAAC;AAC7C,iBAAW,GAAG,wBAAwB,GAAG,CAAC,EAAE,IAAI,MAAM,KAAK,QAAQ;AAAA,QAClE,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,MACZ;AAAA,IACD;AAEA,mBAAe,cAAc;AAAA,EAC9B;AAEA,SAAO;AAAA,IACN,MAAM,EAAE,GAAG,cAAc,GAAG,eAAe;AAAA,IAC3C,QAAQ,OAAO,KAAK,UAAU,EAAE,SAAS,aAAa;AAAA,EACvD;AACD;AAEA,eAAsB,aAErB,gBACA,gBACA,YACA,mBACA,SACA,eAKgC;AAChC,QAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,MAAI,eAAe,CAAC;AAEpB,MAAI,WAAW,YAAY,WAAW,OAAO;AAC5C,mBAAe;AAAA,MACd,QAAQ,CAAC,QAAQ,QAAQ;AAAA,MACzB,UAAU;AAAA,MACV,QAAQ;AAAA,IACT;AAAA,EACD,WAAW,WAAW,YAAY;AACjC,mBAAe;AAAA,MACd,QAAQ,CAAC,EAAE;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,IACT;AAAA,EACD;AAEA,QAAM,UAAU,MAAM,eAAe,OAAO,gBAAgB,YAAY;AAExE,QAAM,YAAkC,CAAC;AACzC,MAAI;AACJ,MAAI;AACJ,MAAI;AAIJ,QAAM,qBAAqB,CAAC,MAAM,QAAQ,QAAQ,WAAW,IAAI;AAEjE,MAAI,WAAW,YAAY;AAC1B,UAAM,oCAAoC,KAAK;AAAA,MAC9C;AAAA,IACD;AAEA,eAAW,WAAW,SAAS;AAC9B,UACC,WAAW,mBAAmB,UAC9B,QAAQ,WAAW,OAAQ,WAAW,gBACrC;AACD;AAAA,MACD;AACA,UACC,WAAW,mBAAmB,UAC7B,WAAW,iBAA4B,QAAQ,WAAW,KAC1D;AACD,mBAAW,iBAAiB,QAAQ,WAAW;AAAA,MAChD;AACA,YAAM,WAAO,oBAAK,QAAQ,OAAO,EAAE,OAAO,GAAG,CAAC;AAE9C,UAAI,SAAS,QAAW;AACvB,cAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,iCAAiC;AAAA,MAC/E;AACA,YAAM,cAAc,MAAM,cAAc;AAAA,QACvC;AAAA,QACA,KAAK;AAAA,QACL;AAAA,MACD;AAEA,gBAAU,KAAK,WAAW;AAAA,IAC3B;AAAA,EACD,WAAW,WAAW,UAAU;AAC/B,UAAM,sBAAsB,KAAK,iBAAiB,qBAAqB;AAEvE,QAAI,oCAAoC;AACxC,QAAI,qBAAqB;AACxB,0CAAoC,KAAK;AAAA,QACxC;AAAA,MACD;AAAA,IACD;AAEA,eAAW,WAAW,SAAS;AAC9B,UACC,WAAW,mBAAmB,UAC9B,QAAQ,WAAW,OAAQ,WAAW,gBACrC;AACD;AAAA,MACD;AACA,UACC,WAAW,mBAAmB,UAC7B,WAAW,iBAA4B,QAAQ,WAAW,KAC1D;AACD,mBAAW,iBAAiB,QAAQ,WAAW;AAAA,MAChD;AACA,YAAM,YAAQ,sBAAS,QAAQ,WAAW,MAAuB;AAEjE,iBAAW;AAAA,QACV,MAAM;AAAA,UACL,UAAU,MAAM,QAAQ,OAAO,SAAS,MAAM;AAAA,UAC9C,WAAW,MAAM,QAAQ,OAAO,SAAS,OAAO;AAAA,UAChD,UAAU,CAAC;AAAA,UACX,YAAY;AAAA,YACX,KAAK,QAAQ,WAAW;AAAA,UACzB;AAAA,QACD;AAAA,MACD;AAEA,YAAM,gBAAgB,QAAQ,MAAM,OAAO,CAAC,SAAS,KAAK,UAAU,QAAQ;AAE5E,YAAM,cAAc,cAAc,CAAC,EAAE;AACrC,WAAK,gBAAgB,OAAO,KAAK,WAAW,GAAG;AAC9C,YAAI,YAAY,YAAY,EAAE,QAAQ;AACrC,cAAI,mBAAmB,SAAS,YAAY,GAAG;AAC9C,qBAAS,KAAK,YAAY,IAAI,YAAY,YAAY,EAAE,CAAC;AAAA,UAC1D,OAAO;AACN,YAAC,SAAS,KAAK,SAAyB,YAAY,IAAI,YAAY,YAAY,EAAE,CAAC;AAAA,UACpF;AAAA,QACD;AAAA,MACD;AAEA,UAAI,qBAAqB;AAExB,sBAAc,MAAM,cAAc,gBAAgB,OAAO,OAAO;AAChE,YAAI,YAAY,QAAQ;AACvB,mBAAS,SAAS,CAAC;AACnB,mBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC5C,qBAAS,OAAO,GAAG,iCAAiC,GAAG,CAAC,EAAE,IAAI,YAAY,CAAC;AAAA,UAC5E;AAAA,QACD;AAAA,MACD;AAEA,gBAAU,KAAK,QAAQ;AAAA,IACxB;AAAA,EACD,WAAW,WAAW,OAAO;AAC5B,eAAW,WAAW,SAAS;AAC9B,UACC,WAAW,mBAAmB,UAC9B,QAAQ,WAAW,OAAQ,WAAW,gBACrC;AACD;AAAA,MACD;AACA,UACC,WAAW,mBAAmB,UAC7B,WAAW,iBAA4B,QAAQ,WAAW,KAC1D;AACD,mBAAW,iBAAiB,QAAQ,WAAW;AAAA,MAChD;AACA,YAAM,WAAO,oBAAK,QAAQ,OAAO,EAAE,OAAO,OAAO,CAAC;AAElD,UAAI,SAAS,QAAW;AACvB,cAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,iCAAiC;AAAA,MAC/E;AAEA,iBAAW;AAAA,QACV,MAAM;AAAA,UACL,KAAK,KAAK;AAAA,QACX;AAAA,MACD;AAEA,gBAAU,KAAK,QAAQ;AAAA,IACxB;AAAA,EACD;AAGA,MAAI,sBAAsB,QAAQ;AACjC,UAAM,UAAU,QAAQ,IAAI,CAAC,MAAM,EAAE,WAAW,GAAG;AACnD,QAAI,QAAQ,SAAS,GAAG;AACvB,YAAM,eAAe,SAAS,SAAS,QAAQ;AAAA,IAChD;AAAA,EACD;AACA,SAAO;AACR;", "names": []}