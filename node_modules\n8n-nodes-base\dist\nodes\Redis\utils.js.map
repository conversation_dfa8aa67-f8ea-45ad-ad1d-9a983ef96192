{"version": 3, "sources": ["../../../nodes/Redis/utils.ts"], "sourcesContent": ["import type {\n\tICredentialTestFunctions,\n\tICredentialsDecrypted,\n\tIDataObject,\n\tIExecuteFunctions,\n\tINodeCredentialTestResult,\n} from 'n8n-workflow';\nimport { NodeOperationError } from 'n8n-workflow';\nimport { createClient } from 'redis';\n\nimport type { RedisCredential, RedisClient } from './types';\n\nexport function setupRedisClient(credentials: RedisCredential): RedisClient {\n\treturn createClient({\n\t\tsocket: {\n\t\t\thost: credentials.host,\n\t\t\tport: credentials.port,\n\t\t\ttls: credentials.ssl === true,\n\t\t},\n\t\tdatabase: credentials.database,\n\t\tusername: credentials.user || undefined,\n\t\tpassword: credentials.password || undefined,\n\t});\n}\n\nexport async function redisConnectionTest(\n\tthis: ICredentialTestFunctions,\n\tcredential: ICredentialsDecrypted,\n): Promise<INodeCredentialTestResult> {\n\tconst credentials = credential.data as RedisCredential;\n\n\ttry {\n\t\tconst client = setupRedisClient(credentials);\n\t\tawait client.connect();\n\t\tawait client.ping();\n\t\treturn {\n\t\t\tstatus: 'OK',\n\t\t\tmessage: 'Connection successful!',\n\t\t};\n\t} catch (error) {\n\t\treturn {\n\t\t\tstatus: 'Error',\n\t\t\tmessage: error.message,\n\t\t};\n\t}\n}\n\n/** Parses the given value in a number if it is one else returns a string */\nfunction getParsedValue(value: string): string | number {\n\tif (value.match(/^[\\d\\.]+$/) === null) {\n\t\t// Is a string\n\t\treturn value;\n\t} else {\n\t\t// Is a number\n\t\treturn parseFloat(value);\n\t}\n}\n\n/** Converts the Redis Info String into an object */\nexport function convertInfoToObject(stringData: string): IDataObject {\n\tconst returnData: IDataObject = {};\n\n\tlet key: string, value: string;\n\tfor (const line of stringData.split('\\n')) {\n\t\tif (['#', ''].includes(line.charAt(0))) {\n\t\t\tcontinue;\n\t\t}\n\t\t[key, value] = line.split(':');\n\t\tif (key === undefined || value === undefined) {\n\t\t\tcontinue;\n\t\t}\n\t\tvalue = value.trim();\n\n\t\tif (value.includes('=')) {\n\t\t\treturnData[key] = {};\n\t\t\tlet key2: string, value2: string;\n\t\t\tfor (const keyValuePair of value.split(',')) {\n\t\t\t\t[key2, value2] = keyValuePair.split('=');\n\t\t\t\t(returnData[key] as IDataObject)[key2] = getParsedValue(value2);\n\t\t\t}\n\t\t} else {\n\t\t\treturnData[key] = getParsedValue(value);\n\t\t}\n\t}\n\n\treturn returnData;\n}\n\nexport async function getValue(client: RedisClient, keyName: string, type?: string) {\n\tif (type === undefined || type === 'automatic') {\n\t\t// Request the type first\n\t\ttype = await client.type(keyName);\n\t}\n\n\tif (type === 'string') {\n\t\treturn await client.get(keyName);\n\t} else if (type === 'hash') {\n\t\treturn await client.hGetAll(keyName);\n\t} else if (type === 'list') {\n\t\treturn await client.lRange(keyName, 0, -1);\n\t} else if (type === 'sets') {\n\t\treturn await client.sMembers(keyName);\n\t}\n}\n\nexport async function setValue(\n\tthis: IExecuteFunctions,\n\tclient: RedisClient,\n\tkeyName: string,\n\tvalue: string | number | object | string[] | number[],\n\texpire: boolean,\n\tttl: number,\n\ttype?: string,\n\tvalueIsJSON?: boolean,\n) {\n\tif (type === undefined || type === 'automatic') {\n\t\t// Request the type first\n\t\tif (typeof value === 'string') {\n\t\t\ttype = 'string';\n\t\t} else if (Array.isArray(value)) {\n\t\t\ttype = 'list';\n\t\t} else if (typeof value === 'object') {\n\t\t\ttype = 'hash';\n\t\t} else {\n\t\t\tthrow new NodeOperationError(\n\t\t\t\tthis.getNode(),\n\t\t\t\t'Could not identify the type to set. Please set it manually!',\n\t\t\t);\n\t\t}\n\t}\n\n\tif (type === 'string') {\n\t\tawait client.set(keyName, value.toString());\n\t} else if (type === 'hash') {\n\t\tif (valueIsJSON) {\n\t\t\tlet values: unknown;\n\t\t\tif (typeof value === 'string') {\n\t\t\t\ttry {\n\t\t\t\t\tvalues = JSON.parse(value);\n\t\t\t\t} catch {\n\t\t\t\t\t// This is how we originally worked and prevents a breaking change\n\t\t\t\t\tvalues = value;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tvalues = value;\n\t\t\t}\n\t\t\tfor (const key of Object.keys(values as object)) {\n\t\t\t\tawait client.hSet(keyName, key, (values as IDataObject)[key]!.toString());\n\t\t\t}\n\t\t} else {\n\t\t\tconst values = value.toString().split(' ');\n\t\t\tawait client.hSet(keyName, values);\n\t\t}\n\t} else if (type === 'list') {\n\t\tfor (let index = 0; index < (value as string[]).length; index++) {\n\t\t\tawait client.lSet(keyName, index, (value as IDataObject)[index]!.toString());\n\t\t}\n\t} else if (type === 'sets') {\n\t\t//@ts-ignore\n\t\tawait client.sAdd(keyName, value);\n\t}\n\n\tif (expire) {\n\t\tawait client.expire(keyName, ttl);\n\t}\n\treturn;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,0BAAmC;AACnC,mBAA6B;AAItB,SAAS,iBAAiB,aAA2C;AAC3E,aAAO,2BAAa;AAAA,IACnB,QAAQ;AAAA,MACP,MAAM,YAAY;AAAA,MAClB,MAAM,YAAY;AAAA,MAClB,KAAK,YAAY,QAAQ;AAAA,IAC1B;AAAA,IACA,UAAU,YAAY;AAAA,IACtB,UAAU,YAAY,QAAQ;AAAA,IAC9B,UAAU,YAAY,YAAY;AAAA,EACnC,CAAC;AACF;AAEA,eAAsB,oBAErB,YACqC;AACrC,QAAM,cAAc,WAAW;AAE/B,MAAI;AACH,UAAM,SAAS,iBAAiB,WAAW;AAC3C,UAAM,OAAO,QAAQ;AACrB,UAAM,OAAO,KAAK;AAClB,WAAO;AAAA,MACN,QAAQ;AAAA,MACR,SAAS;AAAA,IACV;AAAA,EACD,SAAS,OAAO;AACf,WAAO;AAAA,MACN,QAAQ;AAAA,MACR,SAAS,MAAM;AAAA,IAChB;AAAA,EACD;AACD;AAGA,SAAS,eAAe,OAAgC;AACvD,MAAI,MAAM,MAAM,WAAW,MAAM,MAAM;AAEtC,WAAO;AAAA,EACR,OAAO;AAEN,WAAO,WAAW,KAAK;AAAA,EACxB;AACD;AAGO,SAAS,oBAAoB,YAAiC;AACpE,QAAM,aAA0B,CAAC;AAEjC,MAAI,KAAa;AACjB,aAAW,QAAQ,WAAW,MAAM,IAAI,GAAG;AAC1C,QAAI,CAAC,KAAK,EAAE,EAAE,SAAS,KAAK,OAAO,CAAC,CAAC,GAAG;AACvC;AAAA,IACD;AACA,KAAC,KAAK,KAAK,IAAI,KAAK,MAAM,GAAG;AAC7B,QAAI,QAAQ,UAAa,UAAU,QAAW;AAC7C;AAAA,IACD;AACA,YAAQ,MAAM,KAAK;AAEnB,QAAI,MAAM,SAAS,GAAG,GAAG;AACxB,iBAAW,GAAG,IAAI,CAAC;AACnB,UAAI,MAAc;AAClB,iBAAW,gBAAgB,MAAM,MAAM,GAAG,GAAG;AAC5C,SAAC,MAAM,MAAM,IAAI,aAAa,MAAM,GAAG;AACvC,QAAC,WAAW,GAAG,EAAkB,IAAI,IAAI,eAAe,MAAM;AAAA,MAC/D;AAAA,IACD,OAAO;AACN,iBAAW,GAAG,IAAI,eAAe,KAAK;AAAA,IACvC;AAAA,EACD;AAEA,SAAO;AACR;AAEA,eAAsB,SAAS,QAAqB,SAAiB,MAAe;AACnF,MAAI,SAAS,UAAa,SAAS,aAAa;AAE/C,WAAO,MAAM,OAAO,KAAK,OAAO;AAAA,EACjC;AAEA,MAAI,SAAS,UAAU;AACtB,WAAO,MAAM,OAAO,IAAI,OAAO;AAAA,EAChC,WAAW,SAAS,QAAQ;AAC3B,WAAO,MAAM,OAAO,QAAQ,OAAO;AAAA,EACpC,WAAW,SAAS,QAAQ;AAC3B,WAAO,MAAM,OAAO,OAAO,SAAS,GAAG,EAAE;AAAA,EAC1C,WAAW,SAAS,QAAQ;AAC3B,WAAO,MAAM,OAAO,SAAS,OAAO;AAAA,EACrC;AACD;AAEA,eAAsB,SAErB,QACA,SACA,OACA,QACA,KACA,MACA,aACC;AACD,MAAI,SAAS,UAAa,SAAS,aAAa;AAE/C,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO;AAAA,IACR,WAAW,MAAM,QAAQ,KAAK,GAAG;AAChC,aAAO;AAAA,IACR,WAAW,OAAO,UAAU,UAAU;AACrC,aAAO;AAAA,IACR,OAAO;AACN,YAAM,IAAI;AAAA,QACT,KAAK,QAAQ;AAAA,QACb;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,MAAI,SAAS,UAAU;AACtB,UAAM,OAAO,IAAI,SAAS,MAAM,SAAS,CAAC;AAAA,EAC3C,WAAW,SAAS,QAAQ;AAC3B,QAAI,aAAa;AAChB,UAAI;AACJ,UAAI,OAAO,UAAU,UAAU;AAC9B,YAAI;AACH,mBAAS,KAAK,MAAM,KAAK;AAAA,QAC1B,QAAQ;AAEP,mBAAS;AAAA,QACV;AAAA,MACD,OAAO;AACN,iBAAS;AAAA,MACV;AACA,iBAAW,OAAO,OAAO,KAAK,MAAgB,GAAG;AAChD,cAAM,OAAO,KAAK,SAAS,KAAM,OAAuB,GAAG,EAAG,SAAS,CAAC;AAAA,MACzE;AAAA,IACD,OAAO;AACN,YAAM,SAAS,MAAM,SAAS,EAAE,MAAM,GAAG;AACzC,YAAM,OAAO,KAAK,SAAS,MAAM;AAAA,IAClC;AAAA,EACD,WAAW,SAAS,QAAQ;AAC3B,aAAS,QAAQ,GAAG,QAAS,MAAmB,QAAQ,SAAS;AAChE,YAAM,OAAO,KAAK,SAAS,OAAQ,MAAsB,KAAK,EAAG,SAAS,CAAC;AAAA,IAC5E;AAAA,EACD,WAAW,SAAS,QAAQ;AAE3B,UAAM,OAAO,KAAK,SAAS,KAAK;AAAA,EACjC;AAEA,MAAI,QAAQ;AACX,UAAM,OAAO,OAAO,SAAS,GAAG;AAAA,EACjC;AACA;AACD;", "names": []}