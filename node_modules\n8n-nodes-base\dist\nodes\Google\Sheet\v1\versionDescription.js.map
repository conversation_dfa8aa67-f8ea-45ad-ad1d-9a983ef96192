{"version": 3, "sources": ["../../../../../nodes/Google/Sheet/v1/versionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport { NodeConnectionTypes, type INodeTypeDescription } from 'n8n-workflow';\n\nimport { oldVersionNotice } from '@utils/descriptions';\n\nexport const versionDescription: INodeTypeDescription = {\n\tdisplayName: 'Google Sheets ',\n\tname: 'googleSheets',\n\ticon: 'file:googleSheets.svg',\n\tgroup: ['input', 'output'],\n\tversion: [1, 2],\n\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\tdescription: 'Read, update and write data to Google Sheets',\n\tdefaults: {\n\t\tname: 'Google Sheets',\n\t},\n\tinputs: [NodeConnectionTypes.Main],\n\toutputs: [NodeConnectionTypes.Main],\n\tcredentials: [\n\t\t{\n\t\t\tname: 'googleApi',\n\t\t\trequired: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tauthentication: ['serviceAccount'],\n\t\t\t\t},\n\t\t\t},\n\t\t\ttestedBy: 'googleApiCredentialTest',\n\t\t},\n\t\t{\n\t\t\tname: 'googleSheetsOAuth2Api',\n\t\t\trequired: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tauthentication: ['oAuth2'],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t],\n\tproperties: [\n\t\toldVersionNotice,\n\t\t{\n\t\t\tdisplayName: 'Authentication',\n\t\t\tname: 'authentication',\n\t\t\ttype: 'options',\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Service Account',\n\t\t\t\t\tvalue: 'serviceAccount',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'OAuth2',\n\t\t\t\t\tvalue: 'oAuth2',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'serviceAccount',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\t'@version': [1],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Authentication',\n\t\t\tname: 'authentication',\n\t\t\ttype: 'options',\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-miscased\n\t\t\t\t\tname: 'OAuth2 (recommended)',\n\t\t\t\t\tvalue: 'oAuth2',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Service Account',\n\t\t\t\t\tvalue: 'serviceAccount',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'oAuth2',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\t'@version': [2],\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Spreadsheet',\n\t\t\t\t\tvalue: 'spreadsheet',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Sheet',\n\t\t\t\t\tvalue: 'sheet',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'sheet',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Operation',\n\t\t\tname: 'operation',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t},\n\t\t\t},\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Append',\n\t\t\t\t\tvalue: 'append',\n\t\t\t\t\tdescription: 'Append data to a sheet',\n\t\t\t\t\taction: 'Append data to a sheet',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Clear',\n\t\t\t\t\tvalue: 'clear',\n\t\t\t\t\tdescription: 'Clear data from a sheet',\n\t\t\t\t\taction: 'Clear a sheet',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Create',\n\t\t\t\t\tvalue: 'create',\n\t\t\t\t\tdescription: 'Create a new sheet',\n\t\t\t\t\taction: 'Create a sheet',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Create or Update',\n\t\t\t\t\tvalue: 'upsert',\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'Create a new record, or update the current one if it already exists (upsert)',\n\t\t\t\t\taction: 'Create or update a sheet',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Delete',\n\t\t\t\t\tvalue: 'delete',\n\t\t\t\t\tdescription: 'Delete columns and rows from a sheet',\n\t\t\t\t\taction: 'Delete a sheet',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Lookup',\n\t\t\t\t\tvalue: 'lookup',\n\t\t\t\t\tdescription: 'Look up a specific column value and return the matching row',\n\t\t\t\t\taction: 'Look up a column value in a sheet',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Read',\n\t\t\t\t\tvalue: 'read',\n\t\t\t\t\tdescription: 'Read data from a sheet',\n\t\t\t\t\taction: 'Read a sheet',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Remove',\n\t\t\t\t\tvalue: 'remove',\n\t\t\t\t\tdescription: 'Remove a sheet',\n\t\t\t\t\taction: 'Remove a sheet',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Update',\n\t\t\t\t\tvalue: 'update',\n\t\t\t\t\tdescription: 'Update rows in a sheet',\n\t\t\t\t\taction: 'Update a sheet',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'read',\n\t\t},\n\n\t\t// ----------------------------------\n\t\t//         All\n\t\t// ----------------------------------\n\t\t{\n\t\t\tdisplayName: 'Spreadsheet ID',\n\t\t\tname: 'sheetId',\n\t\t\ttype: 'string',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdefault: '',\n\t\t\trequired: true,\n\t\t\tdescription:\n\t\t\t\t'The ID of the Google Spreadsheet. Found as part of the sheet URL https://docs.google.com/spreadsheets/d/{ID}/.',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Range',\n\t\t\tname: 'range',\n\t\t\ttype: 'string',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t},\n\t\t\t\thide: {\n\t\t\t\t\toperation: ['create', 'delete', 'remove'],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdefault: 'A:F',\n\t\t\trequired: true,\n\t\t\tdescription:\n\t\t\t\t'The table range to read from or to append data to. See the Google <a href=\"https://developers.google.com/sheets/api/guides/values#writing\">documentation</a> for the details. If it contains multiple sheets it can also be added like this: \"MySheet!A:F\"',\n\t\t},\n\n\t\t// ----------------------------------\n\t\t//         Delete\n\t\t// ----------------------------------\n\t\t{\n\t\t\tdisplayName: 'To Delete',\n\t\t\tname: 'toDelete',\n\t\t\tplaceholder: 'Add Columns/Rows to delete',\n\t\t\tdescription: 'Deletes columns and rows from a sheet',\n\t\t\ttype: 'fixedCollection',\n\t\t\ttypeOptions: {\n\t\t\t\tmultipleValues: true,\n\t\t\t},\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t\toperation: ['delete'],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdefault: {},\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Columns',\n\t\t\t\t\tname: 'columns',\n\t\t\t\t\tvalues: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tdisplayName: 'Sheet Name or ID',\n\t\t\t\t\t\t\tname: 'sheetId',\n\t\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\t\tloadOptionsMethod: 'getSheets',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\toptions: [],\n\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tdisplayName: 'Start Index',\n\t\t\t\t\t\t\tname: 'startIndex',\n\t\t\t\t\t\t\ttype: 'number',\n\t\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\t\tminValue: 0,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tdefault: 0,\n\t\t\t\t\t\t\tdescription: 'The start index (0 based and inclusive) of column to delete',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tdisplayName: 'Amount',\n\t\t\t\t\t\t\tname: 'amount',\n\t\t\t\t\t\t\ttype: 'number',\n\t\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\t\tminValue: 1,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tdefault: 1,\n\t\t\t\t\t\t\tdescription: 'Number of columns to delete',\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Rows',\n\t\t\t\t\tname: 'rows',\n\t\t\t\t\tvalues: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tdisplayName: 'Sheet Name or ID',\n\t\t\t\t\t\t\tname: 'sheetId',\n\t\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\t\tloadOptionsMethod: 'getSheets',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\toptions: [],\n\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tdisplayName: 'Start Index',\n\t\t\t\t\t\t\tname: 'startIndex',\n\t\t\t\t\t\t\ttype: 'number',\n\t\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\t\tminValue: 0,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tdefault: 0,\n\t\t\t\t\t\t\tdescription: 'The start index (0 based and inclusive) of row to delete',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tdisplayName: 'Amount',\n\t\t\t\t\t\t\tname: 'amount',\n\t\t\t\t\t\t\ttype: 'number',\n\t\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\t\tminValue: 1,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tdefault: 1,\n\t\t\t\t\t\t\tdescription: 'Number of rows to delete',\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t},\n\t\t\t],\n\t\t},\n\n\t\t// ----------------------------------\n\t\t//         Read\n\t\t// ----------------------------------\n\t\t{\n\t\t\tdisplayName: 'RAW Data',\n\t\t\tname: 'rawData',\n\t\t\ttype: 'boolean',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t\toperation: ['read'],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdefault: false,\n\t\t\tdescription:\n\t\t\t\t'Whether the data should be returned RAW instead of parsed into keys according to their header',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Data Property',\n\t\t\tname: 'dataProperty',\n\t\t\ttype: 'string',\n\t\t\tdefault: 'data',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t\toperation: ['read'],\n\t\t\t\t\trawData: [true],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription: 'The name of the property into which to write the RAW data',\n\t\t},\n\n\t\t// ----------------------------------\n\t\t//         Update\n\t\t// ----------------------------------\n\t\t{\n\t\t\tdisplayName: 'RAW Data',\n\t\t\tname: 'rawData',\n\t\t\ttype: 'boolean',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t\toperation: ['update', 'upsert'],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdefault: false,\n\t\t\tdescription: 'Whether the data supplied is RAW instead of parsed into keys',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Data Property',\n\t\t\tname: 'dataProperty',\n\t\t\ttype: 'string',\n\t\t\tdefault: 'data',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t\toperation: ['update', 'upsert'],\n\t\t\t\t\trawData: [true],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription: 'The name of the property from which to read the RAW data',\n\t\t},\n\n\t\t// ----------------------------------\n\t\t//         Read & Update & lookupColumn\n\t\t// ----------------------------------\n\t\t{\n\t\t\tdisplayName: 'Data Start Row',\n\t\t\tname: 'dataStartRow',\n\t\t\ttype: 'number',\n\t\t\ttypeOptions: {\n\t\t\t\tminValue: 1,\n\t\t\t},\n\t\t\tdefault: 1,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t},\n\t\t\t\thide: {\n\t\t\t\t\toperation: ['append', 'create', 'clear', 'delete', 'remove'],\n\t\t\t\t\trawData: [true],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription:\n\t\t\t\t'Index of the first row which contains the actual data and not the keys. Starts with 0.',\n\t\t},\n\n\t\t// ----------------------------------\n\t\t//         Mixed\n\t\t// ----------------------------------\n\t\t{\n\t\t\tdisplayName: 'Key Row',\n\t\t\tname: 'keyRow',\n\t\t\ttype: 'number',\n\t\t\ttypeOptions: {\n\t\t\t\tminValue: 0,\n\t\t\t},\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t},\n\t\t\t\thide: {\n\t\t\t\t\toperation: ['clear', 'create', 'delete', 'remove'],\n\t\t\t\t\trawData: [true],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdefault: 0,\n\t\t\tdescription:\n\t\t\t\t'Index of the row which contains the keys. Starts at 0. The incoming node data is matched to the keys for assignment. The matching is case sensitive.',\n\t\t},\n\n\t\t// ----------------------------------\n\t\t//         lookup\n\t\t// ----------------------------------\n\t\t{\n\t\t\tdisplayName: 'Lookup Column',\n\t\t\tname: 'lookupColumn',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\tplaceholder: 'Email',\n\t\t\trequired: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t\toperation: ['lookup'],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription: 'The name of the column in which to look for value',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Lookup Value',\n\t\t\tname: 'lookupValue',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\tplaceholder: '<EMAIL>',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t\toperation: ['lookup'],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription: 'The value to look for in column',\n\t\t},\n\n\t\t// ----------------------------------\n\t\t//         Update\n\t\t// ----------------------------------\n\t\t{\n\t\t\tdisplayName: 'Key',\n\t\t\tname: 'key',\n\t\t\ttype: 'string',\n\t\t\tdefault: 'id',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t\toperation: ['update', 'upsert'],\n\t\t\t\t\trawData: [false],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription: 'The name of the key to identify which data should be updated in the sheet',\n\t\t},\n\n\t\t{\n\t\t\tdisplayName: 'Options',\n\t\t\tname: 'options',\n\t\t\ttype: 'collection',\n\t\t\tplaceholder: 'Add option',\n\t\t\tdefault: {},\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t\toperation: ['append', 'lookup', 'read', 'update', 'upsert'],\n\t\t\t\t},\n\t\t\t},\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Continue If Empty',\n\t\t\t\t\tname: 'continue',\n\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\tdefault: false,\n\t\t\t\t\tdisplayOptions: {\n\t\t\t\t\t\tshow: {\n\t\t\t\t\t\t\t'/operation': ['lookup', 'read'],\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-description-boolean-without-whether\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'By default, the workflow stops executing if the lookup/read does not return values',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Return All Matches',\n\t\t\t\t\tname: 'returnAllMatches',\n\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\tdefault: false,\n\t\t\t\t\tdisplayOptions: {\n\t\t\t\t\t\tshow: {\n\t\t\t\t\t\t\t'/operation': ['lookup'],\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-description-boolean-without-whether\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'By default only the first result gets returned. If options gets set all found matches get returned.',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Use Header Names as JSON Paths',\n\t\t\t\t\tname: 'usePathForKeyRow',\n\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\tdefault: false,\n\t\t\t\t\tdisplayOptions: {\n\t\t\t\t\t\tshow: {\n\t\t\t\t\t\t\t'/operation': ['append'],\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'Whether you want to match the headers as path, for example, the row header \"category.name\" will match the \"category\" object and get the field \"name\" from it. By default \"category.name\" will match with the field with exact name, not nested object.',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Value Input Mode',\n\t\t\t\t\tname: 'valueInputMode',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\tdisplayOptions: {\n\t\t\t\t\t\tshow: {\n\t\t\t\t\t\t\t'/operation': ['append', 'update', 'upsert'],\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t\toptions: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'RAW',\n\t\t\t\t\t\t\tvalue: 'RAW',\n\t\t\t\t\t\t\tdescription: 'The values will not be parsed and will be stored as-is',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'User Entered',\n\t\t\t\t\t\t\tvalue: 'USER_ENTERED',\n\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t'The values will be parsed as if the user typed them into the UI. Numbers will stay as numbers, but strings may be converted to numbers, dates, etc. following the same rules that are applied when entering text into a cell via the Google Sheets UI.',\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t\tdefault: 'RAW',\n\t\t\t\t\tdescription: 'Determines how data should be interpreted',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Value Render Mode',\n\t\t\t\t\tname: 'valueRenderMode',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\tdisplayOptions: {\n\t\t\t\t\t\tshow: {\n\t\t\t\t\t\t\t'/operation': ['lookup', 'read'],\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t\toptions: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Formatted Value',\n\t\t\t\t\t\t\tvalue: 'FORMATTED_VALUE',\n\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t\"Values will be calculated & formatted in the reply according to the cell's formatting.Formatting is based on the spreadsheet's locale, not the requesting user's locale.For example, if A1 is 1.23 and A2 is =A1 and formatted as currency, then A2 would return \\\"$1.23\\\"\",\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Formula',\n\t\t\t\t\t\t\tvalue: 'FORMULA',\n\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t'Values will not be calculated. The reply will include the formulas. For example, if A1 is 1.23 and A2 is =A1 and formatted as currency, then A2 would return \"=A1\".',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Unformatted Value',\n\t\t\t\t\t\t\tvalue: 'UNFORMATTED_VALUE',\n\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t'Values will be calculated, but not formatted in the reply. For example, if A1 is 1.23 and A2 is =A1 and formatted as currency, then A2 would return the number 1.23.',\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t\tdefault: 'UNFORMATTED_VALUE',\n\t\t\t\t\tdescription: 'Determines how values should be rendered in the output',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Value Render Mode',\n\t\t\t\t\tname: 'valueRenderMode',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\tdisplayOptions: {\n\t\t\t\t\t\tshow: {\n\t\t\t\t\t\t\t'/operation': ['update', 'upsert'],\n\t\t\t\t\t\t\t'/rawData': [false],\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t\toptions: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Formatted Value',\n\t\t\t\t\t\t\tvalue: 'FORMATTED_VALUE',\n\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t\"Values will be calculated & formatted in the reply according to the cell's formatting.Formatting is based on the spreadsheet's locale, not the requesting user's locale. For example, if A1 is 1.23 and A2 is =A1 and formatted as currency, then A2 would return \\\"$1.23\\\".\",\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Formula',\n\t\t\t\t\t\t\tvalue: 'FORMULA',\n\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t'Values will not be calculated. The reply will include the formulas. For example, if A1 is 1.23 and A2 is =A1 and formatted as currency, then A2 would return \"=A1\".',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Unformatted Value',\n\t\t\t\t\t\t\tvalue: 'UNFORMATTED_VALUE',\n\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t'Values will be calculated, but not formatted in the reply. For example, if A1 is 1.23 and A2 is =A1 and formatted as currency, then A2 would return the number 1.23.',\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t\tdefault: 'UNFORMATTED_VALUE',\n\t\t\t\t\tdescription: 'Determines how values should be rendered in the output',\n\t\t\t\t},\n\t\t\t],\n\t\t},\n\n\t\t{\n\t\t\tdisplayName: 'Operation',\n\t\t\tname: 'operation',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['spreadsheet'],\n\t\t\t\t},\n\t\t\t},\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Create',\n\t\t\t\t\tvalue: 'create',\n\t\t\t\t\tdescription: 'Create a spreadsheet',\n\t\t\t\t\taction: 'Create a spreadsheet',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'create',\n\t\t},\n\t\t// ----------------------------------\n\t\t//         spreadsheet:create\n\t\t// ----------------------------------\n\t\t{\n\t\t\tdisplayName: 'Title',\n\t\t\tname: 'title',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['spreadsheet'],\n\t\t\t\t\toperation: ['create'],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription: 'The title of the spreadsheet',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Sheets',\n\t\t\tname: 'sheetsUi',\n\t\t\tplaceholder: 'Add Sheet',\n\t\t\ttype: 'fixedCollection',\n\t\t\ttypeOptions: {\n\t\t\t\tmultipleValues: true,\n\t\t\t},\n\t\t\tdefault: {},\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['spreadsheet'],\n\t\t\t\t\toperation: ['create'],\n\t\t\t\t},\n\t\t\t},\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'sheetValues',\n\t\t\t\t\tdisplayName: 'Sheet',\n\t\t\t\t\tvalues: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tdisplayName: 'Sheet Properties',\n\t\t\t\t\t\t\tname: 'propertiesUi',\n\t\t\t\t\t\t\tplaceholder: 'Add Property',\n\t\t\t\t\t\t\ttype: 'collection',\n\t\t\t\t\t\t\tdefault: {},\n\t\t\t\t\t\t\toptions: [\n\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\tdisplayName: 'Hidden',\n\t\t\t\t\t\t\t\t\tname: 'hidden',\n\t\t\t\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\t\t\t\tdescription: 'Whether the Sheet should be hidden in the UI',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\tdisplayName: 'Title',\n\t\t\t\t\t\t\t\t\tname: 'title',\n\t\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\t\tdescription: 'Title of the property to create',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t],\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t},\n\t\t\t],\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Options',\n\t\t\tname: 'options',\n\t\t\ttype: 'collection',\n\t\t\tplaceholder: 'Add option',\n\t\t\tdefault: {},\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['spreadsheet'],\n\t\t\t\t\toperation: ['create'],\n\t\t\t\t},\n\t\t\t},\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Locale',\n\t\t\t\t\tname: 'locale',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tplaceholder: 'en_US',\n\t\t\t\t\tdescription: `The locale of the spreadsheet in one of the following formats:\n\t\t\t\t\t<ul>\n\t\t\t\t\t\t<li>en (639-1)</li>\n\t\t\t\t\t\t<li>fil (639-2 if no 639-1 format exists)</li>\n\t\t\t\t\t\t<li>en_US (combination of ISO language an country)</li>\n\t\t\t\t\t<ul>`,\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Recalculation Interval',\n\t\t\t\t\tname: 'autoRecalc',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\toptions: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Default',\n\t\t\t\t\t\t\tvalue: '',\n\t\t\t\t\t\t\tdescription: 'Default value',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'On Change',\n\t\t\t\t\t\t\tvalue: 'ON_CHANGE',\n\t\t\t\t\t\t\tdescription: 'Volatile functions are updated on every change',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Minute',\n\t\t\t\t\t\t\tvalue: 'MINUTE',\n\t\t\t\t\t\t\tdescription: 'Volatile functions are updated on every change and every minute',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Hour',\n\t\t\t\t\t\t\tvalue: 'HOUR',\n\t\t\t\t\t\t\tdescription: 'Volatile functions are updated on every change and hourly',\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription: 'Cell recalculation interval options',\n\t\t\t\t},\n\t\t\t],\n\t\t},\n\n\t\t// ----------------------------------\n\t\t//         sheet:create\n\t\t// ----------------------------------\n\t\t{\n\t\t\tdisplayName: 'Simplify',\n\t\t\tname: 'simple',\n\t\t\ttype: 'boolean',\n\t\t\tdefault: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t\toperation: ['create'],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription: 'Whether to return a simplified version of the response instead of the raw data',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Options',\n\t\t\tname: 'options',\n\t\t\ttype: 'collection',\n\t\t\tplaceholder: 'Add option',\n\t\t\tdefault: {},\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t\toperation: ['create'],\n\t\t\t\t},\n\t\t\t},\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Grid Properties',\n\t\t\t\t\tname: 'gridProperties',\n\t\t\t\t\ttype: 'collection',\n\t\t\t\t\tplaceholder: 'Add Property',\n\t\t\t\t\tdefault: {},\n\t\t\t\t\toptions: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tdisplayName: 'Column Count',\n\t\t\t\t\t\t\tname: 'columnCount',\n\t\t\t\t\t\t\ttype: 'number',\n\t\t\t\t\t\t\tdefault: 0,\n\t\t\t\t\t\t\tdescription: 'The number of columns in the grid',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tdisplayName: 'Column Group Control After',\n\t\t\t\t\t\t\tname: 'columnGroupControlAfter',\n\t\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\t\tdescription: 'Whether the column grouping control toggle is shown after the group',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tdisplayName: 'Frozen Column Count',\n\t\t\t\t\t\t\tname: 'frozenColumnCount',\n\t\t\t\t\t\t\ttype: 'number',\n\t\t\t\t\t\t\tdefault: 0,\n\t\t\t\t\t\t\tdescription: 'The number of columns that are frozen in the grid',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tdisplayName: 'Frozen Row Count',\n\t\t\t\t\t\t\tname: 'frozenRowCount',\n\t\t\t\t\t\t\ttype: 'number',\n\t\t\t\t\t\t\tdefault: 0,\n\t\t\t\t\t\t\tdescription: 'The number of rows that are frozen in the grid',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tdisplayName: 'Hide Gridlines',\n\t\t\t\t\t\t\tname: 'hideGridlines',\n\t\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\t\tdescription: \"Whether the grid isn't showing gridlines in the UI\",\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tdisplayName: 'Row Count',\n\t\t\t\t\t\t\tname: 'rowCount',\n\t\t\t\t\t\t\ttype: 'number',\n\t\t\t\t\t\t\tdefault: 0,\n\t\t\t\t\t\t\tdescription: 'The number of rows in the grid',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tdisplayName: 'Row Group Control After',\n\t\t\t\t\t\t\tname: 'rowGroupControlAfter',\n\t\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\t\tdescription: 'Whether the row grouping control toggle is shown after the group',\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t\tdescription: 'The type of the sheet',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Hidden',\n\t\t\t\t\tname: 'hidden',\n\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\tdefault: false,\n\t\t\t\t\tdescription: \"Whether the sheet is hidden in the UI, false if it's visible\",\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Right To Left',\n\t\t\t\t\tname: 'rightToLeft',\n\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\tdefault: false,\n\t\t\t\t\tdescription: 'Whether the sheet is an RTL sheet instead of an LTR sheet',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Sheet ID',\n\t\t\t\t\tname: 'sheetId',\n\t\t\t\t\ttype: 'number',\n\t\t\t\t\tdefault: 0,\n\t\t\t\t\tdescription:\n\t\t\t\t\t\t'The ID of the sheet. Must be non-negative. This field cannot be changed once set.',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Sheet Index',\n\t\t\t\t\tname: 'index',\n\t\t\t\t\ttype: 'number',\n\t\t\t\t\tdefault: 0,\n\t\t\t\t\tdescription: 'The index of the sheet within the spreadsheet',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Tab Color',\n\t\t\t\t\tname: 'tabColor',\n\t\t\t\t\ttype: 'color',\n\t\t\t\t\tdefault: '0aa55c',\n\t\t\t\t\tdescription: 'The color of the tab in the UI',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Title',\n\t\t\t\t\tname: 'title',\n\t\t\t\t\ttype: 'string',\n\t\t\t\t\tdefault: '',\n\t\t\t\t\tdescription: 'The Sheet name',\n\t\t\t\t},\n\t\t\t],\n\t\t},\n\n\t\t// ----------------------------------\n\t\t//         sheet:remove\n\t\t// ----------------------------------\n\t\t{\n\t\t\tdisplayName: 'Sheet ID',\n\t\t\tname: 'id',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\trequired: true,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tresource: ['sheet'],\n\t\t\t\t\toperation: ['remove'],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription: 'The ID of the sheet to delete',\n\t\t},\n\t],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAA+D;AAE/D,0BAAiC;AAE1B,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,SAAS,QAAQ;AAAA,EACzB,SAAS,CAAC,GAAG,CAAC;AAAA,EACd,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,aAAa;AAAA,IACZ;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,gBAAgB,CAAC,gBAAgB;AAAA,QAClC;AAAA,MACD;AAAA,MACA,UAAU;AAAA,IACX;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,gBAAgB,CAAC,QAAQ;AAAA,QAC1B;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA,YAAY;AAAA,IACX;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,MACT,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,YAAY,CAAC,CAAC;AAAA,QACf;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,QACR;AAAA;AAAA,UAEC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,MACT,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,YAAY,CAAC,CAAC;AAAA,QACf;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,QACnB;AAAA,MACD;AAAA,MACA,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aACC;AAAA,UACD,QAAQ;AAAA,QACT;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,QACnB;AAAA,MACD;AAAA,MACA,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aACC;AAAA,IACF;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,QACnB;AAAA,QACA,MAAM;AAAA,UACL,WAAW,CAAC,UAAU,UAAU,QAAQ;AAAA,QACzC;AAAA,MACD;AAAA,MACA,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aACC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,aAAa;AAAA,MACb,aAAa;AAAA,MACb,MAAM;AAAA,MACN,aAAa;AAAA,QACZ,gBAAgB;AAAA,MACjB;AAAA,MACA,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,UAClB,WAAW,CAAC,QAAQ;AAAA,QACrB;AAAA,MACD;AAAA,MACA,SAAS,CAAC;AAAA,MACV,SAAS;AAAA,QACR;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,QAAQ;AAAA,YACP;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,aAAa;AAAA,gBACZ,mBAAmB;AAAA,cACpB;AAAA,cACA,SAAS,CAAC;AAAA,cACV,SAAS;AAAA,cACT,UAAU;AAAA,cACV,aACC;AAAA,YACF;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,aAAa;AAAA,gBACZ,UAAU;AAAA,cACX;AAAA,cACA,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,aAAa;AAAA,gBACZ,UAAU;AAAA,cACX;AAAA,cACA,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,UACD;AAAA,QACD;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,QAAQ;AAAA,YACP;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,aAAa;AAAA,gBACZ,mBAAmB;AAAA,cACpB;AAAA,cACA,SAAS,CAAC;AAAA,cACV,SAAS;AAAA,cACT,UAAU;AAAA,cACV,aACC;AAAA,YACF;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,aAAa;AAAA,gBACZ,UAAU;AAAA,cACX;AAAA,cACA,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,aAAa;AAAA,gBACZ,UAAU;AAAA,cACX;AAAA,cACA,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,UAClB,WAAW,CAAC,MAAM;AAAA,QACnB;AAAA,MACD;AAAA,MACA,SAAS;AAAA,MACT,aACC;AAAA,IACF;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,UAClB,WAAW,CAAC,MAAM;AAAA,UAClB,SAAS,CAAC,IAAI;AAAA,QACf;AAAA,MACD;AAAA,MACA,aAAa;AAAA,IACd;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,UAClB,WAAW,CAAC,UAAU,QAAQ;AAAA,QAC/B;AAAA,MACD;AAAA,MACA,SAAS;AAAA,MACT,aAAa;AAAA,IACd;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,UAClB,WAAW,CAAC,UAAU,QAAQ;AAAA,UAC9B,SAAS,CAAC,IAAI;AAAA,QACf;AAAA,MACD;AAAA,MACA,aAAa;AAAA,IACd;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,QACZ,UAAU;AAAA,MACX;AAAA,MACA,SAAS;AAAA,MACT,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,QACnB;AAAA,QACA,MAAM;AAAA,UACL,WAAW,CAAC,UAAU,UAAU,SAAS,UAAU,QAAQ;AAAA,UAC3D,SAAS,CAAC,IAAI;AAAA,QACf;AAAA,MACD;AAAA,MACA,aACC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,QACZ,UAAU;AAAA,MACX;AAAA,MACA,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,QACnB;AAAA,QACA,MAAM;AAAA,UACL,WAAW,CAAC,SAAS,UAAU,UAAU,QAAQ;AAAA,UACjD,SAAS,CAAC,IAAI;AAAA,QACf;AAAA,MACD;AAAA,MACA,SAAS;AAAA,MACT,aACC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,UAClB,WAAW,CAAC,QAAQ;AAAA,QACrB;AAAA,MACD;AAAA,MACA,aAAa;AAAA,IACd;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,aAAa;AAAA,MACb,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,UAClB,WAAW,CAAC,QAAQ;AAAA,QACrB;AAAA,MACD;AAAA,MACA,aAAa;AAAA,IACd;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,UAClB,WAAW,CAAC,UAAU,QAAQ;AAAA,UAC9B,SAAS,CAAC,KAAK;AAAA,QAChB;AAAA,MACD;AAAA,MACA,aAAa;AAAA,IACd;AAAA,IAEA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS,CAAC;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,UAClB,WAAW,CAAC,UAAU,UAAU,QAAQ,UAAU,QAAQ;AAAA,QAC3D;AAAA,MACD;AAAA,MACA,SAAS;AAAA,QACR;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,cAAc,CAAC,UAAU,MAAM;AAAA,YAChC;AAAA,UACD;AAAA;AAAA,UAEA,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,cAAc,CAAC,QAAQ;AAAA,YACxB;AAAA,UACD;AAAA;AAAA,UAEA,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,cAAc,CAAC,QAAQ;AAAA,YACxB;AAAA,UACD;AAAA,UACA,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,cAAc,CAAC,UAAU,UAAU,QAAQ;AAAA,YAC5C;AAAA,UACD;AAAA,UACA,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aACC;AAAA,YACF;AAAA,UACD;AAAA,UACA,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,cAAc,CAAC,UAAU,MAAM;AAAA,YAChC;AAAA,UACD;AAAA,UACA,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aACC;AAAA,YACF;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aACC;AAAA,YACF;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aACC;AAAA,YACF;AAAA,UACD;AAAA,UACA,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,cAAc,CAAC,UAAU,QAAQ;AAAA,cACjC,YAAY,CAAC,KAAK;AAAA,YACnB;AAAA,UACD;AAAA,UACA,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aACC;AAAA,YACF;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aACC;AAAA,YACF;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aACC;AAAA,YACF;AAAA,UACD;AAAA,UACA,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,MACD;AAAA,IACD;AAAA,IAEA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,aAAa;AAAA,QACzB;AAAA,MACD;AAAA,MACA,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA;AAAA;AAAA;AAAA,IAIA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,aAAa;AAAA,UACxB,WAAW,CAAC,QAAQ;AAAA,QACrB;AAAA,MACD;AAAA,MACA,aAAa;AAAA,IACd;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,aAAa;AAAA,MACb,MAAM;AAAA,MACN,aAAa;AAAA,QACZ,gBAAgB;AAAA,MACjB;AAAA,MACA,SAAS,CAAC;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,aAAa;AAAA,UACxB,WAAW,CAAC,QAAQ;AAAA,QACrB;AAAA,MACD;AAAA,MACA,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,aAAa;AAAA,UACb,QAAQ;AAAA,YACP;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,aAAa;AAAA,cACb,MAAM;AAAA,cACN,SAAS,CAAC;AAAA,cACV,SAAS;AAAA,gBACR;AAAA,kBACC,aAAa;AAAA,kBACb,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,SAAS;AAAA,kBACT,aAAa;AAAA,gBACd;AAAA,gBACA;AAAA,kBACC,aAAa;AAAA,kBACb,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,SAAS;AAAA,kBACT,aAAa;AAAA,gBACd;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS,CAAC;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,aAAa;AAAA,UACxB,WAAW,CAAC,QAAQ;AAAA,QACrB;AAAA,MACD;AAAA,MACA,SAAS;AAAA,QACR;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,UACb,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,UACD;AAAA,UACA,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,MACD;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,UAClB,WAAW,CAAC,QAAQ;AAAA,QACrB;AAAA,MACD;AAAA,MACA,aAAa;AAAA,IACd;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS,CAAC;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,UAClB,WAAW,CAAC,QAAQ;AAAA,QACrB;AAAA,MACD;AAAA,MACA,SAAS;AAAA,QACR;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,SAAS,CAAC;AAAA,UACV,SAAS;AAAA,YACR;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,UACD;AAAA,UACA,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aACC;AAAA,QACF;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,MACD;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,MACV,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,UAAU,CAAC,OAAO;AAAA,UAClB,WAAW,CAAC,QAAQ;AAAA,QACrB;AAAA,MACD;AAAA,MACA,aAAa;AAAA,IACd;AAAA,EACD;AACD;", "names": []}