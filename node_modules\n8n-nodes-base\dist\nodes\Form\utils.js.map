{"version": 3, "sources": ["../../../nodes/Form/utils.ts"], "sourcesContent": ["import type { Response } from 'express';\nimport isbot from 'isbot';\nimport { DateTime } from 'luxon';\nimport type {\n\tINodeExecutionData,\n\tMultiPartFormData,\n\tIDataObject,\n\tIWebhookFunctions,\n\tFormFieldsParameter,\n\tNodeTypeAndVersion,\n} from 'n8n-workflow';\nimport {\n\tFORM_NODE_TYPE,\n\tFORM_TRIGGER_NODE_TYPE,\n\tNodeOperationError,\n\tWAIT_NODE_TYPE,\n\tjsonParse,\n} from 'n8n-workflow';\nimport sanitize from 'sanitize-html';\n\nimport type { FormTriggerData, FormTriggerInput } from './interfaces';\nimport { FORM_TRIGGER_AUTHENTICATION_PROPERTY } from './interfaces';\nimport { getResolvables } from '../../utils/utilities';\nimport { WebhookAuthorizationError } from '../Webhook/error';\nimport { validateWebhookAuthentication } from '../Webhook/utils';\n\nexport function sanitizeHtml(text: string) {\n\treturn sanitize(text, {\n\t\tallowedTags: [\n\t\t\t'b',\n\t\t\t'div',\n\t\t\t'i',\n\t\t\t'iframe',\n\t\t\t'img',\n\t\t\t'video',\n\t\t\t'source',\n\t\t\t'em',\n\t\t\t'strong',\n\t\t\t'a',\n\t\t\t'h1',\n\t\t\t'h2',\n\t\t\t'h3',\n\t\t\t'h4',\n\t\t\t'h5',\n\t\t\t'h6',\n\t\t\t'u',\n\t\t\t'sub',\n\t\t\t'sup',\n\t\t\t'code',\n\t\t\t'pre',\n\t\t\t'span',\n\t\t\t'br',\n\t\t\t'ul',\n\t\t\t'ol',\n\t\t\t'li',\n\t\t\t'p',\n\t\t],\n\t\tallowedAttributes: {\n\t\t\ta: ['href', 'target', 'rel'],\n\t\t\timg: ['src', 'alt', 'width', 'height'],\n\t\t\tvideo: ['*'],\n\t\t\tiframe: ['*'],\n\t\t\tsource: ['*'],\n\t\t},\n\t\ttransformTags: {\n\t\t\tiframe: sanitize.simpleTransform('iframe', {\n\t\t\t\tsandbox: '',\n\t\t\t\treferrerpolicy: 'strict-origin-when-cross-origin',\n\t\t\t\tallow: 'fullscreen; autoplay; encrypted-media',\n\t\t\t}),\n\t\t},\n\t});\n}\n\nexport const prepareFormFields = (context: IWebhookFunctions, fields: FormFieldsParameter) => {\n\treturn fields.map((field) => {\n\t\tif (field.fieldType === 'html') {\n\t\t\tlet { html } = field;\n\n\t\t\tif (!html) return field;\n\n\t\t\tfor (const resolvable of getResolvables(html)) {\n\t\t\t\thtml = html.replace(resolvable, context.evaluateExpression(resolvable) as string);\n\t\t\t}\n\n\t\t\tfield.html = sanitizeHtml(html);\n\t\t}\n\n\t\tif (field.fieldType === 'hiddenField') {\n\t\t\tfield.fieldLabel = field.fieldName as string;\n\t\t}\n\n\t\treturn field;\n\t});\n};\n\nexport function sanitizeCustomCss(css: string | undefined): string | undefined {\n\tif (!css) return undefined;\n\n\t// Use sanitize-html with custom settings for CSS\n\treturn sanitize(css, {\n\t\tallowedTags: [], // No HTML tags allowed\n\t\tallowedAttributes: {}, // No attributes allowed\n\t\t// This ensures we're only keeping the text content\n\t\t// which should be the CSS, while removing any HTML/script tags\n\t});\n}\n\nexport function createDescriptionMetadata(description: string) {\n\treturn description === ''\n\t\t? 'n8n form'\n\t\t: description.replace(/^\\s*\\n+|<\\/?[^>]+(>|$)/g, '').slice(0, 150);\n}\n\nexport function prepareFormData({\n\tformTitle,\n\tformDescription,\n\tformSubmittedHeader,\n\tformSubmittedText,\n\tredirectUrl,\n\tformFields,\n\ttestRun,\n\tquery,\n\tinstanceId,\n\tuseResponseData,\n\tappendAttribution = true,\n\tbuttonLabel,\n\tcustomCss,\n}: {\n\tformTitle: string;\n\tformDescription: string;\n\tformSubmittedText: string | undefined;\n\tredirectUrl: string | undefined;\n\tformFields: FormFieldsParameter;\n\ttestRun: boolean;\n\tquery: IDataObject;\n\tinstanceId?: string;\n\tuseResponseData?: boolean;\n\tappendAttribution?: boolean;\n\tbuttonLabel?: string;\n\tformSubmittedHeader?: string;\n\tcustomCss?: string;\n}) {\n\tconst utm_campaign = instanceId ? `&utm_campaign=${instanceId}` : '';\n\tconst n8nWebsiteLink = `https://n8n.io/?utm_source=n8n-internal&utm_medium=form-trigger${utm_campaign}`;\n\n\tif (formSubmittedText === undefined) {\n\t\tformSubmittedText = 'Your response has been recorded';\n\t}\n\n\tconst formData: FormTriggerData = {\n\t\ttestRun,\n\t\tformTitle,\n\t\tformDescription,\n\t\tformDescriptionMetadata: createDescriptionMetadata(formDescription),\n\t\tformSubmittedHeader,\n\t\tformSubmittedText,\n\t\tn8nWebsiteLink,\n\t\tformFields: [],\n\t\tuseResponseData,\n\t\tappendAttribution,\n\t\tbuttonLabel,\n\t\tdangerousCustomCss: sanitizeCustomCss(customCss),\n\t};\n\n\tif (redirectUrl) {\n\t\tif (!redirectUrl.includes('://')) {\n\t\t\tredirectUrl = `http://${redirectUrl}`;\n\t\t}\n\t\tformData.redirectUrl = redirectUrl;\n\t}\n\n\tfor (const [index, field] of formFields.entries()) {\n\t\tconst { fieldType, requiredField, multiselect, placeholder } = field;\n\n\t\tconst input: IDataObject = {\n\t\t\tid: `field-${index}`,\n\t\t\terrorId: `error-field-${index}`,\n\t\t\tlabel: field.fieldLabel,\n\t\t\tinputRequired: requiredField ? 'form-required' : '',\n\t\t\tdefaultValue: query[field.fieldLabel] ?? '',\n\t\t\tplaceholder,\n\t\t};\n\n\t\tif (multiselect) {\n\t\t\tinput.isMultiSelect = true;\n\t\t\tinput.multiSelectOptions =\n\t\t\t\tfield.fieldOptions?.values.map((e, i) => ({\n\t\t\t\t\tid: `option${i}_${input.id}`,\n\t\t\t\t\tlabel: e.option,\n\t\t\t\t})) ?? [];\n\t\t} else if (fieldType === 'file') {\n\t\t\tinput.isFileInput = true;\n\t\t\tinput.acceptFileTypes = field.acceptFileTypes;\n\t\t\tinput.multipleFiles = field.multipleFiles ? 'multiple' : '';\n\t\t} else if (fieldType === 'dropdown') {\n\t\t\tinput.isSelect = true;\n\t\t\tconst fieldOptions = field.fieldOptions?.values ?? [];\n\t\t\tinput.selectOptions = fieldOptions.map((e) => e.option);\n\t\t} else if (fieldType === 'textarea') {\n\t\t\tinput.isTextarea = true;\n\t\t} else if (fieldType === 'html') {\n\t\t\tinput.isHtml = true;\n\t\t\tinput.html = field.html as string;\n\t\t} else if (fieldType === 'hiddenField') {\n\t\t\tinput.isHidden = true;\n\t\t\tinput.hiddenName = field.fieldName as string;\n\t\t\tinput.hiddenValue =\n\t\t\t\tinput.defaultValue === '' ? (field.fieldValue as string) : input.defaultValue;\n\t\t} else {\n\t\t\tinput.isInput = true;\n\t\t\tinput.type = fieldType as 'text' | 'number' | 'date' | 'email';\n\t\t}\n\n\t\tformData.formFields.push(input as FormTriggerInput);\n\t}\n\n\treturn formData;\n}\n\nexport const validateResponseModeConfiguration = (context: IWebhookFunctions) => {\n\tconst responseMode = context.getNodeParameter('responseMode', 'onReceived') as string;\n\tconst connectedNodes = context.getChildNodes(context.getNode().name);\n\tconst nodeVersion = context.getNode().typeVersion;\n\n\tconst isRespondToWebhookConnected = connectedNodes.some(\n\t\t(node) => node.type === 'n8n-nodes-base.respondToWebhook',\n\t);\n\n\tif (!isRespondToWebhookConnected && responseMode === 'responseNode') {\n\t\tthrow new NodeOperationError(\n\t\t\tcontext.getNode(),\n\t\t\tnew Error('No Respond to Webhook node found in the workflow'),\n\t\t\t{\n\t\t\t\tdescription:\n\t\t\t\t\t'Insert a Respond to Webhook node to your workflow to respond to the form submission or choose another option for the “Respond When” parameter',\n\t\t\t},\n\t\t);\n\t}\n\n\tif (isRespondToWebhookConnected && responseMode !== 'responseNode' && nodeVersion <= 2.1) {\n\t\tthrow new NodeOperationError(\n\t\t\tcontext.getNode(),\n\t\t\tnew Error(`${context.getNode().name} node not correctly configured`),\n\t\t\t{\n\t\t\t\tdescription:\n\t\t\t\t\t'Set the “Respond When” parameter to “Using Respond to Webhook Node” or remove the Respond to Webhook node',\n\t\t\t},\n\t\t);\n\t}\n\n\tif (isRespondToWebhookConnected && nodeVersion > 2.1) {\n\t\tthrow new NodeOperationError(\n\t\t\tcontext.getNode(),\n\t\t\tnew Error(\n\t\t\t\t'The \"Respond to Webhook\" node is not supported in workflows initiated by the \"n8n Form Trigger\"',\n\t\t\t),\n\t\t\t{\n\t\t\t\tdescription:\n\t\t\t\t\t'To configure your response, add an \"n8n Form\" node and set the \"Page Type\" to \"Form Ending\"',\n\t\t\t},\n\t\t);\n\t}\n};\n\nexport function addFormResponseDataToReturnItem(\n\treturnItem: INodeExecutionData,\n\tformFields: FormFieldsParameter,\n\tbodyData: IDataObject,\n) {\n\tfor (const [index, field] of formFields.entries()) {\n\t\tconst key = `field-${index}`;\n\t\tconst name = field.fieldLabel ?? field.fieldName;\n\t\tlet value = bodyData[key] ?? null;\n\n\t\tif (value === null) {\n\t\t\treturnItem.json[name] = null;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (field.fieldType === 'html') {\n\t\t\tif (field.elementName) {\n\t\t\t\treturnItem.json[field.elementName] = value;\n\t\t\t}\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (field.fieldType === 'number') {\n\t\t\tvalue = Number(value);\n\t\t}\n\t\tif (field.fieldType === 'text') {\n\t\t\tvalue = String(value).trim();\n\t\t}\n\t\tif (field.multiselect && typeof value === 'string') {\n\t\t\tvalue = jsonParse(value);\n\t\t}\n\t\tif (field.fieldType === 'date' && value && field.formatDate !== '') {\n\t\t\tvalue = DateTime.fromFormat(String(value), 'yyyy-mm-dd').toFormat(field.formatDate as string);\n\t\t}\n\t\tif (field.fieldType === 'file' && field.multipleFiles && !Array.isArray(value)) {\n\t\t\tvalue = [value];\n\t\t}\n\n\t\treturnItem.json[name] = value;\n\t}\n}\n\nexport async function prepareFormReturnItem(\n\tcontext: IWebhookFunctions,\n\tformFields: FormFieldsParameter,\n\tmode: 'test' | 'production',\n\tuseWorkflowTimezone: boolean = false,\n) {\n\tconst bodyData = (context.getBodyData().data as IDataObject) ?? {};\n\tconst files = (context.getBodyData().files as IDataObject) ?? {};\n\n\tconst returnItem: INodeExecutionData = {\n\t\tjson: {},\n\t};\n\tif (files && Object.keys(files).length) {\n\t\treturnItem.binary = {};\n\t}\n\n\tfor (const key of Object.keys(files)) {\n\t\tconst processFiles: MultiPartFormData.File[] = [];\n\t\tlet multiFile = false;\n\t\tconst filesInput = files[key] as MultiPartFormData.File[] | MultiPartFormData.File;\n\n\t\tif (Array.isArray(filesInput)) {\n\t\t\tbodyData[key] = filesInput.map((file) => ({\n\t\t\t\tfilename: file.originalFilename,\n\t\t\t\tmimetype: file.mimetype,\n\t\t\t\tsize: file.size,\n\t\t\t}));\n\t\t\tprocessFiles.push(...filesInput);\n\t\t\tmultiFile = true;\n\t\t} else {\n\t\t\tbodyData[key] = {\n\t\t\t\tfilename: filesInput.originalFilename,\n\t\t\t\tmimetype: filesInput.mimetype,\n\t\t\t\tsize: filesInput.size,\n\t\t\t};\n\t\t\tprocessFiles.push(filesInput);\n\t\t}\n\n\t\tconst entryIndex = Number(key.replace(/field-/g, ''));\n\t\tconst fieldLabel = isNaN(entryIndex) ? key : formFields[entryIndex].fieldLabel;\n\n\t\tlet fileCount = 0;\n\t\tfor (const file of processFiles) {\n\t\t\tlet binaryPropertyName = fieldLabel.replace(/\\W/g, '_');\n\n\t\t\tif (multiFile) {\n\t\t\t\tbinaryPropertyName += `_${fileCount++}`;\n\t\t\t}\n\n\t\t\treturnItem.binary![binaryPropertyName] = await context.nodeHelpers.copyBinaryFile(\n\t\t\t\tfile.filepath,\n\t\t\t\tfile.originalFilename ?? file.newFilename,\n\t\t\t\tfile.mimetype,\n\t\t\t);\n\t\t}\n\t}\n\n\taddFormResponseDataToReturnItem(returnItem, formFields, bodyData);\n\n\tconst timezone = useWorkflowTimezone ? context.getTimezone() : 'UTC';\n\treturnItem.json.submittedAt = DateTime.now().setZone(timezone).toISO();\n\n\treturnItem.json.formMode = mode;\n\n\tif (\n\t\tcontext.getNode().type === FORM_TRIGGER_NODE_TYPE &&\n\t\tObject.keys(context.getRequestObject().query || {}).length\n\t) {\n\t\treturnItem.json.formQueryParameters = context.getRequestObject().query;\n\t}\n\n\treturn returnItem;\n}\n\nexport function renderForm({\n\tcontext,\n\tres,\n\tformTitle,\n\tformDescription,\n\tformFields,\n\tresponseMode,\n\tmode,\n\tformSubmittedText,\n\tredirectUrl,\n\tappendAttribution,\n\tbuttonLabel,\n\tcustomCss,\n}: {\n\tcontext: IWebhookFunctions;\n\tres: Response;\n\tformTitle: string;\n\tformDescription: string;\n\tformFields: FormFieldsParameter;\n\tresponseMode: string;\n\tmode: 'test' | 'production';\n\tformSubmittedText?: string;\n\tredirectUrl?: string;\n\tappendAttribution?: boolean;\n\tbuttonLabel?: string;\n\tcustomCss?: string;\n}) {\n\tformDescription = (formDescription || '').replace(/\\\\n/g, '\\n').replace(/<br>/g, '\\n');\n\tconst instanceId = context.getInstanceId();\n\n\tconst useResponseData = responseMode === 'responseNode';\n\n\tlet query: IDataObject = {};\n\n\tif (context.getNode().type === FORM_TRIGGER_NODE_TYPE) {\n\t\tquery = context.getRequestObject().query as IDataObject;\n\t} else if (context.getNode().type === FORM_NODE_TYPE) {\n\t\tconst parentNodes = context.getParentNodes(context.getNode().name);\n\t\tconst trigger = parentNodes.find(\n\t\t\t(node) => node.type === FORM_TRIGGER_NODE_TYPE,\n\t\t) as NodeTypeAndVersion;\n\t\ttry {\n\t\t\tconst triggerQueryParameters = context.evaluateExpression(\n\t\t\t\t`{{ $('${trigger?.name}').first().json.formQueryParameters }}`,\n\t\t\t) as IDataObject;\n\n\t\t\tif (triggerQueryParameters) {\n\t\t\t\tquery = triggerQueryParameters;\n\t\t\t}\n\t\t} catch (error) {}\n\t}\n\n\tformFields = prepareFormFields(context, formFields);\n\n\tconst data = prepareFormData({\n\t\tformTitle,\n\t\tformDescription,\n\t\tformSubmittedText,\n\t\tredirectUrl,\n\t\tformFields,\n\t\ttestRun: mode === 'test',\n\t\tquery,\n\t\tinstanceId,\n\t\tuseResponseData,\n\t\tappendAttribution,\n\t\tbuttonLabel,\n\t\tcustomCss,\n\t});\n\n\tres.render('form-trigger', data);\n}\n\nexport const isFormConnected = (nodes: NodeTypeAndVersion[]) => {\n\treturn nodes.some(\n\t\t(n) =>\n\t\t\tn.type === FORM_NODE_TYPE || (n.type === WAIT_NODE_TYPE && n.parameters?.resume === 'form'),\n\t);\n};\n\nexport async function formWebhook(\n\tcontext: IWebhookFunctions,\n\tauthProperty = FORM_TRIGGER_AUTHENTICATION_PROPERTY,\n) {\n\tconst node = context.getNode();\n\tconst options = context.getNodeParameter('options', {}) as {\n\t\tignoreBots?: boolean;\n\t\trespondWithOptions?: {\n\t\t\tvalues: {\n\t\t\t\trespondWith: 'text' | 'redirect';\n\t\t\t\tformSubmittedText: string;\n\t\t\t\tredirectUrl: string;\n\t\t\t};\n\t\t};\n\t\tformSubmittedText?: string;\n\t\tuseWorkflowTimezone?: boolean;\n\t\tappendAttribution?: boolean;\n\t\tbuttonLabel?: string;\n\t\tcustomCss?: string;\n\t};\n\tconst res = context.getResponseObject();\n\tconst req = context.getRequestObject();\n\n\ttry {\n\t\tif (options.ignoreBots && isbot(req.headers['user-agent'])) {\n\t\t\tthrow new WebhookAuthorizationError(403);\n\t\t}\n\t\tif (node.typeVersion > 1) {\n\t\t\tawait validateWebhookAuthentication(context, authProperty);\n\t\t}\n\t} catch (error) {\n\t\tif (error instanceof WebhookAuthorizationError) {\n\t\t\tres.setHeader('WWW-Authenticate', 'Basic realm=\"Enter credentials\"');\n\t\t\tres.status(401).send();\n\t\t\treturn { noWebhookResponse: true };\n\t\t}\n\t\tthrow error;\n\t}\n\n\tconst mode = context.getMode() === 'manual' ? 'test' : 'production';\n\tconst formFields = context.getNodeParameter('formFields.values', []) as FormFieldsParameter;\n\n\tconst method = context.getRequestObject().method;\n\n\tvalidateResponseModeConfiguration(context);\n\n\t//Show the form on GET request\n\tif (method === 'GET') {\n\t\tconst formTitle = context.getNodeParameter('formTitle', '') as string;\n\t\tconst formDescription = sanitizeHtml(context.getNodeParameter('formDescription', '') as string);\n\t\tlet responseMode = context.getNodeParameter('responseMode', '') as string;\n\n\t\tlet formSubmittedText;\n\t\tlet redirectUrl;\n\t\tlet appendAttribution = true;\n\n\t\tif (options.respondWithOptions) {\n\t\t\tconst values = (options.respondWithOptions as IDataObject).values as IDataObject;\n\t\t\tif (values.respondWith === 'text') {\n\t\t\t\tformSubmittedText = values.formSubmittedText as string;\n\t\t\t}\n\t\t\tif (values.respondWith === 'redirect') {\n\t\t\t\tredirectUrl = values.redirectUrl as string;\n\t\t\t}\n\t\t} else {\n\t\t\tformSubmittedText = options.formSubmittedText as string;\n\t\t}\n\n\t\tif (options.appendAttribution === false) {\n\t\t\tappendAttribution = false;\n\t\t}\n\n\t\tlet buttonLabel = 'Submit';\n\n\t\tif (options.buttonLabel) {\n\t\t\tbuttonLabel = options.buttonLabel;\n\t\t}\n\n\t\tconst connectedNodes = context.getChildNodes(context.getNode().name, {\n\t\t\tincludeNodeParameters: true,\n\t\t});\n\t\tconst hasNextPage = isFormConnected(connectedNodes);\n\n\t\tif (hasNextPage) {\n\t\t\tredirectUrl = undefined;\n\t\t\tresponseMode = 'responseNode';\n\t\t}\n\n\t\trenderForm({\n\t\t\tcontext,\n\t\t\tres,\n\t\t\tformTitle,\n\t\t\tformDescription,\n\t\t\tformFields,\n\t\t\tresponseMode,\n\t\t\tmode,\n\t\t\tformSubmittedText,\n\t\t\tredirectUrl,\n\t\t\tappendAttribution,\n\t\t\tbuttonLabel,\n\t\t\tcustomCss: options.customCss,\n\t\t});\n\n\t\treturn {\n\t\t\tnoWebhookResponse: true,\n\t\t};\n\t}\n\n\tlet { useWorkflowTimezone } = options;\n\n\tif (useWorkflowTimezone === undefined && node.typeVersion > 2) {\n\t\tuseWorkflowTimezone = true;\n\t}\n\n\tconst returnItem = await prepareFormReturnItem(context, formFields, mode, useWorkflowTimezone);\n\n\treturn {\n\t\twebhookResponse: { status: 200 },\n\t\tworkflowData: [[returnItem]],\n\t};\n}\n\nexport function resolveRawData(context: IWebhookFunctions, rawData: string) {\n\tconst resolvables = getResolvables(rawData);\n\tlet returnData: string = rawData;\n\n\tif (returnData.startsWith('=')) {\n\t\treturnData = returnData.replace(/^=+/, '');\n\t} else {\n\t\treturn returnData;\n\t}\n\n\tif (resolvables.length) {\n\t\tfor (const resolvable of resolvables) {\n\t\t\tconst resolvedValue = context.evaluateExpression(`${resolvable}`);\n\n\t\t\tif (typeof resolvedValue === 'object' && resolvedValue !== null) {\n\t\t\t\treturnData = returnData.replace(resolvable, JSON.stringify(resolvedValue));\n\t\t\t} else {\n\t\t\t\treturnData = returnData.replace(resolvable, resolvedValue as string);\n\t\t\t}\n\t\t}\n\t}\n\treturn returnData;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,mBAAkB;AAClB,mBAAyB;AASzB,0BAMO;AACP,2BAAqB;AAGrB,wBAAqD;AACrD,uBAA+B;AAC/B,mBAA0C;AAC1C,mBAA8C;AAEvC,SAAS,aAAa,MAAc;AAC1C,aAAO,qBAAAA,SAAS,MAAM;AAAA,IACrB,aAAa;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,IACA,mBAAmB;AAAA,MAClB,GAAG,CAAC,QAAQ,UAAU,KAAK;AAAA,MAC3B,KAAK,CAAC,OAAO,OAAO,SAAS,QAAQ;AAAA,MACrC,OAAO,CAAC,GAAG;AAAA,MACX,QAAQ,CAAC,GAAG;AAAA,MACZ,QAAQ,CAAC,GAAG;AAAA,IACb;AAAA,IACA,eAAe;AAAA,MACd,QAAQ,qBAAAA,QAAS,gBAAgB,UAAU;AAAA,QAC1C,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACR,CAAC;AAAA,IACF;AAAA,EACD,CAAC;AACF;AAEO,MAAM,oBAAoB,CAAC,SAA4B,WAAgC;AAC7F,SAAO,OAAO,IAAI,CAAC,UAAU;AAC5B,QAAI,MAAM,cAAc,QAAQ;AAC/B,UAAI,EAAE,KAAK,IAAI;AAEf,UAAI,CAAC,KAAM,QAAO;AAElB,iBAAW,kBAAc,iCAAe,IAAI,GAAG;AAC9C,eAAO,KAAK,QAAQ,YAAY,QAAQ,mBAAmB,UAAU,CAAW;AAAA,MACjF;AAEA,YAAM,OAAO,aAAa,IAAI;AAAA,IAC/B;AAEA,QAAI,MAAM,cAAc,eAAe;AACtC,YAAM,aAAa,MAAM;AAAA,IAC1B;AAEA,WAAO;AAAA,EACR,CAAC;AACF;AAEO,SAAS,kBAAkB,KAA6C;AAC9E,MAAI,CAAC,IAAK,QAAO;AAGjB,aAAO,qBAAAA,SAAS,KAAK;AAAA,IACpB,aAAa,CAAC;AAAA;AAAA,IACd,mBAAmB,CAAC;AAAA;AAAA;AAAA;AAAA,EAGrB,CAAC;AACF;AAEO,SAAS,0BAA0B,aAAqB;AAC9D,SAAO,gBAAgB,KACpB,aACA,YAAY,QAAQ,2BAA2B,EAAE,EAAE,MAAM,GAAG,GAAG;AACnE;AAEO,SAAS,gBAAgB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,oBAAoB;AAAA,EACpB;AAAA,EACA;AACD,GAcG;AACF,QAAM,eAAe,aAAa,iBAAiB,UAAU,KAAK;AAClE,QAAM,iBAAiB,kEAAkE,YAAY;AAErG,MAAI,sBAAsB,QAAW;AACpC,wBAAoB;AAAA,EACrB;AAEA,QAAM,WAA4B;AAAA,IACjC;AAAA,IACA;AAAA,IACA;AAAA,IACA,yBAAyB,0BAA0B,eAAe;AAAA,IAClE;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY,CAAC;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,oBAAoB,kBAAkB,SAAS;AAAA,EAChD;AAEA,MAAI,aAAa;AAChB,QAAI,CAAC,YAAY,SAAS,KAAK,GAAG;AACjC,oBAAc,UAAU,WAAW;AAAA,IACpC;AACA,aAAS,cAAc;AAAA,EACxB;AAEA,aAAW,CAAC,OAAO,KAAK,KAAK,WAAW,QAAQ,GAAG;AAClD,UAAM,EAAE,WAAW,eAAe,aAAa,YAAY,IAAI;AAE/D,UAAM,QAAqB;AAAA,MAC1B,IAAI,SAAS,KAAK;AAAA,MAClB,SAAS,eAAe,KAAK;AAAA,MAC7B,OAAO,MAAM;AAAA,MACb,eAAe,gBAAgB,kBAAkB;AAAA,MACjD,cAAc,MAAM,MAAM,UAAU,KAAK;AAAA,MACzC;AAAA,IACD;AAEA,QAAI,aAAa;AAChB,YAAM,gBAAgB;AACtB,YAAM,qBACL,MAAM,cAAc,OAAO,IAAI,CAAC,GAAG,OAAO;AAAA,QACzC,IAAI,SAAS,CAAC,IAAI,MAAM,EAAE;AAAA,QAC1B,OAAO,EAAE;AAAA,MACV,EAAE,KAAK,CAAC;AAAA,IACV,WAAW,cAAc,QAAQ;AAChC,YAAM,cAAc;AACpB,YAAM,kBAAkB,MAAM;AAC9B,YAAM,gBAAgB,MAAM,gBAAgB,aAAa;AAAA,IAC1D,WAAW,cAAc,YAAY;AACpC,YAAM,WAAW;AACjB,YAAM,eAAe,MAAM,cAAc,UAAU,CAAC;AACpD,YAAM,gBAAgB,aAAa,IAAI,CAAC,MAAM,EAAE,MAAM;AAAA,IACvD,WAAW,cAAc,YAAY;AACpC,YAAM,aAAa;AAAA,IACpB,WAAW,cAAc,QAAQ;AAChC,YAAM,SAAS;AACf,YAAM,OAAO,MAAM;AAAA,IACpB,WAAW,cAAc,eAAe;AACvC,YAAM,WAAW;AACjB,YAAM,aAAa,MAAM;AACzB,YAAM,cACL,MAAM,iBAAiB,KAAM,MAAM,aAAwB,MAAM;AAAA,IACnE,OAAO;AACN,YAAM,UAAU;AAChB,YAAM,OAAO;AAAA,IACd;AAEA,aAAS,WAAW,KAAK,KAAyB;AAAA,EACnD;AAEA,SAAO;AACR;AAEO,MAAM,oCAAoC,CAAC,YAA+B;AAChF,QAAM,eAAe,QAAQ,iBAAiB,gBAAgB,YAAY;AAC1E,QAAM,iBAAiB,QAAQ,cAAc,QAAQ,QAAQ,EAAE,IAAI;AACnE,QAAM,cAAc,QAAQ,QAAQ,EAAE;AAEtC,QAAM,8BAA8B,eAAe;AAAA,IAClD,CAAC,SAAS,KAAK,SAAS;AAAA,EACzB;AAEA,MAAI,CAAC,+BAA+B,iBAAiB,gBAAgB;AACpE,UAAM,IAAI;AAAA,MACT,QAAQ,QAAQ;AAAA,MAChB,IAAI,MAAM,kDAAkD;AAAA,MAC5D;AAAA,QACC,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AAEA,MAAI,+BAA+B,iBAAiB,kBAAkB,eAAe,KAAK;AACzF,UAAM,IAAI;AAAA,MACT,QAAQ,QAAQ;AAAA,MAChB,IAAI,MAAM,GAAG,QAAQ,QAAQ,EAAE,IAAI,gCAAgC;AAAA,MACnE;AAAA,QACC,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AAEA,MAAI,+BAA+B,cAAc,KAAK;AACrD,UAAM,IAAI;AAAA,MACT,QAAQ,QAAQ;AAAA,MAChB,IAAI;AAAA,QACH;AAAA,MACD;AAAA,MACA;AAAA,QACC,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AACD;AAEO,SAAS,gCACf,YACA,YACA,UACC;AACD,aAAW,CAAC,OAAO,KAAK,KAAK,WAAW,QAAQ,GAAG;AAClD,UAAM,MAAM,SAAS,KAAK;AAC1B,UAAM,OAAO,MAAM,cAAc,MAAM;AACvC,QAAI,QAAQ,SAAS,GAAG,KAAK;AAE7B,QAAI,UAAU,MAAM;AACnB,iBAAW,KAAK,IAAI,IAAI;AACxB;AAAA,IACD;AAEA,QAAI,MAAM,cAAc,QAAQ;AAC/B,UAAI,MAAM,aAAa;AACtB,mBAAW,KAAK,MAAM,WAAW,IAAI;AAAA,MACtC;AACA;AAAA,IACD;AAEA,QAAI,MAAM,cAAc,UAAU;AACjC,cAAQ,OAAO,KAAK;AAAA,IACrB;AACA,QAAI,MAAM,cAAc,QAAQ;AAC/B,cAAQ,OAAO,KAAK,EAAE,KAAK;AAAA,IAC5B;AACA,QAAI,MAAM,eAAe,OAAO,UAAU,UAAU;AACnD,kBAAQ,+BAAU,KAAK;AAAA,IACxB;AACA,QAAI,MAAM,cAAc,UAAU,SAAS,MAAM,eAAe,IAAI;AACnE,cAAQ,sBAAS,WAAW,OAAO,KAAK,GAAG,YAAY,EAAE,SAAS,MAAM,UAAoB;AAAA,IAC7F;AACA,QAAI,MAAM,cAAc,UAAU,MAAM,iBAAiB,CAAC,MAAM,QAAQ,KAAK,GAAG;AAC/E,cAAQ,CAAC,KAAK;AAAA,IACf;AAEA,eAAW,KAAK,IAAI,IAAI;AAAA,EACzB;AACD;AAEA,eAAsB,sBACrB,SACA,YACA,MACA,sBAA+B,OAC9B;AACD,QAAM,WAAY,QAAQ,YAAY,EAAE,QAAwB,CAAC;AACjE,QAAM,QAAS,QAAQ,YAAY,EAAE,SAAyB,CAAC;AAE/D,QAAM,aAAiC;AAAA,IACtC,MAAM,CAAC;AAAA,EACR;AACA,MAAI,SAAS,OAAO,KAAK,KAAK,EAAE,QAAQ;AACvC,eAAW,SAAS,CAAC;AAAA,EACtB;AAEA,aAAW,OAAO,OAAO,KAAK,KAAK,GAAG;AACrC,UAAM,eAAyC,CAAC;AAChD,QAAI,YAAY;AAChB,UAAM,aAAa,MAAM,GAAG;AAE5B,QAAI,MAAM,QAAQ,UAAU,GAAG;AAC9B,eAAS,GAAG,IAAI,WAAW,IAAI,CAAC,UAAU;AAAA,QACzC,UAAU,KAAK;AAAA,QACf,UAAU,KAAK;AAAA,QACf,MAAM,KAAK;AAAA,MACZ,EAAE;AACF,mBAAa,KAAK,GAAG,UAAU;AAC/B,kBAAY;AAAA,IACb,OAAO;AACN,eAAS,GAAG,IAAI;AAAA,QACf,UAAU,WAAW;AAAA,QACrB,UAAU,WAAW;AAAA,QACrB,MAAM,WAAW;AAAA,MAClB;AACA,mBAAa,KAAK,UAAU;AAAA,IAC7B;AAEA,UAAM,aAAa,OAAO,IAAI,QAAQ,WAAW,EAAE,CAAC;AACpD,UAAM,aAAa,MAAM,UAAU,IAAI,MAAM,WAAW,UAAU,EAAE;AAEpE,QAAI,YAAY;AAChB,eAAW,QAAQ,cAAc;AAChC,UAAI,qBAAqB,WAAW,QAAQ,OAAO,GAAG;AAEtD,UAAI,WAAW;AACd,8BAAsB,IAAI,WAAW;AAAA,MACtC;AAEA,iBAAW,OAAQ,kBAAkB,IAAI,MAAM,QAAQ,YAAY;AAAA,QAClE,KAAK;AAAA,QACL,KAAK,oBAAoB,KAAK;AAAA,QAC9B,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AAEA,kCAAgC,YAAY,YAAY,QAAQ;AAEhE,QAAM,WAAW,sBAAsB,QAAQ,YAAY,IAAI;AAC/D,aAAW,KAAK,cAAc,sBAAS,IAAI,EAAE,QAAQ,QAAQ,EAAE,MAAM;AAErE,aAAW,KAAK,WAAW;AAE3B,MACC,QAAQ,QAAQ,EAAE,SAAS,8CAC3B,OAAO,KAAK,QAAQ,iBAAiB,EAAE,SAAS,CAAC,CAAC,EAAE,QACnD;AACD,eAAW,KAAK,sBAAsB,QAAQ,iBAAiB,EAAE;AAAA,EAClE;AAEA,SAAO;AACR;AAEO,SAAS,WAAW;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,GAaG;AACF,qBAAmB,mBAAmB,IAAI,QAAQ,QAAQ,IAAI,EAAE,QAAQ,SAAS,IAAI;AACrF,QAAM,aAAa,QAAQ,cAAc;AAEzC,QAAM,kBAAkB,iBAAiB;AAEzC,MAAI,QAAqB,CAAC;AAE1B,MAAI,QAAQ,QAAQ,EAAE,SAAS,4CAAwB;AACtD,YAAQ,QAAQ,iBAAiB,EAAE;AAAA,EACpC,WAAW,QAAQ,QAAQ,EAAE,SAAS,oCAAgB;AACrD,UAAM,cAAc,QAAQ,eAAe,QAAQ,QAAQ,EAAE,IAAI;AACjE,UAAM,UAAU,YAAY;AAAA,MAC3B,CAAC,SAAS,KAAK,SAAS;AAAA,IACzB;AACA,QAAI;AACH,YAAM,yBAAyB,QAAQ;AAAA,QACtC,SAAS,SAAS,IAAI;AAAA,MACvB;AAEA,UAAI,wBAAwB;AAC3B,gBAAQ;AAAA,MACT;AAAA,IACD,SAAS,OAAO;AAAA,IAAC;AAAA,EAClB;AAEA,eAAa,kBAAkB,SAAS,UAAU;AAElD,QAAM,OAAO,gBAAgB;AAAA,IAC5B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS,SAAS;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,CAAC;AAED,MAAI,OAAO,gBAAgB,IAAI;AAChC;AAEO,MAAM,kBAAkB,CAAC,UAAgC;AAC/D,SAAO,MAAM;AAAA,IACZ,CAAC,MACA,EAAE,SAAS,sCAAmB,EAAE,SAAS,sCAAkB,EAAE,YAAY,WAAW;AAAA,EACtF;AACD;AAEA,eAAsB,YACrB,SACA,eAAe,wDACd;AACD,QAAM,OAAO,QAAQ,QAAQ;AAC7B,QAAM,UAAU,QAAQ,iBAAiB,WAAW,CAAC,CAAC;AAetD,QAAM,MAAM,QAAQ,kBAAkB;AACtC,QAAM,MAAM,QAAQ,iBAAiB;AAErC,MAAI;AACH,QAAI,QAAQ,kBAAc,aAAAC,SAAM,IAAI,QAAQ,YAAY,CAAC,GAAG;AAC3D,YAAM,IAAI,uCAA0B,GAAG;AAAA,IACxC;AACA,QAAI,KAAK,cAAc,GAAG;AACzB,gBAAM,4CAA8B,SAAS,YAAY;AAAA,IAC1D;AAAA,EACD,SAAS,OAAO;AACf,QAAI,iBAAiB,wCAA2B;AAC/C,UAAI,UAAU,oBAAoB,iCAAiC;AACnE,UAAI,OAAO,GAAG,EAAE,KAAK;AACrB,aAAO,EAAE,mBAAmB,KAAK;AAAA,IAClC;AACA,UAAM;AAAA,EACP;AAEA,QAAM,OAAO,QAAQ,QAAQ,MAAM,WAAW,SAAS;AACvD,QAAM,aAAa,QAAQ,iBAAiB,qBAAqB,CAAC,CAAC;AAEnE,QAAM,SAAS,QAAQ,iBAAiB,EAAE;AAE1C,oCAAkC,OAAO;AAGzC,MAAI,WAAW,OAAO;AACrB,UAAM,YAAY,QAAQ,iBAAiB,aAAa,EAAE;AAC1D,UAAM,kBAAkB,aAAa,QAAQ,iBAAiB,mBAAmB,EAAE,CAAW;AAC9F,QAAI,eAAe,QAAQ,iBAAiB,gBAAgB,EAAE;AAE9D,QAAI;AACJ,QAAI;AACJ,QAAI,oBAAoB;AAExB,QAAI,QAAQ,oBAAoB;AAC/B,YAAM,SAAU,QAAQ,mBAAmC;AAC3D,UAAI,OAAO,gBAAgB,QAAQ;AAClC,4BAAoB,OAAO;AAAA,MAC5B;AACA,UAAI,OAAO,gBAAgB,YAAY;AACtC,sBAAc,OAAO;AAAA,MACtB;AAAA,IACD,OAAO;AACN,0BAAoB,QAAQ;AAAA,IAC7B;AAEA,QAAI,QAAQ,sBAAsB,OAAO;AACxC,0BAAoB;AAAA,IACrB;AAEA,QAAI,cAAc;AAElB,QAAI,QAAQ,aAAa;AACxB,oBAAc,QAAQ;AAAA,IACvB;AAEA,UAAM,iBAAiB,QAAQ,cAAc,QAAQ,QAAQ,EAAE,MAAM;AAAA,MACpE,uBAAuB;AAAA,IACxB,CAAC;AACD,UAAM,cAAc,gBAAgB,cAAc;AAElD,QAAI,aAAa;AAChB,oBAAc;AACd,qBAAe;AAAA,IAChB;AAEA,eAAW;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,QAAQ;AAAA,IACpB,CAAC;AAED,WAAO;AAAA,MACN,mBAAmB;AAAA,IACpB;AAAA,EACD;AAEA,MAAI,EAAE,oBAAoB,IAAI;AAE9B,MAAI,wBAAwB,UAAa,KAAK,cAAc,GAAG;AAC9D,0BAAsB;AAAA,EACvB;AAEA,QAAM,aAAa,MAAM,sBAAsB,SAAS,YAAY,MAAM,mBAAmB;AAE7F,SAAO;AAAA,IACN,iBAAiB,EAAE,QAAQ,IAAI;AAAA,IAC/B,cAAc,CAAC,CAAC,UAAU,CAAC;AAAA,EAC5B;AACD;AAEO,SAAS,eAAe,SAA4B,SAAiB;AAC3E,QAAM,kBAAc,iCAAe,OAAO;AAC1C,MAAI,aAAqB;AAEzB,MAAI,WAAW,WAAW,GAAG,GAAG;AAC/B,iBAAa,WAAW,QAAQ,OAAO,EAAE;AAAA,EAC1C,OAAO;AACN,WAAO;AAAA,EACR;AAEA,MAAI,YAAY,QAAQ;AACvB,eAAW,cAAc,aAAa;AACrC,YAAM,gBAAgB,QAAQ,mBAAmB,GAAG,UAAU,EAAE;AAEhE,UAAI,OAAO,kBAAkB,YAAY,kBAAkB,MAAM;AAChE,qBAAa,WAAW,QAAQ,YAAY,KAAK,UAAU,aAAa,CAAC;AAAA,MAC1E,OAAO;AACN,qBAAa,WAAW,QAAQ,YAAY,aAAuB;AAAA,MACpE;AAAA,IACD;AAAA,EACD;AACA,SAAO;AACR;", "names": ["sanitize", "isbot"]}