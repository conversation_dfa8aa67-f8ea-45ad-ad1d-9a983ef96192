import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  BedrockAgentRuntimeClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../BedrockAgentRuntimeClient";
import {
  ListFlowExecutionEventsRequest,
  ListFlowExecutionEventsResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface ListFlowExecutionEventsCommandInput
  extends ListFlowExecutionEventsRequest {}
export interface ListFlowExecutionEventsCommandOutput
  extends ListFlowExecutionEventsResponse,
    __MetadataBearer {}
declare const ListFlowExecutionEventsCommand_base: {
  new (
    input: ListFlowExecutionEventsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListFlowExecutionEventsCommandInput,
    ListFlowExecutionEventsCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListFlowExecutionEventsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListFlowExecutionEventsCommandInput,
    ListFlowExecutionEventsCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListFlowExecutionEventsCommand extends ListFlowExecutionEventsCommand_base {
  protected static __types: {
    api: {
      input: ListFlowExecutionEventsRequest;
      output: ListFlowExecutionEventsResponse;
    };
    sdk: {
      input: ListFlowExecutionEventsCommandInput;
      output: ListFlowExecutionEventsCommandOutput;
    };
  };
}
