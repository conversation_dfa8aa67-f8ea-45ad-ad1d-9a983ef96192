{"version": 3, "sources": ["../../../nodes/Code/ValidationError.ts"], "sourcesContent": ["import { ApplicationError } from 'n8n-workflow';\n\nexport class ValidationError extends ApplicationError {\n\tdescription = '';\n\n\titemIndex: number | undefined = undefined;\n\n\tcontext: { itemIndex: number } | undefined = undefined;\n\n\tlineNumber: number | undefined = undefined;\n\n\tconstructor({\n\t\tmessage,\n\t\tdescription,\n\t\titemIndex,\n\t\tlineNumber,\n\t}: {\n\t\tmessage: string;\n\t\tdescription: string;\n\t\titemIndex?: number;\n\t\tlineNumber?: number;\n\t}) {\n\t\tsuper(message);\n\n\t\tthis.lineNumber = lineNumber;\n\t\tthis.itemIndex = itemIndex;\n\n\t\tif (this.lineNumber !== undefined && this.itemIndex !== undefined) {\n\t\t\tthis.message = `${message} [line ${lineNumber}, for item ${itemIndex}]`;\n\t\t} else if (this.lineNumber !== undefined) {\n\t\t\tthis.message = `${message} [line ${lineNumber}]`;\n\t\t} else if (this.itemIndex !== undefined) {\n\t\t\tthis.message = `${message} [item ${itemIndex}]`;\n\t\t} else {\n\t\t\tthis.message = message;\n\t\t}\n\n\t\tthis.description = description;\n\n\t\tif (this.itemIndex !== undefined) {\n\t\t\tthis.context = { itemIndex: this.itemIndex };\n\t\t}\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAAiC;AAE1B,MAAM,wBAAwB,qCAAiB;AAAA,EASrD,YAAY;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAKG;AACF,UAAM,OAAO;AAnBd,uBAAc;AAEd,qBAAgC;AAEhC,mBAA6C;AAE7C,sBAAiC;AAehC,SAAK,aAAa;AAClB,SAAK,YAAY;AAEjB,QAAI,KAAK,eAAe,UAAa,KAAK,cAAc,QAAW;AAClE,WAAK,UAAU,GAAG,OAAO,UAAU,UAAU,cAAc,SAAS;AAAA,IACrE,WAAW,KAAK,eAAe,QAAW;AACzC,WAAK,UAAU,GAAG,OAAO,UAAU,UAAU;AAAA,IAC9C,WAAW,KAAK,cAAc,QAAW;AACxC,WAAK,UAAU,GAAG,OAAO,UAAU,SAAS;AAAA,IAC7C,OAAO;AACN,WAAK,UAAU;AAAA,IAChB;AAEA,SAAK,cAAc;AAEnB,QAAI,KAAK,cAAc,QAAW;AACjC,WAAK,UAAU,EAAE,WAAW,KAAK,UAAU;AAAA,IAC5C;AAAA,EACD;AACD;", "names": []}