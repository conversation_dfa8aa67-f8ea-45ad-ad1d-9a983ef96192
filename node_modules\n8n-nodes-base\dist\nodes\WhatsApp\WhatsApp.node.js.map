{"version": 3, "sources": ["../../../nodes/WhatsApp/WhatsApp.node.ts"], "sourcesContent": ["import type { IExecuteFunctions, INodeType, INodeTypeDescription } from 'n8n-workflow';\nimport { NodeConnectionTypes, NodeOperationError, SEND_AND_WAIT_OPERATION } from 'n8n-workflow';\n\nimport { createMessage, WHATSAPP_BASE_URL } from './GenericFunctions';\nimport { mediaFields, mediaTypeFields } from './MediaDescription';\nimport { sanitizePhoneNumber } from './MessageFunctions';\nimport { messageFields, messageTypeFields } from './MessagesDescription';\nimport { configureWaitTillDate } from '../../utils/sendAndWait/configureWaitTillDate.util';\nimport { sendAndWaitWebhooksDescription } from '../../utils/sendAndWait/descriptions';\nimport {\n\tgetSendAndWaitConfig,\n\tgetSendAndWaitProperties,\n\tsendAndWaitWebhook,\n} from '../../utils/sendAndWait/utils';\n\nconst WHATSAPP_CREDENTIALS_TYPE = 'whatsAppApi';\n\nexport class WhatsApp implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'WhatsApp Business Cloud',\n\t\tname: 'whatsApp',\n\t\ticon: 'file:whatsapp.svg',\n\t\tgroup: ['output'],\n\t\tversion: 1,\n\t\tsubtitle: '={{ $parameter[\"resource\"] + \": \" + $parameter[\"operation\"] }}',\n\t\tdescription: 'Access WhatsApp API',\n\t\tdefaults: {\n\t\t\tname: 'WhatsApp Business Cloud',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\twebhooks: sendAndWaitWebhooksDescription,\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: WHATSAPP_CREDENTIALS_TYPE,\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\trequestDefaults: {\n\t\t\tbaseURL: WHATSAPP_BASE_URL,\n\t\t},\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Message',\n\t\t\t\t\t\tvalue: 'message',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Media',\n\t\t\t\t\t\tvalue: 'media',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'message',\n\t\t\t},\n\t\t\t...messageFields,\n\t\t\t...mediaFields,\n\t\t\t...messageTypeFields,\n\t\t\t...mediaTypeFields,\n\t\t\t...getSendAndWaitProperties([], 'message', undefined, {\n\t\t\t\tnoButtonStyle: true,\n\t\t\t\tdefaultApproveLabel: '✓ Approve',\n\t\t\t\tdefaultDisapproveLabel: '✗ Decline',\n\t\t\t}).filter((p) => p.name !== 'subject'),\n\t\t],\n\t};\n\n\twebhook = sendAndWaitWebhook;\n\n\tcustomOperations = {\n\t\tmessage: {\n\t\t\tasync [SEND_AND_WAIT_OPERATION](this: IExecuteFunctions) {\n\t\t\t\ttry {\n\t\t\t\t\tconst phoneNumberId = this.getNodeParameter('phoneNumberId', 0) as string;\n\n\t\t\t\t\tconst recipientPhoneNumber = sanitizePhoneNumber(\n\t\t\t\t\t\tthis.getNodeParameter('recipientPhoneNumber', 0) as string,\n\t\t\t\t\t);\n\n\t\t\t\t\tconst config = getSendAndWaitConfig(this);\n\t\t\t\t\tconst instanceId = this.getInstanceId();\n\n\t\t\t\t\tawait this.helpers.httpRequestWithAuthentication.call(\n\t\t\t\t\t\tthis,\n\t\t\t\t\t\tWHATSAPP_CREDENTIALS_TYPE,\n\t\t\t\t\t\tcreateMessage(config, phoneNumberId, recipientPhoneNumber, instanceId),\n\t\t\t\t\t);\n\n\t\t\t\t\tconst waitTill = configureWaitTillDate(this);\n\n\t\t\t\t\tawait this.putExecutionToWait(waitTill);\n\t\t\t\t\treturn [this.getInputData()];\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthrow new NodeOperationError(this.getNode(), error);\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAAiF;AAEjF,8BAAiD;AACjD,8BAA6C;AAC7C,8BAAoC;AACpC,iCAAiD;AACjD,mCAAsC;AACtC,0BAA+C;AAC/C,mBAIO;AAEP,MAAM,4BAA4B;AAE3B,MAAM,SAA8B;AAAA,EAApC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,QAAQ;AAAA,MAChB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,UAAU;AAAA,MACV,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,iBAAiB;AAAA,QAChB,SAAS;AAAA,MACV;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,OAAG,uCAAyB,CAAC,GAAG,WAAW,QAAW;AAAA,UACrD,eAAe;AAAA,UACf,qBAAqB;AAAA,UACrB,wBAAwB;AAAA,QACzB,CAAC,EAAE,OAAO,CAAC,MAAM,EAAE,SAAS,SAAS;AAAA,MACtC;AAAA,IACD;AAEA,mBAAU;AAEV,4BAAmB;AAAA,MAClB,SAAS;AAAA,QACR,OAAO,2CAAuB,IAA2B;AACxD,cAAI;AACH,kBAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,CAAC;AAE9D,kBAAM,2BAAuB;AAAA,cAC5B,KAAK,iBAAiB,wBAAwB,CAAC;AAAA,YAChD;AAEA,kBAAM,aAAS,mCAAqB,IAAI;AACxC,kBAAM,aAAa,KAAK,cAAc;AAEtC,kBAAM,KAAK,QAAQ,8BAA8B;AAAA,cAChD;AAAA,cACA;AAAA,kBACA,uCAAc,QAAQ,eAAe,sBAAsB,UAAU;AAAA,YACtE;AAEA,kBAAM,eAAW,oDAAsB,IAAI;AAE3C,kBAAM,KAAK,mBAAmB,QAAQ;AACtC,mBAAO,CAAC,KAAK,aAAa,CAAC;AAAA,UAC5B,SAAS,OAAO;AACf,kBAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,KAAK;AAAA,UACnD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA;AACD;", "names": []}