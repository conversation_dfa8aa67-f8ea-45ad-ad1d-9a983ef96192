{"version": 3, "sources": ["../../../../../nodes/Files/ReadWriteFile/actions/write.operation.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tINodeExecutionData,\n\tINodeProperties,\n\tJsonObject,\n} from 'n8n-workflow';\nimport { BINARY_ENCODING, NodeApiError } from 'n8n-workflow';\nimport type { Readable } from 'stream';\n\nimport { updateDisplayOptions } from '@utils/utilities';\n\nimport { errorMapper } from '../helpers/utils';\n\nexport const properties: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'File Path and Name',\n\t\tname: 'fileName',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\trequired: true,\n\t\tplaceholder: 'e.g. /data/example.jpg',\n\t\tdescription:\n\t\t\t'Path and name of the file that should be written. Also include the file extension.',\n\t},\n\t{\n\t\tdisplayName: 'Input Binary Field',\n\t\tname: 'dataPropertyName',\n\t\ttype: 'string',\n\t\tdefault: 'data',\n\t\tplaceholder: 'e.g. data',\n\t\trequired: true,\n\t\thint: 'The name of the input binary field containing the file to be written',\n\t},\n\t{\n\t\tdisplayName: 'Options',\n\t\tname: 'options',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add option',\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Append',\n\t\t\t\tname: 'append',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription:\n\t\t\t\t\t\"Whether to append to an existing file. While it's commonly used with text files, it's not limited to them, however, it wouldn't be applicable for file types that have a specific structure like most binary formats.\",\n\t\t\t},\n\t\t],\n\t},\n];\n\nconst displayOptions = {\n\tshow: {\n\t\toperation: ['write'],\n\t},\n};\n\nexport const description = updateDisplayOptions(displayOptions, properties);\n\nexport async function execute(this: IExecuteFunctions, items: INodeExecutionData[]) {\n\tconst returnData: INodeExecutionData[] = [];\n\tlet fileName;\n\n\tlet item: INodeExecutionData;\n\tfor (let itemIndex = 0; itemIndex < items.length; itemIndex++) {\n\t\ttry {\n\t\t\tconst dataPropertyName = this.getNodeParameter('dataPropertyName', itemIndex);\n\t\t\tfileName = this.getNodeParameter('fileName', itemIndex) as string;\n\t\t\tconst options = this.getNodeParameter('options', itemIndex, {});\n\t\t\tconst flag: string = options.append ? 'a' : 'w';\n\n\t\t\titem = items[itemIndex];\n\n\t\t\tconst newItem: INodeExecutionData = {\n\t\t\t\tjson: {},\n\t\t\t\tpairedItem: {\n\t\t\t\t\titem: itemIndex,\n\t\t\t\t},\n\t\t\t};\n\t\t\tObject.assign(newItem.json, item.json);\n\n\t\t\tconst binaryData = this.helpers.assertBinaryData(itemIndex, dataPropertyName);\n\n\t\t\tlet fileContent: Buffer | Readable;\n\t\t\tif (binaryData.id) {\n\t\t\t\tfileContent = await this.helpers.getBinaryStream(binaryData.id);\n\t\t\t} else {\n\t\t\t\tfileContent = Buffer.from(binaryData.data, BINARY_ENCODING);\n\t\t\t}\n\n\t\t\t// Write the file to disk\n\t\t\tawait this.helpers.writeContentToFile(fileName, fileContent, flag);\n\n\t\t\tif (item.binary !== undefined) {\n\t\t\t\t// Create a shallow copy of the binary data so that the old\n\t\t\t\t// data references which do not get changed still stay behind\n\t\t\t\t// but the incoming data does not get changed.\n\t\t\t\tnewItem.binary = {};\n\t\t\t\tObject.assign(newItem.binary, item.binary);\n\t\t\t}\n\n\t\t\t// Add the file name to data\n\t\t\tnewItem.json.fileName = fileName;\n\n\t\t\treturnData.push(newItem);\n\t\t} catch (error) {\n\t\t\tconst nodeOperatioinError = errorMapper.call(this, error, itemIndex, {\n\t\t\t\tfilePath: fileName,\n\t\t\t\toperation: 'write',\n\t\t\t});\n\t\t\tif (this.continueOnFail()) {\n\t\t\t\treturnData.push({\n\t\t\t\t\tjson: {\n\t\t\t\t\t\terror: nodeOperatioinError.message,\n\t\t\t\t\t},\n\t\t\t\t\tpairedItem: {\n\t\t\t\t\t\titem: itemIndex,\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tthrow new NodeApiError(this.getNode(), error as JsonObject, { itemIndex });\n\t\t}\n\t}\n\n\treturn returnData;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,0BAA8C;AAG9C,uBAAqC;AAErC,mBAA4B;AAErB,MAAM,aAAgC;AAAA,EAC5C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,UAAU;AAAA,IACV,MAAM;AAAA,EACP;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AACD;AAEA,MAAM,iBAAiB;AAAA,EACtB,MAAM;AAAA,IACL,WAAW,CAAC,OAAO;AAAA,EACpB;AACD;AAEO,MAAM,kBAAc,uCAAqB,gBAAgB,UAAU;AAE1E,eAAsB,QAAiC,OAA6B;AACnF,QAAM,aAAmC,CAAC;AAC1C,MAAI;AAEJ,MAAI;AACJ,WAAS,YAAY,GAAG,YAAY,MAAM,QAAQ,aAAa;AAC9D,QAAI;AACH,YAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,SAAS;AAC5E,iBAAW,KAAK,iBAAiB,YAAY,SAAS;AACtD,YAAM,UAAU,KAAK,iBAAiB,WAAW,WAAW,CAAC,CAAC;AAC9D,YAAM,OAAe,QAAQ,SAAS,MAAM;AAE5C,aAAO,MAAM,SAAS;AAEtB,YAAM,UAA8B;AAAA,QACnC,MAAM,CAAC;AAAA,QACP,YAAY;AAAA,UACX,MAAM;AAAA,QACP;AAAA,MACD;AACA,aAAO,OAAO,QAAQ,MAAM,KAAK,IAAI;AAErC,YAAM,aAAa,KAAK,QAAQ,iBAAiB,WAAW,gBAAgB;AAE5E,UAAI;AACJ,UAAI,WAAW,IAAI;AAClB,sBAAc,MAAM,KAAK,QAAQ,gBAAgB,WAAW,EAAE;AAAA,MAC/D,OAAO;AACN,sBAAc,OAAO,KAAK,WAAW,MAAM,mCAAe;AAAA,MAC3D;AAGA,YAAM,KAAK,QAAQ,mBAAmB,UAAU,aAAa,IAAI;AAEjE,UAAI,KAAK,WAAW,QAAW;AAI9B,gBAAQ,SAAS,CAAC;AAClB,eAAO,OAAO,QAAQ,QAAQ,KAAK,MAAM;AAAA,MAC1C;AAGA,cAAQ,KAAK,WAAW;AAExB,iBAAW,KAAK,OAAO;AAAA,IACxB,SAAS,OAAO;AACf,YAAM,sBAAsB,yBAAY,KAAK,MAAM,OAAO,WAAW;AAAA,QACpE,UAAU;AAAA,QACV,WAAW;AAAA,MACZ,CAAC;AACD,UAAI,KAAK,eAAe,GAAG;AAC1B,mBAAW,KAAK;AAAA,UACf,MAAM;AAAA,YACL,OAAO,oBAAoB;AAAA,UAC5B;AAAA,UACA,YAAY;AAAA,YACX,MAAM;AAAA,UACP;AAAA,QACD,CAAC;AACD;AAAA,MACD;AACA,YAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,OAAqB,EAAE,UAAU,CAAC;AAAA,IAC1E;AAAA,EACD;AAEA,SAAO;AACR;", "names": []}