{"version": 3, "sources": ["../../../../nodes/Gong/descriptions/UserDescription.ts"], "sourcesContent": ["import type {\n\tIDataObject,\n\tIExecuteSingleFunctions,\n\tIHttpRequestOptions,\n\tINodeProperties,\n} from 'n8n-workflow';\nimport { NodeApiError } from 'n8n-workflow';\n\nimport {\n\tgetCursorPaginatorUsers,\n\tisValidNumberIds,\n\thandleErrorPostReceive,\n} from '../GenericFunctions';\n\nexport const userOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\tdescription: 'Retrieve data for a specific user',\n\t\t\t\taction: 'Get user',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\turl: '/v2/users/extensive',\n\t\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [handleErrorPostReceive],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Retrieve a list of users',\n\t\t\t\taction: 'Get many users',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\turl: '/v2/users/extensive',\n\t\t\t\t\t\tbody: {\n\t\t\t\t\t\t\tfilter: {},\n\t\t\t\t\t\t},\n\t\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [handleErrorPostReceive],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t],\n\t\tdefault: 'get',\n\t},\n];\n\nconst getOperation: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'User to Get',\n\t\tname: 'user',\n\t\tdefault: {\n\t\t\tmode: 'list',\n\t\t\tvalue: '',\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t\trequired: true,\n\t\tmodes: [\n\t\t\t{\n\t\t\t\tdisplayName: 'From List',\n\t\t\t\tname: 'list',\n\t\t\t\ttype: 'list',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tsearchListMethod: 'getUsers',\n\t\t\t\t\tsearchable: true,\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'By ID',\n\t\t\t\tname: 'id',\n\t\t\t\tplaceholder: 'e.g. 7782342274025937895',\n\t\t\t\ttype: 'string',\n\t\t\t\tvalidation: [\n\t\t\t\t\t{\n\t\t\t\t\t\ttype: 'regex',\n\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\tregex: '[0-9]{1,20}',\n\t\t\t\t\t\t\terrorMessage: 'Not a valid Gong User ID',\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\ttype: 'body',\n\t\t\t\tproperty: 'filter.userIds',\n\t\t\t\tpropertyInDotNotation: true,\n\t\t\t\tvalue: '={{ [$value] }}',\n\t\t\t},\n\t\t\toutput: {\n\t\t\t\tpostReceive: [\n\t\t\t\t\t{\n\t\t\t\t\t\ttype: 'rootProperty',\n\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\tproperty: 'users',\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t},\n\t\ttype: 'resourceLocator',\n\t},\n];\n\nconst getAllOperation: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\trouting: {\n\t\t\tsend: {\n\t\t\t\tpaginate: '={{ $value }}',\n\t\t\t},\n\t\t\toperations: {\n\t\t\t\tpagination: getCursorPaginatorUsers(),\n\t\t\t},\n\t\t},\n\t\ttype: 'boolean',\n\t\tvalidateType: 'boolean',\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\tdefault: 50,\n\t\tdescription: 'Max number of results to return',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t\trouting: {\n\t\t\toutput: {\n\t\t\t\tpostReceive: [\n\t\t\t\t\t{\n\t\t\t\t\t\ttype: 'rootProperty',\n\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\tproperty: 'users',\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttype: 'limit',\n\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\tmaxResults: '={{ $value }}',\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t},\n\t\ttype: 'number',\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t},\n\t\tvalidateType: 'number',\n\t},\n\t{\n\t\tdisplayName: 'Filters',\n\t\tname: 'filters',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Created After',\n\t\t\t\tname: 'createdFromDateTime',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'An optional user creation time lower limit, if supplied the API will return only the users created at or after this time',\n\t\t\t\tplaceholder: 'e.g. 2018-02-18T02:30:00-07:00 or 2018-02-18T08:00:00Z',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t\tproperty: 'filter.createdFromDateTime',\n\t\t\t\t\t\tpropertyInDotNotation: true,\n\t\t\t\t\t\tvalue: '={{ new Date($value).toISOString() }}',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tvalidateType: 'dateTime',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Created Before',\n\t\t\t\tname: 'createdToDateTime',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'An optional user creation time upper limit, if supplied the API will return only the users created before this time',\n\t\t\t\tplaceholder: 'e.g. 2018-02-18T02:30:00-07:00 or 2018-02-18T08:00:00Z',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\ttype: 'body',\n\t\t\t\t\t\tproperty: 'filter.createdToDateTime',\n\t\t\t\t\t\tpropertyInDotNotation: true,\n\t\t\t\t\t\tvalue: '={{ new Date($value).toISOString() }}',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tvalidateType: 'dateTime',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'User IDs',\n\t\t\t\tname: 'userIds',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: \"Set of Gong's unique numeric identifiers for the users (up to 20 digits)\",\n\t\t\t\thint: 'Comma separated list of IDs, array of strings can be set in expression',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tpreSend: [\n\t\t\t\t\t\t\tasync function (\n\t\t\t\t\t\t\t\tthis: IExecuteSingleFunctions,\n\t\t\t\t\t\t\t\trequestOptions: IHttpRequestOptions,\n\t\t\t\t\t\t\t): Promise<IHttpRequestOptions> {\n\t\t\t\t\t\t\t\tconst userIdsParam = this.getNodeParameter('filters.userIds') as\n\t\t\t\t\t\t\t\t\t| number\n\t\t\t\t\t\t\t\t\t| number[]\n\t\t\t\t\t\t\t\t\t| string\n\t\t\t\t\t\t\t\t\t| string[];\n\t\t\t\t\t\t\t\tif (userIdsParam && !isValidNumberIds(userIdsParam)) {\n\t\t\t\t\t\t\t\t\tthrow new NodeApiError(this.getNode(), {\n\t\t\t\t\t\t\t\t\t\tmessage: 'User IDs must be numeric',\n\t\t\t\t\t\t\t\t\t\tdescription: \"Double-check the value in the parameter 'User IDs' and try again\",\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tconst userIds = Array.isArray(userIdsParam)\n\t\t\t\t\t\t\t\t\t? userIdsParam.map((x) => x.toString())\n\t\t\t\t\t\t\t\t\t: userIdsParam\n\t\t\t\t\t\t\t\t\t\t\t.toString()\n\t\t\t\t\t\t\t\t\t\t\t.split(',')\n\t\t\t\t\t\t\t\t\t\t\t.map((x) => x.trim());\n\n\t\t\t\t\t\t\t\trequestOptions.body ||= {};\n\t\t\t\t\t\t\t\t(requestOptions.body as IDataObject).filter ||= {};\n\t\t\t\t\t\t\t\tObject.assign((requestOptions.body as IDataObject).filter as IDataObject, {\n\t\t\t\t\t\t\t\t\tuserIds,\n\t\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\t\treturn requestOptions;\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tplaceholder: 'e.g. 7782342274025937895',\n\t\t\t\ttype: 'string',\n\t\t\t},\n\t\t],\n\t\tplaceholder: 'Add Filter',\n\t\ttype: 'collection',\n\t},\n];\n\nexport const userFields: INodeProperties[] = [...getOperation, ...getAllOperation];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,0BAA6B;AAE7B,8BAIO;AAEA,MAAM,iBAAoC;AAAA,EAChD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,wBAAwB;AAAA,UACzB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa,CAAC,8CAAsB;AAAA,UACrC;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,MAAM;AAAA,cACL,QAAQ,CAAC;AAAA,YACV;AAAA,YACA,wBAAwB;AAAA,UACzB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa,CAAC,8CAAsB;AAAA,UACrC;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEA,MAAM,eAAkC;AAAA,EACvC;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,UAAU;AAAA,IACV,OAAO;AAAA,MACN;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACb;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,YAAY;AAAA,UACX;AAAA,YACC,MAAM;AAAA,YACN,YAAY;AAAA,cACX,OAAO;AAAA,cACP,cAAc;AAAA,YACf;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR,MAAM;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,QACV,uBAAuB;AAAA,QACvB,OAAO;AAAA,MACR;AAAA,MACA,QAAQ;AAAA,QACP,aAAa;AAAA,UACZ;AAAA,YACC,MAAM;AAAA,YACN,YAAY;AAAA,cACX,UAAU;AAAA,YACX;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IACA,MAAM;AAAA,EACP;AACD;AAEA,MAAM,kBAAqC;AAAA,EAC1C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR,MAAM;AAAA,QACL,UAAU;AAAA,MACX;AAAA,MACA,YAAY;AAAA,QACX,gBAAY,iDAAwB;AAAA,MACrC;AAAA,IACD;AAAA,IACA,MAAM;AAAA,IACN,cAAc;AAAA,EACf;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,QACpB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR,QAAQ;AAAA,QACP,aAAa;AAAA,UACZ;AAAA,YACC,MAAM;AAAA,YACN,YAAY;AAAA,cACX,UAAU;AAAA,YACX;AAAA,UACD;AAAA,UACA;AAAA,YACC,MAAM;AAAA,YACN,YAAY;AAAA,cACX,YAAY;AAAA,YACb;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IACA,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,UAAU;AAAA,IACX;AAAA,IACA,cAAc;AAAA,EACf;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,QACD,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,MAAM;AAAA,YACN,UAAU;AAAA,YACV,uBAAuB;AAAA,YACvB,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,QACD,aAAa;AAAA,QACb,SAAS;AAAA,UACR,MAAM;AAAA,YACL,MAAM;AAAA,YACN,UAAU;AAAA,YACV,uBAAuB;AAAA,YACvB,OAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MACf;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,UACR,MAAM;AAAA,YACL,SAAS;AAAA,cACR,eAEC,gBAC+B;AAC/B,sBAAM,eAAe,KAAK,iBAAiB,iBAAiB;AAK5D,oBAAI,gBAAgB,KAAC,0CAAiB,YAAY,GAAG;AACpD,wBAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG;AAAA,oBACtC,SAAS;AAAA,oBACT,aAAa;AAAA,kBACd,CAAC;AAAA,gBACF;AAEA,sBAAM,UAAU,MAAM,QAAQ,YAAY,IACvC,aAAa,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,IACpC,aACC,SAAS,EACT,MAAM,GAAG,EACT,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;AAEvB,+BAAe,SAAS,CAAC;AACzB,gBAAC,eAAe,KAAqB,WAAW,CAAC;AACjD,uBAAO,OAAQ,eAAe,KAAqB,QAAuB;AAAA,kBACzE;AAAA,gBACD,CAAC;AAED,uBAAO;AAAA,cACR;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,aAAa;AAAA,QACb,MAAM;AAAA,MACP;AAAA,IACD;AAAA,IACA,aAAa;AAAA,IACb,MAAM;AAAA,EACP;AACD;AAEO,MAAM,aAAgC,CAAC,GAAG,cAAc,GAAG,eAAe;", "names": []}