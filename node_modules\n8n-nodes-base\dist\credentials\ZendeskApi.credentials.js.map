{"version": 3, "sources": ["../../credentials/ZendeskApi.credentials.ts"], "sourcesContent": ["import type {\n\tICredentialDataDecryptedObject,\n\tICredentialTestRequest,\n\tICredentialType,\n\tIHttpRequestOptions,\n\tINodeProperties,\n} from 'n8n-workflow';\n\nexport class ZendeskApi implements ICredentialType {\n\tname = 'zendeskApi';\n\n\tdisplayName = 'Zendesk API';\n\n\tdocumentationUrl = 'zendesk';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Subdomain',\n\t\t\tname: 'subdomain',\n\t\t\ttype: 'string',\n\t\t\tdescription: 'The subdomain of your Zendesk work environment',\n\t\t\tplaceholder: 'company',\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Email',\n\t\t\tname: 'email',\n\t\t\ttype: 'string',\n\t\t\tplaceholder: '<EMAIL>',\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'API Token',\n\t\t\tname: 'apiToken',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\ttypeOptions: {\n\t\t\t\tpassword: true,\n\t\t\t},\n\t\t},\n\t];\n\n\tasync authenticate(\n\t\tcredentials: ICredentialDataDecryptedObject,\n\t\trequestOptions: IHttpRequestOptions,\n\t): Promise<IHttpRequestOptions> {\n\t\trequestOptions.auth = {\n\t\t\tusername: `${credentials.email}/token`,\n\t\t\tpassword: credentials.apiToken as string,\n\t\t};\n\t\treturn requestOptions;\n\t}\n\n\ttest: ICredentialTestRequest = {\n\t\trequest: {\n\t\t\tbaseURL: '=https://{{$credentials.subdomain}}.zendesk.com/api/v2',\n\t\t\turl: '/ticket_fields.json',\n\t\t},\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAQO,MAAM,WAAsC;AAAA,EAA5C;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,aAAa;AAAA,QACb,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,MACD;AAAA,IACD;AAaA,gBAA+B;AAAA,MAC9B,SAAS;AAAA,QACR,SAAS;AAAA,QACT,KAAK;AAAA,MACN;AAAA,IACD;AAAA;AAAA,EAhBA,MAAM,aACL,aACA,gBAC+B;AAC/B,mBAAe,OAAO;AAAA,MACrB,UAAU,GAAG,YAAY,KAAK;AAAA,MAC9B,UAAU,YAAY;AAAA,IACvB;AACA,WAAO;AAAA,EACR;AAQD;", "names": []}