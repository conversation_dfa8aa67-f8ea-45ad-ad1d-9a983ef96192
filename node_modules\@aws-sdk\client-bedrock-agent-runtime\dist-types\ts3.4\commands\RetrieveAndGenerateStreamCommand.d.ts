import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  BedrockAgentRuntimeClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../BedrockAgentRuntimeClient";
import {
  RetrieveAndGenerateStreamRequest,
  RetrieveAndGenerateStreamResponse,
} from "../models/models_1";
export { __MetadataBearer };
export { $Command };
export interface RetrieveAndGenerateStreamCommandInput
  extends RetrieveAndGenerateStreamRequest {}
export interface RetrieveAndGenerateStreamCommandOutput
  extends RetrieveAndGenerateStreamResponse,
    __MetadataBearer {}
declare const RetrieveAndGenerateStreamCommand_base: {
  new (
    input: RetrieveAndGenerateStreamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    RetrieveAndGenerateStreamCommandInput,
    RetrieveAndGenerateStreamCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: RetrieveAndGenerateStreamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    RetrieveAndGenerateStreamCommandInput,
    RetrieveAndGenerateStreamCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class RetrieveAndGenerateStreamCommand extends RetrieveAndGenerateStreamCommand_base {
  protected static __types: {
    api: {
      input: RetrieveAndGenerateStreamRequest;
      output: RetrieveAndGenerateStreamResponse;
    };
    sdk: {
      input: RetrieveAndGenerateStreamCommandInput;
      output: RetrieveAndGenerateStreamCommandOutput;
    };
  };
}
