{"version": 3, "sources": ["../../../nodes/Pipedrive/utils.ts"], "sourcesContent": ["export const currencies = [\n\t{ name: 'US Dollar', value: 'USD' },\n\t{ name: 'Euro', value: 'EUR' },\n\t{ name: 'UAE Dirham', value: 'AED' },\n\t{ name: 'Afghani', value: 'AFN' },\n\t{ name: 'Lek', value: 'ALL' },\n\t{ name: 'Argentine Peso', value: 'ARS' },\n\t{ name: 'Australian Dollar', value: 'AUD' },\n\t{ name: 'Azerbaijan Manat', value: 'AZN' },\n\t{ name: 'Barbados Dollar', value: 'BBD' },\n\t{ name: 'Taka', value: 'BDT' },\n\t{ name: 'Bulgarian Lev', value: 'BGN' },\n\t{ name: 'Bermudian Dollar', value: 'BMD' },\n\t{ name: 'Brunei Dollar', value: 'BND' },\n\t{ name: 'Boliviano', value: 'BOB' },\n\t{ name: 'Brazilian Real', value: 'BRL' },\n\t{ name: 'Bahamian Dollar', value: 'BSD' },\n\t{ name: 'Pula', value: 'BWP' },\n\t{ name: 'Belize Dollar', value: 'BZD' },\n\t{ name: 'Canadian Dollar', value: 'CAD' },\n\t{ name: 'Swiss Franc', value: 'CHF' },\n\t{ name: 'Chilean Peso', value: 'CLP' },\n\t{ name: '<PERSON>', value: 'CNY' },\n\t{ name: 'Colombian Peso', value: 'COP' },\n\t{ name: 'Costa Rican Colon', value: 'CRC' },\n\t{ name: 'Czech Koruna', value: 'CZK' },\n\t{ name: 'Danish Krone', value: 'DKK' },\n\t{ name: 'Dominican Peso', value: 'DOP' },\n\t{ name: 'Algerian Dinar', value: 'DZD' },\n\t{ name: 'Egyptian Pound', value: 'EGP' },\n\t{ name: 'Fiji Dollar', value: 'FJD' },\n\t{ name: 'Pound Sterling', value: 'GBP' },\n\t{ name: 'Quetzal', value: 'GTQ' },\n\t{ name: 'Hong Kong Dollar', value: 'HKD' },\n\t{ name: 'Lempira', value: 'HNL' },\n\t{ name: 'Kuna', value: 'HRK' },\n\t{ name: 'Forint', value: 'HUF' },\n\t{ name: 'Rupiah', value: 'IDR' },\n\t{ name: 'New Israeli Sheqel', value: 'ILS' },\n\t{ name: 'Indian Rupee', value: 'INR' },\n\t{ name: 'Jamaican Dollar', value: 'JMD' },\n\t{ name: 'Yen', value: 'JPY' },\n\t{ name: 'Kenyan Shilling', value: 'KES' },\n\t{ name: 'Won', value: 'KRW' },\n\t{ name: 'Tenge', value: 'KZT' },\n\t{ name: 'Lao Kip', value: 'LAK' },\n\t{ name: 'Lebanese Pound', value: 'LBP' },\n\t{ name: 'Sri Lanka Rupee', value: 'LKR' },\n\t{ name: 'Liberian Dollar', value: 'LRD' },\n\t{ name: 'Moroccan Dirham', value: 'MAD' },\n\t{ name: 'Kyat', value: 'MMK' },\n\t{ name: 'Pataca', value: 'MOP' },\n\t{ name: 'Ouguiya', value: 'MRO' },\n\t{ name: 'Mauritius Rupee', value: 'MUR' },\n\t{ name: 'Rufiyaa', value: 'MVR' },\n\t{ name: 'Mexican Peso', value: 'MXN' },\n\t{ name: 'Malaysian Ringgit', value: 'MYR' },\n\t{ name: 'Cordoba Oro', value: 'NIO' },\n\t{ name: 'Norwegian Krone', value: 'NOK' },\n\t{ name: 'Nepalese Rupee', value: 'NPR' },\n\t{ name: 'New Zealand Dollar', value: 'NZD' },\n\t{ name: 'Sol', value: 'PEN' },\n\t{ name: 'Kina', value: 'PGK' },\n\t{ name: 'Philippine Peso', value: 'PHP' },\n\t{ name: 'Pakistan Rupee', value: 'PKR' },\n\t{ name: 'Zloty', value: 'PLN' },\n\t{ name: 'Qatari Rial', value: 'QAR' },\n\t{ name: 'Romanian Leu', value: 'RON' },\n\t{ name: 'Russian Ruble', value: 'RUB' },\n\t{ name: 'Saudi Riyal', value: 'SAR' },\n\t{ name: 'Solomon Islands Dollar', value: 'SBD' },\n\t{ name: 'Seychelles Rupee', value: 'SCR' },\n\t{ name: 'Swedish Krona', value: 'SEK' },\n\t{ name: 'Singapore Dollar', value: 'SGD' },\n\t{ name: 'Syrian Pound', value: 'SYP' },\n\t{ name: 'Baht', value: 'THB' },\n\t{ name: 'Pa’anga', value: 'TOP' },\n\t{ name: 'Turkish Lira', value: 'TRY' },\n\t{ name: 'Trinidad and Tobago Dollar', value: 'TTD' },\n\t{ name: 'New Taiwan Dollar', value: 'TWD' },\n\t{ name: 'Hryvnia', value: 'UAH' },\n\t{ name: 'Dong', value: 'VND' },\n\t{ name: 'Vatu', value: 'VUV' },\n\t{ name: 'Tala', value: 'WST' },\n\t{ name: 'East Caribbean Dollar', value: 'XCD' },\n\t{ name: 'West African CFA Franc', value: 'XOF' },\n\t{ name: 'Yemeni Rial', value: 'YER' },\n\t{ name: 'Rand', value: 'ZAR' },\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,MAAM,aAAa;AAAA,EACzB,EAAE,MAAM,aAAa,OAAO,MAAM;AAAA,EAClC,EAAE,MAAM,QAAQ,OAAO,MAAM;AAAA,EAC7B,EAAE,MAAM,cAAc,OAAO,MAAM;AAAA,EACnC,EAAE,MAAM,WAAW,OAAO,MAAM;AAAA,EAChC,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,EAC5B,EAAE,MAAM,kBAAkB,OAAO,MAAM;AAAA,EACvC,EAAE,MAAM,qBAAqB,OAAO,MAAM;AAAA,EAC1C,EAAE,MAAM,oBAAoB,OAAO,MAAM;AAAA,EACzC,EAAE,MAAM,mBAAmB,OAAO,MAAM;AAAA,EACxC,EAAE,MAAM,QAAQ,OAAO,MAAM;AAAA,EAC7B,EAAE,MAAM,iBAAiB,OAAO,MAAM;AAAA,EACtC,EAAE,MAAM,oBAAoB,OAAO,MAAM;AAAA,EACzC,EAAE,MAAM,iBAAiB,OAAO,MAAM;AAAA,EACtC,EAAE,MAAM,aAAa,OAAO,MAAM;AAAA,EAClC,EAAE,MAAM,kBAAkB,OAAO,MAAM;AAAA,EACvC,EAAE,MAAM,mBAAmB,OAAO,MAAM;AAAA,EACxC,EAAE,MAAM,QAAQ,OAAO,MAAM;AAAA,EAC7B,EAAE,MAAM,iBAAiB,OAAO,MAAM;AAAA,EACtC,EAAE,MAAM,mBAAmB,OAAO,MAAM;AAAA,EACxC,EAAE,MAAM,eAAe,OAAO,MAAM;AAAA,EACpC,EAAE,MAAM,gBAAgB,OAAO,MAAM;AAAA,EACrC,EAAE,MAAM,iBAAiB,OAAO,MAAM;AAAA,EACtC,EAAE,MAAM,kBAAkB,OAAO,MAAM;AAAA,EACvC,EAAE,MAAM,qBAAqB,OAAO,MAAM;AAAA,EAC1C,EAAE,MAAM,gBAAgB,OAAO,MAAM;AAAA,EACrC,EAAE,MAAM,gBAAgB,OAAO,MAAM;AAAA,EACrC,EAAE,MAAM,kBAAkB,OAAO,MAAM;AAAA,EACvC,EAAE,MAAM,kBAAkB,OAAO,MAAM;AAAA,EACvC,EAAE,MAAM,kBAAkB,OAAO,MAAM;AAAA,EACvC,EAAE,MAAM,eAAe,OAAO,MAAM;AAAA,EACpC,EAAE,MAAM,kBAAkB,OAAO,MAAM;AAAA,EACvC,EAAE,MAAM,WAAW,OAAO,MAAM;AAAA,EAChC,EAAE,MAAM,oBAAoB,OAAO,MAAM;AAAA,EACzC,EAAE,MAAM,WAAW,OAAO,MAAM;AAAA,EAChC,EAAE,MAAM,QAAQ,OAAO,MAAM;AAAA,EAC7B,EAAE,MAAM,UAAU,OAAO,MAAM;AAAA,EAC/B,EAAE,MAAM,UAAU,OAAO,MAAM;AAAA,EAC/B,EAAE,MAAM,sBAAsB,OAAO,MAAM;AAAA,EAC3C,EAAE,MAAM,gBAAgB,OAAO,MAAM;AAAA,EACrC,EAAE,MAAM,mBAAmB,OAAO,MAAM;AAAA,EACxC,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,EAC5B,EAAE,MAAM,mBAAmB,OAAO,MAAM;AAAA,EACxC,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,EAC5B,EAAE,MAAM,SAAS,OAAO,MAAM;AAAA,EAC9B,EAAE,MAAM,WAAW,OAAO,MAAM;AAAA,EAChC,EAAE,MAAM,kBAAkB,OAAO,MAAM;AAAA,EACvC,EAAE,MAAM,mBAAmB,OAAO,MAAM;AAAA,EACxC,EAAE,MAAM,mBAAmB,OAAO,MAAM;AAAA,EACxC,EAAE,MAAM,mBAAmB,OAAO,MAAM;AAAA,EACxC,EAAE,MAAM,QAAQ,OAAO,MAAM;AAAA,EAC7B,EAAE,MAAM,UAAU,OAAO,MAAM;AAAA,EAC/B,EAAE,MAAM,WAAW,OAAO,MAAM;AAAA,EAChC,EAAE,MAAM,mBAAmB,OAAO,MAAM;AAAA,EACxC,EAAE,MAAM,WAAW,OAAO,MAAM;AAAA,EAChC,EAAE,MAAM,gBAAgB,OAAO,MAAM;AAAA,EACrC,EAAE,MAAM,qBAAqB,OAAO,MAAM;AAAA,EAC1C,EAAE,MAAM,eAAe,OAAO,MAAM;AAAA,EACpC,EAAE,MAAM,mBAAmB,OAAO,MAAM;AAAA,EACxC,EAAE,MAAM,kBAAkB,OAAO,MAAM;AAAA,EACvC,EAAE,MAAM,sBAAsB,OAAO,MAAM;AAAA,EAC3C,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,EAC5B,EAAE,MAAM,QAAQ,OAAO,MAAM;AAAA,EAC7B,EAAE,MAAM,mBAAmB,OAAO,MAAM;AAAA,EACxC,EAAE,MAAM,kBAAkB,OAAO,MAAM;AAAA,EACvC,EAAE,MAAM,SAAS,OAAO,MAAM;AAAA,EAC9B,EAAE,MAAM,eAAe,OAAO,MAAM;AAAA,EACpC,EAAE,MAAM,gBAAgB,OAAO,MAAM;AAAA,EACrC,EAAE,MAAM,iBAAiB,OAAO,MAAM;AAAA,EACtC,EAAE,MAAM,eAAe,OAAO,MAAM;AAAA,EACpC,EAAE,MAAM,0BAA0B,OAAO,MAAM;AAAA,EAC/C,EAAE,MAAM,oBAAoB,OAAO,MAAM;AAAA,EACzC,EAAE,MAAM,iBAAiB,OAAO,MAAM;AAAA,EACtC,EAAE,MAAM,oBAAoB,OAAO,MAAM;AAAA,EACzC,EAAE,MAAM,gBAAgB,OAAO,MAAM;AAAA,EACrC,EAAE,MAAM,QAAQ,OAAO,MAAM;AAAA,EAC7B,EAAE,MAAM,gBAAW,OAAO,MAAM;AAAA,EAChC,EAAE,MAAM,gBAAgB,OAAO,MAAM;AAAA,EACrC,EAAE,MAAM,8BAA8B,OAAO,MAAM;AAAA,EACnD,EAAE,MAAM,qBAAqB,OAAO,MAAM;AAAA,EAC1C,EAAE,MAAM,WAAW,OAAO,MAAM;AAAA,EAChC,EAAE,MAAM,QAAQ,OAAO,MAAM;AAAA,EAC7B,EAAE,MAAM,QAAQ,OAAO,MAAM;AAAA,EAC7B,EAAE,MAAM,QAAQ,OAAO,MAAM;AAAA,EAC7B,EAAE,MAAM,yBAAyB,OAAO,MAAM;AAAA,EAC9C,EAAE,MAAM,0BAA0B,OAAO,MAAM;AAAA,EAC/C,EAAE,MAAM,eAAe,OAAO,MAAM;AAAA,EACpC,EAAE,MAAM,QAAQ,OAAO,MAAM;AAC9B;", "names": []}