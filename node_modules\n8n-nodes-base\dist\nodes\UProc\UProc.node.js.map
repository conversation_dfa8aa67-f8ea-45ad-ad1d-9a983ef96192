{"version": 3, "sources": ["../../../nodes/UProc/UProc.node.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport type {\n\tIExecuteFunctions,\n\tIDataObject,\n\tINodeExecutionData,\n\tINodeType,\n\tINodeTypeDescription,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes } from 'n8n-workflow';\n\nimport { uprocApiRequest } from './GenericFunctions';\nimport { groupOptions } from './GroupDescription';\nimport { toolOperations, toolParameters } from './ToolDescription';\n\nexport class UProc implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'uProc',\n\t\tname: 'uproc',\n\t\t// eslint-disable-next-line n8n-nodes-base/node-class-description-icon-not-svg\n\t\ticon: 'file:uproc.png',\n\t\tgroup: ['output'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"tool\"]}}',\n\t\tdescription: 'Consume uProc API',\n\t\tdefaults: {\n\t\t\tname: 'uProc',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'uprocApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t...groupOptions,\n\t\t\t...toolOperations,\n\t\t\t...toolParameters,\n\t\t\t{\n\t\t\t\tdisplayName: 'Additional Options',\n\t\t\t\tname: 'additionalOptions',\n\t\t\t\ttype: 'collection',\n\t\t\t\tplaceholder: 'Add option',\n\t\t\t\tdefault: {},\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tgroup: [\n\t\t\t\t\t\t\t'audio',\n\t\t\t\t\t\t\t'communication',\n\t\t\t\t\t\t\t'company',\n\t\t\t\t\t\t\t'finance',\n\t\t\t\t\t\t\t'geographic',\n\t\t\t\t\t\t\t'image',\n\t\t\t\t\t\t\t'internet',\n\t\t\t\t\t\t\t'personal',\n\t\t\t\t\t\t\t'product',\n\t\t\t\t\t\t\t'security',\n\t\t\t\t\t\t\t'text',\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Data Webhook',\n\t\t\t\t\t\tname: 'dataWebhook',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdescription: 'URL to send tool response when tool has resolved your request',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: IDataObject[] = [];\n\t\tconst length = items.length;\n\t\tlet responseData;\n\t\tconst group = this.getNodeParameter('group', 0) as string;\n\t\tconst tool = this.getNodeParameter('tool', 0) as string;\n\t\tconst additionalOptions = this.getNodeParameter('additionalOptions', 0) as IDataObject;\n\n\t\tconst dataWebhook = additionalOptions.dataWebhook as string;\n\n\t\tinterface LooseObject {\n\t\t\t[key: string]: any;\n\t\t}\n\n\t\tconst fields = toolParameters\n\t\t\t.filter((field) => {\n\t\t\t\treturn (\n\t\t\t\t\tfield?.displayOptions?.show?.group &&\n\t\t\t\t\tfield.displayOptions.show.tool &&\n\t\t\t\t\tfield.displayOptions.show.group.indexOf(group) !== -1 &&\n\t\t\t\t\tfield.displayOptions.show.tool.indexOf(tool) !== -1\n\t\t\t\t);\n\t\t\t})\n\t\t\t.map((field) => {\n\t\t\t\treturn field.name;\n\t\t\t});\n\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tconst toolKey = tool.replace(/([A-Z]+)/g, '-$1').toLowerCase();\n\t\t\t\tconst body: LooseObject = {\n\t\t\t\t\tprocessor: toolKey,\n\t\t\t\t\tparams: {},\n\t\t\t\t};\n\n\t\t\t\tfields.forEach((field) => {\n\t\t\t\t\tif (field?.length) {\n\t\t\t\t\t\tconst data = this.getNodeParameter(field, i) as string;\n\t\t\t\t\t\tbody.params[field] = data + '';\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tif (dataWebhook?.length) {\n\t\t\t\t\tbody.callback = {};\n\t\t\t\t}\n\n\t\t\t\tif (dataWebhook?.length) {\n\t\t\t\t\tbody.callback.data = dataWebhook;\n\t\t\t\t}\n\n\t\t\t\t//Change to multiple requests\n\t\t\t\tresponseData = await uprocApiRequest.call(this, 'POST', body);\n\n\t\t\t\tif (Array.isArray(responseData)) {\n\t\t\t\t\treturnData.push.apply(returnData, responseData as IDataObject[]);\n\t\t\t\t} else {\n\t\t\t\t\treturnData.push(responseData as IDataObject);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ error: error.message });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\t\treturn [this.helpers.returnJsonArray(returnData)];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,0BAAoC;AAEpC,8BAAgC;AAChC,8BAA6B;AAC7B,6BAA+C;AAExC,MAAM,MAA2B;AAAA,EAAjC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA;AAAA,MAEN,MAAM;AAAA,MACN,OAAO,CAAC,QAAQ;AAAA,MAChB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,SAAS,CAAC;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,OAAO;AAAA,gBACN;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,UACA,SAAS;AAAA,YACR;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,aAAa;AAAA,cACb,SAAS;AAAA,YACV;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAA4B,CAAC;AACnC,UAAM,SAAS,MAAM;AACrB,QAAI;AACJ,UAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,UAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC5C,UAAM,oBAAoB,KAAK,iBAAiB,qBAAqB,CAAC;AAEtE,UAAM,cAAc,kBAAkB;AAMtC,UAAM,SAAS,sCACb,OAAO,CAAC,UAAU;AAClB,aACC,OAAO,gBAAgB,MAAM,SAC7B,MAAM,eAAe,KAAK,QAC1B,MAAM,eAAe,KAAK,MAAM,QAAQ,KAAK,MAAM,MACnD,MAAM,eAAe,KAAK,KAAK,QAAQ,IAAI,MAAM;AAAA,IAEnD,CAAC,EACA,IAAI,CAAC,UAAU;AACf,aAAO,MAAM;AAAA,IACd,CAAC;AAEF,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,cAAM,UAAU,KAAK,QAAQ,aAAa,KAAK,EAAE,YAAY;AAC7D,cAAM,OAAoB;AAAA,UACzB,WAAW;AAAA,UACX,QAAQ,CAAC;AAAA,QACV;AAEA,eAAO,QAAQ,CAAC,UAAU;AACzB,cAAI,OAAO,QAAQ;AAClB,kBAAM,OAAO,KAAK,iBAAiB,OAAO,CAAC;AAC3C,iBAAK,OAAO,KAAK,IAAI,OAAO;AAAA,UAC7B;AAAA,QACD,CAAC;AAED,YAAI,aAAa,QAAQ;AACxB,eAAK,WAAW,CAAC;AAAA,QAClB;AAEA,YAAI,aAAa,QAAQ;AACxB,eAAK,SAAS,OAAO;AAAA,QACtB;AAGA,uBAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,IAAI;AAE5D,YAAI,MAAM,QAAQ,YAAY,GAAG;AAChC,qBAAW,KAAK,MAAM,YAAY,YAA6B;AAAA,QAChE,OAAO;AACN,qBAAW,KAAK,YAA2B;AAAA,QAC5C;AAAA,MACD,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,OAAO,MAAM,QAAQ,CAAC;AACxC;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AACA,WAAO,CAAC,KAAK,QAAQ,gBAAgB,UAAU,CAAC;AAAA,EACjD;AACD;", "names": []}