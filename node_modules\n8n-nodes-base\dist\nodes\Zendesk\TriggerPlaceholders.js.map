{"version": 3, "sources": ["../../../nodes/Zendesk/TriggerPlaceholders.ts"], "sourcesContent": ["import type { INodePropertyOptions } from 'n8n-workflow';\n\nexport const triggerPlaceholders = [\n\t{\n\t\tname: 'Title',\n\t\tvalue: 'ticket.title',\n\t\tdescription: \"Ticket's subject\",\n\t},\n\t{\n\t\tname: 'Description',\n\t\tvalue: 'ticket.description',\n\t\tdescription: \"Ticket's description\",\n\t},\n\t{\n\t\tname: 'URL',\n\t\tvalue: 'ticket.url',\n\t\tdescription: \"Ticket's URL\",\n\t},\n\t{\n\t\tname: 'ID',\n\t\tvalue: 'ticket.id',\n\t\tdescription: \"Ticket's ID\",\n\t},\n\t{\n\t\tname: 'External ID',\n\t\tvalue: 'ticket.external_id',\n\t\tdescription: \"Ticket's external ID\",\n\t},\n\t{\n\t\tname: 'Via',\n\t\tvalue: 'ticket.via',\n\t\tdescription: \"Ticket's source\",\n\t},\n\t{\n\t\tname: 'Status',\n\t\tvalue: 'ticket.status',\n\t\tdescription: \"Ticket's status\",\n\t},\n\t{\n\t\tname: 'Priority',\n\t\tvalue: 'ticket.priority',\n\t\tdescription: \"Ticket's priority\",\n\t},\n\t{\n\t\tname: 'Type',\n\t\tvalue: 'ticket.ticket_type',\n\t\tdescription: \"Ticket's type\",\n\t},\n\t{\n\t\tname: 'Group Name',\n\t\tvalue: 'ticket.group.name',\n\t\tdescription: \"Ticket's assigned group\",\n\t},\n\t{\n\t\tname: 'Brand Name',\n\t\tvalue: 'ticket.brand.name',\n\t\tdescription: \"Ticket's brand\",\n\t},\n\t{\n\t\tname: 'Due Date',\n\t\tvalue: 'ticket.due_date',\n\t\tdescription: \"Ticket's due date (relevant for tickets of type Task)\",\n\t},\n\t{\n\t\tname: 'Account',\n\t\tvalue: 'ticket.account',\n\t\tdescription: \"This Zendesk Support's account name\",\n\t},\n\t{\n\t\tname: 'Assignee Email',\n\t\tvalue: 'ticket.assignee.email',\n\t\tdescription: 'Ticket assignee email (if any)',\n\t},\n\t{\n\t\tname: 'Assignee Name',\n\t\tvalue: 'ticket.assignee.name',\n\t\tdescription: \"Assignee's full name\",\n\t},\n\t{\n\t\tname: 'Assignee First Name',\n\t\tvalue: 'ticket.assignee.first_name',\n\t\tdescription: \"Assignee's first name\",\n\t},\n\t{\n\t\tname: 'Assignee Last Name',\n\t\tvalue: 'ticket.assignee.last_name',\n\t\tdescription: \"Assignee's last name\",\n\t},\n\t{\n\t\tname: 'Requester Full Name',\n\t\tvalue: 'ticket.requester.name',\n\t\tdescription: \"Requester's full name\",\n\t},\n\t{\n\t\tname: 'Requester First Name',\n\t\tvalue: 'ticket.requester.first_name',\n\t\tdescription: \"Requester's first name\",\n\t},\n\t{\n\t\tname: 'Requester Last Name',\n\t\tvalue: 'ticket.requester.last_name',\n\t\tdescription: \"Requester's last name\",\n\t},\n\t{\n\t\tname: 'Requester Email',\n\t\tvalue: 'ticket.requester.email',\n\t\tdescription: \"Requester's email\",\n\t},\n\t{\n\t\tname: 'Requester Language',\n\t\tvalue: 'ticket.requester.language',\n\t\tdescription: \"Requester's language\",\n\t},\n\t{\n\t\tname: 'Requester Phone',\n\t\tvalue: 'ticket.requester.phone',\n\t\tdescription: \"Requester's phone number\",\n\t},\n\t{\n\t\tname: 'Requester External ID',\n\t\tvalue: 'ticket.requester.external_id',\n\t\tdescription: \"Requester's external ID\",\n\t},\n\t{\n\t\tname: 'Requester Field',\n\t\tvalue: 'ticket.requester.requester_field',\n\t\tdescription: 'Name or email',\n\t},\n\t{\n\t\tname: 'Requester Details',\n\t\tvalue: 'ticket.requester.details',\n\t\tdescription: \"Detailed information about the ticket's requester\",\n\t},\n\t{\n\t\tname: 'Requester Organization',\n\t\tvalue: 'ticket.organization.name',\n\t\tdescription: \"Requester's organization\",\n\t},\n\t{\n\t\tname: \"Ticket's Organization External ID\",\n\t\tvalue: 'ticket.organization.external_id',\n\t},\n\t{\n\t\tname: 'Organization Details',\n\t\tvalue: 'ticket.organization.details',\n\t\tdescription: \"The details about the organization of the ticket's requester\",\n\t},\n\t{\n\t\tname: 'Organization Note',\n\t\tvalue: 'ticket.organization.notes',\n\t\tdescription: \"The notes about the organization of the ticket's requester\",\n\t},\n\t{\n\t\tname: \"Ticket's CCs\",\n\t\tvalue: 'ticket.ccs',\n\t},\n\t{\n\t\tname: \"Ticket's CCs Names\",\n\t\tvalue: 'ticket.cc_names',\n\t},\n\t{\n\t\tname: \"Ticket's Tags\",\n\t\tvalue: 'ticket.tags',\n\t},\n\t{\n\t\tname: 'Current Holiday Name',\n\t\tvalue: 'ticket.current_holiday_name',\n\t\tdescription: \"Displays the name of the current holiday on the ticket's schedule\",\n\t},\n\t{\n\t\tname: 'Current User Name',\n\t\tvalue: 'current_user.name',\n\t\tdescription: 'Your full name',\n\t},\n\t{\n\t\tname: 'Current User First Name',\n\t\tvalue: 'current_user.first_name',\n\t\tdescription: 'Your first name',\n\t},\n\t{\n\t\tname: 'Current User Email',\n\t\tvalue: 'current_user.email',\n\t\tdescription: 'Your primary email',\n\t},\n\t{\n\t\tname: 'Current User Organization Name',\n\t\tvalue: 'current_user.organization.name',\n\t\tdescription: 'Your default organization',\n\t},\n\t{\n\t\tname: 'Current User Organization Details',\n\t\tvalue: 'current_user.organization.details',\n\t\tdescription: \"Your default organization's details\",\n\t},\n\t{\n\t\tname: 'Current User Organization Notes',\n\t\tvalue: 'current_user.organization.notes',\n\t\tdescription: \"Your default organization's note\",\n\t},\n\t{\n\t\tname: 'Current User Language',\n\t\tvalue: 'current_user.language',\n\t\tdescription: 'Your chosen language',\n\t},\n\t{\n\t\tname: 'Current User External ID',\n\t\tvalue: 'current_user.external_id',\n\t\tdescription: 'Your external ID',\n\t},\n\t{\n\t\tname: 'Current User Notes',\n\t\tvalue: 'current_user.notes',\n\t\tdescription: 'Your notes, stored in your profile',\n\t},\n\t{\n\t\tname: 'Satisfaction Current Rating',\n\t\tvalue: 'satisfaction.current_rating',\n\t\tdescription: 'The text of the current satisfaction rating',\n\t},\n\t{\n\t\tname: 'Satisfaction Current Comment',\n\t\tvalue: 'satisfaction.current_comment',\n\t\tdescription: 'The text of the current satisfaction rating comment',\n\t},\n] as INodePropertyOptions[];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,sBAAsB;AAAA,EAClC;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,EACR;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,EACR;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,EACR;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,EACR;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,EACd;AACD;", "names": []}