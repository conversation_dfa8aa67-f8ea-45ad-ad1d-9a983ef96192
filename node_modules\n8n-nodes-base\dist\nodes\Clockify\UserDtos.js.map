{"version": 3, "sources": ["../../../nodes/Clockify/UserDtos.ts"], "sourcesContent": ["import type { IDataObject } from 'n8n-workflow';\n\nimport type { IMembershipDto } from './CommonDtos';\n\nconst UserStatuses = {\n\tACTIVE: 0,\n\tPENDING_EMAIL_VERIFICATION: 1,\n\tDELETED: 2,\n};\n\nexport type UserStatusEnum = (typeof UserStatuses)[keyof typeof UserStatuses];\n\nexport interface IUserDto {\n\tactiveWorkspace: string;\n\tdefaultWorkspace: string;\n\temail: string;\n\tid: string;\n\tmemberships: IMembershipDto[];\n\tname: string;\n\tprofilePicture: string;\n\tsettings: IDataObject;\n\tstatus: UserStatusEnum;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAIA,MAAM,eAAe;AAAA,EACpB,QAAQ;AAAA,EACR,4BAA4B;AAAA,EAC5B,SAAS;AACV;", "names": []}