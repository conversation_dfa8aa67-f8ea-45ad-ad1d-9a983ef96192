{"version": 3, "sources": ["../../../nodes/Zendesk/ZendeskTrigger.node.ts"], "sourcesContent": ["import type {\n\tIHookFunctions,\n\tIWebhookFunctions,\n\tIDataObject,\n\tILoadOptionsFunctions,\n\tINodePropertyOptions,\n\tINodeType,\n\tINodeTypeDescription,\n\tIWebhookResponseData,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes, NodeOperationError } from 'n8n-workflow';\n\nimport { conditionFields } from './ConditionDescription';\nimport { zendeskApiRequest, zendeskApiRequestAllItems } from './GenericFunctions';\nimport { triggerPlaceholders } from './TriggerPlaceholders';\n\nexport class ZendeskTrigger implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Zendesk Trigger',\n\t\tname: 'zendeskTrigger',\n\t\ticon: 'file:zendesk.svg',\n\t\tgroup: ['trigger'],\n\t\tversion: 1,\n\t\tdescription: 'Handle Zendesk events via webhooks',\n\t\tdefaults: {\n\t\t\tname: 'Zendesk Trigger',\n\t\t},\n\t\tinputs: [],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'zendesk<PERSON><PERSON>',\n\t\t\t\trequired: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tauthentication: ['apiToken'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'zendeskOAuth2Api',\n\t\t\t\trequired: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tauthentication: ['oAuth2'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t],\n\t\twebhooks: [\n\t\t\t{\n\t\t\t\tname: 'default',\n\t\t\t\thttpMethod: 'POST',\n\t\t\t\tresponseMode: 'onReceived',\n\t\t\t\tpath: 'webhook',\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Authentication',\n\t\t\t\tname: 'authentication',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'API Token',\n\t\t\t\t\t\tvalue: 'apiToken',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'OAuth2',\n\t\t\t\t\t\tvalue: 'oAuth2',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'apiToken',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Service',\n\t\t\t\tname: 'service',\n\t\t\t\ttype: 'options',\n\t\t\t\trequired: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Support',\n\t\t\t\t\t\tvalue: 'support',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'support',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Options',\n\t\t\t\tname: 'options',\n\t\t\t\ttype: 'collection',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tservice: ['support'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Field Names or IDs',\n\t\t\t\t\t\tname: 'fields',\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'The fields to return the values of. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t\t\ttype: 'multiOptions',\n\t\t\t\t\t\tdefault: [],\n\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\tloadOptionsMethod: 'getFields',\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tplaceholder: 'Add option',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Conditions',\n\t\t\t\tname: 'conditions',\n\t\t\t\tplaceholder: 'Add Condition',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tservice: ['support'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdescription: 'The condition to set',\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'all',\n\t\t\t\t\t\tdisplayName: 'All',\n\t\t\t\t\t\tvalues: [...conditionFields],\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'any',\n\t\t\t\t\t\tdisplayName: 'Any',\n\t\t\t\t\t\tvalues: [...conditionFields],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\t// Get all the fields to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = triggerPlaceholders;\n\t\t\t\tconst customFields = [\n\t\t\t\t\t'text',\n\t\t\t\t\t'textarea',\n\t\t\t\t\t'date',\n\t\t\t\t\t'integer',\n\t\t\t\t\t'decimal',\n\t\t\t\t\t'regexp',\n\t\t\t\t\t'multiselect',\n\t\t\t\t\t'tagger',\n\t\t\t\t];\n\t\t\t\tconst fields = await zendeskApiRequestAllItems.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'ticket_fields',\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/ticket_fields',\n\t\t\t\t);\n\t\t\t\tfor (const field of fields) {\n\t\t\t\t\tif (customFields.includes(field.type as string) && field.removable && field.active) {\n\t\t\t\t\t\tconst fieldName = field.title;\n\t\t\t\t\t\tconst fieldId = field.id;\n\t\t\t\t\t\treturnData.push({\n\t\t\t\t\t\t\tname: fieldName,\n\t\t\t\t\t\t\tvalue: `ticket.ticket_field_${fieldId}`,\n\t\t\t\t\t\t\tdescription: `Custom field ${fieldName}`,\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the groups to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getGroups(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst groups = await zendeskApiRequestAllItems.call(this, 'groups', 'GET', '/groups');\n\t\t\t\tfor (const group of groups) {\n\t\t\t\t\tconst groupName = group.name;\n\t\t\t\t\tconst groupId = group.id;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: groupName,\n\t\t\t\t\t\tvalue: groupId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the users to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getUsers(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst users = await zendeskApiRequestAllItems.call(this, 'users', 'GET', '/users');\n\t\t\t\tfor (const user of users) {\n\t\t\t\t\tconst userName = user.name;\n\t\t\t\t\tconst userId = user.id;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: userName,\n\t\t\t\t\t\tvalue: userId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturnData.push({\n\t\t\t\t\tname: 'Current User',\n\t\t\t\t\tvalue: 'current_user',\n\t\t\t\t});\n\t\t\t\treturnData.push({\n\t\t\t\t\tname: 'Requester',\n\t\t\t\t\tvalue: 'requester_id',\n\t\t\t\t});\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t},\n\t};\n\n\twebhookMethods = {\n\t\tdefault: {\n\t\t\tasync checkExists(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default') as string;\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\tconst conditions = this.getNodeParameter('conditions') as IDataObject;\n\n\t\t\t\tlet endpoint = '';\n\t\t\t\tconst resultAll = [],\n\t\t\t\t\tresultAny = [];\n\n\t\t\t\tconst conditionsAll = conditions.all as [IDataObject];\n\t\t\t\tif (conditionsAll) {\n\t\t\t\t\tfor (const conditionAll of conditionsAll) {\n\t\t\t\t\t\tconst aux: IDataObject = {};\n\t\t\t\t\t\taux.field = conditionAll.field;\n\t\t\t\t\t\taux.operator = conditionAll.operation;\n\t\t\t\t\t\tif (conditionAll.operation !== 'changed' && conditionAll.operation !== 'not_changed') {\n\t\t\t\t\t\t\taux.value = conditionAll.value;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\taux.value = null;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresultAll.push(aux);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tconst conditionsAny = conditions.any as [IDataObject];\n\t\t\t\tif (conditionsAny) {\n\t\t\t\t\tfor (const conditionAny of conditionsAny) {\n\t\t\t\t\t\tconst aux: IDataObject = {};\n\t\t\t\t\t\taux.field = conditionAny.field;\n\t\t\t\t\t\taux.operator = conditionAny.operation;\n\t\t\t\t\t\tif (conditionAny.operation !== 'changed' && conditionAny.operation !== 'not_changed') {\n\t\t\t\t\t\t\taux.value = conditionAny.value;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\taux.value = null;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresultAny.push(aux);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// get all webhooks\n\t\t\t\t// https://developer.zendesk.com/api-reference/event-connectors/webhooks/webhooks/#list-webhooks\n\t\t\t\tconst { webhooks } = await zendeskApiRequest.call(this, 'GET', '/webhooks');\n\t\t\t\tfor (const webhook of webhooks) {\n\t\t\t\t\tif (webhook.endpoint === webhookUrl) {\n\t\t\t\t\t\twebhookData.targetId = webhook.id;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// no target was found\n\t\t\t\tif (webhookData.targetId === undefined) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\tendpoint = '/triggers/active';\n\t\t\t\tconst triggers = await zendeskApiRequestAllItems.call(this, 'triggers', 'GET', endpoint);\n\n\t\t\t\tfor (const trigger of triggers) {\n\t\t\t\t\tconst toDeleteTriggers = [];\n\t\t\t\t\t// this trigger belong to the current target\n\t\t\t\t\tif (trigger.actions[0].value[0].toString() === webhookData.targetId?.toString()) {\n\t\t\t\t\t\ttoDeleteTriggers.push(trigger.id);\n\t\t\t\t\t}\n\t\t\t\t\t// delete all trigger attach to this target;\n\t\t\t\t\tif (toDeleteTriggers.length !== 0) {\n\t\t\t\t\t\tawait zendeskApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'DELETE',\n\t\t\t\t\t\t\t'/triggers/destroy_many',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t{ ids: toDeleteTriggers.join(',') },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tasync create(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default') as string;\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\tconst service = this.getNodeParameter('service') as string;\n\n\t\t\t\tif (service === 'support') {\n\t\t\t\t\tconst message: IDataObject = {};\n\t\t\t\t\tconst resultAll = [],\n\t\t\t\t\t\tresultAny = [];\n\t\t\t\t\tconst conditions = this.getNodeParameter('conditions') as IDataObject;\n\t\t\t\t\tconst options = this.getNodeParameter('options') as IDataObject;\n\n\t\t\t\t\tif (Object.keys(conditions).length === 0) {\n\t\t\t\t\t\tthrow new NodeOperationError(this.getNode(), 'You must have at least one condition');\n\t\t\t\t\t}\n\n\t\t\t\t\tif (options.fields) {\n\t\t\t\t\t\tfor (const field of options.fields as string[]) {\n\t\t\t\t\t\t\tmessage[field] = `{{${field}}}`;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmessage['ticket.id'] = '{{ticket.id}}';\n\t\t\t\t\t}\n\n\t\t\t\t\tconst conditionsAll = conditions.all as [IDataObject];\n\t\t\t\t\tif (conditionsAll) {\n\t\t\t\t\t\tfor (const conditionAll of conditionsAll) {\n\t\t\t\t\t\t\tconst aux: IDataObject = {};\n\t\t\t\t\t\t\taux.field = conditionAll.field;\n\t\t\t\t\t\t\taux.operator = conditionAll.operation;\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\tconditionAll.operation !== 'changed' &&\n\t\t\t\t\t\t\t\tconditionAll.operation !== 'not_changed'\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\taux.value = conditionAll.value;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\taux.value = null;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tresultAll.push(aux);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tconst conditionsAny = conditions.any as [IDataObject];\n\t\t\t\t\tif (conditionsAny) {\n\t\t\t\t\t\tfor (const conditionAny of conditionsAny) {\n\t\t\t\t\t\t\tconst aux: IDataObject = {};\n\t\t\t\t\t\t\taux.field = conditionAny.field;\n\t\t\t\t\t\t\taux.operator = conditionAny.operation;\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\tconditionAny.operation !== 'changed' &&\n\t\t\t\t\t\t\t\tconditionAny.operation !== 'not_changed'\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\taux.value = conditionAny.value;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\taux.value = null;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tresultAny.push(aux);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tconst urlParts = new URL(webhookUrl);\n\n\t\t\t\t\tconst bodyTrigger: IDataObject = {\n\t\t\t\t\t\ttrigger: {\n\t\t\t\t\t\t\ttitle: `n8n-webhook:${urlParts.pathname}`,\n\t\t\t\t\t\t\tconditions: {\n\t\t\t\t\t\t\t\tall: resultAll,\n\t\t\t\t\t\t\t\tany: resultAny,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tactions: [\n\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\tfield: 'notification_webhook',\n\t\t\t\t\t\t\t\t\tvalue: [],\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t],\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\n\t\t\t\t\tconst bodyTarget: IDataObject = {\n\t\t\t\t\t\twebhook: {\n\t\t\t\t\t\t\tname: 'n8n webhook',\n\t\t\t\t\t\t\tendpoint: webhookUrl,\n\t\t\t\t\t\t\thttp_method: 'POST',\n\t\t\t\t\t\t\tstatus: 'active',\n\t\t\t\t\t\t\trequest_format: 'json',\n\t\t\t\t\t\t\tsubscriptions: ['conditional_ticket_events'],\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\t\t\t\t\tlet target: IDataObject = {};\n\n\t\t\t\t\t// if target id exists but trigger does not then reuse the target\n\t\t\t\t\t// and create the trigger else create both\n\t\t\t\t\tif (webhookData.targetId !== undefined) {\n\t\t\t\t\t\ttarget.id = webhookData.targetId;\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// create a webhook\n\t\t\t\t\t\t// https://developer.zendesk.com/api-reference/event-connectors/webhooks/webhooks/#create-or-clone-webhook\n\t\t\t\t\t\ttarget = (await zendeskApiRequest.call(this, 'POST', '/webhooks', bodyTarget))\n\t\t\t\t\t\t\t.webhook as IDataObject;\n\t\t\t\t\t}\n\n\t\t\t\t\t((bodyTrigger.trigger as IDataObject).actions as IDataObject[])[0].value = [\n\t\t\t\t\t\ttarget.id,\n\t\t\t\t\t\tJSON.stringify(message),\n\t\t\t\t\t];\n\n\t\t\t\t\tconst { trigger } = await zendeskApiRequest.call(this, 'POST', '/triggers', bodyTrigger);\n\t\t\t\t\twebhookData.webhookId = trigger.id;\n\t\t\t\t\twebhookData.targetId = target.id;\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t},\n\t\t\tasync delete(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\ttry {\n\t\t\t\t\tawait zendeskApiRequest.call(this, 'DELETE', `/triggers/${webhookData.webhookId}`);\n\t\t\t\t\tawait zendeskApiRequest.call(this, 'DELETE', `/webhooks/${webhookData.targetId}`);\n\t\t\t\t} catch (error) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tdelete webhookData.triggerId;\n\t\t\t\tdelete webhookData.targetId;\n\t\t\t\treturn true;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync webhook(this: IWebhookFunctions): Promise<IWebhookResponseData> {\n\t\tconst req = this.getRequestObject();\n\t\treturn {\n\t\t\tworkflowData: [this.helpers.returnJsonArray(req.body as IDataObject)],\n\t\t};\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,0BAAwD;AAExD,kCAAgC;AAChC,8BAA6D;AAC7D,iCAAoC;AAE7B,MAAM,eAAoC;AAAA,EAA1C;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,SAAS;AAAA,MACjB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,gBAAgB,CAAC,UAAU;AAAA,YAC5B;AAAA,UACD;AAAA,QACD;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,gBAAgB,CAAC,QAAQ;AAAA,YAC1B;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA,UAAU;AAAA,QACT;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,MAAM;AAAA,QACP;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,SAAS,CAAC,SAAS;AAAA,YACpB;AAAA,UACD;AAAA,UACA,SAAS,CAAC;AAAA,UACV,SAAS;AAAA,YACR;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,aACC;AAAA,cACD,MAAM;AAAA,cACN,SAAS,CAAC;AAAA,cACV,aAAa;AAAA,gBACZ,mBAAmB;AAAA,cACpB;AAAA,YACD;AAAA,UACD;AAAA,UACA,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,aAAa;AAAA,YACZ,gBAAgB;AAAA,UACjB;AAAA,UACA,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,SAAS,CAAC,SAAS;AAAA,YACpB;AAAA,UACD;AAAA,UACA,aAAa;AAAA,UACb,SAAS,CAAC;AAAA,UACV,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,aAAa;AAAA,cACb,QAAQ,CAAC,GAAG,2CAAe;AAAA,YAC5B;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,aAAa;AAAA,cACb,QAAQ,CAAC,GAAG,2CAAe;AAAA,YAC5B;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA;AAAA;AAAA,QAGZ,MAAM,YAAwE;AAC7E,gBAAM,aAAqC;AAC3C,gBAAM,eAAe;AAAA,YACpB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AACA,gBAAM,SAAS,MAAM,kDAA0B;AAAA,YAC9C;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AACA,qBAAW,SAAS,QAAQ;AAC3B,gBAAI,aAAa,SAAS,MAAM,IAAc,KAAK,MAAM,aAAa,MAAM,QAAQ;AACnF,oBAAM,YAAY,MAAM;AACxB,oBAAM,UAAU,MAAM;AACtB,yBAAW,KAAK;AAAA,gBACf,MAAM;AAAA,gBACN,OAAO,uBAAuB,OAAO;AAAA,gBACrC,aAAa,gBAAgB,SAAS;AAAA,cACvC,CAAC;AAAA,YACF;AAAA,UACD;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,YAAwE;AAC7E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,SAAS,MAAM,kDAA0B,KAAK,MAAM,UAAU,OAAO,SAAS;AACpF,qBAAW,SAAS,QAAQ;AAC3B,kBAAM,YAAY,MAAM;AACxB,kBAAM,UAAU,MAAM;AACtB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,WAAuE;AAC5E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,QAAQ,MAAM,kDAA0B,KAAK,MAAM,SAAS,OAAO,QAAQ;AACjF,qBAAW,QAAQ,OAAO;AACzB,kBAAM,WAAW,KAAK;AACtB,kBAAM,SAAS,KAAK;AACpB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,qBAAW,KAAK;AAAA,YACf,MAAM;AAAA,YACN,OAAO;AAAA,UACR,CAAC;AACD,qBAAW,KAAK;AAAA,YACf,MAAM;AAAA,YACN,OAAO;AAAA,UACR,CAAC;AACD,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAEA,0BAAiB;AAAA,MAChB,SAAS;AAAA,QACR,MAAM,cAAoD;AACzD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AACnD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,gBAAM,aAAa,KAAK,iBAAiB,YAAY;AAErD,cAAI,WAAW;AACf,gBAAM,YAAY,CAAC,GAClB,YAAY,CAAC;AAEd,gBAAM,gBAAgB,WAAW;AACjC,cAAI,eAAe;AAClB,uBAAW,gBAAgB,eAAe;AACzC,oBAAM,MAAmB,CAAC;AAC1B,kBAAI,QAAQ,aAAa;AACzB,kBAAI,WAAW,aAAa;AAC5B,kBAAI,aAAa,cAAc,aAAa,aAAa,cAAc,eAAe;AACrF,oBAAI,QAAQ,aAAa;AAAA,cAC1B,OAAO;AACN,oBAAI,QAAQ;AAAA,cACb;AACA,wBAAU,KAAK,GAAG;AAAA,YACnB;AAAA,UACD;AAEA,gBAAM,gBAAgB,WAAW;AACjC,cAAI,eAAe;AAClB,uBAAW,gBAAgB,eAAe;AACzC,oBAAM,MAAmB,CAAC;AAC1B,kBAAI,QAAQ,aAAa;AACzB,kBAAI,WAAW,aAAa;AAC5B,kBAAI,aAAa,cAAc,aAAa,aAAa,cAAc,eAAe;AACrF,oBAAI,QAAQ,aAAa;AAAA,cAC1B,OAAO;AACN,oBAAI,QAAQ;AAAA,cACb;AACA,wBAAU,KAAK,GAAG;AAAA,YACnB;AAAA,UACD;AAIA,gBAAM,EAAE,SAAS,IAAI,MAAM,0CAAkB,KAAK,MAAM,OAAO,WAAW;AAC1E,qBAAW,WAAW,UAAU;AAC/B,gBAAI,QAAQ,aAAa,YAAY;AACpC,0BAAY,WAAW,QAAQ;AAC/B;AAAA,YACD;AAAA,UACD;AAGA,cAAI,YAAY,aAAa,QAAW;AACvC,mBAAO;AAAA,UACR;AAEA,qBAAW;AACX,gBAAM,WAAW,MAAM,kDAA0B,KAAK,MAAM,YAAY,OAAO,QAAQ;AAEvF,qBAAW,WAAW,UAAU;AAC/B,kBAAM,mBAAmB,CAAC;AAE1B,gBAAI,QAAQ,QAAQ,CAAC,EAAE,MAAM,CAAC,EAAE,SAAS,MAAM,YAAY,UAAU,SAAS,GAAG;AAChF,+BAAiB,KAAK,QAAQ,EAAE;AAAA,YACjC;AAEA,gBAAI,iBAAiB,WAAW,GAAG;AAClC,oBAAM,0CAAkB;AAAA,gBACvB;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD,EAAE,KAAK,iBAAiB,KAAK,GAAG,EAAE;AAAA,cACnC;AAAA,YACD;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AACnD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,gBAAM,UAAU,KAAK,iBAAiB,SAAS;AAE/C,cAAI,YAAY,WAAW;AAC1B,kBAAM,UAAuB,CAAC;AAC9B,kBAAM,YAAY,CAAC,GAClB,YAAY,CAAC;AACd,kBAAM,aAAa,KAAK,iBAAiB,YAAY;AACrD,kBAAM,UAAU,KAAK,iBAAiB,SAAS;AAE/C,gBAAI,OAAO,KAAK,UAAU,EAAE,WAAW,GAAG;AACzC,oBAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,sCAAsC;AAAA,YACpF;AAEA,gBAAI,QAAQ,QAAQ;AACnB,yBAAW,SAAS,QAAQ,QAAoB;AAC/C,wBAAQ,KAAK,IAAI,KAAK,KAAK;AAAA,cAC5B;AAAA,YACD,OAAO;AACN,sBAAQ,WAAW,IAAI;AAAA,YACxB;AAEA,kBAAM,gBAAgB,WAAW;AACjC,gBAAI,eAAe;AAClB,yBAAW,gBAAgB,eAAe;AACzC,sBAAM,MAAmB,CAAC;AAC1B,oBAAI,QAAQ,aAAa;AACzB,oBAAI,WAAW,aAAa;AAC5B,oBACC,aAAa,cAAc,aAC3B,aAAa,cAAc,eAC1B;AACD,sBAAI,QAAQ,aAAa;AAAA,gBAC1B,OAAO;AACN,sBAAI,QAAQ;AAAA,gBACb;AACA,0BAAU,KAAK,GAAG;AAAA,cACnB;AAAA,YACD;AAEA,kBAAM,gBAAgB,WAAW;AACjC,gBAAI,eAAe;AAClB,yBAAW,gBAAgB,eAAe;AACzC,sBAAM,MAAmB,CAAC;AAC1B,oBAAI,QAAQ,aAAa;AACzB,oBAAI,WAAW,aAAa;AAC5B,oBACC,aAAa,cAAc,aAC3B,aAAa,cAAc,eAC1B;AACD,sBAAI,QAAQ,aAAa;AAAA,gBAC1B,OAAO;AACN,sBAAI,QAAQ;AAAA,gBACb;AACA,0BAAU,KAAK,GAAG;AAAA,cACnB;AAAA,YACD;AAEA,kBAAM,WAAW,IAAI,IAAI,UAAU;AAEnC,kBAAM,cAA2B;AAAA,cAChC,SAAS;AAAA,gBACR,OAAO,eAAe,SAAS,QAAQ;AAAA,gBACvC,YAAY;AAAA,kBACX,KAAK;AAAA,kBACL,KAAK;AAAA,gBACN;AAAA,gBACA,SAAS;AAAA,kBACR;AAAA,oBACC,OAAO;AAAA,oBACP,OAAO,CAAC;AAAA,kBACT;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAEA,kBAAM,aAA0B;AAAA,cAC/B,SAAS;AAAA,gBACR,MAAM;AAAA,gBACN,UAAU;AAAA,gBACV,aAAa;AAAA,gBACb,QAAQ;AAAA,gBACR,gBAAgB;AAAA,gBAChB,eAAe,CAAC,2BAA2B;AAAA,cAC5C;AAAA,YACD;AACA,gBAAI,SAAsB,CAAC;AAI3B,gBAAI,YAAY,aAAa,QAAW;AACvC,qBAAO,KAAK,YAAY;AAAA,YACzB,OAAO;AAGN,wBAAU,MAAM,0CAAkB,KAAK,MAAM,QAAQ,aAAa,UAAU,GAC1E;AAAA,YACH;AAEA,YAAE,YAAY,QAAwB,QAA0B,CAAC,EAAE,QAAQ;AAAA,cAC1E,OAAO;AAAA,cACP,KAAK,UAAU,OAAO;AAAA,YACvB;AAEA,kBAAM,EAAE,QAAQ,IAAI,MAAM,0CAAkB,KAAK,MAAM,QAAQ,aAAa,WAAW;AACvF,wBAAY,YAAY,QAAQ;AAChC,wBAAY,WAAW,OAAO;AAAA,UAC/B;AACA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,cAAI;AACH,kBAAM,0CAAkB,KAAK,MAAM,UAAU,aAAa,YAAY,SAAS,EAAE;AACjF,kBAAM,0CAAkB,KAAK,MAAM,UAAU,aAAa,YAAY,QAAQ,EAAE;AAAA,UACjF,SAAS,OAAO;AACf,mBAAO;AAAA,UACR;AACA,iBAAO,YAAY;AACnB,iBAAO,YAAY;AACnB,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAgE;AACrE,UAAM,MAAM,KAAK,iBAAiB;AAClC,WAAO;AAAA,MACN,cAAc,CAAC,KAAK,QAAQ,gBAAgB,IAAI,IAAmB,CAAC;AAAA,IACrE;AAAA,EACD;AACD;", "names": []}