{"version": 3, "sources": ["../../../nodes/Webflow/Webflow.node.ts"], "sourcesContent": ["import type { INodeTypeBaseDescription, IVersionedNodeType } from 'n8n-workflow';\nimport { VersionedNodeType } from 'n8n-workflow';\n\nimport { WebflowV1 } from './V1/WebflowV1.node';\nimport { WebflowV2 } from './V2/WebflowV2.node';\n\nexport class Webflow extends VersionedNodeType {\n\tconstructor() {\n\t\tconst baseDescription: INodeTypeBaseDescription = {\n\t\t\tdisplayName: 'Webflow',\n\t\t\tname: 'webflow',\n\t\t\ticon: 'file:webflow.svg',\n\t\t\tgroup: ['transform'],\n\t\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\t\tdescription: 'Consume the Webflow API',\n\t\t\tdefaultVersion: 2,\n\t\t};\n\n\t\tconst nodeVersions: IVersionedNodeType['nodeVersions'] = {\n\t\t\t1: new WebflowV1(baseDescription),\n\t\t\t2: new WebflowV2(baseDescription),\n\t\t};\n\n\t\tsuper(nodeVersions, baseDescription);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAAkC;AAElC,uBAA0B;AAC1B,uBAA0B;AAEnB,MAAM,gBAAgB,sCAAkB;AAAA,EAC9C,cAAc;AACb,UAAM,kBAA4C;AAAA,MACjD,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,WAAW;AAAA,MACnB,UAAU;AAAA,MACV,aAAa;AAAA,MACb,gBAAgB;AAAA,IACjB;AAEA,UAAM,eAAmD;AAAA,MACxD,GAAG,IAAI,2BAAU,eAAe;AAAA,MAChC,GAAG,IAAI,2BAAU,eAAe;AAAA,IACjC;AAEA,UAAM,cAAc,eAAe;AAAA,EACpC;AACD;", "names": []}