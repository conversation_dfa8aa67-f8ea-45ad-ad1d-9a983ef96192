{"version": 3, "sources": ["../../../../nodes/Slack/V1/UserProfileDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const userProfileOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userProfile'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\tdescription: \"Get your user's profile\",\n\t\t\t\taction: 'Get a user profile',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Update',\n\t\t\t\tvalue: 'update',\n\t\t\t\tdescription: \"Update user's profile\",\n\t\t\t\taction: 'Update a user profile',\n\t\t\t},\n\t\t],\n\t\tdefault: 'get',\n\t},\n];\n\nexport const userProfileFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                                userProfile:update                          */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userProfile'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Custom Fields',\n\t\t\t\tname: 'customFieldUi',\n\t\t\t\tplaceholder: 'Add Custom Fields',\n\t\t\t\ttype: 'fixedCollection',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tmultipleValues: true,\n\t\t\t\t},\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'customFieldValues',\n\t\t\t\t\t\tdisplayName: 'Custom Field',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Field Name or ID',\n\t\t\t\t\t\t\t\tname: 'id',\n\t\t\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\t\t\tloadOptionsMethod: 'getTeamFields',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t\t\t'ID of the field to set. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Field Value',\n\t\t\t\t\t\t\t\tname: 'value',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t\tdescription: 'Value of the field to set',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdisplayName: 'Alt',\n\t\t\t\t\t\t\t\tname: 'alt',\n\t\t\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Email',\n\t\t\t\tname: 'email',\n\t\t\t\ttype: 'string',\n\t\t\t\tplaceholder: '<EMAIL>',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'This field can only be changed by admins for users on paid teams',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'First Name',\n\t\t\t\tname: 'first_name',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Last Name',\n\t\t\t\tname: 'last_name',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Status Emoji',\n\t\t\t\tname: 'status_emoji',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'Is a string referencing an emoji enabled for the Slack team, such as :mountain_railway:',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Status Expiration',\n\t\t\t\tname: 'status_expiration',\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'Is an integer specifying seconds since the epoch, more commonly known as \"UNIX time\". Providing 0 or omitting this field results in a custom status that will not expire.',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Status Text',\n\t\t\t\tname: 'status_text',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Allows up to 100 characters, though we strongly encourage brevity',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'User ID',\n\t\t\t\tname: 'user',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription:\n\t\t\t\t\t'ID of user to change. This argument may only be specified by team admins on paid teams.',\n\t\t\t},\n\t\t],\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                userProfile:get                             */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userProfile'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Include Labels',\n\t\t\t\tname: 'include_labels',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: false,\n\t\t\t\tdescription: 'Whether to include labels for each ID in custom profile fields',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'User ID',\n\t\t\t\tname: 'user',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'User to retrieve profile info for',\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,wBAA2C;AAAA,EACvD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,aAAa;AAAA,MACzB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,oBAAuC;AAAA;AAAA;AAAA;AAAA,EAInD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,aAAa;AAAA,QACxB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACA,SAAS,CAAC;AAAA,QACV,SAAS;AAAA,UACR;AAAA,YACC,MAAM;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,cACP;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,aAAa;AAAA,kBACZ,mBAAmB;AAAA,gBACpB;AAAA,gBACA,SAAS;AAAA,gBACT,aACC;AAAA,cACF;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,aAAa;AAAA,cACd;AAAA,cACA;AAAA,gBACC,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,cACV;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,aAAa;AAAA,QACxB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AACD;", "names": []}