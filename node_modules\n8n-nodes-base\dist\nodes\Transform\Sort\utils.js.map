{"version": 3, "sources": ["../../../../nodes/Transform/Sort/utils.ts"], "sourcesContent": ["import { NodeVM } from '@n8n/vm2';\nimport type { IExecuteFunctions, INodeExecutionData } from 'n8n-workflow';\nimport { NodeOperationError } from 'n8n-workflow';\n\nconst returnRegExp = /\\breturn\\b/g;\nexport function sortByCode(\n\tthis: IExecuteFunctions,\n\titems: INodeExecutionData[],\n): INodeExecutionData[] {\n\tconst code = this.getNodeParameter('code', 0) as string;\n\tif (!returnRegExp.test(code)) {\n\t\tthrow new NodeOperationError(\n\t\t\tthis.getNode(),\n\t\t\t\"Sort code doesn't return. Please add a 'return' statement to your code\",\n\t\t);\n\t}\n\n\tconst mode = this.getMode();\n\tconst vm = new NodeVM({\n\t\tconsole: mode === 'manual' ? 'redirect' : 'inherit',\n\t\tsandbox: { items },\n\t});\n\n\treturn vm.run(`module.exports = items.sort((a, b) => { ${code} })`);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAuB;AAEvB,0BAAmC;AAEnC,MAAM,eAAe;AACd,SAAS,WAEf,OACuB;AACvB,QAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC5C,MAAI,CAAC,aAAa,KAAK,IAAI,GAAG;AAC7B,UAAM,IAAI;AAAA,MACT,KAAK,QAAQ;AAAA,MACb;AAAA,IACD;AAAA,EACD;AAEA,QAAM,OAAO,KAAK,QAAQ;AAC1B,QAAM,KAAK,IAAI,kBAAO;AAAA,IACrB,SAAS,SAAS,WAAW,aAAa;AAAA,IAC1C,SAAS,EAAE,MAAM;AAAA,EAClB,CAAC;AAED,SAAO,GAAG,IAAI,2CAA2C,IAAI,KAAK;AACnE;", "names": []}