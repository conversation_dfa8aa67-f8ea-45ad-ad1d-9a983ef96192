{"version": 3, "sources": ["../../../nodes/Segment/TrackInterface.ts"], "sourcesContent": ["import type { IDataObject } from 'n8n-workflow';\n\nexport interface ITrack {\n\tevent?: string;\n\tuserId?: string;\n\tname?: string;\n\tanonymousId?: string;\n\ttraits?: IDataObject;\n\tcontext?: IDataObject;\n\ttimestamp?: string;\n\tproperties?: IDataObject;\n\tintegrations?: IDataObject;\n}\n\nexport interface IGroup extends ITrack {\n\tgroupId: string;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}