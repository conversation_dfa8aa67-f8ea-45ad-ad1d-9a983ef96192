{"version": 3, "sources": ["../../../../nodes/Transform/Aggregate/utils.ts"], "sourcesContent": ["import type { IBinaryData, INodeExecutionData } from 'n8n-workflow';\n\ntype PartialBinaryData = Omit<IBinaryData, 'data'>;\nconst isBinaryUniqueSetup = () => {\n\tconst binaries: PartialBinaryData[] = [];\n\treturn (binary: IBinaryData) => {\n\t\tfor (const existingBinary of binaries) {\n\t\t\tif (\n\t\t\t\texistingBinary.mimeType === binary.mimeType &&\n\t\t\t\texistingBinary.fileType === binary.fileType &&\n\t\t\t\texistingBinary.fileSize === binary.fileSize &&\n\t\t\t\texistingBinary.fileExtension === binary.fileExtension\n\t\t\t) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tbinaries.push({\n\t\t\tmimeType: binary.mimeType,\n\t\t\tfileType: binary.fileType,\n\t\t\tfileSize: binary.fileSize,\n\t\t\tfileExtension: binary.fileExtension,\n\t\t});\n\n\t\treturn true;\n\t};\n};\n\nexport function addBinariesToItem(\n\tnewItem: INodeExecutionData,\n\titems: INodeExecutionData[],\n\tuniqueOnly?: boolean,\n) {\n\tconst isBinaryUnique = uniqueOnly ? isBinaryUniqueSetup() : undefined;\n\n\tfor (const item of items) {\n\t\tif (item.binary === undefined) continue;\n\n\t\tfor (const key of Object.keys(item.binary)) {\n\t\t\tif (!newItem.binary) newItem.binary = {};\n\t\t\tlet binaryKey = key;\n\t\t\tconst binary = item.binary[key];\n\n\t\t\tif (isBinaryUnique && !isBinaryUnique(binary)) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\t// If the binary key already exists add a suffix to it\n\t\t\tlet i = 1;\n\t\t\twhile (newItem.binary[binaryKey] !== undefined) {\n\t\t\t\tbinaryKey = `${key}_${i}`;\n\t\t\t\ti++;\n\t\t\t}\n\n\t\t\tnewItem.binary[binaryKey] = binary;\n\t\t}\n\t}\n\n\treturn newItem;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,MAAM,sBAAsB,MAAM;AACjC,QAAM,WAAgC,CAAC;AACvC,SAAO,CAAC,WAAwB;AAC/B,eAAW,kBAAkB,UAAU;AACtC,UACC,eAAe,aAAa,OAAO,YACnC,eAAe,aAAa,OAAO,YACnC,eAAe,aAAa,OAAO,YACnC,eAAe,kBAAkB,OAAO,eACvC;AACD,eAAO;AAAA,MACR;AAAA,IACD;AAEA,aAAS,KAAK;AAAA,MACb,UAAU,OAAO;AAAA,MACjB,UAAU,OAAO;AAAA,MACjB,UAAU,OAAO;AAAA,MACjB,eAAe,OAAO;AAAA,IACvB,CAAC;AAED,WAAO;AAAA,EACR;AACD;AAEO,SAAS,kBACf,SACA,OACA,YACC;AACD,QAAM,iBAAiB,aAAa,oBAAoB,IAAI;AAE5D,aAAW,QAAQ,OAAO;AACzB,QAAI,KAAK,WAAW,OAAW;AAE/B,eAAW,OAAO,OAAO,KAAK,KAAK,MAAM,GAAG;AAC3C,UAAI,CAAC,QAAQ,OAAQ,SAAQ,SAAS,CAAC;AACvC,UAAI,YAAY;AAChB,YAAM,SAAS,KAAK,OAAO,GAAG;AAE9B,UAAI,kBAAkB,CAAC,eAAe,MAAM,GAAG;AAC9C;AAAA,MACD;AAGA,UAAI,IAAI;AACR,aAAO,QAAQ,OAAO,SAAS,MAAM,QAAW;AAC/C,oBAAY,GAAG,GAAG,IAAI,CAAC;AACvB;AAAA,MACD;AAEA,cAAQ,OAAO,SAAS,IAAI;AAAA,IAC7B;AAAA,EACD;AAEA,SAAO;AACR;", "names": []}