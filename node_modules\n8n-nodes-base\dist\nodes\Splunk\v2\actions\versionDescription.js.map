{"version": 3, "sources": ["../../../../../nodes/Splunk/v2/actions/versionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport { NodeConnectionTypes, type INodeTypeDescription } from 'n8n-workflow';\n\nimport * as alert from './alert';\nimport * as report from './report';\nimport * as search from './search';\nimport * as user from './user';\n\nexport const versionDescription: INodeTypeDescription = {\n\tdisplayName: 'Splunk',\n\tname: 'splunk',\n\ticon: 'file:splunk.svg',\n\tgroup: ['transform'],\n\tversion: 2,\n\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\tdescription: 'Consume the Splunk Enterprise API',\n\tdefaults: {\n\t\tname: 'Splunk',\n\t},\n\tinputs: [NodeConnectionTypes.Main],\n\toutputs: [NodeConnectionTypes.Main],\n\tcredentials: [\n\t\t{\n\t\t\tname: 'splunk<PERSON><PERSON>',\n\t\t\trequired: true,\n\t\t},\n\t],\n\tproperties: [\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: '<PERSON><PERSON>',\n\t\t\t\t\tvalue: 'alert',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Report',\n\t\t\t\t\tvalue: 'report',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Search',\n\t\t\t\t\tvalue: 'search',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'User',\n\t\t\t\t\tvalue: 'user',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'search',\n\t\t},\n\n\t\t...alert.description,\n\t\t...report.description,\n\t\t...search.description,\n\t\t...user.description,\n\t],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAA+D;AAE/D,YAAuB;AACvB,aAAwB;AACxB,aAAwB;AACxB,WAAsB;AAEf,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,WAAW;AAAA,EACnB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,aAAa;AAAA,IACZ;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA,EACD;AAAA,EACA,YAAY;AAAA,IACX;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IAEA,GAAG,MAAM;AAAA,IACT,GAAG,OAAO;AAAA,IACV,GAAG,OAAO;AAAA,IACV,GAAG,KAAK;AAAA,EACT;AACD;", "names": []}