{"version": 3, "sources": ["../../../../../nodes/ItemLists/V3/actions/versionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport { NodeConnectionTypes, type INodeTypeDescription } from 'n8n-workflow';\n\nimport * as itemList from './itemList';\n\nexport const versionDescription: INodeTypeDescription = {\n\tdisplayName: 'Item Lists',\n\tname: 'itemLists',\n\ticon: 'file:itemLists.svg',\n\tgroup: ['input'],\n\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\tdescription: 'Helper for working with lists of items and transforming arrays',\n\tversion: [3, 3.1],\n\tdefaults: {\n\t\tname: 'Item Lists',\n\t},\n\tinputs: [NodeConnectionTypes.Main],\n\toutputs: [NodeConnectionTypes.Main],\n\tcredentials: [],\n\tproperties: [\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'hidden',\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Item List',\n\t\t\t\t\tvalue: 'itemList',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'itemList',\n\t\t},\n\t\t...itemList.description,\n\t],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAA+D;AAE/D,eAA0B;AAEnB,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,OAAO;AAAA,EACf,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS,CAAC,GAAG,GAAG;AAAA,EAChB,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,aAAa,CAAC;AAAA,EACd,YAAY;AAAA,IACX;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA,GAAG,SAAS;AAAA,EACb;AACD;", "names": []}