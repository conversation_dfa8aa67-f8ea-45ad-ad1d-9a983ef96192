{"version": 3, "sources": ["../../../../nodes/Aws/DynamoDB/utils.ts"], "sourcesContent": ["import type { IDataObject, INodeExecutionData } from 'n8n-workflow';\nimport { deepCopy, assert, ApplicationError } from 'n8n-workflow';\n\nimport type {\n\tAdjustedPutItem,\n\tAttributeValueType,\n\tEAttributeValueType,\n\tIAttributeNameUi,\n\tIAttributeValue,\n\tIAttributeValueUi,\n\tIAttributeValueValue,\n\tPutItemUi,\n} from './types';\n\nconst addColon = (attribute: string) =>\n\t(attribute = attribute.charAt(0) === ':' ? attribute : `:${attribute}`);\n\nconst addPound = (key: string) => (key = key.charAt(0) === '#' ? key : `#${key}`);\n\nexport function adjustExpressionAttributeValues(eavUi: IAttributeValueUi[]) {\n\tconst eav: IAttributeValue = {};\n\n\teavUi.forEach(({ attribute, type, value }) => {\n\t\teav[addColon(attribute)] = { [type]: value } as IAttributeValueValue;\n\t});\n\n\treturn eav;\n}\n\nexport function adjustExpressionAttributeName(eanUi: IAttributeNameUi[]) {\n\tconst ean: { [key: string]: string } = {};\n\n\teanUi.forEach(({ key, value }) => {\n\t\tean[addPound(key)] = value;\n\t});\n\n\treturn ean;\n}\n\nexport function adjustPutItem(putItemUi: PutItemUi) {\n\tconst adjustedPutItem: AdjustedPutItem = {};\n\n\tObject.entries(putItemUi).forEach(([attribute, value]) => {\n\t\tlet type: string;\n\n\t\tif (typeof value === 'boolean') {\n\t\t\ttype = 'BOOL';\n\t\t} else if (typeof value === 'object' && !Array.isArray(value) && value !== null) {\n\t\t\ttype = 'M';\n\t\t} else if (isNaN(Number(value))) {\n\t\t\ttype = 'S';\n\t\t} else {\n\t\t\ttype = 'N';\n\t\t}\n\n\t\tadjustedPutItem[attribute] = { [type]: value.toString() };\n\t});\n\n\treturn adjustedPutItem;\n}\n\nexport function simplify(item: IAttributeValue): IDataObject {\n\tconst output: IDataObject = {};\n\n\tfor (const [attribute, value] of Object.entries(item)) {\n\t\tconst [type, content] = Object.entries(value)[0] as [AttributeValueType, string];\n\t\t//nedded as simplify is used in decodeItem\n\t\t// eslint-disable-next-line @typescript-eslint/no-use-before-define\n\t\toutput[attribute] = decodeAttribute(type, content);\n\t}\n\n\treturn output;\n}\n\nfunction decodeAttribute(type: AttributeValueType, attribute: string | IAttributeValue) {\n\tswitch (type) {\n\t\tcase 'BOOL':\n\t\t\treturn Boolean(attribute);\n\t\tcase 'N':\n\t\t\treturn Number(attribute);\n\t\tcase 'S':\n\t\t\treturn String(attribute);\n\t\tcase 'SS':\n\t\tcase 'NS':\n\t\t\treturn attribute;\n\t\tcase 'M':\n\t\t\tassert(\n\t\t\t\ttypeof attribute === 'object' && !Array.isArray(attribute) && attribute !== null,\n\t\t\t\t'Attribute must be an object',\n\t\t\t);\n\t\t\treturn simplify(attribute);\n\t\tdefault:\n\t\t\treturn null;\n\t}\n}\n\nexport function validateJSON(input: any): object {\n\ttry {\n\t\treturn JSON.parse(input as string);\n\t} catch (error) {\n\t\tthrow new ApplicationError('Items must be a valid JSON', { level: 'warning' });\n\t}\n}\n\nexport function copyInputItem(item: INodeExecutionData, properties: string[]): IDataObject {\n\t// Prepare the data to insert and copy it to be returned\n\tconst newItem: IDataObject = {};\n\tfor (const property of properties) {\n\t\tif (item.json[property] === undefined) {\n\t\t\tnewItem[property] = null;\n\t\t} else {\n\t\t\tnewItem[property] = deepCopy(item.json[property]);\n\t\t}\n\t}\n\treturn newItem;\n}\n\nexport function mapToAttributeValues(item: IDataObject): void {\n\tfor (const key of Object.keys(item)) {\n\t\tif (!key.startsWith(':')) {\n\t\t\titem[`:${key}`] = item[key];\n\t\t\tdelete item[key];\n\t\t}\n\t}\n}\n\nexport function decodeItem(item: IAttributeValue): IDataObject {\n\tconst _item: IDataObject = {};\n\tfor (const entry of Object.entries(item)) {\n\t\tconst [attribute, value]: [string, object] = entry;\n\t\tconst [type, content]: [string, object] = Object.entries(value)[0];\n\t\t_item[attribute] = decodeAttribute(type as EAttributeValueType, content as unknown as string);\n\t}\n\n\treturn _item;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAAmD;AAanD,MAAM,WAAW,CAAC,cAChB,YAAY,UAAU,OAAO,CAAC,MAAM,MAAM,YAAY,IAAI,SAAS;AAErE,MAAM,WAAW,CAAC,QAAiB,MAAM,IAAI,OAAO,CAAC,MAAM,MAAM,MAAM,IAAI,GAAG;AAEvE,SAAS,gCAAgC,OAA4B;AAC3E,QAAM,MAAuB,CAAC;AAE9B,QAAM,QAAQ,CAAC,EAAE,WAAW,MAAM,MAAM,MAAM;AAC7C,QAAI,SAAS,SAAS,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,MAAM;AAAA,EAC5C,CAAC;AAED,SAAO;AACR;AAEO,SAAS,8BAA8B,OAA2B;AACxE,QAAM,MAAiC,CAAC;AAExC,QAAM,QAAQ,CAAC,EAAE,KAAK,MAAM,MAAM;AACjC,QAAI,SAAS,GAAG,CAAC,IAAI;AAAA,EACtB,CAAC;AAED,SAAO;AACR;AAEO,SAAS,cAAc,WAAsB;AACnD,QAAM,kBAAmC,CAAC;AAE1C,SAAO,QAAQ,SAAS,EAAE,QAAQ,CAAC,CAAC,WAAW,KAAK,MAAM;AACzD,QAAI;AAEJ,QAAI,OAAO,UAAU,WAAW;AAC/B,aAAO;AAAA,IACR,WAAW,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK,KAAK,UAAU,MAAM;AAChF,aAAO;AAAA,IACR,WAAW,MAAM,OAAO,KAAK,CAAC,GAAG;AAChC,aAAO;AAAA,IACR,OAAO;AACN,aAAO;AAAA,IACR;AAEA,oBAAgB,SAAS,IAAI,EAAE,CAAC,IAAI,GAAG,MAAM,SAAS,EAAE;AAAA,EACzD,CAAC;AAED,SAAO;AACR;AAEO,SAAS,SAAS,MAAoC;AAC5D,QAAM,SAAsB,CAAC;AAE7B,aAAW,CAAC,WAAW,KAAK,KAAK,OAAO,QAAQ,IAAI,GAAG;AACtD,UAAM,CAAC,MAAM,OAAO,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAG/C,WAAO,SAAS,IAAI,gBAAgB,MAAM,OAAO;AAAA,EAClD;AAEA,SAAO;AACR;AAEA,SAAS,gBAAgB,MAA0B,WAAqC;AACvF,UAAQ,MAAM;AAAA,IACb,KAAK;AACJ,aAAO,QAAQ,SAAS;AAAA,IACzB,KAAK;AACJ,aAAO,OAAO,SAAS;AAAA,IACxB,KAAK;AACJ,aAAO,OAAO,SAAS;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ;AAAA,QACC,OAAO,cAAc,YAAY,CAAC,MAAM,QAAQ,SAAS,KAAK,cAAc;AAAA,QAC5E;AAAA,MACD;AACA,aAAO,SAAS,SAAS;AAAA,IAC1B;AACC,aAAO;AAAA,EACT;AACD;AAEO,SAAS,aAAa,OAAoB;AAChD,MAAI;AACH,WAAO,KAAK,MAAM,KAAe;AAAA,EAClC,SAAS,OAAO;AACf,UAAM,IAAI,qCAAiB,8BAA8B,EAAE,OAAO,UAAU,CAAC;AAAA,EAC9E;AACD;AAEO,SAAS,cAAc,MAA0B,YAAmC;AAE1F,QAAM,UAAuB,CAAC;AAC9B,aAAW,YAAY,YAAY;AAClC,QAAI,KAAK,KAAK,QAAQ,MAAM,QAAW;AACtC,cAAQ,QAAQ,IAAI;AAAA,IACrB,OAAO;AACN,cAAQ,QAAQ,QAAI,8BAAS,KAAK,KAAK,QAAQ,CAAC;AAAA,IACjD;AAAA,EACD;AACA,SAAO;AACR;AAEO,SAAS,qBAAqB,MAAyB;AAC7D,aAAW,OAAO,OAAO,KAAK,IAAI,GAAG;AACpC,QAAI,CAAC,IAAI,WAAW,GAAG,GAAG;AACzB,WAAK,IAAI,GAAG,EAAE,IAAI,KAAK,GAAG;AAC1B,aAAO,KAAK,GAAG;AAAA,IAChB;AAAA,EACD;AACD;AAEO,SAAS,WAAW,MAAoC;AAC9D,QAAM,QAAqB,CAAC;AAC5B,aAAW,SAAS,OAAO,QAAQ,IAAI,GAAG;AACzC,UAAM,CAAC,WAAW,KAAK,IAAsB;AAC7C,UAAM,CAAC,MAAM,OAAO,IAAsB,OAAO,QAAQ,KAAK,EAAE,CAAC;AACjE,UAAM,SAAS,IAAI,gBAAgB,MAA6B,OAA4B;AAAA,EAC7F;AAEA,SAAO;AACR;", "names": []}