{"version": 3, "sources": ["../../credentials/WebflowOAuth2Api.credentials.ts"], "sourcesContent": ["import type { ICredentialType, INodeProperties } from 'n8n-workflow';\n\nexport class WebflowOAuth2Api implements ICredentialType {\n\tname = 'webflowOAuth2Api';\n\n\textends = ['oAuth2Api'];\n\n\tdisplayName = 'Webflow OAuth2 API';\n\n\tdocumentationUrl = 'webflow';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Grant Type',\n\t\t\tname: 'grantType',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: 'authorizationCode',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Legacy',\n\t\t\tname: 'legacy',\n\t\t\ttype: 'boolean',\n\t\t\tdefault: true,\n\t\t\tdescription: 'If the legacy API should be used',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Authorization URL',\n\t\t\tname: 'authUrl',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: 'https://webflow.com/oauth/authorize',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Access Token URL',\n\t\t\tname: 'accessTokenUrl',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: 'https://api.webflow.com/oauth/access_token',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Scope',\n\t\t\tname: 'scope',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: '={{$self[\"legacy\"] ? \"\" : \"cms:read cms:write sites:read forms:read\"}}',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Auth URI Query Parameters',\n\t\t\tname: 'authQueryParameters',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: '',\n\t\t\tdescription:\n\t\t\t\t'For some services additional query parameters have to be set which can be defined here',\n\t\t\tplaceholder: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Authentication',\n\t\t\tname: 'authentication',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: 'body',\n\t\t},\n\t];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,iBAA4C;AAAA,EAAlD;AACN,gBAAO;AAEP,mBAAU,CAAC,WAAW;AAEtB,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,QACD,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,IACD;AAAA;AACD;", "names": []}