{"version": 3, "sources": ["../../../nodes/UProc/ToolDescription.ts"], "sourcesContent": ["import type { IDataObject, INodeProperties } from 'n8n-workflow';\nimport { deepCopy } from 'n8n-workflow';\n\nimport { groups } from './Json/Groups';\nimport { tools } from './Json/Tools';\n\nfunction capitalize(str: string): string {\n\tif (!str) {\n\t\treturn '';\n\t} else {\n\t\treturn str.charAt(0).toUpperCase() + str.slice(1);\n\t}\n}\n\nconst operations = [];\n\nfor (const group of (groups as IDataObject).groups as IDataObject[]) {\n\tconst item = {\n\t\tdisplayName: 'Operation',\n\t\tname: 'tool',\n\t\ttype: 'options',\n\t\tdescription: 'The Operation to consume',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tgroup: [group.name],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\toptions: [],\n\t};\n\n\tconst options = [];\n\tfor (const tool of (tools as IDataObject).processors as IDataObject[]) {\n\t\tif (tool.g === group.name) {\n\t\t\tconst link =\n\t\t\t\t'https://app.uproc.io/#/tools/processor/' +\n\t\t\t\t(tool.k as string)\n\t\t\t\t\t.replace(/([A-Z]+)/g, '-$1')\n\t\t\t\t\t.toLowerCase()\n\t\t\t\t\t.replace('-', '/')\n\t\t\t\t\t.replace('-', '/');\n\t\t\tconst option = {\n\t\t\t\tname: tool.d as string,\n\t\t\t\tvalue: tool.k,\n\t\t\t\tdescription: (tool.ed as string) + ` <a href=\"${link}\" target='_blank'>Info</a>`,\n\t\t\t};\n\t\t\toptions.push(option);\n\t\t}\n\t}\n\n\t//Tool\n\titem.options = options.sort((a, b) => (a.name > b.name ? 1 : -1)) as any;\n\titem.default = options[0].value as string;\n\toperations.push(item);\n}\n\nexport const toolOperations = operations as INodeProperties[];\n\nlet parameters = [];\n//all tools\nfor (const tool of (tools as IDataObject).processors as IDataObject[]) {\n\t//all parameters in tool\n\tfor (const param of tool.p as IDataObject[]) {\n\t\tconst displayName = param.n as string;\n\t\tconst capitalizedDisplayName = capitalize(displayName.replace(/_/g, ' '));\n\t\tconst description = `The \"${capitalizedDisplayName}\" value to use as a parameter for this Operation`;\n\t\tconst parameter = {\n\t\t\tdisplayName: capitalizedDisplayName,\n\t\t\tname: param.n,\n\t\t\ttype: param.t,\n\t\t\tdefault: '',\n\t\t\tplaceholder: param.p,\n\t\t\trequired: param.r,\n\t\t\toptions: param.o,\n\t\t\tdisplayOptions: {\n\t\t\t\tshow: {\n\t\t\t\t\tgroup: [tool.g],\n\t\t\t\t\ttool: [tool.k],\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription: deepCopy(description),\n\t\t};\n\n\t\tlet modifiedParam = null;\n\t\t//Check if param exists previously\n\t\tfor (const currentParam of parameters) {\n\t\t\t//Get old param in parameters array\n\t\t\tif (currentParam.name === param.n) {\n\t\t\t\tmodifiedParam = currentParam;\n\t\t\t}\n\t\t}\n\t\t//if exists, other wise\n\t\tif (modifiedParam) {\n\t\t\t//Assign new group and tool\n\t\t\tmodifiedParam.displayOptions.show.group.push(tool.g);\n\t\t\tmodifiedParam.displayOptions.show.tool.push(tool.k);\n\n\t\t\t//build new array\n\t\t\tconst newParameters = [];\n\t\t\tfor (const currentParam of parameters) {\n\t\t\t\t//Get old param in parameters array\n\t\t\t\tif (currentParam.name === modifiedParam.name) {\n\t\t\t\t\tnewParameters.push(modifiedParam);\n\t\t\t\t} else {\n\t\t\t\t\tnewParameters.push(currentParam);\n\t\t\t\t}\n\t\t\t}\n\t\t\t// eslint-disable-next-line n8n-local-rules/no-json-parse-json-stringify\n\t\t\tparameters = JSON.parse(JSON.stringify(newParameters));\n\t\t} else {\n\t\t\tparameters.push(parameter);\n\t\t}\n\t}\n}\n\nexport const toolParameters = parameters as INodeProperties[];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAAyB;AAEzB,oBAAuB;AACvB,mBAAsB;AAEtB,SAAS,WAAW,KAAqB;AACxC,MAAI,CAAC,KAAK;AACT,WAAO;AAAA,EACR,OAAO;AACN,WAAO,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,MAAM,CAAC;AAAA,EACjD;AACD;AAEA,MAAM,aAAa,CAAC;AAEpB,WAAW,SAAU,qBAAuB,QAAyB;AACpE,QAAM,OAAO;AAAA,IACZ,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,OAAO,CAAC,MAAM,IAAI;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,SAAS,CAAC;AAAA,EACX;AAEA,QAAM,UAAU,CAAC;AACjB,aAAW,QAAS,mBAAsB,YAA6B;AACtE,QAAI,KAAK,MAAM,MAAM,MAAM;AAC1B,YAAM,OACL,4CACC,KAAK,EACJ,QAAQ,aAAa,KAAK,EAC1B,YAAY,EACZ,QAAQ,KAAK,GAAG,EAChB,QAAQ,KAAK,GAAG;AACnB,YAAM,SAAS;AAAA,QACd,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,QACZ,aAAc,KAAK,KAAgB,aAAa,IAAI;AAAA,MACrD;AACA,cAAQ,KAAK,MAAM;AAAA,IACpB;AAAA,EACD;AAGA,OAAK,UAAU,QAAQ,KAAK,CAAC,GAAG,MAAO,EAAE,OAAO,EAAE,OAAO,IAAI,EAAG;AAChE,OAAK,UAAU,QAAQ,CAAC,EAAE;AAC1B,aAAW,KAAK,IAAI;AACrB;AAEO,MAAM,iBAAiB;AAE9B,IAAI,aAAa,CAAC;AAElB,WAAW,QAAS,mBAAsB,YAA6B;AAEtE,aAAW,SAAS,KAAK,GAAoB;AAC5C,UAAM,cAAc,MAAM;AAC1B,UAAM,yBAAyB,WAAW,YAAY,QAAQ,MAAM,GAAG,CAAC;AACxE,UAAM,cAAc,QAAQ,sBAAsB;AAClD,UAAM,YAAY;AAAA,MACjB,aAAa;AAAA,MACb,MAAM,MAAM;AAAA,MACZ,MAAM,MAAM;AAAA,MACZ,SAAS;AAAA,MACT,aAAa,MAAM;AAAA,MACnB,UAAU,MAAM;AAAA,MAChB,SAAS,MAAM;AAAA,MACf,gBAAgB;AAAA,QACf,MAAM;AAAA,UACL,OAAO,CAAC,KAAK,CAAC;AAAA,UACd,MAAM,CAAC,KAAK,CAAC;AAAA,QACd;AAAA,MACD;AAAA,MACA,iBAAa,8BAAS,WAAW;AAAA,IAClC;AAEA,QAAI,gBAAgB;AAEpB,eAAW,gBAAgB,YAAY;AAEtC,UAAI,aAAa,SAAS,MAAM,GAAG;AAClC,wBAAgB;AAAA,MACjB;AAAA,IACD;AAEA,QAAI,eAAe;AAElB,oBAAc,eAAe,KAAK,MAAM,KAAK,KAAK,CAAC;AACnD,oBAAc,eAAe,KAAK,KAAK,KAAK,KAAK,CAAC;AAGlD,YAAM,gBAAgB,CAAC;AACvB,iBAAW,gBAAgB,YAAY;AAEtC,YAAI,aAAa,SAAS,cAAc,MAAM;AAC7C,wBAAc,KAAK,aAAa;AAAA,QACjC,OAAO;AACN,wBAAc,KAAK,YAAY;AAAA,QAChC;AAAA,MACD;AAEA,mBAAa,KAAK,MAAM,KAAK,UAAU,aAAa,CAAC;AAAA,IACtD,OAAO;AACN,iBAAW,KAAK,SAAS;AAAA,IAC1B;AAAA,EACD;AACD;AAEO,MAAM,iBAAiB;", "names": []}