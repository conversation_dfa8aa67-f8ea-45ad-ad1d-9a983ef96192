{"name": "Image generation togetherai", "nodes": [{"parameters": {"amount": 10}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [760, -200], "id": "7ca260fb-8865-4787-a7b3-49dec9a71121", "name": "Wait", "webhookId": "5a7dfb3a-6d19-495a-b42e-2a4845326a5e"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-680, 0], "id": "7531d198-f7c3-4d82-bb09-11c392bf84b8", "name": "prompt imput", "webhookId": "08443b5b-73a7-4bc7-ba63-a313bb722329"}, {"parameters": {"method": "POST", "url": "https://api.together.xyz/v1/images/generations", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"black-forest-labs/FLUX.1-schnell-Free\",\n  \"prompt\": \"{{$json.chatInput}}\",\n  \"n\": 4,\n  \"width\": 1024,\n  \"height\": 768,\n  \"steps\": 4,\n  \"seed\": 1,\n  \"response_format\": \"url\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [-460, 0], "id": "6db882c4-88c0-4143-bf18-498aadf7cfa4", "name": "Generate Images", "credentials": {"httpHeaderAuth": {"id": "3DgzXavgcodjyVL3", "name": "Together ai"}}}, {"parameters": {"fieldToSplitOut": "data", "options": {}}, "type": "n8n-nodes-base.itemLists", "typeVersion": 1, "position": [-280, -200], "id": "37a91de3-6a16-4e95-87f9-11f24260f2f6", "name": "Split URLs1"}, {"parameters": {"url": "={{$json.url}}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [-80, -200], "id": "e8d0c794-4567-415b-a82e-a38bb36ba62a", "name": "Download Image1"}, {"parameters": {"jsCode": "if (!$item().binary || !$item().binary.data) {\n  // Log failed download\n  $item().json.error = 'Missing binary data';\n  return []; // skip item\n}\n\n$item().binary.data.fileName = 'image_' + new Date().getTime() + '.jpg';\nreturn $item();\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [340, -200], "id": "b715ac9e-ff37-494a-af58-1c922f52cad8", "name": "Set Filename1"}, {"parameters": {"method": "POST", "url": "https://webhook.site/96d28f72-71b3-4d73-a728-73afb3887080", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [580, -200], "id": "fa8b7a13-404e-41fe-9589-4dbba041749a", "name": "Upload to Webhook1"}, {"parameters": {"jsCode": "if (!$item().binary || !$item().binary.data) {\n  return []; // skip this item silently\n}\n\nreturn $item();\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [140, -200], "id": "3f8de17a-05b9-47c7-b899-6ab7f793039f", "name": "Binary check"}], "pinData": {}, "connections": {"prompt imput": {"main": [[{"node": "Generate Images", "type": "main", "index": 0}]]}, "Generate Images": {"main": [[{"node": "Split URLs1", "type": "main", "index": 0}]]}, "Split URLs1": {"main": [[{"node": "Download Image1", "type": "main", "index": 0}]]}, "Download Image1": {"main": [[{"node": "Binary check", "type": "main", "index": 0}]]}, "Set Filename1": {"main": [[{"node": "Upload to Webhook1", "type": "main", "index": 0}]]}, "Binary check": {"main": [[{"node": "Set Filename1", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner", "executionTimeout": -1}, "versionId": "1de7c584-d61f-4a00-8fa0-d9b46874f468", "meta": {"templateCredsSetupCompleted": true, "instanceId": "cc3aa6fc596bbfae08db5db34f845d5510fbbfc7a170ee6169c37e6c46c535a7"}, "id": "Tgw9CVRLs61CEFJc", "tags": []}