{"version": 3, "sources": ["../../../../nodes/Venafi/Datacenter/VenafiTlsProtectDatacenter.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tIDataObject,\n\tINodeExecutionData,\n\tINodeType,\n\tINodeTypeDescription,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes } from 'n8n-workflow';\n\nimport { certificateFields, certificateOperations } from './CertificateDescription';\nimport { venafiApiRequest, venafiApiRequestAllItems } from './GenericFunctions';\nimport { policyFields, policyOperations } from './PolicyDescription';\n\nexport class VenafiTlsProtectDatacenter implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Venafi TLS Protect Datacenter',\n\t\tname: 'venafiTlsProtectDatacenter',\n\t\ticon: 'file:../venafi.svg',\n\t\tgroup: ['input'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume Venafi TLS Protect Datacenter',\n\t\tdefaults: {\n\t\t\tname: 'Venafi TLS Protect Datacenter',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'venafiTlsProtectDatacenterApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Certificate',\n\t\t\t\t\t\tvalue: 'certificate',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Policy',\n\t\t\t\t\t\tvalue: 'policy',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'certificate',\n\t\t\t},\n\t\t\t...certificateOperations,\n\t\t\t...certificateFields,\n\t\t\t...policyOperations,\n\t\t\t...policyFields,\n\t\t],\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: IDataObject[] = [];\n\t\tconst length = items.length;\n\t\tconst qs: IDataObject = {};\n\t\tlet responseData;\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'certificate') {\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst policyDN = this.getNodeParameter('PolicyDN', i) as string;\n\n\t\t\t\t\t\tconst subject = this.getNodeParameter('Subject', i) as string;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tPolicyDN: policyDN,\n\t\t\t\t\t\t\tSubject: subject,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\n\t\t\t\t\t\tif (body.SubjectAltNamesUi) {\n\t\t\t\t\t\t\tbody.SubjectAltNames = (body.SubjectAltNamesUi as IDataObject).SubjectAltNamesValues;\n\n\t\t\t\t\t\t\tdelete body.SubjectAltNamesUi;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await venafiApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/vedsdk/Certificates/Request',\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tconst certificateId = this.getNodeParameter('certificateId', i) as string;\n\n\t\t\t\t\t\tresponseData = await venafiApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'DELETE',\n\t\t\t\t\t\t\t`/vedsdk/Certificates/${certificateId}`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (operation === 'download') {\n\t\t\t\t\t\tconst certificateDn = this.getNodeParameter('certificateDn', i) as string;\n\t\t\t\t\t\tconst includePrivateKey = this.getNodeParameter('includePrivateKey', i) as boolean;\n\t\t\t\t\t\tconst binaryProperty = this.getNodeParameter('binaryProperty', i);\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tCertificateDN: certificateDn,\n\t\t\t\t\t\t\tFormat: 'Base64',\n\t\t\t\t\t\t\tIncludeChain: true,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (includePrivateKey) {\n\t\t\t\t\t\t\tconst password = this.getNodeParameter('password', i) as string;\n\t\t\t\t\t\t\tbody.IncludePrivateKey = true;\n\t\t\t\t\t\t\tbody.Password = password;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\n\t\t\t\t\t\tresponseData = await venafiApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/vedsdk/Certificates/Retrieve',\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tconst binaryData = await this.helpers.prepareBinaryData(\n\t\t\t\t\t\t\tBuffer.from(responseData.CertificateData as BufferEncoding, 'base64'),\n\t\t\t\t\t\t\tresponseData.Filename as string,\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tresponseData = {\n\t\t\t\t\t\t\tjson: {},\n\t\t\t\t\t\t\tbinary: {\n\t\t\t\t\t\t\t\t[binaryProperty]: binaryData,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tconst certificateId = this.getNodeParameter('certificateId', i) as string;\n\n\t\t\t\t\t\tresponseData = await venafiApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t`/vedsdk/Certificates/${certificateId}`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (operation === 'getMany') {\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\n\t\t\t\t\t\tif (options.fields) {\n\t\t\t\t\t\t\tqs.OptionalFields = (options.fields as string[]).join(',');\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await venafiApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'Certificates',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/vedsdk/Certificates',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tqs.Limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = await venafiApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/vedsdk/Certificates',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\n\t\t\t\t\t\t\tresponseData = responseData.Certificates;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tif (operation === 'renew') {\n\t\t\t\t\t\tconst certificateDN = this.getNodeParameter('certificateDN', i) as string;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tCertificateDN: certificateDN,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\n\t\t\t\t\t\tresponseData = await venafiApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/vedsdk/Certificates/Renew',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (resource === 'policy') {\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tconst policy = this.getNodeParameter('policyDn', i) as string;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tPolicyDN: policy,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\n\t\t\t\t\t\tresponseData = await venafiApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/vedsdk/Certificates/CheckPolicy',\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturnData.push(\n\t\t\t\t\t...this.helpers.constructExecutionMetaData(\n\t\t\t\t\t\tthis.helpers.returnJsonArray(responseData as IDataObject[]),\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\titemData: { item: i },\n\t\t\t\t\t\t},\n\t\t\t\t\t),\n\t\t\t\t);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ json: { error: error.message } });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\n\t\treturn [returnData as INodeExecutionData[]];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,0BAAoC;AAEpC,oCAAyD;AACzD,8BAA2D;AAC3D,+BAA+C;AAExC,MAAM,2BAAgD;AAAA,EAAtD;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,OAAO;AAAA,MACf,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAA4B,CAAC;AACnC,UAAM,SAAS,MAAM;AACrB,UAAM,KAAkB,CAAC;AACzB,QAAI;AACJ,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,YAAI,aAAa,eAAe;AAC/B,cAAI,cAAc,UAAU;AAC3B,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAEpD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,kBAAM,OAAoB;AAAA,cACzB,UAAU;AAAA,cACV,SAAS;AAAA,YACV;AAEA,mBAAO,OAAO,MAAM,gBAAgB;AAEpC,gBAAI,KAAK,mBAAmB;AAC3B,mBAAK,kBAAmB,KAAK,kBAAkC;AAE/D,qBAAO,KAAK;AAAA,YACb;AAEA,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,CAAC;AAE9D,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA,wBAAwB,aAAa;AAAA,cACrC,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,YAAY;AAC7B,kBAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,CAAC;AAC9D,kBAAM,oBAAoB,KAAK,iBAAiB,qBAAqB,CAAC;AACtE,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,kBAAM,OAAoB;AAAA,cACzB,eAAe;AAAA,cACf,QAAQ;AAAA,cACR,cAAc;AAAA,YACf;AAEA,gBAAI,mBAAmB;AACtB,oBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,mBAAK,oBAAoB;AACzB,mBAAK,WAAW;AAAA,YACjB;AAEA,mBAAO,OAAO,MAAM,gBAAgB;AAEpC,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAEA,kBAAM,aAAa,MAAM,KAAK,QAAQ;AAAA,cACrC,OAAO,KAAK,aAAa,iBAAmC,QAAQ;AAAA,cACpE,aAAa;AAAA,YACd;AAEA,2BAAe;AAAA,cACd,MAAM,CAAC;AAAA,cACP,QAAQ;AAAA,gBACP,CAAC,cAAc,GAAG;AAAA,cACnB;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,OAAO;AACxB,kBAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,CAAC;AAE9D,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA,wBAAwB,aAAa;AAAA,cACrC,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,WAAW;AAC5B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gBAAI,QAAQ,QAAQ;AACnB,iBAAG,iBAAkB,QAAQ,OAAoB,KAAK,GAAG;AAAA,YAC1D;AAEA,gBAAI,WAAW;AACd,6BAAe,MAAM,iDAAyB;AAAA,gBAC7C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,iBAAG,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC3C,6BAAe,MAAM,yCAAiB;AAAA,gBACrC;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAEA,6BAAe,aAAa;AAAA,YAC7B;AAAA,UACD;AAEA,cAAI,cAAc,SAAS;AAC1B,kBAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,CAAC;AAE9D,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,kBAAM,OAAoB;AAAA,cACzB,eAAe;AAAA,YAChB;AAEA,mBAAO,OAAO,MAAM,gBAAgB;AAEpC,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAEA,YAAI,aAAa,UAAU;AAC1B,cAAI,cAAc,OAAO;AACxB,kBAAM,SAAS,KAAK,iBAAiB,YAAY,CAAC;AAElD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,kBAAM,OAAoB;AAAA,cACzB,UAAU;AAAA,YACX;AAEA,mBAAO,OAAO,MAAM,gBAAgB;AAEpC,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAEA,mBAAW;AAAA,UACV,GAAG,KAAK,QAAQ;AAAA,YACf,KAAK,QAAQ,gBAAgB,YAA6B;AAAA,YAC1D;AAAA,cACC,UAAU,EAAE,MAAM,EAAE;AAAA,YACrB;AAAA,UACD;AAAA,QACD;AAAA,MACD,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,MAAM,EAAE,OAAO,MAAM,QAAQ,EAAE,CAAC;AAClD;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AAEA,WAAO,CAAC,UAAkC;AAAA,EAC3C;AACD;", "names": []}