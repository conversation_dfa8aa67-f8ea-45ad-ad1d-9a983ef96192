{"version": 3, "sources": ["../../../../../../../nodes/Google/Drive/v2/actions/file/upload.operation.ts"], "sourcesContent": ["import type {\n\tIDataObject,\n\tIExecuteFunctions,\n\tINodeExecutionData,\n\tINodeProperties,\n} from 'n8n-workflow';\n\nimport { updateDisplayOptions } from '@utils/utilities';\n\nimport {\n\tgetItemBinaryData,\n\tsetFileProperties,\n\tsetUpdateCommonParams,\n\tsetParentFolder,\n\tprocessInChunks,\n} from '../../helpers/utils';\nimport { googleApiRequest } from '../../transport';\nimport { driveRLC, folderRLC, updateCommonOptions } from '../common.descriptions';\n\nconst properties: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Input Data Field Name',\n\t\tname: 'inputDataFieldName',\n\t\ttype: 'string',\n\t\tplaceholder: '“e.g. data',\n\t\tdefault: 'data',\n\t\trequired: true,\n\t\thint: 'The name of the input field containing the binary file data to update the file',\n\t\tdescription:\n\t\t\t'Find the name of input field containing the binary data to update the file in the Input panel on the left, in the Binary tab',\n\t},\n\t{\n\t\tdisplayName: 'File Name',\n\t\tname: 'name',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\tplaceholder: 'e.g. My New File',\n\t\tdescription: 'If not specified, the original file name will be used',\n\t},\n\t{\n\t\t...driveRLC,\n\t\tdisplayName: 'Parent Drive',\n\t\tdescription: 'The drive where to upload the file',\n\t},\n\t{\n\t\t...folderRLC,\n\t\tdisplayName: 'Parent Folder',\n\t\tdescription: 'The folder where to upload the file',\n\t},\n\t{\n\t\tdisplayName: 'Options',\n\t\tname: 'options',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add option',\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t...updateCommonOptions,\n\t\t\t{\n\t\t\t\tdisplayName: 'Simplify Output',\n\t\t\t\tname: 'simplifyOutput',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: true,\n\t\t\t\tdescription: 'Whether to return a simplified version of the response instead of all fields',\n\t\t\t},\n\t\t],\n\t},\n];\n\nconst displayOptions = {\n\tshow: {\n\t\tresource: ['file'],\n\t\toperation: ['upload'],\n\t},\n};\n\nexport const description = updateDisplayOptions(displayOptions, properties);\n\nexport async function execute(this: IExecuteFunctions, i: number): Promise<INodeExecutionData[]> {\n\tconst returnData: INodeExecutionData[] = [];\n\n\tconst inputDataFieldName = this.getNodeParameter('inputDataFieldName', i) as string;\n\n\tconst { contentLength, fileContent, originalFilename, mimeType } = await getItemBinaryData.call(\n\t\tthis,\n\t\tinputDataFieldName,\n\t\ti,\n\t);\n\n\tconst name = (this.getNodeParameter('name', i) as string) || originalFilename;\n\n\tconst driveId = this.getNodeParameter('driveId', i, undefined, {\n\t\textractValue: true,\n\t}) as string;\n\n\tconst folderId = this.getNodeParameter('folderId', i, undefined, {\n\t\textractValue: true,\n\t}) as string;\n\n\tlet uploadId;\n\tif (Buffer.isBuffer(fileContent)) {\n\t\tconst response = await googleApiRequest.call(\n\t\t\tthis,\n\t\t\t'POST',\n\t\t\t'/upload/drive/v3/files',\n\t\t\tfileContent,\n\t\t\t{\n\t\t\t\tuploadType: 'media',\n\t\t\t},\n\t\t\tundefined,\n\t\t\t{\n\t\t\t\theaders: {\n\t\t\t\t\t'Content-Type': mimeType,\n\t\t\t\t\t'Content-Length': contentLength,\n\t\t\t\t},\n\t\t\t},\n\t\t);\n\n\t\tuploadId = response.id;\n\t} else {\n\t\tconst resumableUpload = await googleApiRequest.call(\n\t\t\tthis,\n\t\t\t'POST',\n\t\t\t'/upload/drive/v3/files',\n\t\t\tundefined,\n\t\t\t{ uploadType: 'resumable' },\n\t\t\tundefined,\n\t\t\t{\n\t\t\t\treturnFullResponse: true,\n\t\t\t},\n\t\t);\n\n\t\tconst uploadUrl = resumableUpload.headers.location;\n\n\t\t// 2MB chunks, needs to be a multiple of 256kB for Google Drive API\n\t\tconst chunkSizeBytes = 2048 * 1024;\n\n\t\tawait processInChunks(fileContent, chunkSizeBytes, async (chunk, offset) => {\n\t\t\ttry {\n\t\t\t\tconst response = await this.helpers.httpRequest({\n\t\t\t\t\tmethod: 'PUT',\n\t\t\t\t\turl: uploadUrl,\n\t\t\t\t\theaders: {\n\t\t\t\t\t\t'Content-Length': chunk.length,\n\t\t\t\t\t\t'Content-Range': `bytes ${offset}-${offset + chunk.byteLength - 1}/${contentLength}`,\n\t\t\t\t\t},\n\t\t\t\t\tbody: chunk,\n\t\t\t\t});\n\t\t\t\tuploadId = response?.id;\n\t\t\t} catch (error) {\n\t\t\t\tif (error.response?.status !== 308) throw error;\n\t\t\t}\n\t\t});\n\t}\n\n\tconst options = this.getNodeParameter('options', i, {});\n\n\tconst qs = setUpdateCommonParams(\n\t\t{\n\t\t\taddParents: setParentFolder(folderId, driveId),\n\t\t\tincludeItemsFromAllDrives: true,\n\t\t\tsupportsAllDrives: true,\n\t\t\tspaces: 'appDataFolder, drive',\n\t\t\tcorpora: 'allDrives',\n\t\t},\n\t\toptions,\n\t);\n\n\tif (!options.simplifyOutput) {\n\t\tqs.fields = '*';\n\t}\n\n\tconst body = setFileProperties(\n\t\t{\n\t\t\tmimeType,\n\t\t\tname,\n\t\t\toriginalFilename,\n\t\t},\n\t\toptions,\n\t);\n\n\tconst response = await googleApiRequest.call(\n\t\tthis,\n\t\t'PATCH',\n\t\t`/drive/v3/files/${uploadId}`,\n\t\tbody,\n\t\tqs,\n\t);\n\n\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\tthis.helpers.returnJsonArray(response as IDataObject[]),\n\t\t{ itemData: { item: i } },\n\t);\n\treturnData.push(...executionData);\n\n\treturn returnData;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,uBAAqC;AAErC,mBAMO;AACP,uBAAiC;AACjC,oBAAyD;AAEzD,MAAM,aAAgC;AAAA,EACrC;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,GAAG;AAAA,IACH,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,GAAG;AAAA,IACH,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR,GAAG;AAAA,MACH;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AACD;AAEA,MAAM,iBAAiB;AAAA,EACtB,MAAM;AAAA,IACL,UAAU,CAAC,MAAM;AAAA,IACjB,WAAW,CAAC,QAAQ;AAAA,EACrB;AACD;AAEO,MAAM,kBAAc,uCAAqB,gBAAgB,UAAU;AAE1E,eAAsB,QAAiC,GAA0C;AAChG,QAAM,aAAmC,CAAC;AAE1C,QAAM,qBAAqB,KAAK,iBAAiB,sBAAsB,CAAC;AAExE,QAAM,EAAE,eAAe,aAAa,kBAAkB,SAAS,IAAI,MAAM,+BAAkB;AAAA,IAC1F;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAEA,QAAM,OAAQ,KAAK,iBAAiB,QAAQ,CAAC,KAAgB;AAE7D,QAAM,UAAU,KAAK,iBAAiB,WAAW,GAAG,QAAW;AAAA,IAC9D,cAAc;AAAA,EACf,CAAC;AAED,QAAM,WAAW,KAAK,iBAAiB,YAAY,GAAG,QAAW;AAAA,IAChE,cAAc;AAAA,EACf,CAAC;AAED,MAAI;AACJ,MAAI,OAAO,SAAS,WAAW,GAAG;AACjC,UAAMA,YAAW,MAAM,kCAAiB;AAAA,MACvC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACC,YAAY;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,QACC,SAAS;AAAA,UACR,gBAAgB;AAAA,UAChB,kBAAkB;AAAA,QACnB;AAAA,MACD;AAAA,IACD;AAEA,eAAWA,UAAS;AAAA,EACrB,OAAO;AACN,UAAM,kBAAkB,MAAM,kCAAiB;AAAA,MAC9C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,EAAE,YAAY,YAAY;AAAA,MAC1B;AAAA,MACA;AAAA,QACC,oBAAoB;AAAA,MACrB;AAAA,IACD;AAEA,UAAM,YAAY,gBAAgB,QAAQ;AAG1C,UAAM,iBAAiB,OAAO;AAE9B,cAAM,8BAAgB,aAAa,gBAAgB,OAAO,OAAO,WAAW;AAC3E,UAAI;AACH,cAAMA,YAAW,MAAM,KAAK,QAAQ,YAAY;AAAA,UAC/C,QAAQ;AAAA,UACR,KAAK;AAAA,UACL,SAAS;AAAA,YACR,kBAAkB,MAAM;AAAA,YACxB,iBAAiB,SAAS,MAAM,IAAI,SAAS,MAAM,aAAa,CAAC,IAAI,aAAa;AAAA,UACnF;AAAA,UACA,MAAM;AAAA,QACP,CAAC;AACD,mBAAWA,WAAU;AAAA,MACtB,SAAS,OAAO;AACf,YAAI,MAAM,UAAU,WAAW,IAAK,OAAM;AAAA,MAC3C;AAAA,IACD,CAAC;AAAA,EACF;AAEA,QAAM,UAAU,KAAK,iBAAiB,WAAW,GAAG,CAAC,CAAC;AAEtD,QAAM,SAAK;AAAA,IACV;AAAA,MACC,gBAAY,8BAAgB,UAAU,OAAO;AAAA,MAC7C,2BAA2B;AAAA,MAC3B,mBAAmB;AAAA,MACnB,QAAQ;AAAA,MACR,SAAS;AAAA,IACV;AAAA,IACA;AAAA,EACD;AAEA,MAAI,CAAC,QAAQ,gBAAgB;AAC5B,OAAG,SAAS;AAAA,EACb;AAEA,QAAM,WAAO;AAAA,IACZ;AAAA,MACC;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,IACA;AAAA,EACD;AAEA,QAAM,WAAW,MAAM,kCAAiB;AAAA,IACvC;AAAA,IACA;AAAA,IACA,mBAAmB,QAAQ;AAAA,IAC3B;AAAA,IACA;AAAA,EACD;AAEA,QAAM,gBAAgB,KAAK,QAAQ;AAAA,IAClC,KAAK,QAAQ,gBAAgB,QAAyB;AAAA,IACtD,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,EACzB;AACA,aAAW,KAAK,GAAG,aAAa;AAEhC,SAAO;AACR;", "names": ["response"]}