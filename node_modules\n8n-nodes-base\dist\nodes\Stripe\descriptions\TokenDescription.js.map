{"version": 3, "sources": ["../../../../nodes/Stripe/descriptions/TokenDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const tokenOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdefault: 'create',\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Create',\n\t\t\t\tvalue: 'create',\n\t\t\t\tdescription: 'Create a token',\n\t\t\t\taction: 'Create a token',\n\t\t\t},\n\t\t],\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['token'],\n\t\t\t},\n\t\t},\n\t},\n];\n\nexport const tokenFields: INodeProperties[] = [\n\t// ----------------------------------\n\t//          token: create\n\t// ----------------------------------\n\t{\n\t\tdisplayName: 'Type',\n\t\tname: 'type',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\tdefault: 'cardToken',\n\t\tdescription: 'Type of token to create',\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Card Token',\n\t\t\t\tvalue: 'cardToken',\n\t\t\t},\n\t\t],\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['token'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Card Number',\n\t\tname: 'number',\n\t\ttype: 'string',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['token'],\n\t\t\t\toperation: ['create'],\n\t\t\t\ttype: ['cardToken'],\n\t\t\t},\n\t\t},\n\t\tplaceholder: '****************',\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'CVC',\n\t\tname: 'cvc',\n\t\ttype: 'string',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['token'],\n\t\t\t\toperation: ['create'],\n\t\t\t\ttype: ['cardToken'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tplaceholder: '314',\n\t\tdescription: 'Security code printed on the back of the card',\n\t},\n\t{\n\t\tdisplayName: 'Expiration Month',\n\t\tdescription: 'Number of the month when the card will expire',\n\t\tname: 'expirationMonth',\n\t\ttype: 'string',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['token'],\n\t\t\t\toperation: ['create'],\n\t\t\t\ttype: ['cardToken'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tplaceholder: '10',\n\t},\n\t{\n\t\tdisplayName: 'Expiration Year',\n\t\tdescription: 'Year when the card will expire',\n\t\tname: 'expirationYear',\n\t\ttype: 'string',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['token'],\n\t\t\t\toperation: ['create'],\n\t\t\t\ttype: ['cardToken'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tplaceholder: '2022',\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,kBAAqC;AAAA,EACjD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,cAAiC;AAAA;AAAA;AAAA;AAAA,EAI7C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,IACb,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,QAClB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,QAClB,WAAW,CAAC,QAAQ;AAAA,QACpB,MAAM,CAAC,WAAW;AAAA,MACnB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,IACb,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,QAClB,WAAW,CAAC,QAAQ;AAAA,QACpB,MAAM,CAAC,WAAW;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,QAClB,WAAW,CAAC,QAAQ;AAAA,QACpB,MAAM,CAAC,WAAW;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,OAAO;AAAA,QAClB,WAAW,CAAC,QAAQ;AAAA,QACpB,MAAM,CAAC,WAAW;AAAA,MACnB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AACD;", "names": []}