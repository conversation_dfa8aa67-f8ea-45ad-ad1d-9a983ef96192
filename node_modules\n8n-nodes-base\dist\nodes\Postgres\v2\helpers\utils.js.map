{"version": 3, "sources": ["../../../../../nodes/Postgres/v2/helpers/utils.ts"], "sourcesContent": ["import type {\n\tIDataObject,\n\tIExecuteFunctions,\n\tINode,\n\tINodeExecutionData,\n\tINodePropertyOptions,\n\tNodeParameterValueType,\n} from 'n8n-workflow';\nimport { NodeOperationError, jsonParse } from 'n8n-workflow';\n\nimport type {\n\tColumnInfo,\n\tEnumInfo,\n\tPgpClient,\n\tPgpDatabase,\n\tQueryMode,\n\tQueryValues,\n\tQueryWithValues,\n\tSortRule,\n\tWhereClause,\n} from './interfaces';\nimport { generatePairedItemData } from '../../../../utils/utilities';\n\nexport function isJSON(str: string) {\n\ttry {\n\t\tJSON.parse(str.trim());\n\t\treturn true;\n\t} catch {\n\t\treturn false;\n\t}\n}\n\nexport function evaluateExpression(expression: NodeParameterValueType) {\n\tif (expression === undefined) {\n\t\treturn '';\n\t} else if (expression === null) {\n\t\treturn 'null';\n\t} else {\n\t\treturn typeof expression === 'object' ? JSON.stringify(expression) : expression.toString();\n\t}\n}\n\nexport function stringToArray(str: NodeParameterValueType | undefined) {\n\tif (str === undefined) return [];\n\treturn String(str)\n\t\t.split(',')\n\t\t.filter((entry) => entry)\n\t\t.map((entry) => entry.trim());\n}\n\nexport function wrapData(data: IDataObject | IDataObject[]): INodeExecutionData[] {\n\tif (!Array.isArray(data)) {\n\t\treturn [{ json: data }];\n\t}\n\treturn data.map((item) => ({\n\t\tjson: item,\n\t}));\n}\n\nexport function prepareErrorItem(\n\titems: INodeExecutionData[],\n\terror: IDataObject | NodeOperationError | Error,\n\tindex: number,\n) {\n\treturn {\n\t\tjson: { message: error.message, item: { ...items[index].json }, error: { ...error } },\n\t\tpairedItem: { item: index },\n\t} as INodeExecutionData;\n}\n\nexport function parsePostgresError(\n\tnode: INode,\n\terror: any,\n\tqueries: QueryWithValues[],\n\titemIndex?: number,\n) {\n\tif (error.message.includes('syntax error at or near') && queries.length) {\n\t\ttry {\n\t\t\tconst snippet = error.message.match(/syntax error at or near \"(.*)\"/)[1] as string;\n\t\t\tconst failedQureryIndex = queries.findIndex((query) => query.query.includes(snippet));\n\n\t\t\tif (failedQureryIndex !== -1) {\n\t\t\t\tif (!itemIndex) {\n\t\t\t\t\titemIndex = failedQureryIndex;\n\t\t\t\t}\n\t\t\t\tconst failedQuery = queries[failedQureryIndex].query;\n\t\t\t\tconst lines = failedQuery.split('\\n');\n\t\t\t\tconst lineIndex = lines.findIndex((line) => line.includes(snippet));\n\t\t\t\tconst errorMessage = `Syntax error at line ${lineIndex + 1} near \"${snippet}\"`;\n\t\t\t\terror.message = errorMessage;\n\t\t\t}\n\t\t} catch {}\n\t}\n\n\tlet message = error.message;\n\tconst errorDescription = error.description ? error.description : error.detail || error.hint;\n\tlet description = errorDescription;\n\n\tif (!description && queries[itemIndex || 0]?.query) {\n\t\tdescription = `Failed query: ${queries[itemIndex || 0].query}`;\n\t}\n\n\tif (error.message.includes('ECONNREFUSED')) {\n\t\tmessage = 'Connection refused';\n\t\ttry {\n\t\t\tdescription = error.message.split('ECONNREFUSED ')[1].trim();\n\t\t} catch (e) {}\n\t}\n\n\tif (error.message.includes('ENOTFOUND')) {\n\t\tmessage = 'Host not found';\n\t\ttry {\n\t\t\tdescription = error.message.split('ENOTFOUND ')[1].trim();\n\t\t} catch (e) {}\n\t}\n\n\tif (error.message.includes('ETIMEDOUT')) {\n\t\tmessage = 'Connection timed out';\n\t\ttry {\n\t\t\tdescription = error.message.split('ETIMEDOUT ')[1].trim();\n\t\t} catch (e) {}\n\t}\n\n\treturn new NodeOperationError(node, error as Error, {\n\t\tmessage,\n\t\tdescription,\n\t\titemIndex,\n\t});\n}\n\nexport function addWhereClauses(\n\tnode: INode,\n\titemIndex: number,\n\tquery: string,\n\tclauses: WhereClause[],\n\treplacements: QueryValues,\n\tcombineConditions: string,\n): [string, QueryValues] {\n\tif (clauses.length === 0) return [query, replacements];\n\n\tlet combineWith = 'AND';\n\n\tif (combineConditions === 'OR') {\n\t\tcombineWith = 'OR';\n\t}\n\n\tlet replacementIndex = replacements.length + 1;\n\n\tlet whereQuery = ' WHERE';\n\tconst values: QueryValues = [];\n\n\tclauses.forEach((clause, index) => {\n\t\tif (clause.condition === 'equal') {\n\t\t\tclause.condition = '=';\n\t\t}\n\t\tif (['>', '<', '>=', '<='].includes(clause.condition)) {\n\t\t\tconst value = Number(clause.value);\n\n\t\t\tif (Number.isNaN(value)) {\n\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\tnode,\n\t\t\t\t\t`Operator in entry ${index + 1} of 'Select Rows' works with numbers, but value ${\n\t\t\t\t\t\tclause.value\n\t\t\t\t\t} is not a number`,\n\t\t\t\t\t{\n\t\t\t\t\t\titemIndex,\n\t\t\t\t\t},\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tclause.value = value;\n\t\t}\n\t\tconst columnReplacement = `$${replacementIndex}:name`;\n\t\tvalues.push(clause.column);\n\t\treplacementIndex = replacementIndex + 1;\n\n\t\tlet valueReplacement = '';\n\t\tif (clause.condition !== 'IS NULL' && clause.condition !== 'IS NOT NULL') {\n\t\t\tvalueReplacement = ` $${replacementIndex}`;\n\t\t\tvalues.push(clause.value);\n\t\t\treplacementIndex = replacementIndex + 1;\n\t\t}\n\n\t\tconst operator = index === clauses.length - 1 ? '' : ` ${combineWith}`;\n\n\t\twhereQuery += ` ${columnReplacement} ${clause.condition}${valueReplacement}${operator}`;\n\t});\n\n\treturn [`${query}${whereQuery}`, replacements.concat(...values)];\n}\n\nexport function addSortRules(\n\tquery: string,\n\trules: SortRule[],\n\treplacements: QueryValues,\n): [string, QueryValues] {\n\tif (rules.length === 0) return [query, replacements];\n\n\tlet replacementIndex = replacements.length + 1;\n\n\tlet orderByQuery = ' ORDER BY';\n\tconst values: string[] = [];\n\n\trules.forEach((rule, index) => {\n\t\tconst columnReplacement = `$${replacementIndex}:name`;\n\t\tvalues.push(rule.column);\n\t\treplacementIndex = replacementIndex + 1;\n\n\t\tconst endWith = index === rules.length - 1 ? '' : ',';\n\n\t\tconst sortDirection = rule.direction === 'DESC' ? 'DESC' : 'ASC';\n\n\t\torderByQuery += ` ${columnReplacement} ${sortDirection}${endWith}`;\n\t});\n\n\treturn [`${query}${orderByQuery}`, replacements.concat(...values)];\n}\n\nexport function addReturning(\n\tquery: string,\n\toutputColumns: string[],\n\treplacements: QueryValues,\n): [string, QueryValues] {\n\tif (outputColumns.includes('*')) return [`${query} RETURNING *`, replacements];\n\n\tconst replacementIndex = replacements.length + 1;\n\n\treturn [`${query} RETURNING $${replacementIndex}:name`, [...replacements, outputColumns]];\n}\n\nconst isSelectQuery = (query: string) => {\n\treturn query\n\t\t.replace(/\\/\\*.*?\\*\\//g, '') // remove multiline comments\n\t\t.replace(/\\n/g, '')\n\t\t.split(';')\n\t\t.filter((statement) => statement && !statement.startsWith('--')) // remove comments and empty statements\n\t\t.every((statement) => statement.trim().toLowerCase().startsWith('select'));\n};\n\nexport function configureQueryRunner(\n\tthis: IExecuteFunctions,\n\tnode: INode,\n\tcontinueOnFail: boolean,\n\tpgp: PgpClient,\n\tdb: PgpDatabase,\n) {\n\treturn async (queries: QueryWithValues[], items: INodeExecutionData[], options: IDataObject) => {\n\t\tlet returnData: INodeExecutionData[] = [];\n\t\tconst emptyReturnData: INodeExecutionData[] =\n\t\t\toptions.operation === 'select' ? [] : [{ json: { success: true } }];\n\n\t\tconst queryBatching = (options.queryBatching as QueryMode) || 'single';\n\n\t\tif (queryBatching === 'single') {\n\t\t\ttry {\n\t\t\t\treturnData = (await db.multi(pgp.helpers.concat(queries)))\n\t\t\t\t\t.map((result, i) => {\n\t\t\t\t\t\treturn this.helpers.constructExecutionMetaData(wrapData(result as IDataObject[]), {\n\t\t\t\t\t\t\titemData: { item: i },\n\t\t\t\t\t\t});\n\t\t\t\t\t})\n\t\t\t\t\t.flat();\n\n\t\t\t\tif (!returnData.length) {\n\t\t\t\t\tconst pairedItem = generatePairedItemData(queries.length);\n\n\t\t\t\t\tif ((options?.nodeVersion as number) < 2.3) {\n\t\t\t\t\t\tif (emptyReturnData.length) {\n\t\t\t\t\t\t\temptyReturnData[0].pairedItem = pairedItem;\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturnData = emptyReturnData;\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturnData = queries.every((query) => isSelectQuery(query.query))\n\t\t\t\t\t\t\t? []\n\t\t\t\t\t\t\t: [{ json: { success: true }, pairedItem }];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\tconst error = parsePostgresError(node, err, queries);\n\t\t\t\tif (!continueOnFail) throw error;\n\n\t\t\t\treturn [\n\t\t\t\t\t{\n\t\t\t\t\t\tjson: {\n\t\t\t\t\t\t\tmessage: error.message,\n\t\t\t\t\t\t\terror: { ...error },\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t];\n\t\t\t}\n\t\t}\n\n\t\tif (queryBatching === 'transaction') {\n\t\t\treturnData = await db.tx(async (transaction) => {\n\t\t\t\tconst result: INodeExecutionData[] = [];\n\t\t\t\tfor (let i = 0; i < queries.length; i++) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst query = queries[i].query;\n\t\t\t\t\t\tconst values = queries[i].values;\n\n\t\t\t\t\t\tlet transactionResults;\n\t\t\t\t\t\tif ((options?.nodeVersion as number) < 2.3) {\n\t\t\t\t\t\t\ttransactionResults = await transaction.any(query, values);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\ttransactionResults = (await transaction.multi(query, values)).flat();\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (!transactionResults.length) {\n\t\t\t\t\t\t\tif ((options?.nodeVersion as number) < 2.3) {\n\t\t\t\t\t\t\t\ttransactionResults = emptyReturnData;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\ttransactionResults = isSelectQuery(query) ? [] : [{ success: true }];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\t\t\twrapData(transactionResults),\n\t\t\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tresult.push(...executionData);\n\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\tconst error = parsePostgresError(node, err, queries, i);\n\t\t\t\t\t\tif (!continueOnFail) throw error;\n\t\t\t\t\t\tresult.push(prepareErrorItem(items, error, i));\n\t\t\t\t\t\treturn result;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t});\n\t\t}\n\n\t\tif (queryBatching === 'independently') {\n\t\t\treturnData = await db.task(async (task) => {\n\t\t\t\tconst result: INodeExecutionData[] = [];\n\t\t\t\tfor (let i = 0; i < queries.length; i++) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst query = queries[i].query;\n\t\t\t\t\t\tconst values = queries[i].values;\n\n\t\t\t\t\t\tlet transactionResults;\n\t\t\t\t\t\tif ((options?.nodeVersion as number) < 2.3) {\n\t\t\t\t\t\t\ttransactionResults = await task.any(query, values);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\ttransactionResults = (await task.multi(query, values)).flat();\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (!transactionResults.length) {\n\t\t\t\t\t\t\tif ((options?.nodeVersion as number) < 2.3) {\n\t\t\t\t\t\t\t\ttransactionResults = emptyReturnData;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\ttransactionResults = isSelectQuery(query) ? [] : [{ success: true }];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\t\t\twrapData(transactionResults),\n\t\t\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tresult.push(...executionData);\n\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\tconst error = parsePostgresError(node, err, queries, i);\n\t\t\t\t\t\tif (!continueOnFail) throw error;\n\t\t\t\t\t\tresult.push(prepareErrorItem(items, error, i));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t});\n\t\t}\n\n\t\treturn returnData;\n\t};\n}\n\nexport function replaceEmptyStringsByNulls(\n\titems: INodeExecutionData[],\n\treplace?: boolean,\n): INodeExecutionData[] {\n\tif (!replace) return items;\n\n\tconst returnData: INodeExecutionData[] = items.map((item) => {\n\t\tconst newItem = { ...item };\n\t\tconst keys = Object.keys(newItem.json);\n\n\t\tfor (const key of keys) {\n\t\t\tif (newItem.json[key] === '') {\n\t\t\t\tnewItem.json[key] = null;\n\t\t\t}\n\t\t}\n\n\t\treturn newItem;\n\t});\n\n\treturn returnData;\n}\n\nexport function prepareItem(values: IDataObject[]) {\n\tconst item = values.reduce((acc, { column, value }) => {\n\t\tacc[column as string] = value;\n\t\treturn acc;\n\t}, {} as IDataObject);\n\n\treturn item;\n}\n\nexport function hasJsonDataTypeInSchema(schema: ColumnInfo[]) {\n\treturn schema.some(({ data_type }) => data_type === 'json');\n}\n\nexport function convertValuesToJsonWithPgp(\n\tpgp: PgpClient,\n\tschema: ColumnInfo[],\n\tvalues: IDataObject,\n) {\n\tschema\n\t\t.filter(\n\t\t\t({ data_type, column_name }) =>\n\t\t\t\tdata_type === 'json' && values[column_name] !== null && values[column_name] !== undefined,\n\t\t)\n\t\t.forEach(({ column_name }) => {\n\t\t\tvalues[column_name] = pgp.as.json(values[column_name], true);\n\t\t});\n\n\treturn values;\n}\n\nexport async function columnFeatureSupport(\n\tdb: PgpDatabase,\n): Promise<{ identity_generation: boolean; is_generated: boolean }> {\n\tconst result = await db.any(\n\t\t`SELECT EXISTS (\n\t\t\tSELECT 1 FROM information_schema.columns WHERE table_name = 'columns' AND table_schema = 'information_schema' AND column_name = 'is_generated'\n\t\t) as is_generated,\n\t\tEXISTS (\n\t\t\tSELECT 1 FROM information_schema.columns WHERE table_name = 'columns' AND table_schema = 'information_schema' AND column_name = 'identity_generation'\n\t\t) as identity_generation;`,\n\t);\n\n\treturn result[0];\n}\n\nexport async function getTableSchema(\n\tdb: PgpDatabase,\n\tschema: string,\n\ttable: string,\n\toptions?: { getColumnsForResourceMapper?: boolean },\n): Promise<ColumnInfo[]> {\n\tconst select = ['column_name', 'data_type', 'is_nullable', 'udt_name', 'column_default'];\n\n\tif (options?.getColumnsForResourceMapper) {\n\t\t// Check if columns exist before querying (identity_generation was added in v10, is_generated in v12)\n\t\tconst supported = await columnFeatureSupport(db);\n\n\t\tif (supported.identity_generation) {\n\t\t\tselect.push('identity_generation');\n\t\t}\n\n\t\tif (supported.is_generated) {\n\t\t\tselect.push('is_generated');\n\t\t}\n\t}\n\n\tconst selectString = select.join(', ');\n\tconst columns = await db.any(\n\t\t`SELECT ${selectString} FROM information_schema.columns WHERE table_schema = $1 AND table_name = $2`,\n\t\t[schema, table],\n\t);\n\n\treturn columns;\n}\n\nexport async function uniqueColumns(db: PgpDatabase, table: string, schema = 'public') {\n\t// Using the modified query from https://wiki.postgresql.org/wiki/Retrieve_primary_key_columns\n\t// `quote_ident` - properly quote and escape an identifier\n\t// `::regclass` - cast a string to a regclass (internal type for object names)\n\tconst unique = await db.any(\n\t\t`\n\t\tSELECT DISTINCT a.attname\n\t\t\tFROM pg_index i JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)\n\t\tWHERE i.indrelid = (quote_ident($1) || '.' || quote_ident($2))::regclass\n\t\t\tAND (i.indisprimary OR i.indisunique);\n\t\t`,\n\t\t[schema, table],\n\t);\n\treturn unique as IDataObject[];\n}\n\nexport async function getEnums(db: PgpDatabase): Promise<EnumInfo[]> {\n\tconst enumsData = await db.any(\n\t\t'SELECT pg_type.typname, pg_enum.enumlabel FROM pg_type JOIN pg_enum ON pg_enum.enumtypid = pg_type.oid;',\n\t);\n\treturn enumsData as EnumInfo[];\n}\n\nexport function getEnumValues(enumInfo: EnumInfo[], enumName: string): INodePropertyOptions[] {\n\treturn enumInfo.reduce((acc, current) => {\n\t\tif (current.typname === enumName) {\n\t\t\tacc.push({ name: current.enumlabel, value: current.enumlabel });\n\t\t}\n\t\treturn acc;\n\t}, [] as INodePropertyOptions[]);\n}\n\nexport async function doesRowExist(\n\tdb: PgpDatabase,\n\tschema: string,\n\ttable: string,\n\tvalues: string[],\n): Promise<boolean> {\n\tconst where = [];\n\tfor (let i = 3; i < 3 + values.length; i += 2) {\n\t\twhere.push(`$${i}:name=$${i + 1}`);\n\t}\n\tconst exists = await db.any(\n\t\t`SELECT EXISTS(SELECT 1 FROM $1:name.$2:name WHERE ${where.join(' AND ')})`,\n\t\t[schema, table, ...values],\n\t);\n\treturn exists[0].exists;\n}\n\nexport function checkItemAgainstSchema(\n\tnode: INode,\n\titem: IDataObject,\n\tcolumnsInfo: ColumnInfo[],\n\tindex: number,\n) {\n\tif (columnsInfo.length === 0) return item;\n\tconst schema = columnsInfo.reduce((acc, { column_name, data_type, is_nullable }) => {\n\t\tacc[column_name] = { type: data_type.toUpperCase(), nullable: is_nullable === 'YES' };\n\t\treturn acc;\n\t}, {} as IDataObject);\n\n\tfor (const key of Object.keys(item)) {\n\t\tif (schema[key] === undefined) {\n\t\t\tthrow new NodeOperationError(node, `Column '${key}' does not exist in selected table`, {\n\t\t\t\titemIndex: index,\n\t\t\t});\n\t\t}\n\t\tif (item[key] === null && !(schema[key] as IDataObject)?.nullable) {\n\t\t\tthrow new NodeOperationError(node, `Column '${key}' is not nullable`, {\n\t\t\t\titemIndex: index,\n\t\t\t});\n\t\t}\n\t}\n\n\treturn item;\n}\n\nexport const configureTableSchemaUpdater = (initialSchema: string, initialTable: string) => {\n\tlet currentSchema = initialSchema;\n\tlet currentTable = initialTable;\n\treturn async (db: PgpDatabase, tableSchema: ColumnInfo[], schema: string, table: string) => {\n\t\tif (currentSchema !== schema || currentTable !== table) {\n\t\t\tcurrentSchema = schema;\n\t\t\tcurrentTable = table;\n\t\t\ttableSchema = await getTableSchema(db, schema, table);\n\t\t}\n\t\treturn tableSchema;\n\t};\n};\n\n/**\n * If postgress column type is array we need to convert it to fornmat that postgres understands, original object data would be modified\n * @param data the object with keys representing column names and values\n * @param schema table schema\n * @param node INode\n * @param itemIndex the index of the current item\n */\nexport const convertArraysToPostgresFormat = (\n\tdata: IDataObject,\n\tschema: ColumnInfo[],\n\tnode: INode,\n\titemIndex = 0,\n) => {\n\tfor (const columnInfo of schema) {\n\t\t//in case column type is array we need to convert it to fornmat that postgres understands\n\t\tif (columnInfo.data_type.toUpperCase() === 'ARRAY') {\n\t\t\tlet columnValue = data[columnInfo.column_name];\n\n\t\t\tif (typeof columnValue === 'string') {\n\t\t\t\tcolumnValue = jsonParse(columnValue);\n\t\t\t}\n\n\t\t\tif (Array.isArray(columnValue)) {\n\t\t\t\tconst arrayEntries = columnValue.map((entry) => {\n\t\t\t\t\tif (typeof entry === 'number') {\n\t\t\t\t\t\treturn entry;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (typeof entry === 'boolean') {\n\t\t\t\t\t\tentry = String(entry);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (typeof entry === 'object') {\n\t\t\t\t\t\tentry = JSON.stringify(entry);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (typeof entry === 'string') {\n\t\t\t\t\t\treturn `\"${entry.replace(/\"/g, '\\\\\"')}\"`; //escape double quotes\n\t\t\t\t\t}\n\n\t\t\t\t\treturn entry;\n\t\t\t\t});\n\n\t\t\t\t//wrap in {} instead of [] as postgres does and join with ,\n\t\t\t\tdata[columnInfo.column_name] = `{${arrayEntries.join(',')}}`;\n\t\t\t} else {\n\t\t\t\tif (columnInfo.is_nullable === 'NO') {\n\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\tnode,\n\t\t\t\t\t\t`Column '${columnInfo.column_name}' has to be an array`,\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\titemIndex,\n\t\t\t\t\t\t},\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n};\n\nexport function addExecutionHints(\n\tcontext: IExecuteFunctions,\n\titems: INodeExecutionData[],\n\toperation: string,\n\texecuteOnce: boolean | undefined,\n) {\n\tif (operation === 'select' && items.length > 1 && !executeOnce) {\n\t\tcontext.addExecutionHints({\n\t\t\tmessage: `This node ran ${items.length} times, once for each input item. To run for the first item only, enable 'execute once' in the node settings`,\n\t\t\tlocation: 'outputPane',\n\t\t});\n\t}\n\n\tif (\n\t\toperation === 'executeQuery' &&\n\t\titems.length > 1 &&\n\t\t(context.getNodeParameter('options.queryBatching', 0, 'single') as string) === 'single' &&\n\t\t(context.getNodeParameter('query', 0, '') as string).toLowerCase().startsWith('insert')\n\t) {\n\t\tcontext.addExecutionHints({\n\t\t\tmessage:\n\t\t\t\t\"Inserts were batched for performance. If you need to preserve item matching, consider changing 'Query batching' to 'Independent' in the options.\",\n\t\t\tlocation: 'outputPane',\n\t\t});\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,0BAA8C;AAa9C,uBAAuC;AAEhC,SAAS,OAAO,KAAa;AACnC,MAAI;AACH,SAAK,MAAM,IAAI,KAAK,CAAC;AACrB,WAAO;AAAA,EACR,QAAQ;AACP,WAAO;AAAA,EACR;AACD;AAEO,SAAS,mBAAmB,YAAoC;AACtE,MAAI,eAAe,QAAW;AAC7B,WAAO;AAAA,EACR,WAAW,eAAe,MAAM;AAC/B,WAAO;AAAA,EACR,OAAO;AACN,WAAO,OAAO,eAAe,WAAW,KAAK,UAAU,UAAU,IAAI,WAAW,SAAS;AAAA,EAC1F;AACD;AAEO,SAAS,cAAc,KAAyC;AACtE,MAAI,QAAQ,OAAW,QAAO,CAAC;AAC/B,SAAO,OAAO,GAAG,EACf,MAAM,GAAG,EACT,OAAO,CAAC,UAAU,KAAK,EACvB,IAAI,CAAC,UAAU,MAAM,KAAK,CAAC;AAC9B;AAEO,SAAS,SAAS,MAAyD;AACjF,MAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACzB,WAAO,CAAC,EAAE,MAAM,KAAK,CAAC;AAAA,EACvB;AACA,SAAO,KAAK,IAAI,CAAC,UAAU;AAAA,IAC1B,MAAM;AAAA,EACP,EAAE;AACH;AAEO,SAAS,iBACf,OACA,OACA,OACC;AACD,SAAO;AAAA,IACN,MAAM,EAAE,SAAS,MAAM,SAAS,MAAM,EAAE,GAAG,MAAM,KAAK,EAAE,KAAK,GAAG,OAAO,EAAE,GAAG,MAAM,EAAE;AAAA,IACpF,YAAY,EAAE,MAAM,MAAM;AAAA,EAC3B;AACD;AAEO,SAAS,mBACf,MACA,OACA,SACA,WACC;AACD,MAAI,MAAM,QAAQ,SAAS,yBAAyB,KAAK,QAAQ,QAAQ;AACxE,QAAI;AACH,YAAM,UAAU,MAAM,QAAQ,MAAM,gCAAgC,EAAE,CAAC;AACvE,YAAM,oBAAoB,QAAQ,UAAU,CAAC,UAAU,MAAM,MAAM,SAAS,OAAO,CAAC;AAEpF,UAAI,sBAAsB,IAAI;AAC7B,YAAI,CAAC,WAAW;AACf,sBAAY;AAAA,QACb;AACA,cAAM,cAAc,QAAQ,iBAAiB,EAAE;AAC/C,cAAM,QAAQ,YAAY,MAAM,IAAI;AACpC,cAAM,YAAY,MAAM,UAAU,CAAC,SAAS,KAAK,SAAS,OAAO,CAAC;AAClE,cAAM,eAAe,wBAAwB,YAAY,CAAC,UAAU,OAAO;AAC3E,cAAM,UAAU;AAAA,MACjB;AAAA,IACD,QAAQ;AAAA,IAAC;AAAA,EACV;AAEA,MAAI,UAAU,MAAM;AACpB,QAAM,mBAAmB,MAAM,cAAc,MAAM,cAAc,MAAM,UAAU,MAAM;AACvF,MAAI,cAAc;AAElB,MAAI,CAAC,eAAe,QAAQ,aAAa,CAAC,GAAG,OAAO;AACnD,kBAAc,iBAAiB,QAAQ,aAAa,CAAC,EAAE,KAAK;AAAA,EAC7D;AAEA,MAAI,MAAM,QAAQ,SAAS,cAAc,GAAG;AAC3C,cAAU;AACV,QAAI;AACH,oBAAc,MAAM,QAAQ,MAAM,eAAe,EAAE,CAAC,EAAE,KAAK;AAAA,IAC5D,SAAS,GAAG;AAAA,IAAC;AAAA,EACd;AAEA,MAAI,MAAM,QAAQ,SAAS,WAAW,GAAG;AACxC,cAAU;AACV,QAAI;AACH,oBAAc,MAAM,QAAQ,MAAM,YAAY,EAAE,CAAC,EAAE,KAAK;AAAA,IACzD,SAAS,GAAG;AAAA,IAAC;AAAA,EACd;AAEA,MAAI,MAAM,QAAQ,SAAS,WAAW,GAAG;AACxC,cAAU;AACV,QAAI;AACH,oBAAc,MAAM,QAAQ,MAAM,YAAY,EAAE,CAAC,EAAE,KAAK;AAAA,IACzD,SAAS,GAAG;AAAA,IAAC;AAAA,EACd;AAEA,SAAO,IAAI,uCAAmB,MAAM,OAAgB;AAAA,IACnD;AAAA,IACA;AAAA,IACA;AAAA,EACD,CAAC;AACF;AAEO,SAAS,gBACf,MACA,WACA,OACA,SACA,cACA,mBACwB;AACxB,MAAI,QAAQ,WAAW,EAAG,QAAO,CAAC,OAAO,YAAY;AAErD,MAAI,cAAc;AAElB,MAAI,sBAAsB,MAAM;AAC/B,kBAAc;AAAA,EACf;AAEA,MAAI,mBAAmB,aAAa,SAAS;AAE7C,MAAI,aAAa;AACjB,QAAM,SAAsB,CAAC;AAE7B,UAAQ,QAAQ,CAAC,QAAQ,UAAU;AAClC,QAAI,OAAO,cAAc,SAAS;AACjC,aAAO,YAAY;AAAA,IACpB;AACA,QAAI,CAAC,KAAK,KAAK,MAAM,IAAI,EAAE,SAAS,OAAO,SAAS,GAAG;AACtD,YAAM,QAAQ,OAAO,OAAO,KAAK;AAEjC,UAAI,OAAO,MAAM,KAAK,GAAG;AACxB,cAAM,IAAI;AAAA,UACT;AAAA,UACA,qBAAqB,QAAQ,CAAC,mDAC7B,OAAO,KACR;AAAA,UACA;AAAA,YACC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,aAAO,QAAQ;AAAA,IAChB;AACA,UAAM,oBAAoB,IAAI,gBAAgB;AAC9C,WAAO,KAAK,OAAO,MAAM;AACzB,uBAAmB,mBAAmB;AAEtC,QAAI,mBAAmB;AACvB,QAAI,OAAO,cAAc,aAAa,OAAO,cAAc,eAAe;AACzE,yBAAmB,KAAK,gBAAgB;AACxC,aAAO,KAAK,OAAO,KAAK;AACxB,yBAAmB,mBAAmB;AAAA,IACvC;AAEA,UAAM,WAAW,UAAU,QAAQ,SAAS,IAAI,KAAK,IAAI,WAAW;AAEpE,kBAAc,IAAI,iBAAiB,IAAI,OAAO,SAAS,GAAG,gBAAgB,GAAG,QAAQ;AAAA,EACtF,CAAC;AAED,SAAO,CAAC,GAAG,KAAK,GAAG,UAAU,IAAI,aAAa,OAAO,GAAG,MAAM,CAAC;AAChE;AAEO,SAAS,aACf,OACA,OACA,cACwB;AACxB,MAAI,MAAM,WAAW,EAAG,QAAO,CAAC,OAAO,YAAY;AAEnD,MAAI,mBAAmB,aAAa,SAAS;AAE7C,MAAI,eAAe;AACnB,QAAM,SAAmB,CAAC;AAE1B,QAAM,QAAQ,CAAC,MAAM,UAAU;AAC9B,UAAM,oBAAoB,IAAI,gBAAgB;AAC9C,WAAO,KAAK,KAAK,MAAM;AACvB,uBAAmB,mBAAmB;AAEtC,UAAM,UAAU,UAAU,MAAM,SAAS,IAAI,KAAK;AAElD,UAAM,gBAAgB,KAAK,cAAc,SAAS,SAAS;AAE3D,oBAAgB,IAAI,iBAAiB,IAAI,aAAa,GAAG,OAAO;AAAA,EACjE,CAAC;AAED,SAAO,CAAC,GAAG,KAAK,GAAG,YAAY,IAAI,aAAa,OAAO,GAAG,MAAM,CAAC;AAClE;AAEO,SAAS,aACf,OACA,eACA,cACwB;AACxB,MAAI,cAAc,SAAS,GAAG,EAAG,QAAO,CAAC,GAAG,KAAK,gBAAgB,YAAY;AAE7E,QAAM,mBAAmB,aAAa,SAAS;AAE/C,SAAO,CAAC,GAAG,KAAK,eAAe,gBAAgB,SAAS,CAAC,GAAG,cAAc,aAAa,CAAC;AACzF;AAEA,MAAM,gBAAgB,CAAC,UAAkB;AACxC,SAAO,MACL,QAAQ,gBAAgB,EAAE,EAC1B,QAAQ,OAAO,EAAE,EACjB,MAAM,GAAG,EACT,OAAO,CAAC,cAAc,aAAa,CAAC,UAAU,WAAW,IAAI,CAAC,EAC9D,MAAM,CAAC,cAAc,UAAU,KAAK,EAAE,YAAY,EAAE,WAAW,QAAQ,CAAC;AAC3E;AAEO,SAAS,qBAEf,MACA,gBACA,KACA,IACC;AACD,SAAO,OAAO,SAA4B,OAA6B,YAAyB;AAC/F,QAAI,aAAmC,CAAC;AACxC,UAAM,kBACL,QAAQ,cAAc,WAAW,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,SAAS,KAAK,EAAE,CAAC;AAEnE,UAAM,gBAAiB,QAAQ,iBAA+B;AAE9D,QAAI,kBAAkB,UAAU;AAC/B,UAAI;AACH,sBAAc,MAAM,GAAG,MAAM,IAAI,QAAQ,OAAO,OAAO,CAAC,GACtD,IAAI,CAAC,QAAQ,MAAM;AACnB,iBAAO,KAAK,QAAQ,2BAA2B,SAAS,MAAuB,GAAG;AAAA,YACjF,UAAU,EAAE,MAAM,EAAE;AAAA,UACrB,CAAC;AAAA,QACF,CAAC,EACA,KAAK;AAEP,YAAI,CAAC,WAAW,QAAQ;AACvB,gBAAM,iBAAa,yCAAuB,QAAQ,MAAM;AAExD,cAAK,SAAS,cAAyB,KAAK;AAC3C,gBAAI,gBAAgB,QAAQ;AAC3B,8BAAgB,CAAC,EAAE,aAAa;AAAA,YACjC;AACA,yBAAa;AAAA,UACd,OAAO;AACN,yBAAa,QAAQ,MAAM,CAAC,UAAU,cAAc,MAAM,KAAK,CAAC,IAC7D,CAAC,IACD,CAAC,EAAE,MAAM,EAAE,SAAS,KAAK,GAAG,WAAW,CAAC;AAAA,UAC5C;AAAA,QACD;AAAA,MACD,SAAS,KAAK;AACb,cAAM,QAAQ,mBAAmB,MAAM,KAAK,OAAO;AACnD,YAAI,CAAC,eAAgB,OAAM;AAE3B,eAAO;AAAA,UACN;AAAA,YACC,MAAM;AAAA,cACL,SAAS,MAAM;AAAA,cACf,OAAO,EAAE,GAAG,MAAM;AAAA,YACnB;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,QAAI,kBAAkB,eAAe;AACpC,mBAAa,MAAM,GAAG,GAAG,OAAO,gBAAgB;AAC/C,cAAM,SAA+B,CAAC;AACtC,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,cAAI;AACH,kBAAM,QAAQ,QAAQ,CAAC,EAAE;AACzB,kBAAM,SAAS,QAAQ,CAAC,EAAE;AAE1B,gBAAI;AACJ,gBAAK,SAAS,cAAyB,KAAK;AAC3C,mCAAqB,MAAM,YAAY,IAAI,OAAO,MAAM;AAAA,YACzD,OAAO;AACN,oCAAsB,MAAM,YAAY,MAAM,OAAO,MAAM,GAAG,KAAK;AAAA,YACpE;AAEA,gBAAI,CAAC,mBAAmB,QAAQ;AAC/B,kBAAK,SAAS,cAAyB,KAAK;AAC3C,qCAAqB;AAAA,cACtB,OAAO;AACN,qCAAqB,cAAc,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,SAAS,KAAK,CAAC;AAAA,cACpE;AAAA,YACD;AAEA,kBAAM,gBAAgB,KAAK,QAAQ;AAAA,cAClC,SAAS,kBAAkB;AAAA,cAC3B,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,YACzB;AAEA,mBAAO,KAAK,GAAG,aAAa;AAAA,UAC7B,SAAS,KAAK;AACb,kBAAM,QAAQ,mBAAmB,MAAM,KAAK,SAAS,CAAC;AACtD,gBAAI,CAAC,eAAgB,OAAM;AAC3B,mBAAO,KAAK,iBAAiB,OAAO,OAAO,CAAC,CAAC;AAC7C,mBAAO;AAAA,UACR;AAAA,QACD;AACA,eAAO;AAAA,MACR,CAAC;AAAA,IACF;AAEA,QAAI,kBAAkB,iBAAiB;AACtC,mBAAa,MAAM,GAAG,KAAK,OAAO,SAAS;AAC1C,cAAM,SAA+B,CAAC;AACtC,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,cAAI;AACH,kBAAM,QAAQ,QAAQ,CAAC,EAAE;AACzB,kBAAM,SAAS,QAAQ,CAAC,EAAE;AAE1B,gBAAI;AACJ,gBAAK,SAAS,cAAyB,KAAK;AAC3C,mCAAqB,MAAM,KAAK,IAAI,OAAO,MAAM;AAAA,YAClD,OAAO;AACN,oCAAsB,MAAM,KAAK,MAAM,OAAO,MAAM,GAAG,KAAK;AAAA,YAC7D;AAEA,gBAAI,CAAC,mBAAmB,QAAQ;AAC/B,kBAAK,SAAS,cAAyB,KAAK;AAC3C,qCAAqB;AAAA,cACtB,OAAO;AACN,qCAAqB,cAAc,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,SAAS,KAAK,CAAC;AAAA,cACpE;AAAA,YACD;AAEA,kBAAM,gBAAgB,KAAK,QAAQ;AAAA,cAClC,SAAS,kBAAkB;AAAA,cAC3B,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,YACzB;AAEA,mBAAO,KAAK,GAAG,aAAa;AAAA,UAC7B,SAAS,KAAK;AACb,kBAAM,QAAQ,mBAAmB,MAAM,KAAK,SAAS,CAAC;AACtD,gBAAI,CAAC,eAAgB,OAAM;AAC3B,mBAAO,KAAK,iBAAiB,OAAO,OAAO,CAAC,CAAC;AAAA,UAC9C;AAAA,QACD;AACA,eAAO;AAAA,MACR,CAAC;AAAA,IACF;AAEA,WAAO;AAAA,EACR;AACD;AAEO,SAAS,2BACf,OACA,SACuB;AACvB,MAAI,CAAC,QAAS,QAAO;AAErB,QAAM,aAAmC,MAAM,IAAI,CAAC,SAAS;AAC5D,UAAM,UAAU,EAAE,GAAG,KAAK;AAC1B,UAAM,OAAO,OAAO,KAAK,QAAQ,IAAI;AAErC,eAAW,OAAO,MAAM;AACvB,UAAI,QAAQ,KAAK,GAAG,MAAM,IAAI;AAC7B,gBAAQ,KAAK,GAAG,IAAI;AAAA,MACrB;AAAA,IACD;AAEA,WAAO;AAAA,EACR,CAAC;AAED,SAAO;AACR;AAEO,SAAS,YAAY,QAAuB;AAClD,QAAM,OAAO,OAAO,OAAO,CAAC,KAAK,EAAE,QAAQ,MAAM,MAAM;AACtD,QAAI,MAAgB,IAAI;AACxB,WAAO;AAAA,EACR,GAAG,CAAC,CAAgB;AAEpB,SAAO;AACR;AAEO,SAAS,wBAAwB,QAAsB;AAC7D,SAAO,OAAO,KAAK,CAAC,EAAE,UAAU,MAAM,cAAc,MAAM;AAC3D;AAEO,SAAS,2BACf,KACA,QACA,QACC;AACD,SACE;AAAA,IACA,CAAC,EAAE,WAAW,YAAY,MACzB,cAAc,UAAU,OAAO,WAAW,MAAM,QAAQ,OAAO,WAAW,MAAM;AAAA,EAClF,EACC,QAAQ,CAAC,EAAE,YAAY,MAAM;AAC7B,WAAO,WAAW,IAAI,IAAI,GAAG,KAAK,OAAO,WAAW,GAAG,IAAI;AAAA,EAC5D,CAAC;AAEF,SAAO;AACR;AAEA,eAAsB,qBACrB,IACmE;AACnE,QAAM,SAAS,MAAM,GAAG;AAAA,IACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD;AAEA,SAAO,OAAO,CAAC;AAChB;AAEA,eAAsB,eACrB,IACA,QACA,OACA,SACwB;AACxB,QAAM,SAAS,CAAC,eAAe,aAAa,eAAe,YAAY,gBAAgB;AAEvF,MAAI,SAAS,6BAA6B;AAEzC,UAAM,YAAY,MAAM,qBAAqB,EAAE;AAE/C,QAAI,UAAU,qBAAqB;AAClC,aAAO,KAAK,qBAAqB;AAAA,IAClC;AAEA,QAAI,UAAU,cAAc;AAC3B,aAAO,KAAK,cAAc;AAAA,IAC3B;AAAA,EACD;AAEA,QAAM,eAAe,OAAO,KAAK,IAAI;AACrC,QAAM,UAAU,MAAM,GAAG;AAAA,IACxB,UAAU,YAAY;AAAA,IACtB,CAAC,QAAQ,KAAK;AAAA,EACf;AAEA,SAAO;AACR;AAEA,eAAsB,cAAc,IAAiB,OAAe,SAAS,UAAU;AAItF,QAAM,SAAS,MAAM,GAAG;AAAA,IACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,CAAC,QAAQ,KAAK;AAAA,EACf;AACA,SAAO;AACR;AAEA,eAAsB,SAAS,IAAsC;AACpE,QAAM,YAAY,MAAM,GAAG;AAAA,IAC1B;AAAA,EACD;AACA,SAAO;AACR;AAEO,SAAS,cAAc,UAAsB,UAA0C;AAC7F,SAAO,SAAS,OAAO,CAAC,KAAK,YAAY;AACxC,QAAI,QAAQ,YAAY,UAAU;AACjC,UAAI,KAAK,EAAE,MAAM,QAAQ,WAAW,OAAO,QAAQ,UAAU,CAAC;AAAA,IAC/D;AACA,WAAO;AAAA,EACR,GAAG,CAAC,CAA2B;AAChC;AAEA,eAAsB,aACrB,IACA,QACA,OACA,QACmB;AACnB,QAAM,QAAQ,CAAC;AACf,WAAS,IAAI,GAAG,IAAI,IAAI,OAAO,QAAQ,KAAK,GAAG;AAC9C,UAAM,KAAK,IAAI,CAAC,UAAU,IAAI,CAAC,EAAE;AAAA,EAClC;AACA,QAAM,SAAS,MAAM,GAAG;AAAA,IACvB,qDAAqD,MAAM,KAAK,OAAO,CAAC;AAAA,IACxE,CAAC,QAAQ,OAAO,GAAG,MAAM;AAAA,EAC1B;AACA,SAAO,OAAO,CAAC,EAAE;AAClB;AAEO,SAAS,uBACf,MACA,MACA,aACA,OACC;AACD,MAAI,YAAY,WAAW,EAAG,QAAO;AACrC,QAAM,SAAS,YAAY,OAAO,CAAC,KAAK,EAAE,aAAa,WAAW,YAAY,MAAM;AACnF,QAAI,WAAW,IAAI,EAAE,MAAM,UAAU,YAAY,GAAG,UAAU,gBAAgB,MAAM;AACpF,WAAO;AAAA,EACR,GAAG,CAAC,CAAgB;AAEpB,aAAW,OAAO,OAAO,KAAK,IAAI,GAAG;AACpC,QAAI,OAAO,GAAG,MAAM,QAAW;AAC9B,YAAM,IAAI,uCAAmB,MAAM,WAAW,GAAG,sCAAsC;AAAA,QACtF,WAAW;AAAA,MACZ,CAAC;AAAA,IACF;AACA,QAAI,KAAK,GAAG,MAAM,QAAQ,CAAE,OAAO,GAAG,GAAmB,UAAU;AAClE,YAAM,IAAI,uCAAmB,MAAM,WAAW,GAAG,qBAAqB;AAAA,QACrE,WAAW;AAAA,MACZ,CAAC;AAAA,IACF;AAAA,EACD;AAEA,SAAO;AACR;AAEO,MAAM,8BAA8B,CAAC,eAAuB,iBAAyB;AAC3F,MAAI,gBAAgB;AACpB,MAAI,eAAe;AACnB,SAAO,OAAO,IAAiB,aAA2B,QAAgB,UAAkB;AAC3F,QAAI,kBAAkB,UAAU,iBAAiB,OAAO;AACvD,sBAAgB;AAChB,qBAAe;AACf,oBAAc,MAAM,eAAe,IAAI,QAAQ,KAAK;AAAA,IACrD;AACA,WAAO;AAAA,EACR;AACD;AASO,MAAM,gCAAgC,CAC5C,MACA,QACA,MACA,YAAY,MACR;AACJ,aAAW,cAAc,QAAQ;AAEhC,QAAI,WAAW,UAAU,YAAY,MAAM,SAAS;AACnD,UAAI,cAAc,KAAK,WAAW,WAAW;AAE7C,UAAI,OAAO,gBAAgB,UAAU;AACpC,0BAAc,+BAAU,WAAW;AAAA,MACpC;AAEA,UAAI,MAAM,QAAQ,WAAW,GAAG;AAC/B,cAAM,eAAe,YAAY,IAAI,CAAC,UAAU;AAC/C,cAAI,OAAO,UAAU,UAAU;AAC9B,mBAAO;AAAA,UACR;AAEA,cAAI,OAAO,UAAU,WAAW;AAC/B,oBAAQ,OAAO,KAAK;AAAA,UACrB;AAEA,cAAI,OAAO,UAAU,UAAU;AAC9B,oBAAQ,KAAK,UAAU,KAAK;AAAA,UAC7B;AAEA,cAAI,OAAO,UAAU,UAAU;AAC9B,mBAAO,IAAI,MAAM,QAAQ,MAAM,KAAK,CAAC;AAAA,UACtC;AAEA,iBAAO;AAAA,QACR,CAAC;AAGD,aAAK,WAAW,WAAW,IAAI,IAAI,aAAa,KAAK,GAAG,CAAC;AAAA,MAC1D,OAAO;AACN,YAAI,WAAW,gBAAgB,MAAM;AACpC,gBAAM,IAAI;AAAA,YACT;AAAA,YACA,WAAW,WAAW,WAAW;AAAA,YACjC;AAAA,cACC;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AAEO,SAAS,kBACf,SACA,OACA,WACA,aACC;AACD,MAAI,cAAc,YAAY,MAAM,SAAS,KAAK,CAAC,aAAa;AAC/D,YAAQ,kBAAkB;AAAA,MACzB,SAAS,iBAAiB,MAAM,MAAM;AAAA,MACtC,UAAU;AAAA,IACX,CAAC;AAAA,EACF;AAEA,MACC,cAAc,kBACd,MAAM,SAAS,KACd,QAAQ,iBAAiB,yBAAyB,GAAG,QAAQ,MAAiB,YAC9E,QAAQ,iBAAiB,SAAS,GAAG,EAAE,EAAa,YAAY,EAAE,WAAW,QAAQ,GACrF;AACD,YAAQ,kBAAkB;AAAA,MACzB,SACC;AAAA,MACD,UAAU;AAAA,IACX,CAAC;AAAA,EACF;AACD;", "names": []}