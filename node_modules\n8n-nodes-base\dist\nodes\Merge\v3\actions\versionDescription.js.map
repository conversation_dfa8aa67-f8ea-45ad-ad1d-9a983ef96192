{"version": 3, "sources": ["../../../../../nodes/Merge/v3/actions/versionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport { NodeConnectionTypes, type INodeTypeDescription } from 'n8n-workflow';\n\nimport * as mode from './mode';\nimport { configuredInputs } from '../helpers/utils';\n\nexport const versionDescription: INodeTypeDescription = {\n\tdisplayName: 'Merge',\n\tname: 'merge',\n\tgroup: ['transform'],\n\tdescription: 'Merges data of multiple streams once data from both is available',\n\tversion: [3, 3.1],\n\tdefaults: {\n\t\tname: 'Merge',\n\t},\n\tinputs: `={{(${configuredInputs})($parameter)}}`,\n\toutputs: [NodeConnectionTypes.Main],\n\t// If mode is chooseBranch data from both branches is required\n\t// to continue, else data from any input suffices\n\trequiredInputs: '={{ $parameter[\"mode\"] === \"chooseBranch\" ? [0, 1] : 1 }}',\n\tproperties: [...mode.description],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAA+D;AAE/D,WAAsB;AACtB,mBAAiC;AAE1B,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,OAAO,CAAC,WAAW;AAAA,EACnB,aAAa;AAAA,EACb,SAAS,CAAC,GAAG,GAAG;AAAA,EAChB,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,QAAQ,OAAO,6BAAgB;AAAA,EAC/B,SAAS,CAAC,wCAAoB,IAAI;AAAA;AAAA;AAAA,EAGlC,gBAAgB;AAAA,EAChB,YAAY,CAAC,GAAG,KAAK,WAAW;AACjC;", "names": []}