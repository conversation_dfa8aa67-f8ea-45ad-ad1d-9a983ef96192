{"version": 3, "sources": ["../../../../../nodes/Discord/v2/helpers/utils.ts"], "sourcesContent": ["import FormData from 'form-data';\nimport { isEmpty } from 'lodash';\nimport { extension } from 'mime-types';\nimport type {\n\tIBinaryKeyData,\n\tIDataObject,\n\tIExecuteFunctions,\n\tINode,\n\tINodeExecutionData,\n} from 'n8n-workflow';\nimport { jsonParse, NodeApiError, NodeOperationError } from 'n8n-workflow';\n\nimport { getSendAndWaitConfig } from '../../../../utils/sendAndWait/utils';\nimport { capitalize, createUtmCampaignLink } from '../../../../utils/utilities';\nimport { discordApiMultiPartRequest, discordApiRequest } from '../transport';\n\nexport const createSimplifyFunction =\n\t(includedFields: string[]) =>\n\t(item: IDataObject): IDataObject => {\n\t\tconst result: IDataObject = {};\n\n\t\tfor (const field of includedFields) {\n\t\t\tif (item[field] === undefined) continue;\n\n\t\t\tresult[field] = item[field];\n\t\t}\n\n\t\treturn result;\n\t};\n\nexport function parseDiscordError(this: IExecuteFunctions, error: any, itemIndex = 0) {\n\tlet errorData = error.cause.error;\n\tconst errorOptions: IDataObject = { itemIndex };\n\n\tif (!errorData && error.description) {\n\t\ttry {\n\t\t\tconst errorString = (error.description as string).split(' - ')[1];\n\t\t\tif (errorString) {\n\t\t\t\terrorData = jsonParse(errorString);\n\t\t\t}\n\t\t} catch (err) {}\n\t}\n\n\tif (errorData?.message) {\n\t\terrorOptions.message = errorData.message;\n\t}\n\n\tif ((error?.message as string)?.toLowerCase()?.includes('bad request') && errorData) {\n\t\tif (errorData?.message) {\n\t\t\terrorOptions.message = errorData.message;\n\t\t}\n\n\t\tif (errorData?.errors?.embeds) {\n\t\t\tconst embedErrors = errorData.errors.embeds?.[0];\n\t\t\tconst embedErrorsKeys = Object.keys(embedErrors).map((key) => capitalize(key));\n\n\t\t\tif (embedErrorsKeys.length) {\n\t\t\t\tconst message =\n\t\t\t\t\tembedErrorsKeys.length === 1\n\t\t\t\t\t\t? `The parameter ${embedErrorsKeys[0]} is not properly formatted`\n\t\t\t\t\t\t: `The parameters ${embedErrorsKeys.join(', ')} are not properly formatted`;\n\t\t\t\terrorOptions.message = message;\n\t\t\t\terrorOptions.description = 'Review the formatting or clear it';\n\t\t\t}\n\n\t\t\treturn new NodeOperationError(this.getNode(), errorData.errors, errorOptions);\n\t\t}\n\n\t\tif (errorData?.errors?.message_reference) {\n\t\t\terrorOptions.message = \"The message to reply to ID can't be found\";\n\t\t\terrorOptions.description =\n\t\t\t\t'Check the \"Message to Reply to\" parameter and remove it if you don\\'t want to reply to an existing message';\n\n\t\t\treturn new NodeOperationError(this.getNode(), errorData.errors, errorOptions);\n\t\t}\n\n\t\tif (errorOptions.message === 'Cannot send an empty message') {\n\t\t\terrorOptions.description =\n\t\t\t\t'Something has to be send to the channel whether it is a message, an embed or a file';\n\t\t}\n\t}\n\treturn new NodeOperationError(this.getNode(), errorData || error, errorOptions);\n}\n\nexport function prepareErrorData(this: IExecuteFunctions, error: any, i: number) {\n\tlet description = error.description;\n\n\ttry {\n\t\tdescription = JSON.parse(error.description as string);\n\t} catch (err) {}\n\n\treturn this.helpers.constructExecutionMetaData(\n\t\tthis.helpers.returnJsonArray({ error: error.message, description }),\n\t\t{ itemData: { item: i } },\n\t);\n}\n\nexport function prepareOptions(options: IDataObject, guildId?: string) {\n\tif (options.flags) {\n\t\tif ((options.flags as string[]).length === 2) {\n\t\t\toptions.flags = (1 << 2) + (1 << 12);\n\t\t} else if ((options.flags as string[]).includes('SUPPRESS_EMBEDS')) {\n\t\t\toptions.flags = 1 << 2;\n\t\t} else if ((options.flags as string[]).includes('SUPPRESS_NOTIFICATIONS')) {\n\t\t\toptions.flags = 1 << 12;\n\t\t}\n\t}\n\n\tif (options.message_reference) {\n\t\toptions.message_reference = {\n\t\t\tmessage_id: options.message_reference,\n\t\t\tguild_id: guildId,\n\t\t};\n\t}\n\n\treturn options;\n}\n\nexport function prepareEmbeds(this: IExecuteFunctions, embeds: IDataObject[]) {\n\treturn embeds\n\t\t.map((embed) => {\n\t\t\tlet embedReturnData: IDataObject = {};\n\n\t\t\tif (embed.inputMethod === 'json') {\n\t\t\t\tif (typeof embed.json === 'object') {\n\t\t\t\t\tembedReturnData = embed.json as IDataObject;\n\t\t\t\t}\n\t\t\t\ttry {\n\t\t\t\t\tembedReturnData = jsonParse(embed.json as string);\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthrow new NodeOperationError(this.getNode(), 'Not a valid JSON', error);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tdelete embed.inputMethod;\n\n\t\t\t\tfor (const key of Object.keys(embed)) {\n\t\t\t\t\tif (embed[key] !== '') {\n\t\t\t\t\t\tembedReturnData[key] = embed[key];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (embedReturnData.author) {\n\t\t\t\tembedReturnData.author = {\n\t\t\t\t\tname: embedReturnData.author,\n\t\t\t\t};\n\t\t\t}\n\t\t\tif (embedReturnData.color && typeof embedReturnData.color === 'string') {\n\t\t\t\tembedReturnData.color = parseInt(embedReturnData.color.replace('#', ''), 16);\n\t\t\t}\n\t\t\tif (embedReturnData.video) {\n\t\t\t\tembedReturnData.video = {\n\t\t\t\t\turl: embedReturnData.video,\n\t\t\t\t\twidth: 1270,\n\t\t\t\t\theight: 720,\n\t\t\t\t};\n\t\t\t}\n\t\t\tif (embedReturnData.thumbnail) {\n\t\t\t\tembedReturnData.thumbnail = {\n\t\t\t\t\turl: embedReturnData.thumbnail,\n\t\t\t\t};\n\t\t\t}\n\t\t\tif (embedReturnData.image) {\n\t\t\t\tembedReturnData.image = {\n\t\t\t\t\turl: embedReturnData.image,\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn embedReturnData;\n\t\t})\n\t\t.filter((embed) => !isEmpty(embed));\n}\n\nexport async function prepareMultiPartForm(\n\tthis: IExecuteFunctions,\n\titems: INodeExecutionData[],\n\tfiles: IDataObject[],\n\tjsonPayload: IDataObject,\n\ti: number,\n) {\n\tconst multiPartBody = new FormData();\n\tconst attachments: IDataObject[] = [];\n\tconst filesData: IDataObject[] = [];\n\n\tfor (const [index, file] of files.entries()) {\n\t\tconst binaryData = (items[i].binary as IBinaryKeyData)?.[file.inputFieldName as string];\n\n\t\tif (!binaryData) {\n\t\t\tthrow new NodeOperationError(\n\t\t\t\tthis.getNode(),\n\t\t\t\t`Input item [${i}] does not contain binary data on property ${file.inputFieldName}`,\n\t\t\t);\n\t\t}\n\n\t\tlet filename = binaryData.fileName as string;\n\n\t\tif (!filename.includes('.')) {\n\t\t\tif (binaryData.fileExtension) {\n\t\t\t\tfilename += `.${binaryData.fileExtension}`;\n\t\t\t}\n\t\t\tif (binaryData.mimeType) {\n\t\t\t\tfilename += `.${extension(binaryData.mimeType)}`;\n\t\t\t}\n\t\t}\n\n\t\tattachments.push({\n\t\t\tid: index,\n\t\t\tfilename,\n\t\t});\n\n\t\tfilesData.push({\n\t\t\tdata: await this.helpers.getBinaryDataBuffer(i, file.inputFieldName as string),\n\t\t\tname: filename,\n\t\t\tmime: binaryData.mimeType,\n\t\t});\n\t}\n\n\tmultiPartBody.append('payload_json', JSON.stringify({ ...jsonPayload, attachments }), {\n\t\tcontentType: 'application/json',\n\t});\n\n\tfor (const [index, binaryData] of filesData.entries()) {\n\t\tmultiPartBody.append(`files[${index}]`, binaryData.data, {\n\t\t\tcontentType: binaryData.name as string,\n\t\t\tfilename: binaryData.mime as string,\n\t\t});\n\t}\n\n\treturn multiPartBody;\n}\n\nexport function checkAccessToGuild(\n\tnode: INode,\n\tguildId: string,\n\tuserGuilds: IDataObject[],\n\titemIndex = 0,\n) {\n\tif (!userGuilds.some((guild) => guild.id === guildId)) {\n\t\tthrow new NodeOperationError(\n\t\t\tnode,\n\t\t\t`You do not have access to the guild with the id ${guildId}`,\n\t\t\t{\n\t\t\t\titemIndex,\n\t\t\t\tlevel: 'warning',\n\t\t\t},\n\t\t);\n\t}\n}\n\nexport async function checkAccessToChannel(\n\tthis: IExecuteFunctions,\n\tchannelId: string,\n\tuserGuilds: IDataObject[],\n\titemIndex = 0,\n) {\n\tlet guildId = '';\n\n\ttry {\n\t\tconst channel = await discordApiRequest.call(this, 'GET', `/channels/${channelId}`);\n\t\tguildId = channel.guild_id;\n\t} catch (error) {}\n\n\tif (!guildId) {\n\t\tthrow new NodeOperationError(\n\t\t\tthis.getNode(),\n\t\t\t`Could not find server for channel with the id ${channelId}`,\n\t\t\t{\n\t\t\t\titemIndex,\n\t\t\t},\n\t\t);\n\t}\n\n\tcheckAccessToGuild(this.getNode(), guildId, userGuilds, itemIndex);\n}\n\nexport async function setupChannelGetter(this: IExecuteFunctions, userGuilds: IDataObject[]) {\n\tconst isOAuth2 = this.getNodeParameter('authentication', 0) === 'oAuth2';\n\n\treturn async (i: number) => {\n\t\tconst channelId = this.getNodeParameter('channelId', i, undefined, {\n\t\t\textractValue: true,\n\t\t}) as string;\n\n\t\tif (isOAuth2) await checkAccessToChannel.call(this, channelId, userGuilds, i);\n\n\t\treturn channelId;\n\t};\n}\n\nexport async function sendDiscordMessage(\n\tthis: IExecuteFunctions,\n\t{\n\t\tguildId,\n\t\tuserGuilds,\n\t\tisOAuth2,\n\t\tbody,\n\t\titems,\n\t\tfiles = [],\n\t\titemIndex = 0,\n\t}: {\n\t\tguildId: string;\n\t\tuserGuilds: IDataObject[];\n\t\tisOAuth2: boolean;\n\t\tbody: IDataObject;\n\t\titems: INodeExecutionData[];\n\t\tfiles?: IDataObject[];\n\t\titemIndex?: number;\n\t},\n) {\n\tconst sendTo = this.getNodeParameter('sendTo', itemIndex) as string;\n\n\tlet channelId = '';\n\n\tif (sendTo === 'user') {\n\t\tconst userId = this.getNodeParameter('userId', itemIndex, undefined, {\n\t\t\textractValue: true,\n\t\t}) as string;\n\n\t\tif (isOAuth2) {\n\t\t\ttry {\n\t\t\t\tawait discordApiRequest.call(this, 'GET', `/guilds/${guildId}/members/${userId}`);\n\t\t\t} catch (error) {\n\t\t\t\tif (error instanceof NodeApiError && error.httpCode === '404') {\n\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t`User with the id ${userId} is not a member of the selected guild`,\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\titemIndex,\n\t\t\t\t\t\t},\n\t\t\t\t\t);\n\t\t\t\t}\n\n\t\t\t\tthrow new NodeOperationError(this.getNode(), error, {\n\t\t\t\t\titemIndex,\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tchannelId = (\n\t\t\t(await discordApiRequest.call(this, 'POST', '/users/@me/channels', {\n\t\t\t\trecipient_id: userId,\n\t\t\t})) as IDataObject\n\t\t).id as string;\n\n\t\tif (!channelId) {\n\t\t\tthrow new NodeOperationError(\n\t\t\t\tthis.getNode(),\n\t\t\t\t'Could not create a channel to send direct message to',\n\t\t\t\t{ itemIndex },\n\t\t\t);\n\t\t}\n\t}\n\n\tif (sendTo === 'channel') {\n\t\tchannelId = this.getNodeParameter('channelId', itemIndex, undefined, {\n\t\t\textractValue: true,\n\t\t}) as string;\n\t}\n\n\tif (isOAuth2 && sendTo !== 'user') {\n\t\tawait checkAccessToChannel.call(this, channelId, userGuilds, itemIndex);\n\t}\n\n\tif (!channelId) {\n\t\tthrow new NodeOperationError(this.getNode(), 'Channel ID is required', {\n\t\t\titemIndex,\n\t\t});\n\t}\n\n\tlet response: IDataObject[] = [];\n\n\tif (files?.length) {\n\t\tconst multiPartBody = await prepareMultiPartForm.call(this, items, files, body, itemIndex);\n\n\t\tresponse = await discordApiMultiPartRequest.call(\n\t\t\tthis,\n\t\t\t'POST',\n\t\t\t`/channels/${channelId}/messages`,\n\t\t\tmultiPartBody,\n\t\t);\n\t} else {\n\t\tresponse = await discordApiRequest.call(this, 'POST', `/channels/${channelId}/messages`, body);\n\t}\n\n\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\tthis.helpers.returnJsonArray(response),\n\t\t{ itemData: { item: itemIndex } },\n\t);\n\n\treturn executionData;\n}\n\nexport function createSendAndWaitMessageBody(context: IExecuteFunctions) {\n\tconst config = getSendAndWaitConfig(context);\n\tlet description = config.message;\n\tif (config.appendAttribution !== false) {\n\t\tconst instanceId = context.getInstanceId();\n\t\tconst attributionText = 'This message was sent automatically with ';\n\t\tconst link = createUtmCampaignLink('n8n-nodes-base.discord', instanceId);\n\t\tdescription = `${config.message}\\n\\n_${attributionText}_[n8n](${link})`;\n\t}\n\n\tconst body = {\n\t\tembeds: [\n\t\t\t{\n\t\t\t\tdescription,\n\t\t\t\tcolor: 5814783,\n\t\t\t},\n\t\t],\n\t\tcomponents: [\n\t\t\t{\n\t\t\t\ttype: 1,\n\t\t\t\tcomponents: config.options.map((option) => {\n\t\t\t\t\treturn {\n\t\t\t\t\t\ttype: 2,\n\t\t\t\t\t\tstyle: 5,\n\t\t\t\t\t\tlabel: option.label,\n\t\t\t\t\t\turl: `${config.url}?approved=${option.value}`,\n\t\t\t\t\t};\n\t\t\t\t}),\n\t\t\t},\n\t\t],\n\t};\n\n\treturn body;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAAqB;AACrB,oBAAwB;AACxB,wBAA0B;AAQ1B,0BAA4D;AAE5D,mBAAqC;AACrC,uBAAkD;AAClD,uBAA8D;AAEvD,MAAM,yBACZ,CAAC,mBACD,CAAC,SAAmC;AACnC,QAAM,SAAsB,CAAC;AAE7B,aAAW,SAAS,gBAAgB;AACnC,QAAI,KAAK,KAAK,MAAM,OAAW;AAE/B,WAAO,KAAK,IAAI,KAAK,KAAK;AAAA,EAC3B;AAEA,SAAO;AACR;AAEM,SAAS,kBAA2C,OAAY,YAAY,GAAG;AACrF,MAAI,YAAY,MAAM,MAAM;AAC5B,QAAM,eAA4B,EAAE,UAAU;AAE9C,MAAI,CAAC,aAAa,MAAM,aAAa;AACpC,QAAI;AACH,YAAM,cAAe,MAAM,YAAuB,MAAM,KAAK,EAAE,CAAC;AAChE,UAAI,aAAa;AAChB,wBAAY,+BAAU,WAAW;AAAA,MAClC;AAAA,IACD,SAAS,KAAK;AAAA,IAAC;AAAA,EAChB;AAEA,MAAI,WAAW,SAAS;AACvB,iBAAa,UAAU,UAAU;AAAA,EAClC;AAEA,MAAK,OAAO,SAAoB,YAAY,GAAG,SAAS,aAAa,KAAK,WAAW;AACpF,QAAI,WAAW,SAAS;AACvB,mBAAa,UAAU,UAAU;AAAA,IAClC;AAEA,QAAI,WAAW,QAAQ,QAAQ;AAC9B,YAAM,cAAc,UAAU,OAAO,SAAS,CAAC;AAC/C,YAAM,kBAAkB,OAAO,KAAK,WAAW,EAAE,IAAI,CAAC,YAAQ,6BAAW,GAAG,CAAC;AAE7E,UAAI,gBAAgB,QAAQ;AAC3B,cAAM,UACL,gBAAgB,WAAW,IACxB,iBAAiB,gBAAgB,CAAC,CAAC,+BACnC,kBAAkB,gBAAgB,KAAK,IAAI,CAAC;AAChD,qBAAa,UAAU;AACvB,qBAAa,cAAc;AAAA,MAC5B;AAEA,aAAO,IAAI,uCAAmB,KAAK,QAAQ,GAAG,UAAU,QAAQ,YAAY;AAAA,IAC7E;AAEA,QAAI,WAAW,QAAQ,mBAAmB;AACzC,mBAAa,UAAU;AACvB,mBAAa,cACZ;AAED,aAAO,IAAI,uCAAmB,KAAK,QAAQ,GAAG,UAAU,QAAQ,YAAY;AAAA,IAC7E;AAEA,QAAI,aAAa,YAAY,gCAAgC;AAC5D,mBAAa,cACZ;AAAA,IACF;AAAA,EACD;AACA,SAAO,IAAI,uCAAmB,KAAK,QAAQ,GAAG,aAAa,OAAO,YAAY;AAC/E;AAEO,SAAS,iBAA0C,OAAY,GAAW;AAChF,MAAI,cAAc,MAAM;AAExB,MAAI;AACH,kBAAc,KAAK,MAAM,MAAM,WAAqB;AAAA,EACrD,SAAS,KAAK;AAAA,EAAC;AAEf,SAAO,KAAK,QAAQ;AAAA,IACnB,KAAK,QAAQ,gBAAgB,EAAE,OAAO,MAAM,SAAS,YAAY,CAAC;AAAA,IAClE,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,EACzB;AACD;AAEO,SAAS,eAAe,SAAsB,SAAkB;AACtE,MAAI,QAAQ,OAAO;AAClB,QAAK,QAAQ,MAAmB,WAAW,GAAG;AAC7C,cAAQ,SAAS,KAAK,MAAM,KAAK;AAAA,IAClC,WAAY,QAAQ,MAAmB,SAAS,iBAAiB,GAAG;AACnE,cAAQ,QAAQ,KAAK;AAAA,IACtB,WAAY,QAAQ,MAAmB,SAAS,wBAAwB,GAAG;AAC1E,cAAQ,QAAQ,KAAK;AAAA,IACtB;AAAA,EACD;AAEA,MAAI,QAAQ,mBAAmB;AAC9B,YAAQ,oBAAoB;AAAA,MAC3B,YAAY,QAAQ;AAAA,MACpB,UAAU;AAAA,IACX;AAAA,EACD;AAEA,SAAO;AACR;AAEO,SAAS,cAAuC,QAAuB;AAC7E,SAAO,OACL,IAAI,CAAC,UAAU;AACf,QAAI,kBAA+B,CAAC;AAEpC,QAAI,MAAM,gBAAgB,QAAQ;AACjC,UAAI,OAAO,MAAM,SAAS,UAAU;AACnC,0BAAkB,MAAM;AAAA,MACzB;AACA,UAAI;AACH,8BAAkB,+BAAU,MAAM,IAAc;AAAA,MACjD,SAAS,OAAO;AACf,cAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,oBAAoB,KAAK;AAAA,MACvE;AAAA,IACD,OAAO;AACN,aAAO,MAAM;AAEb,iBAAW,OAAO,OAAO,KAAK,KAAK,GAAG;AACrC,YAAI,MAAM,GAAG,MAAM,IAAI;AACtB,0BAAgB,GAAG,IAAI,MAAM,GAAG;AAAA,QACjC;AAAA,MACD;AAAA,IACD;AAEA,QAAI,gBAAgB,QAAQ;AAC3B,sBAAgB,SAAS;AAAA,QACxB,MAAM,gBAAgB;AAAA,MACvB;AAAA,IACD;AACA,QAAI,gBAAgB,SAAS,OAAO,gBAAgB,UAAU,UAAU;AACvE,sBAAgB,QAAQ,SAAS,gBAAgB,MAAM,QAAQ,KAAK,EAAE,GAAG,EAAE;AAAA,IAC5E;AACA,QAAI,gBAAgB,OAAO;AAC1B,sBAAgB,QAAQ;AAAA,QACvB,KAAK,gBAAgB;AAAA,QACrB,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,IACD;AACA,QAAI,gBAAgB,WAAW;AAC9B,sBAAgB,YAAY;AAAA,QAC3B,KAAK,gBAAgB;AAAA,MACtB;AAAA,IACD;AACA,QAAI,gBAAgB,OAAO;AAC1B,sBAAgB,QAAQ;AAAA,QACvB,KAAK,gBAAgB;AAAA,MACtB;AAAA,IACD;AAEA,WAAO;AAAA,EACR,CAAC,EACA,OAAO,CAAC,UAAU,KAAC,uBAAQ,KAAK,CAAC;AACpC;AAEA,eAAsB,qBAErB,OACA,OACA,aACA,GACC;AACD,QAAM,gBAAgB,IAAI,iBAAAA,QAAS;AACnC,QAAM,cAA6B,CAAC;AACpC,QAAM,YAA2B,CAAC;AAElC,aAAW,CAAC,OAAO,IAAI,KAAK,MAAM,QAAQ,GAAG;AAC5C,UAAM,aAAc,MAAM,CAAC,EAAE,SAA4B,KAAK,cAAwB;AAEtF,QAAI,CAAC,YAAY;AAChB,YAAM,IAAI;AAAA,QACT,KAAK,QAAQ;AAAA,QACb,eAAe,CAAC,8CAA8C,KAAK,cAAc;AAAA,MAClF;AAAA,IACD;AAEA,QAAI,WAAW,WAAW;AAE1B,QAAI,CAAC,SAAS,SAAS,GAAG,GAAG;AAC5B,UAAI,WAAW,eAAe;AAC7B,oBAAY,IAAI,WAAW,aAAa;AAAA,MACzC;AACA,UAAI,WAAW,UAAU;AACxB,oBAAY,QAAI,6BAAU,WAAW,QAAQ,CAAC;AAAA,MAC/C;AAAA,IACD;AAEA,gBAAY,KAAK;AAAA,MAChB,IAAI;AAAA,MACJ;AAAA,IACD,CAAC;AAED,cAAU,KAAK;AAAA,MACd,MAAM,MAAM,KAAK,QAAQ,oBAAoB,GAAG,KAAK,cAAwB;AAAA,MAC7E,MAAM;AAAA,MACN,MAAM,WAAW;AAAA,IAClB,CAAC;AAAA,EACF;AAEA,gBAAc,OAAO,gBAAgB,KAAK,UAAU,EAAE,GAAG,aAAa,YAAY,CAAC,GAAG;AAAA,IACrF,aAAa;AAAA,EACd,CAAC;AAED,aAAW,CAAC,OAAO,UAAU,KAAK,UAAU,QAAQ,GAAG;AACtD,kBAAc,OAAO,SAAS,KAAK,KAAK,WAAW,MAAM;AAAA,MACxD,aAAa,WAAW;AAAA,MACxB,UAAU,WAAW;AAAA,IACtB,CAAC;AAAA,EACF;AAEA,SAAO;AACR;AAEO,SAAS,mBACf,MACA,SACA,YACA,YAAY,GACX;AACD,MAAI,CAAC,WAAW,KAAK,CAAC,UAAU,MAAM,OAAO,OAAO,GAAG;AACtD,UAAM,IAAI;AAAA,MACT;AAAA,MACA,mDAAmD,OAAO;AAAA,MAC1D;AAAA,QACC;AAAA,QACA,OAAO;AAAA,MACR;AAAA,IACD;AAAA,EACD;AACD;AAEA,eAAsB,qBAErB,WACA,YACA,YAAY,GACX;AACD,MAAI,UAAU;AAEd,MAAI;AACH,UAAM,UAAU,MAAM,mCAAkB,KAAK,MAAM,OAAO,aAAa,SAAS,EAAE;AAClF,cAAU,QAAQ;AAAA,EACnB,SAAS,OAAO;AAAA,EAAC;AAEjB,MAAI,CAAC,SAAS;AACb,UAAM,IAAI;AAAA,MACT,KAAK,QAAQ;AAAA,MACb,iDAAiD,SAAS;AAAA,MAC1D;AAAA,QACC;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,qBAAmB,KAAK,QAAQ,GAAG,SAAS,YAAY,SAAS;AAClE;AAEA,eAAsB,mBAA4C,YAA2B;AAC5F,QAAM,WAAW,KAAK,iBAAiB,kBAAkB,CAAC,MAAM;AAEhE,SAAO,OAAO,MAAc;AAC3B,UAAM,YAAY,KAAK,iBAAiB,aAAa,GAAG,QAAW;AAAA,MAClE,cAAc;AAAA,IACf,CAAC;AAED,QAAI,SAAU,OAAM,qBAAqB,KAAK,MAAM,WAAW,YAAY,CAAC;AAE5E,WAAO;AAAA,EACR;AACD;AAEA,eAAsB,mBAErB;AAAA,EACC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ,CAAC;AAAA,EACT,YAAY;AACb,GASC;AACD,QAAM,SAAS,KAAK,iBAAiB,UAAU,SAAS;AAExD,MAAI,YAAY;AAEhB,MAAI,WAAW,QAAQ;AACtB,UAAM,SAAS,KAAK,iBAAiB,UAAU,WAAW,QAAW;AAAA,MACpE,cAAc;AAAA,IACf,CAAC;AAED,QAAI,UAAU;AACb,UAAI;AACH,cAAM,mCAAkB,KAAK,MAAM,OAAO,WAAW,OAAO,YAAY,MAAM,EAAE;AAAA,MACjF,SAAS,OAAO;AACf,YAAI,iBAAiB,oCAAgB,MAAM,aAAa,OAAO;AAC9D,gBAAM,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,oBAAoB,MAAM;AAAA,YAC1B;AAAA,cACC;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAEA,cAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,OAAO;AAAA,UACnD;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD;AAEA,iBACE,MAAM,mCAAkB,KAAK,MAAM,QAAQ,uBAAuB;AAAA,MAClE,cAAc;AAAA,IACf,CAAC,GACA;AAEF,QAAI,CAAC,WAAW;AACf,YAAM,IAAI;AAAA,QACT,KAAK,QAAQ;AAAA,QACb;AAAA,QACA,EAAE,UAAU;AAAA,MACb;AAAA,IACD;AAAA,EACD;AAEA,MAAI,WAAW,WAAW;AACzB,gBAAY,KAAK,iBAAiB,aAAa,WAAW,QAAW;AAAA,MACpE,cAAc;AAAA,IACf,CAAC;AAAA,EACF;AAEA,MAAI,YAAY,WAAW,QAAQ;AAClC,UAAM,qBAAqB,KAAK,MAAM,WAAW,YAAY,SAAS;AAAA,EACvE;AAEA,MAAI,CAAC,WAAW;AACf,UAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,0BAA0B;AAAA,MACtE;AAAA,IACD,CAAC;AAAA,EACF;AAEA,MAAI,WAA0B,CAAC;AAE/B,MAAI,OAAO,QAAQ;AAClB,UAAM,gBAAgB,MAAM,qBAAqB,KAAK,MAAM,OAAO,OAAO,MAAM,SAAS;AAEzF,eAAW,MAAM,4CAA2B;AAAA,MAC3C;AAAA,MACA;AAAA,MACA,aAAa,SAAS;AAAA,MACtB;AAAA,IACD;AAAA,EACD,OAAO;AACN,eAAW,MAAM,mCAAkB,KAAK,MAAM,QAAQ,aAAa,SAAS,aAAa,IAAI;AAAA,EAC9F;AAEA,QAAM,gBAAgB,KAAK,QAAQ;AAAA,IAClC,KAAK,QAAQ,gBAAgB,QAAQ;AAAA,IACrC,EAAE,UAAU,EAAE,MAAM,UAAU,EAAE;AAAA,EACjC;AAEA,SAAO;AACR;AAEO,SAAS,6BAA6B,SAA4B;AACxE,QAAM,aAAS,mCAAqB,OAAO;AAC3C,MAAI,cAAc,OAAO;AACzB,MAAI,OAAO,sBAAsB,OAAO;AACvC,UAAM,aAAa,QAAQ,cAAc;AACzC,UAAM,kBAAkB;AACxB,UAAM,WAAO,wCAAsB,0BAA0B,UAAU;AACvE,kBAAc,GAAG,OAAO,OAAO;AAAA;AAAA,GAAQ,eAAe,UAAU,IAAI;AAAA,EACrE;AAEA,QAAM,OAAO;AAAA,IACZ,QAAQ;AAAA,MACP;AAAA,QACC;AAAA,QACA,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,YAAY;AAAA,MACX;AAAA,QACC,MAAM;AAAA,QACN,YAAY,OAAO,QAAQ,IAAI,CAAC,WAAW;AAC1C,iBAAO;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,YACP,OAAO,OAAO;AAAA,YACd,KAAK,GAAG,OAAO,GAAG,aAAa,OAAO,KAAK;AAAA,UAC5C;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;", "names": ["FormData"]}