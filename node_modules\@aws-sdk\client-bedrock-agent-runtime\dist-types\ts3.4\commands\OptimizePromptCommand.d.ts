import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  BedrockAgentRuntimeClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../BedrockAgentRuntimeClient";
import {
  OptimizePromptRequest,
  OptimizePromptResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface OptimizePromptCommandInput extends OptimizePromptRequest {}
export interface OptimizePromptCommandOutput
  extends OptimizePromptResponse,
    __MetadataBearer {}
declare const OptimizePromptCommand_base: {
  new (
    input: OptimizePromptCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    OptimizePromptCommandInput,
    OptimizePromptCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: OptimizePromptCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    OptimizePromptCommandInput,
    OptimizePromptCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class OptimizePromptCommand extends OptimizePromptCommand_base {
  protected static __types: {
    api: {
      input: OptimizePromptRequest;
      output: OptimizePromptResponse;
    };
    sdk: {
      input: OptimizePromptCommandInput;
      output: OptimizePromptCommandOutput;
    };
  };
}
