{"version": 3, "sources": ["../../../../../../nodes/Microsoft/Excel/v2/actions/versionDescription.ts"], "sourcesContent": ["/* eslint-disable n8n-nodes-base/node-filename-against-convention */\nimport { NodeConnectionTypes, type INodeTypeDescription } from 'n8n-workflow';\n\nimport * as table from './table/Table.resource';\nimport * as workbook from './workbook/Workbook.resource';\nimport * as worksheet from './worksheet/Worksheet.resource';\n\nexport const versionDescription: INodeTypeDescription = {\n\tdisplayName: 'Microsoft Excel 365',\n\tname: 'microsoftExcel',\n\ticon: 'file:excel.svg',\n\tgroup: ['input'],\n\tversion: [2, 2.1],\n\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\tdescription: 'Consume Microsoft Excel API',\n\tdefaults: {\n\t\tname: 'Microsoft Excel 365',\n\t},\n\tinputs: [NodeConnectionTypes.Main],\n\toutputs: [NodeConnectionTypes.Main],\n\tcredentials: [\n\t\t{\n\t\t\tname: 'microsoftExcelOAuth2Api',\n\t\t\trequired: true,\n\t\t},\n\t],\n\tproperties: [\n\t\t{\n\t\t\tdisplayName:\n\t\t\t\t'This node connects to the Microsoft 365 cloud platform. Use the \\'Extract from File\\' and \\'Convert to File\\' nodes to directly manipulate spreadsheet files (.xls, .csv, etc). <a href=\"https://n8n.io/workflows/890-read-in-an-excel-spreadsheet-file/\" target=\"_blank\">More info</a>.',\n\t\t\tname: 'notice',\n\t\t\ttype: 'notice',\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Resource',\n\t\t\tname: 'resource',\n\t\t\ttype: 'options',\n\t\t\tnoDataExpression: true,\n\t\t\toptions: [\n\t\t\t\t{\n\t\t\t\t\tname: 'Table',\n\t\t\t\t\tvalue: 'table',\n\t\t\t\t\tdescription: 'Represents an Excel table',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Workbook',\n\t\t\t\t\tvalue: 'workbook',\n\t\t\t\t\tdescription: 'A workbook is the top level object which contains one or more worksheets',\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: 'Sheet',\n\t\t\t\t\tvalue: 'worksheet',\n\t\t\t\t\tdescription: 'A sheet is a grid of cells which can contain data, tables, charts, etc',\n\t\t\t\t},\n\t\t\t],\n\t\t\tdefault: 'workbook',\n\t\t},\n\t\t...table.description,\n\t\t...workbook.description,\n\t\t...worksheet.description,\n\t],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAA+D;AAE/D,YAAuB;AACvB,eAA0B;AAC1B,gBAA2B;AAEpB,MAAM,qBAA2C;AAAA,EACvD,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,OAAO;AAAA,EACf,SAAS,CAAC,GAAG,GAAG;AAAA,EAChB,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,EACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,EAClC,aAAa;AAAA,IACZ;AAAA,MACC,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA,EACD;AAAA,EACA,YAAY;AAAA,IACX;AAAA,MACC,aACC;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACA;AAAA,MACC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,IACA,GAAG,MAAM;AAAA,IACT,GAAG,SAAS;AAAA,IACZ,GAAG,UAAU;AAAA,EACd;AACD;", "names": []}