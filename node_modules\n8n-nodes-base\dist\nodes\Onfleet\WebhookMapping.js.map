{"version": 3, "sources": ["../../../nodes/Onfleet/WebhookMapping.ts"], "sourcesContent": ["import type { OnfleetWebhooksMapping } from './interfaces';\n\nexport const webhookMapping: OnfleetWebhooksMapping = {\n\ttaskStarted: {\n\t\tname: 'Task Started',\n\t\tvalue: 'taskStarted',\n\t\tkey: 0,\n\t},\n\ttaskEta: {\n\t\tname: 'Task ETA',\n\t\tvalue: 'taskEta',\n\t\tkey: 1,\n\t},\n\ttaskArrival: {\n\t\tname: 'Task Arrival',\n\t\tvalue: 'taskArrival',\n\t\tkey: 2,\n\t},\n\ttaskCompleted: {\n\t\tname: 'Task Completed',\n\t\tvalue: 'taskCompleted',\n\t\tkey: 3,\n\t},\n\ttaskFailed: {\n\t\tname: 'Task Failed',\n\t\tvalue: 'taskFailed',\n\t\tkey: 4,\n\t},\n\tworkerDuty: {\n\t\tname: 'Worker Duty',\n\t\tvalue: 'workerDuty',\n\t\tkey: 5,\n\t},\n\ttaskCreated: {\n\t\tname: 'Task Created',\n\t\tvalue: 'taskCreated',\n\t\tkey: 6,\n\t},\n\ttaskUpdated: {\n\t\tname: 'Task Updated',\n\t\tvalue: 'taskUpdated',\n\t\tkey: 7,\n\t},\n\ttaskDeleted: {\n\t\tname: 'Task Deleted',\n\t\tvalue: 'taskDeleted',\n\t\tkey: 8,\n\t},\n\ttaskAssigned: {\n\t\tname: 'Task Assigned',\n\t\tvalue: 'taskAssigned',\n\t\tkey: 9,\n\t},\n\ttaskUnassigned: {\n\t\tname: 'Task Unassigned',\n\t\tvalue: 'taskUnassigned',\n\t\tkey: 10,\n\t},\n\ttaskDelayed: {\n\t\tname: 'Task Delayed',\n\t\tvalue: 'taskDelayed',\n\t\tkey: 12,\n\t},\n\ttaskCloned: {\n\t\tname: 'Task Cloned',\n\t\tvalue: 'taskCloned',\n\t\tkey: 13,\n\t},\n\tsmsRecipientResponseMissed: {\n\t\tname: 'SMS Recipient Response Missed',\n\t\tvalue: 'smsRecipientResponseMissed',\n\t\tkey: 14,\n\t},\n\tworkerCreated: {\n\t\tname: 'Worker Created',\n\t\tvalue: 'workerCreated',\n\t\tkey: 15,\n\t},\n\tworkerDeleted: {\n\t\tname: 'Worker Deleted',\n\t\tvalue: 'workerDeleted',\n\t\tkey: 16,\n\t},\n\tSMSRecipientOptOut: {\n\t\tname: 'SMS Recipient Opt Out',\n\t\tvalue: 'SMSRecipientOptOut',\n\t\tkey: 17,\n\t},\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,iBAAyC;AAAA,EACrD,aAAa;AAAA,IACZ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EACA,SAAS;AAAA,IACR,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EACA,aAAa;AAAA,IACZ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EACA,eAAe;AAAA,IACd,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EACA,YAAY;AAAA,IACX,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EACA,YAAY;AAAA,IACX,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EACA,aAAa;AAAA,IACZ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EACA,aAAa;AAAA,IACZ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EACA,aAAa;AAAA,IACZ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EACA,cAAc;AAAA,IACb,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EACA,gBAAgB;AAAA,IACf,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EACA,aAAa;AAAA,IACZ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EACA,YAAY;AAAA,IACX,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EACA,4BAA4B;AAAA,IAC3B,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EACA,eAAe;AAAA,IACd,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EACA,eAAe;AAAA,IACd,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EACA,oBAAoB;AAAA,IACnB,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AACD;", "names": []}