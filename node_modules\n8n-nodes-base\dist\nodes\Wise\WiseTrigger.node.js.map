{"version": 3, "sources": ["../../../nodes/Wise/WiseTrigger.node.ts"], "sourcesContent": ["import { createVerify } from 'crypto';\nimport type {\n\tIHookFunctions,\n\tIWebhookFunctions,\n\tIDataObject,\n\tILoadOptionsFunctions,\n\tINodeType,\n\tINodeTypeDescription,\n\tIWebhookResponseData,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes } from 'n8n-workflow';\n\nimport type { Profile } from './GenericFunctions';\nimport { getTriggerName, livePublicKey, testPublicKey, wiseApiRequest } from './GenericFunctions';\n\nexport class WiseTrigger implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Wise Trigger',\n\t\tname: 'wiseTrigger',\n\t\ticon: 'file:wise.svg',\n\t\tgroup: ['trigger'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"event\"]}}',\n\t\tdescription: 'Handle Wise events via webhooks',\n\t\tdefaults: {\n\t\t\tname: 'Wise Trigger',\n\t\t},\n\t\tinputs: [],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'wiseA<PERSON>',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\twebhooks: [\n\t\t\t{\n\t\t\t\tname: 'default',\n\t\t\t\thttpMethod: 'POST',\n\t\t\t\tresponseMode: 'onReceived',\n\t\t\t\tpath: 'webhook',\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Profile Name or ID',\n\t\t\t\tname: 'profileId',\n\t\t\t\ttype: 'options',\n\t\t\t\tdescription:\n\t\t\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\t\t\trequired: true,\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getProfiles',\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Event',\n\t\t\t\tname: 'event',\n\t\t\t\ttype: 'options',\n\t\t\t\trequired: true,\n\t\t\t\tdefault: '',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Balance Credit',\n\t\t\t\t\t\tvalue: 'balanceCredit',\n\t\t\t\t\t\tdescription: 'Triggered every time a balance account is credited',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Balance Update',\n\t\t\t\t\t\tvalue: 'balanceUpdate',\n\t\t\t\t\t\tdescription: 'Triggered every time a balance account is credited or debited',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Transfer Active Case',\n\t\t\t\t\t\tvalue: 'transferActiveCases',\n\t\t\t\t\t\tdescription: \"Triggered every time a transfer's list of active cases is updated\",\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Transfer State Changed',\n\t\t\t\t\t\tvalue: 'tranferStateChange',\n\t\t\t\t\t\tdescription: \"Triggered every time a transfer's status is updated\",\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\tasync getProfiles(this: ILoadOptionsFunctions) {\n\t\t\t\tconst profiles = await wiseApiRequest.call(this, 'GET', 'v1/profiles');\n\t\t\t\treturn profiles.map(({ id, type }: Profile) => ({\n\t\t\t\t\tname: type.charAt(0).toUpperCase() + type.slice(1),\n\t\t\t\t\tvalue: id,\n\t\t\t\t}));\n\t\t\t},\n\t\t},\n\t};\n\n\twebhookMethods = {\n\t\tdefault: {\n\t\t\tasync checkExists(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\t\t\t\tconst profileId = this.getNodeParameter('profileId') as string;\n\t\t\t\tconst event = this.getNodeParameter('event') as string;\n\t\t\t\tconst webhooks = await wiseApiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t`v3/profiles/${profileId}/subscriptions`,\n\t\t\t\t);\n\t\t\t\tconst trigger = getTriggerName(event);\n\t\t\t\tfor (const webhook of webhooks) {\n\t\t\t\t\tif (\n\t\t\t\t\t\twebhook.delivery.url === webhookUrl &&\n\t\t\t\t\t\twebhook.scope.id === profileId &&\n\t\t\t\t\t\twebhook.trigger_on === trigger\n\t\t\t\t\t) {\n\t\t\t\t\t\twebhookData.webhookId = webhook.id;\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tasync create(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\tconst profileId = this.getNodeParameter('profileId') as string;\n\t\t\t\tconst event = this.getNodeParameter('event') as string;\n\t\t\t\tconst trigger = getTriggerName(event);\n\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\tname: 'n8n Webhook',\n\t\t\t\t\ttrigger_on: trigger,\n\t\t\t\t\tdelivery: {\n\t\t\t\t\t\tversion: '2.0.0',\n\t\t\t\t\t\turl: webhookUrl,\n\t\t\t\t\t},\n\t\t\t\t};\n\t\t\t\tconst webhook = await wiseApiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'POST',\n\t\t\t\t\t`v3/profiles/${profileId}/subscriptions`,\n\t\t\t\t\tbody,\n\t\t\t\t);\n\t\t\t\twebhookData.webhookId = webhook.id;\n\t\t\t\treturn true;\n\t\t\t},\n\t\t\tasync delete(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\tconst profileId = this.getNodeParameter('profileId') as string;\n\t\t\t\ttry {\n\t\t\t\t\tawait wiseApiRequest.call(\n\t\t\t\t\t\tthis,\n\t\t\t\t\t\t'DELETE',\n\t\t\t\t\t\t`v3/profiles/${profileId}/subscriptions/${webhookData.webhookId}`,\n\t\t\t\t\t);\n\t\t\t\t} catch (error) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tdelete webhookData.webhookId;\n\t\t\t\treturn true;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync webhook(this: IWebhookFunctions): Promise<IWebhookResponseData> {\n\t\tconst req = this.getRequestObject();\n\t\tconst headers = this.getHeaderData() as IDataObject;\n\t\tconst credentials = await this.getCredentials('wiseApi');\n\n\t\tif (headers['x-test-notification'] === 'true') {\n\t\t\tconst res = this.getResponseObject();\n\t\t\tres.status(200).end();\n\t\t\treturn {\n\t\t\t\tnoWebhookResponse: true,\n\t\t\t};\n\t\t}\n\n\t\tconst signature = headers['x-signature'] as string;\n\n\t\tconst publicKey =\n\t\t\tcredentials.environment === 'test' ? testPublicKey : (livePublicKey as string);\n\n\t\tconst sig = createVerify('RSA-SHA1').update(req.rawBody);\n\t\tconst verified = sig.verify(publicKey, signature, 'base64');\n\n\t\tif (!verified) {\n\t\t\treturn {};\n\t\t}\n\n\t\treturn {\n\t\t\tworkflowData: [this.helpers.returnJsonArray(req.body as IDataObject)],\n\t\t};\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA6B;AAU7B,0BAAoC;AAGpC,8BAA6E;AAEtE,MAAM,YAAiC;AAAA,EAAvC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,SAAS;AAAA,MACjB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,UAAU;AAAA,QACT;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,MAAM;AAAA,QACP;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aACC;AAAA,UACD,UAAU;AAAA,UACV,aAAa;AAAA,YACZ,mBAAmB;AAAA,UACpB;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS;AAAA,UACT,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA,QACZ,MAAM,cAAyC;AAC9C,gBAAM,WAAW,MAAM,uCAAe,KAAK,MAAM,OAAO,aAAa;AACrE,iBAAO,SAAS,IAAI,CAAC,EAAE,IAAI,KAAK,OAAgB;AAAA,YAC/C,MAAM,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,MAAM,CAAC;AAAA,YACjD,OAAO;AAAA,UACR,EAAE;AAAA,QACH;AAAA,MACD;AAAA,IACD;AAEA,0BAAiB;AAAA,MAChB,SAAS;AAAA,QACR,MAAM,cAAoD;AACzD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AACnD,gBAAM,YAAY,KAAK,iBAAiB,WAAW;AACnD,gBAAM,QAAQ,KAAK,iBAAiB,OAAO;AAC3C,gBAAM,WAAW,MAAM,uCAAe;AAAA,YACrC;AAAA,YACA;AAAA,YACA,eAAe,SAAS;AAAA,UACzB;AACA,gBAAM,cAAU,wCAAe,KAAK;AACpC,qBAAW,WAAW,UAAU;AAC/B,gBACC,QAAQ,SAAS,QAAQ,cACzB,QAAQ,MAAM,OAAO,aACrB,QAAQ,eAAe,SACtB;AACD,0BAAY,YAAY,QAAQ;AAChC,qBAAO;AAAA,YACR;AAAA,UACD;AACA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AACnD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,gBAAM,YAAY,KAAK,iBAAiB,WAAW;AACnD,gBAAM,QAAQ,KAAK,iBAAiB,OAAO;AAC3C,gBAAM,cAAU,wCAAe,KAAK;AACpC,gBAAM,OAAoB;AAAA,YACzB,MAAM;AAAA,YACN,YAAY;AAAA,YACZ,UAAU;AAAA,cACT,SAAS;AAAA,cACT,KAAK;AAAA,YACN;AAAA,UACD;AACA,gBAAM,UAAU,MAAM,uCAAe;AAAA,YACpC;AAAA,YACA;AAAA,YACA,eAAe,SAAS;AAAA,YACxB;AAAA,UACD;AACA,sBAAY,YAAY,QAAQ;AAChC,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,gBAAM,YAAY,KAAK,iBAAiB,WAAW;AACnD,cAAI;AACH,kBAAM,uCAAe;AAAA,cACpB;AAAA,cACA;AAAA,cACA,eAAe,SAAS,kBAAkB,YAAY,SAAS;AAAA,YAChE;AAAA,UACD,SAAS,OAAO;AACf,mBAAO;AAAA,UACR;AACA,iBAAO,YAAY;AACnB,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAgE;AACrE,UAAM,MAAM,KAAK,iBAAiB;AAClC,UAAM,UAAU,KAAK,cAAc;AACnC,UAAM,cAAc,MAAM,KAAK,eAAe,SAAS;AAEvD,QAAI,QAAQ,qBAAqB,MAAM,QAAQ;AAC9C,YAAM,MAAM,KAAK,kBAAkB;AACnC,UAAI,OAAO,GAAG,EAAE,IAAI;AACpB,aAAO;AAAA,QACN,mBAAmB;AAAA,MACpB;AAAA,IACD;AAEA,UAAM,YAAY,QAAQ,aAAa;AAEvC,UAAM,YACL,YAAY,gBAAgB,SAAS,wCAAiB;AAEvD,UAAM,UAAM,4BAAa,UAAU,EAAE,OAAO,IAAI,OAAO;AACvD,UAAM,WAAW,IAAI,OAAO,WAAW,WAAW,QAAQ;AAE1D,QAAI,CAAC,UAAU;AACd,aAAO,CAAC;AAAA,IACT;AAEA,WAAO;AAAA,MACN,cAAc,CAAC,KAAK,QAAQ,gBAAgB,IAAI,IAAmB,CAAC;AAAA,IACrE;AAAA,EACD;AACD;", "names": []}