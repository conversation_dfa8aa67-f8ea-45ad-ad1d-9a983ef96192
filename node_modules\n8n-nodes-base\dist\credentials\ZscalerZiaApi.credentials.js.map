{"version": 3, "sources": ["../../credentials/ZscalerZiaApi.credentials.ts"], "sourcesContent": ["import { ApplicationError } from 'n8n-workflow';\nimport type {\n\tIAuthenticateGeneric,\n\tICredentialDataDecryptedObject,\n\tICredentialTestRequest,\n\tICredentialType,\n\tIHttpRequestHelper,\n\tINodeProperties,\n\tIcon,\n} from 'n8n-workflow';\n\nexport class ZscalerZiaApi implements ICredentialType {\n\tname = 'zscalerZiaApi';\n\n\tdisplayName = 'Zscaler ZIA API';\n\n\tdocumentationUrl = 'zscalerzia';\n\n\ticon: Icon = 'file:icons/Zscaler.svg';\n\n\thttpRequestNode = {\n\t\tname: 'Zscaler ZIA',\n\t\tdocsUrl: 'https://help.zscaler.com/zia/getting-started-zia-api',\n\t\tapiBaseUrl: '',\n\t};\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Cookie',\n\t\t\tname: 'cookie',\n\t\t\ttype: 'hidden',\n\t\t\ttypeOptions: {\n\t\t\t\texpirable: true,\n\t\t\t},\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Base URL',\n\t\t\tname: 'baseUrl',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\tplaceholder: 'e.g. zsapi.zscalerthree.net',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Username',\n\t\t\tname: 'username',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Password',\n\t\t\tname: 'password',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\tdefault: '',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Api Key',\n\t\t\tname: 'apiKey',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: {\n\t\t\t\tpassword: true,\n\t\t\t},\n\t\t\tdefault: '',\n\t\t\trequired: true,\n\t\t},\n\t];\n\n\tasync preAuthentication(this: IHttpRequestHelper, credentials: ICredentialDataDecryptedObject) {\n\t\tconst { baseUrl, username, password, apiKey } = credentials;\n\n\t\tconst url = (baseUrl as string).endsWith('/')\n\t\t\t? (baseUrl as string).slice(0, -1)\n\t\t\t: (baseUrl as string);\n\n\t\tconst now = Date.now().toString();\n\n\t\tconst obfuscate = (key: string, timestamp: string) => {\n\t\t\tconst high = timestamp.substring(timestamp.length - 6);\n\t\t\tlet low = (parseInt(high) >> 1).toString();\n\n\t\t\tlet obfuscatedApiKey = '';\n\t\t\twhile (low.length < 6) {\n\t\t\t\tlow = '0' + low;\n\t\t\t}\n\n\t\t\tfor (let i = 0; i < high.length; i++) {\n\t\t\t\tobfuscatedApiKey += key.charAt(parseInt(high.charAt(i)));\n\t\t\t}\n\t\t\tfor (let j = 0; j < low.length; j++) {\n\t\t\t\tobfuscatedApiKey += key.charAt(parseInt(low.charAt(j)) + 2);\n\t\t\t}\n\n\t\t\treturn obfuscatedApiKey;\n\t\t};\n\n\t\tconst response = await this.helpers.httpRequest({\n\t\t\tmethod: 'POST',\n\t\t\tbaseURL: `https://${url}`,\n\t\t\turl: '/api/v1/authenticatedSession',\n\t\t\theaders: {\n\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t'Cache-Control': 'no-cache',\n\t\t\t},\n\t\t\tbody: {\n\t\t\t\tapiKey: obfuscate(apiKey as string, now),\n\t\t\t\tusername,\n\t\t\t\tpassword,\n\t\t\t\ttimestamp: now,\n\t\t\t},\n\t\t\treturnFullResponse: true,\n\t\t});\n\n\t\tconst headers = response.headers;\n\n\t\tconst cookie = (headers['set-cookie'] as string[])\n\t\t\t?.find((entrt) => entrt.includes('JSESSIONID'))\n\t\t\t?.split(';')\n\t\t\t?.find((entry) => entry.includes('JSESSIONID'));\n\n\t\tif (!cookie) {\n\t\t\tthrow new ApplicationError('No cookie returned. Please check your credentials.', {\n\t\t\t\tlevel: 'warning',\n\t\t\t});\n\t\t}\n\n\t\treturn { cookie };\n\t}\n\n\tauthenticate: IAuthenticateGeneric = {\n\t\ttype: 'generic',\n\t\tproperties: {\n\t\t\theaders: {\n\t\t\t\tCookie: '={{$credentials.cookie}}',\n\t\t\t},\n\t\t},\n\t};\n\n\ttest: ICredentialTestRequest = {\n\t\trequest: {\n\t\t\turl: '=https://{{$credentials.baseUrl}}/api/v1/authSettings/exemptedUrls',\n\t\t},\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAAiC;AAW1B,MAAM,cAAyC;AAAA,EAA/C;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,gBAAa;AAEb,2BAAkB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAEA,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,WAAW;AAAA,QACZ;AAAA,QACA,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,UAAU;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,QACT,UAAU;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,SAAS;AAAA,QACT,UAAU;AAAA,MACX;AAAA,IACD;AA+DA,wBAAqC;AAAA,MACpC,MAAM;AAAA,MACN,YAAY;AAAA,QACX,SAAS;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAEA,gBAA+B;AAAA,MAC9B,SAAS;AAAA,QACR,KAAK;AAAA,MACN;AAAA,IACD;AAAA;AAAA,EA1EA,MAAM,kBAA4C,aAA6C;AAC9F,UAAM,EAAE,SAAS,UAAU,UAAU,OAAO,IAAI;AAEhD,UAAM,MAAO,QAAmB,SAAS,GAAG,IACxC,QAAmB,MAAM,GAAG,EAAE,IAC9B;AAEJ,UAAM,MAAM,KAAK,IAAI,EAAE,SAAS;AAEhC,UAAM,YAAY,CAAC,KAAa,cAAsB;AACrD,YAAM,OAAO,UAAU,UAAU,UAAU,SAAS,CAAC;AACrD,UAAI,OAAO,SAAS,IAAI,KAAK,GAAG,SAAS;AAEzC,UAAI,mBAAmB;AACvB,aAAO,IAAI,SAAS,GAAG;AACtB,cAAM,MAAM;AAAA,MACb;AAEA,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACrC,4BAAoB,IAAI,OAAO,SAAS,KAAK,OAAO,CAAC,CAAC,CAAC;AAAA,MACxD;AACA,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACpC,4BAAoB,IAAI,OAAO,SAAS,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;AAAA,MAC3D;AAEA,aAAO;AAAA,IACR;AAEA,UAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAAA,MAC/C,QAAQ;AAAA,MACR,SAAS,WAAW,GAAG;AAAA,MACvB,KAAK;AAAA,MACL,SAAS;AAAA,QACR,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,MAClB;AAAA,MACA,MAAM;AAAA,QACL,QAAQ,UAAU,QAAkB,GAAG;AAAA,QACvC;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACZ;AAAA,MACA,oBAAoB;AAAA,IACrB,CAAC;AAED,UAAM,UAAU,SAAS;AAEzB,UAAM,SAAU,QAAQ,YAAY,GACjC,KAAK,CAAC,UAAU,MAAM,SAAS,YAAY,CAAC,GAC5C,MAAM,GAAG,GACT,KAAK,CAAC,UAAU,MAAM,SAAS,YAAY,CAAC;AAE/C,QAAI,CAAC,QAAQ;AACZ,YAAM,IAAI,qCAAiB,sDAAsD;AAAA,QAChF,OAAO;AAAA,MACR,CAAC;AAAA,IACF;AAEA,WAAO,EAAE,OAAO;AAAA,EACjB;AAgBD;", "names": []}