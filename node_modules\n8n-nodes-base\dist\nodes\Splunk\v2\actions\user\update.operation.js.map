{"version": 3, "sources": ["../../../../../../nodes/Splunk/v2/actions/user/update.operation.ts"], "sourcesContent": ["import type { INodeProperties, IExecuteFunctions, IDataObject } from 'n8n-workflow';\n\nimport { updateDisplayOptions } from '../../../../../utils/utilities';\nimport { userRLC } from '../../helpers/descriptions';\nimport { formatFeed, populate } from '../../helpers/utils';\nimport { splunkApiRequest } from '../../transport';\n\nconst properties: INodeProperties[] = [\n\tuserRLC,\n\t{\n\t\tdisplayName: 'Update Fields',\n\t\tname: 'updateFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Email',\n\t\t\t\tname: 'email',\n\t\t\t\ttype: 'string',\n\t\t\t\tplaceholder: '<EMAIL>',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Full Name',\n\t\t\t\tname: 'realname',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Full name of the user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Password',\n\t\t\t\tname: 'password',\n\t\t\t\ttype: 'string',\n\t\t\t\ttypeOptions: { password: true },\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Role Names or IDs',\n\t\t\t\tname: 'roles',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\tdescription:\n\t\t\t\t\t'Comma-separated list of roles to assign to the user. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\tdefault: [],\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getRoles',\n\t\t\t\t},\n\t\t\t},\n\t\t],\n\t},\n];\n\nconst displayOptions = {\n\tshow: {\n\t\tresource: ['user'],\n\t\toperation: ['update'],\n\t},\n};\n\nexport const description = updateDisplayOptions(displayOptions, properties);\n\nexport async function execute(\n\tthis: IExecuteFunctions,\n\ti: number,\n): Promise<IDataObject | IDataObject[]> {\n\t// https://docs.splunk.com/Documentation/Splunk/8.2.2/RESTREF/RESTaccess#authentication.2Fusers.2F.7Bname.7D\n\n\tconst body = {} as IDataObject;\n\tconst { roles, ...rest } = this.getNodeParameter('updateFields', i) as IDataObject & {\n\t\troles: string[];\n\t};\n\n\tpopulate(\n\t\t{\n\t\t\t...(roles && { roles }),\n\t\t\t...rest,\n\t\t},\n\t\tbody,\n\t);\n\n\tconst userId = this.getNodeParameter('userId', i, '', { extractValue: true }) as string;\n\tconst endpoint = `/services/authentication/users/${userId}`;\n\n\tconst returnData = await splunkApiRequest.call(this, 'POST', endpoint, body).then(formatFeed);\n\n\treturn returnData;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,uBAAqC;AACrC,0BAAwB;AACxB,mBAAqC;AACrC,uBAAiC;AAEjC,MAAM,aAAgC;AAAA,EACrC;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aACC;AAAA,QACD,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AAEA,MAAM,iBAAiB;AAAA,EACtB,MAAM;AAAA,IACL,UAAU,CAAC,MAAM;AAAA,IACjB,WAAW,CAAC,QAAQ;AAAA,EACrB;AACD;AAEO,MAAM,kBAAc,uCAAqB,gBAAgB,UAAU;AAE1E,eAAsB,QAErB,GACuC;AAGvC,QAAM,OAAO,CAAC;AACd,QAAM,EAAE,OAAO,GAAG,KAAK,IAAI,KAAK,iBAAiB,gBAAgB,CAAC;AAIlE;AAAA,IACC;AAAA,MACC,GAAI,SAAS,EAAE,MAAM;AAAA,MACrB,GAAG;AAAA,IACJ;AAAA,IACA;AAAA,EACD;AAEA,QAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,IAAI,EAAE,cAAc,KAAK,CAAC;AAC5E,QAAM,WAAW,kCAAkC,MAAM;AAEzD,QAAM,aAAa,MAAM,kCAAiB,KAAK,MAAM,QAAQ,UAAU,IAAI,EAAE,KAAK,uBAAU;AAE5F,SAAO;AACR;", "names": []}