{"version": 3, "sources": ["../../../nodes/Okta/UserFunctions.ts"], "sourcesContent": ["import type {\n\tDeclarativeRestApiSettings,\n\tIDataObject,\n\tIExecuteFunctions,\n\tIExecutePaginationFunctions,\n\tIExecuteSingleFunctions,\n\tIHookFunctions,\n\tIHttpRequestMethods,\n\tIHttpRequestOptions,\n\tILoadOptionsFunctions,\n\tIN8nHttpFullResponse,\n\tINodeExecutionData,\n\tINodeListSearchResult,\n\tINodePropertyOptions,\n} from 'n8n-workflow';\n\ntype OktaUser = {\n\tstatus: string;\n\tcreated: string;\n\tactivated: string;\n\tlastLogin: string;\n\tlastUpdated: string;\n\tpasswordChanged: string;\n\tprofile: {\n\t\tlogin: string;\n\t\temail: string;\n\t\tfirstName: string;\n\t\tlastName: string;\n\t};\n\tid: string;\n};\n\nexport async function oktaApiRequest(\n\tthis: IExecuteFunctions | IExecuteSingleFunctions | IHookFunctions | ILoadOptionsFunctions,\n\tmethod: IHttpRequestMethods,\n\tresource: string,\n\tbody: IDataObject = {},\n\tqs: IDataObject = {},\n\turl?: string,\n\toption: IDataObject = {},\n): Promise<OktaUser[]> {\n\tconst credentials = await this.getCredentials('oktaApi');\n\tconst baseUrl = `${credentials.url as string}/api/v1/${resource}`;\n\tconst options: IHttpRequestOptions = {\n\t\theaders: {\n\t\t\t'Content-Type': 'application/json',\n\t\t},\n\t\tmethod,\n\t\tbody: Object.keys(body).length ? body : undefined,\n\t\tqs: Object.keys(qs).length ? qs : undefined,\n\t\turl: url ?? baseUrl,\n\t\tjson: true,\n\t\t...option,\n\t};\n\treturn await (this.helpers.httpRequestWithAuthentication.call(\n\t\tthis,\n\t\t'oktaApi',\n\t\toptions,\n\t) as Promise<OktaUser[]>);\n}\n\nexport async function getUsers(\n\tthis: ILoadOptionsFunctions,\n\tfilter?: string,\n): Promise<INodeListSearchResult> {\n\tconst responseData: OktaUser[] = await oktaApiRequest.call(this, 'GET', '/users/');\n\tconst filteredUsers = responseData.filter((user) => {\n\t\tif (!filter) return true;\n\t\tconst username = `${user.profile.login}`.toLowerCase();\n\t\treturn username.includes(filter.toLowerCase());\n\t});\n\tconst users: INodePropertyOptions[] = filteredUsers.map((user) => ({\n\t\tname: `${user.profile.login}`,\n\t\tvalue: user.id,\n\t}));\n\treturn {\n\t\tresults: users,\n\t};\n}\n\nfunction simplifyOktaUser(item: OktaUser): IDataObject {\n\treturn {\n\t\tid: item.id,\n\t\tstatus: item.status,\n\t\tcreated: item.created,\n\t\tactivated: item.activated,\n\t\tlastLogin: item.lastLogin,\n\t\tlastUpdated: item.lastUpdated,\n\t\tpasswordChanged: item.passwordChanged,\n\t\tprofile: {\n\t\t\tfirstName: item.profile.firstName,\n\t\t\tlastName: item.profile.lastName,\n\t\t\tlogin: item.profile.login,\n\t\t\temail: item.profile.email,\n\t\t},\n\t};\n}\n\nexport async function simplifyGetAllResponse(\n\tthis: IExecuteSingleFunctions,\n\titems: INodeExecutionData[],\n\t_response: IN8nHttpFullResponse,\n): Promise<INodeExecutionData[]> {\n\tif (items.length === 0) return items;\n\tconst simplify = this.getNodeParameter('simplify');\n\tif (!simplify)\n\t\treturn ((items[0].json as unknown as IDataObject[]) ?? []).map((item: IDataObject) => ({\n\t\t\tjson: item,\n\t\t\theaders: _response.headers,\n\t\t})) as INodeExecutionData[];\n\tlet simplifiedItems: INodeExecutionData[] = [];\n\tif (items[0].json) {\n\t\tconst jsonArray = items[0].json as unknown;\n\t\tsimplifiedItems = (jsonArray as OktaUser[]).map((item: OktaUser) => {\n\t\t\tconst simplifiedItem = simplifyOktaUser(item);\n\t\t\treturn {\n\t\t\t\tjson: simplifiedItem,\n\t\t\t\theaders: _response.headers,\n\t\t\t};\n\t\t});\n\t}\n\n\treturn simplifiedItems;\n}\n\nexport async function simplifyGetResponse(\n\tthis: IExecuteSingleFunctions,\n\titems: INodeExecutionData[],\n\t_response: IN8nHttpFullResponse,\n): Promise<INodeExecutionData[]> {\n\tconst simplify = this.getNodeParameter('simplify');\n\tif (!simplify) return items;\n\tconst item = items[0].json as OktaUser;\n\tconst simplifiedItem = simplifyOktaUser(item);\n\n\treturn [\n\t\t{\n\t\t\tjson: simplifiedItem,\n\t\t},\n\t] as INodeExecutionData[];\n}\n\nexport const getCursorPaginator = () => {\n\treturn async function cursorPagination(\n\t\tthis: IExecutePaginationFunctions,\n\t\trequestOptions: DeclarativeRestApiSettings.ResultOptions,\n\t): Promise<INodeExecutionData[]> {\n\t\tif (!requestOptions.options.qs) {\n\t\t\trequestOptions.options.qs = {};\n\t\t}\n\n\t\tlet items: INodeExecutionData[] = [];\n\t\tlet responseData: INodeExecutionData[];\n\t\tlet nextCursor: string | undefined = undefined;\n\t\tconst returnAll = this.getNodeParameter('returnAll', true) as boolean;\n\t\tdo {\n\t\t\trequestOptions.options.qs.limit = 200;\n\t\t\trequestOptions.options.qs.after = nextCursor;\n\t\t\tresponseData = await this.makeRoutingRequest(requestOptions);\n\t\t\tif (responseData.length > 0) {\n\t\t\t\tconst headers = responseData[responseData.length - 1].headers;\n\t\t\t\tconst headersLink = (headers as IDataObject)?.link as string | undefined;\n\t\t\t\tnextCursor = headersLink?.split('after=')[1]?.split('&')[0]?.split('>')[0];\n\t\t\t}\n\t\t\titems = items.concat(responseData);\n\t\t} while (returnAll && nextCursor);\n\n\t\treturn items;\n\t};\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgCA,eAAsB,eAErB,QACA,UACA,OAAoB,CAAC,GACrB,KAAkB,CAAC,GACnB,KACA,SAAsB,CAAC,GACD;AACtB,QAAM,cAAc,MAAM,KAAK,eAAe,SAAS;AACvD,QAAM,UAAU,GAAG,YAAY,GAAa,WAAW,QAAQ;AAC/D,QAAM,UAA+B;AAAA,IACpC,SAAS;AAAA,MACR,gBAAgB;AAAA,IACjB;AAAA,IACA;AAAA,IACA,MAAM,OAAO,KAAK,IAAI,EAAE,SAAS,OAAO;AAAA,IACxC,IAAI,OAAO,KAAK,EAAE,EAAE,SAAS,KAAK;AAAA,IAClC,KAAK,OAAO;AAAA,IACZ,MAAM;AAAA,IACN,GAAG;AAAA,EACJ;AACA,SAAO,MAAO,KAAK,QAAQ,8BAA8B;AAAA,IACxD;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AAEA,eAAsB,SAErB,QACiC;AACjC,QAAM,eAA2B,MAAM,eAAe,KAAK,MAAM,OAAO,SAAS;AACjF,QAAM,gBAAgB,aAAa,OAAO,CAAC,SAAS;AACnD,QAAI,CAAC,OAAQ,QAAO;AACpB,UAAM,WAAW,GAAG,KAAK,QAAQ,KAAK,GAAG,YAAY;AACrD,WAAO,SAAS,SAAS,OAAO,YAAY,CAAC;AAAA,EAC9C,CAAC;AACD,QAAM,QAAgC,cAAc,IAAI,CAAC,UAAU;AAAA,IAClE,MAAM,GAAG,KAAK,QAAQ,KAAK;AAAA,IAC3B,OAAO,KAAK;AAAA,EACb,EAAE;AACF,SAAO;AAAA,IACN,SAAS;AAAA,EACV;AACD;AAEA,SAAS,iBAAiB,MAA6B;AACtD,SAAO;AAAA,IACN,IAAI,KAAK;AAAA,IACT,QAAQ,KAAK;AAAA,IACb,SAAS,KAAK;AAAA,IACd,WAAW,KAAK;AAAA,IAChB,WAAW,KAAK;AAAA,IAChB,aAAa,KAAK;AAAA,IAClB,iBAAiB,KAAK;AAAA,IACtB,SAAS;AAAA,MACR,WAAW,KAAK,QAAQ;AAAA,MACxB,UAAU,KAAK,QAAQ;AAAA,MACvB,OAAO,KAAK,QAAQ;AAAA,MACpB,OAAO,KAAK,QAAQ;AAAA,IACrB;AAAA,EACD;AACD;AAEA,eAAsB,uBAErB,OACA,WACgC;AAChC,MAAI,MAAM,WAAW,EAAG,QAAO;AAC/B,QAAM,WAAW,KAAK,iBAAiB,UAAU;AACjD,MAAI,CAAC;AACJ,YAAS,MAAM,CAAC,EAAE,QAAqC,CAAC,GAAG,IAAI,CAAC,UAAuB;AAAA,MACtF,MAAM;AAAA,MACN,SAAS,UAAU;AAAA,IACpB,EAAE;AACH,MAAI,kBAAwC,CAAC;AAC7C,MAAI,MAAM,CAAC,EAAE,MAAM;AAClB,UAAM,YAAY,MAAM,CAAC,EAAE;AAC3B,sBAAmB,UAAyB,IAAI,CAAC,SAAmB;AACnE,YAAM,iBAAiB,iBAAiB,IAAI;AAC5C,aAAO;AAAA,QACN,MAAM;AAAA,QACN,SAAS,UAAU;AAAA,MACpB;AAAA,IACD,CAAC;AAAA,EACF;AAEA,SAAO;AACR;AAEA,eAAsB,oBAErB,OACA,WACgC;AAChC,QAAM,WAAW,KAAK,iBAAiB,UAAU;AACjD,MAAI,CAAC,SAAU,QAAO;AACtB,QAAM,OAAO,MAAM,CAAC,EAAE;AACtB,QAAM,iBAAiB,iBAAiB,IAAI;AAE5C,SAAO;AAAA,IACN;AAAA,MACC,MAAM;AAAA,IACP;AAAA,EACD;AACD;AAEO,MAAM,qBAAqB,MAAM;AACvC,SAAO,eAAe,iBAErB,gBACgC;AAChC,QAAI,CAAC,eAAe,QAAQ,IAAI;AAC/B,qBAAe,QAAQ,KAAK,CAAC;AAAA,IAC9B;AAEA,QAAI,QAA8B,CAAC;AACnC,QAAI;AACJ,QAAI,aAAiC;AACrC,UAAM,YAAY,KAAK,iBAAiB,aAAa,IAAI;AACzD,OAAG;AACF,qBAAe,QAAQ,GAAG,QAAQ;AAClC,qBAAe,QAAQ,GAAG,QAAQ;AAClC,qBAAe,MAAM,KAAK,mBAAmB,cAAc;AAC3D,UAAI,aAAa,SAAS,GAAG;AAC5B,cAAM,UAAU,aAAa,aAAa,SAAS,CAAC,EAAE;AACtD,cAAM,cAAe,SAAyB;AAC9C,qBAAa,aAAa,MAAM,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,EAAE,CAAC,GAAG,MAAM,GAAG,EAAE,CAAC;AAAA,MAC1E;AACA,cAAQ,MAAM,OAAO,YAAY;AAAA,IAClC,SAAS,aAAa;AAEtB,WAAO;AAAA,EACR;AACD;", "names": []}