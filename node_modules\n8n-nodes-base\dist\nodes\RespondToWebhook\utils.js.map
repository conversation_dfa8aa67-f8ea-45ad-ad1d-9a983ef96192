{"version": 3, "sources": ["../../../nodes/RespondToWebhook/utils.ts"], "sourcesContent": ["export const configuredOutputs = (version: number) => {\n\tif (version >= 1.3) {\n\t\treturn [\n\t\t\t{\n\t\t\t\ttype: 'main',\n\t\t\t\tdisplayName: 'Input Data',\n\t\t\t},\n\t\t\t{\n\t\t\t\ttype: 'main',\n\t\t\t\tdisplayName: 'Response',\n\t\t\t},\n\t\t];\n\t}\n\n\treturn ['main'];\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,MAAM,oBAAoB,CAAC,YAAoB;AACrD,MAAI,WAAW,KAAK;AACnB,WAAO;AAAA,MACN;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAEA,SAAO,CAAC,MAAM;AACf;", "names": []}