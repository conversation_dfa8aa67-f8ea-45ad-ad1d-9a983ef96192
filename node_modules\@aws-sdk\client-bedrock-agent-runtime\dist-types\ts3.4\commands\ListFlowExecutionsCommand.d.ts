import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  BedrockAgentRuntimeClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../BedrockAgentRuntimeClient";
import {
  ListFlowExecutionsRequest,
  ListFlowExecutionsResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface ListFlowExecutionsCommandInput
  extends ListFlowExecutionsRequest {}
export interface ListFlowExecutionsCommandOutput
  extends ListFlowExecutionsResponse,
    __MetadataBearer {}
declare const ListFlowExecutionsCommand_base: {
  new (
    input: ListFlowExecutionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListFlowExecutionsCommandInput,
    ListFlowExecutionsCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListFlowExecutionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListFlowExecutionsCommandInput,
    ListFlowExecutionsCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListFlowExecutionsCommand extends ListFlowExecutionsCommand_base {
  protected static __types: {
    api: {
      input: ListFlowExecutionsRequest;
      output: ListFlowExecutionsResponse;
    };
    sdk: {
      input: ListFlowExecutionsCommandInput;
      output: ListFlowExecutionsCommandOutput;
    };
  };
}
