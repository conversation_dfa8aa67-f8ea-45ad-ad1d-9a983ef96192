{"version": 3, "sources": ["../../../nodes/Vero/UserDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const userOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Add Tags',\n\t\t\t\tvalue: 'addTags',\n\t\t\t\tdescription: 'Adds a tag to a users profile',\n\t\t\t\taction: 'Add tags to a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: '<PERSON><PERSON>',\n\t\t\t\tvalue: 'alias',\n\t\t\t\tdescription: 'Change a users identifier',\n\t\t\t\taction: \"Change a user's alias\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Create or Update',\n\t\t\t\tvalue: 'create',\n\t\t\t\tdescription: 'Create or update a user profile',\n\t\t\t\taction: 'Create or update a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'delete',\n\t\t\t\tdescription: 'Delete a user',\n\t\t\t\taction: 'Delete a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Re-Subscribe',\n\t\t\t\tvalue: 'resubscribe',\n\t\t\t\tdescription: 'Resubscribe a user',\n\t\t\t\taction: 'Resubscribe a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Remove Tags',\n\t\t\t\tvalue: 'removeTags',\n\t\t\t\tdescription: 'Removes a tag from a users profile',\n\t\t\t\taction: 'Remove tags from a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Unsubscribe',\n\t\t\t\tvalue: 'unsubscribe',\n\t\t\t\tdescription: 'Unsubscribe a user',\n\t\t\t\taction: 'Unsubscribe a user',\n\t\t\t},\n\t\t],\n\t\tdefault: 'create',\n\t},\n];\n\nexport const userFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                                user:create                                 */\n\t/* -------------------------------------------------------------------------- */\n\n\t{\n\t\tdisplayName: 'ID',\n\t\tname: 'id',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'The unique identifier of the customer',\n\t},\n\t{\n\t\tdisplayName: 'JSON Parameters',\n\t\tname: 'jsonParameters',\n\t\ttype: 'boolean',\n\t\tdefault: false,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Email',\n\t\t\t\tname: 'email',\n\t\t\t\ttype: 'string',\n\t\t\t\tplaceholder: '<EMAIL>',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The table to create the row in',\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Data',\n\t\tname: 'dataAttributesUi',\n\t\tplaceholder: 'Add Data',\n\t\tdescription: 'Key value pairs that represent the custom user properties you want to update',\n\t\ttype: 'fixedCollection',\n\t\ttypeOptions: {\n\t\t\tmultipleValues: true,\n\t\t},\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t\tjsonParameters: [false],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'dataAttributesValues',\n\t\t\t\tdisplayName: 'Data',\n\t\t\t\tvalues: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Key',\n\t\t\t\t\t\tname: 'key',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription: 'Name of the property to set',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Value',\n\t\t\t\t\t\tname: 'value',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t\tdescription: 'Value of the property to set',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Data',\n\t\tname: 'dataAttributesJson',\n\t\ttype: 'json',\n\t\tdefault: '',\n\t\ttypeOptions: {\n\t\t\talwaysOpenEditWindow: true,\n\t\t},\n\t\tdescription: 'Key value pairs that represent the custom user properties you want to update',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t\tjsonParameters: [true],\n\t\t\t},\n\t\t},\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                   user:alias                                */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'ID',\n\t\tname: 'id',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['alias'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'The old unique identifier of the user',\n\t},\n\t{\n\t\tdisplayName: 'New ID',\n\t\tname: 'newId',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['alias'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'The new unique identifier of the user',\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                   user:unsubscribe                         */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'ID',\n\t\tname: 'id',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['unsubscribe'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'The unique identifier of the user',\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 user:resubscribe                           */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'ID',\n\t\tname: 'id',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['resubscribe'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'The unique identifier of the user',\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 user:delete                                */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'ID',\n\t\tname: 'id',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['delete'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'The unique identifier of the user',\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 user:addTags                               */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'ID',\n\t\tname: 'id',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['addTags'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'The unique identifier of the user',\n\t},\n\t{\n\t\tdisplayName: 'Tags',\n\t\tname: 'tags',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['addTags'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'Tags to add separated by \",\"',\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                 user:removeTags                            */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'ID',\n\t\tname: 'id',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['removeTags'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'The unique identifier of the user',\n\t},\n\t{\n\t\tdisplayName: 'Tags',\n\t\tname: 'tags',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['removeTags'],\n\t\t\t},\n\t\t},\n\t\tdescription: 'Tags to remove separated by \",\"',\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,iBAAoC;AAAA,EAChD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,aAAgC;AAAA;AAAA;AAAA;AAAA,EAK5C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,gBAAgB;AAAA,IACjB;AAAA,IACA,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,QACpB,gBAAgB,CAAC,KAAK;AAAA,MACvB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,UACP;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,aAAa;AAAA,UACd;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,aAAa;AAAA,UACd;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,MACZ,sBAAsB;AAAA,IACvB;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,QACpB,gBAAgB,CAAC,IAAI;AAAA,MACtB;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,OAAO;AAAA,MACpB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,OAAO;AAAA,MACpB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,aAAa;AAAA,MAC1B;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,aAAa;AAAA,MAC1B;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,SAAS;AAAA,MACtB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,SAAS;AAAA,MACtB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,YAAY;AAAA,MACzB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,YAAY;AAAA,MACzB;AAAA,IACD;AAAA,IACA,aAAa;AAAA,EACd;AACD;", "names": []}