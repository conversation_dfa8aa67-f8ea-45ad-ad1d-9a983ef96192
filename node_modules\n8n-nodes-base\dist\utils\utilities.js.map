{"version": 3, "sources": ["../../utils/utilities.ts"], "sourcesContent": ["import { isEqual, isNull, merge, isObject, reduce, get } from 'lodash';\nimport type {\n\tIDataObject,\n\tIDisplayOptions,\n\tINodeExecutionData,\n\tINodeProperties,\n\tIPairedItemData,\n} from 'n8n-workflow';\nimport { ApplicationError, jsonParse, randomInt } from 'n8n-workflow';\n\n/**\n * Creates an array of elements split into groups the length of `size`.\n * If `array` can't be split evenly, the final chunk will be the remaining\n * elements.\n *\n * @param {Array} array The array to process.\n * @param {number} [size=1] The length of each chunk\n * @example\n *\n * chunk(['a', 'b', 'c', 'd'], 2)\n * // => [['a', 'b'], ['c', 'd']]\n *\n * chunk(['a', 'b', 'c', 'd'], 3)\n * // => [['a', 'b', 'c'], ['d']]\n */\n\nexport function chunk<T>(array: T[], size = 1) {\n\tconst length = array === null ? 0 : array.length;\n\tif (!length || size < 1) {\n\t\treturn [];\n\t}\n\tlet index = 0;\n\tlet resIndex = 0;\n\tconst result = new Array(Math.ceil(length / size));\n\n\twhile (index < length) {\n\t\tresult[resIndex++] = array.slice(index, (index += size));\n\t}\n\treturn result as T[][];\n}\n\n/**\n * Shuffles an array in place using the Fisher-Yates shuffle algorithm\n * @param {Array} array The array to shuffle.\n */\nexport const shuffleArray = <T>(array: T[]): void => {\n\tfor (let i = array.length - 1; i > 0; i--) {\n\t\tconst j = randomInt(i + 1);\n\t\t[array[i], array[j]] = [array[j], array[i]];\n\t}\n};\n\n/**\n * Flattens an object with deep data\n * @param {IDataObject} data The object to flatten\n * @param {string[]} prefix The prefix to add to each key in the returned flat object\n */\nexport const flattenKeys = (obj: IDataObject, prefix: string[] = []): IDataObject => {\n\treturn !isObject(obj)\n\t\t? { [prefix.join('.')]: obj }\n\t\t: reduce(\n\t\t\t\tobj,\n\t\t\t\t(cum, next, key) => merge(cum, flattenKeys(next as IDataObject, [...prefix, key])),\n\t\t\t\t{},\n\t\t\t);\n};\n\n/**\n * Takes a multidimensional array and converts it to a one-dimensional array.\n *\n * @param {Array} nestedArray The array to be flattened.\n * @example\n *\n * flatten([['a', 'b'], ['c', 'd']])\n * // => ['a', 'b', 'c', 'd']\n *\n */\n\nexport function flatten<T>(nestedArray: T[][]) {\n\tconst result = [];\n\n\t(function loop(array: T[] | T[][]) {\n\t\tfor (let i = 0; i < array.length; i++) {\n\t\t\tif (Array.isArray(array[i])) {\n\t\t\t\tloop(array[i] as T[]);\n\t\t\t} else {\n\t\t\t\tresult.push(array[i]);\n\t\t\t}\n\t\t}\n\t})(nestedArray);\n\n\t//TODO: check logic in MicrosoftSql.node.ts\n\n\treturn result as any;\n}\n\n/**\n * Compares the values of specified keys in two objects.\n *\n * @param {T} obj1 - The first object to compare.\n * @param {T} obj2 - The second object to compare.\n * @param {string[]} keys - An array of keys to compare.\n * @param {boolean} disableDotNotation - Whether to use dot notation to access nested properties.\n * @returns {boolean} - Whether the values of the specified keys are equal in both objects.\n */\nexport const compareItems = <T extends { json: Record<string, unknown> }>(\n\tobj1: T,\n\tobj2: T,\n\tkeys: string[],\n\tdisableDotNotation: boolean = false,\n): boolean => {\n\tlet result = true;\n\tfor (const key of keys) {\n\t\tif (!disableDotNotation) {\n\t\t\tif (!isEqual(get(obj1.json, key), get(obj2.json, key))) {\n\t\t\t\tresult = false;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t} else {\n\t\t\tif (!isEqual(obj1.json[key], obj2.json[key])) {\n\t\t\t\tresult = false;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\treturn result;\n};\n\nexport function updateDisplayOptions(\n\tdisplayOptions: IDisplayOptions,\n\tproperties: INodeProperties[],\n) {\n\treturn properties.map((nodeProperty) => {\n\t\treturn {\n\t\t\t...nodeProperty,\n\t\t\tdisplayOptions: merge({}, nodeProperty.displayOptions, displayOptions),\n\t\t};\n\t});\n}\n\nexport function processJsonInput<T>(jsonData: T, inputName?: string) {\n\tlet values;\n\tconst input = inputName ? `'${inputName}' ` : '';\n\n\tif (typeof jsonData === 'string') {\n\t\ttry {\n\t\t\tvalues = jsonParse(jsonData);\n\t\t} catch (error) {\n\t\t\tthrow new ApplicationError(`Input ${input} must contain a valid JSON`, { level: 'warning' });\n\t\t}\n\t} else if (typeof jsonData === 'object') {\n\t\tvalues = jsonData;\n\t} else {\n\t\tthrow new ApplicationError(`Input ${input} must contain a valid JSON`, { level: 'warning' });\n\t}\n\n\treturn values;\n}\n\nfunction isFalsy<T>(value: T) {\n\tif (isNull(value)) return true;\n\tif (typeof value === 'string' && value === '') return true;\n\tif (Array.isArray(value) && value.length === 0) return true;\n\treturn false;\n}\n\nconst parseStringAndCompareToObject = (str: string, arr: IDataObject) => {\n\ttry {\n\t\tconst parsedArray = jsonParse(str);\n\t\treturn isEqual(parsedArray, arr);\n\t} catch (error) {\n\t\treturn false;\n\t}\n};\n\nexport const fuzzyCompare = (useFuzzyCompare: boolean, compareVersion = 1) => {\n\tif (!useFuzzyCompare) {\n\t\t//Fuzzy compare is false we do strict comparison\n\t\treturn <T, U>(item1: T, item2: U) => isEqual(item1, item2);\n\t}\n\n\treturn <T, U>(item1: T, item2: U) => {\n\t\t//Both types are the same, so we do strict comparison\n\t\tif (!isNull(item1) && !isNull(item2) && typeof item1 === typeof item2) {\n\t\t\treturn isEqual(item1, item2);\n\t\t}\n\n\t\tif (compareVersion >= 2) {\n\t\t\t//Null, 0 and \"0\" treated as equal\n\t\t\tif (isNull(item1) && (isNull(item2) || item2 === 0 || item2 === '0')) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tif (isNull(item2) && (isNull(item1) || item1 === 0 || item1 === '0')) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\t//Null, empty strings, empty arrays all treated as the same\n\t\tif (isFalsy(item1) && isFalsy(item2)) return true;\n\n\t\t//When a field is missing in one branch and isFalsy() in another, treat them as matching\n\t\tif (isFalsy(item1) && item2 === undefined) return true;\n\t\tif (item1 === undefined && isFalsy(item2)) return true;\n\n\t\t//Compare numbers and strings representing that number\n\t\tif (typeof item1 === 'number' && typeof item2 === 'string') {\n\t\t\treturn item1.toString() === item2;\n\t\t}\n\n\t\tif (typeof item1 === 'string' && typeof item2 === 'number') {\n\t\t\treturn item1 === item2.toString();\n\t\t}\n\n\t\t//Compare objects/arrays and their stringified version\n\t\tif (!isNull(item1) && typeof item1 === 'object' && typeof item2 === 'string') {\n\t\t\treturn parseStringAndCompareToObject(item2, item1 as IDataObject);\n\t\t}\n\n\t\tif (!isNull(item2) && typeof item1 === 'string' && typeof item2 === 'object') {\n\t\t\treturn parseStringAndCompareToObject(item1, item2 as IDataObject);\n\t\t}\n\n\t\t//Compare booleans and strings representing the boolean (’true’, ‘True’, ‘TRUE’)\n\t\tif (typeof item1 === 'boolean' && typeof item2 === 'string') {\n\t\t\tif (item1 === true && item2.toLocaleLowerCase() === 'true') return true;\n\t\t\tif (item1 === false && item2.toLocaleLowerCase() === 'false') return true;\n\t\t}\n\n\t\tif (typeof item2 === 'boolean' && typeof item1 === 'string') {\n\t\t\tif (item2 === true && item1.toLocaleLowerCase() === 'true') return true;\n\t\t\tif (item2 === false && item1.toLocaleLowerCase() === 'false') return true;\n\t\t}\n\n\t\t//Compare booleans and the numbers/string 0 and 1\n\t\tif (typeof item1 === 'boolean' && typeof item2 === 'number') {\n\t\t\tif (item1 === true && item2 === 1) return true;\n\t\t\tif (item1 === false && item2 === 0) return true;\n\t\t}\n\n\t\tif (typeof item2 === 'boolean' && typeof item1 === 'number') {\n\t\t\tif (item2 === true && item1 === 1) return true;\n\t\t\tif (item2 === false && item1 === 0) return true;\n\t\t}\n\n\t\tif (typeof item1 === 'boolean' && typeof item2 === 'string') {\n\t\t\tif (item1 === true && item2 === '1') return true;\n\t\t\tif (item1 === false && item2 === '0') return true;\n\t\t}\n\n\t\tif (typeof item2 === 'boolean' && typeof item1 === 'string') {\n\t\t\tif (item2 === true && item1 === '1') return true;\n\t\t\tif (item2 === false && item1 === '0') return true;\n\t\t}\n\n\t\treturn isEqual(item1, item2);\n\t};\n};\n\nexport function wrapData(data: IDataObject | IDataObject[]): INodeExecutionData[] {\n\tif (!Array.isArray(data)) {\n\t\treturn [{ json: data }];\n\t}\n\treturn data.map((item) => ({\n\t\tjson: item,\n\t}));\n}\n\nexport const keysToLowercase = <T>(headers: T) => {\n\tif (typeof headers !== 'object' || Array.isArray(headers) || headers === null) return headers;\n\treturn Object.entries(headers).reduce((acc, [key, value]) => {\n\t\tacc[key.toLowerCase()] = value as IDataObject;\n\t\treturn acc;\n\t}, {} as IDataObject);\n};\n\n/**\n * Formats a private key by removing unnecessary whitespace and adding line breaks.\n * @param privateKey - The private key to format.\n * @returns The formatted private key.\n */\nexport function formatPrivateKey(privateKey: string, keyIsPublic = false): string {\n\tlet regex = /(PRIVATE KEY|CERTIFICATE)/;\n\tif (keyIsPublic) {\n\t\tregex = /(PUBLIC KEY)/;\n\t}\n\tif (!privateKey || /\\n/.test(privateKey)) {\n\t\treturn privateKey;\n\t}\n\tlet formattedPrivateKey = '';\n\tconst parts = privateKey.split('-----').filter((item) => item !== '');\n\tparts.forEach((part) => {\n\t\tif (regex.test(part)) {\n\t\t\tformattedPrivateKey += `-----${part}-----`;\n\t\t} else {\n\t\t\tconst passRegex = /Proc-Type|DEK-Info/;\n\t\t\tif (passRegex.test(part)) {\n\t\t\t\tpart = part.replace(/:\\s+/g, ':');\n\t\t\t\tformattedPrivateKey += part.replace(/\\\\n/g, '\\n').replace(/\\s+/g, '\\n');\n\t\t\t} else {\n\t\t\t\tformattedPrivateKey += part.replace(/\\\\n/g, '\\n').replace(/\\s+/g, '\\n');\n\t\t\t}\n\t\t}\n\t});\n\treturn formattedPrivateKey;\n}\n\n/**\n * @TECH_DEBT Explore replacing with handlebars\n */\nexport function getResolvables(expression: string) {\n\tif (!expression) return [];\n\n\tconst resolvables = [];\n\tconst resolvableRegex = /({{[\\s\\S]*?}})/g;\n\n\tlet match;\n\n\twhile ((match = resolvableRegex.exec(expression)) !== null) {\n\t\tif (match[1]) {\n\t\t\tresolvables.push(match[1]);\n\t\t}\n\t}\n\n\treturn resolvables;\n}\n\n/**\n * Flattens an object with deep data\n *\n * @param {IDataObject} data The object to flatten\n */\nexport function flattenObject(data: IDataObject) {\n\tconst returnData: IDataObject = {};\n\tfor (const key1 of Object.keys(data)) {\n\t\tif (data[key1] !== null && typeof data[key1] === 'object') {\n\t\t\tif (data[key1] instanceof Date) {\n\t\t\t\treturnData[key1] = data[key1]?.toString();\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tconst flatObject = flattenObject(data[key1] as IDataObject);\n\t\t\tfor (const key2 in flatObject) {\n\t\t\t\tif (flatObject[key2] === undefined) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\treturnData[`${key1}.${key2}`] = flatObject[key2];\n\t\t\t}\n\t\t} else {\n\t\t\treturnData[key1] = data[key1];\n\t\t}\n\t}\n\treturn returnData;\n}\n\n/**\n * Capitalizes the first letter of a string\n *\n * @param {string} string The string to capitalize\n */\nexport function capitalize(str: string): string {\n\tif (!str) return str;\n\n\tconst chars = str.split('');\n\tchars[0] = chars[0].toUpperCase();\n\n\treturn chars.join('');\n}\n\nexport function generatePairedItemData(length: number): IPairedItemData[] {\n\treturn Array.from({ length }, (_, item) => ({\n\t\titem,\n\t}));\n}\n\n/**\n * Output Paired Item Data Array\n *\n * @param {number | IPairedItemData | IPairedItemData[] | undefined} pairedItem\n */\nexport function preparePairedItemDataArray(\n\tpairedItem: number | IPairedItemData | IPairedItemData[] | undefined,\n): IPairedItemData[] {\n\tif (pairedItem === undefined) return [];\n\tif (typeof pairedItem === 'number') return [{ item: pairedItem }];\n\tif (Array.isArray(pairedItem)) return pairedItem;\n\treturn [pairedItem];\n}\n\nexport const sanitizeDataPathKey = (item: IDataObject, key: string) => {\n\tif (item[key] !== undefined) {\n\t\treturn key;\n\t}\n\n\tif (\n\t\t(key.startsWith(\"['\") && key.endsWith(\"']\")) ||\n\t\t(key.startsWith('[\"') && key.endsWith('\"]'))\n\t) {\n\t\tkey = key.slice(2, -2);\n\t\tif (item[key] !== undefined) {\n\t\t\treturn key;\n\t\t}\n\t}\n\treturn key;\n};\n\n/**\n * Escape HTML\n *\n * @param {string} text The text to escape\n */\nexport function escapeHtml(text: string): string {\n\tif (!text) return '';\n\treturn text.replace(/&amp;|&lt;|&gt;|&#39;|&quot;/g, (match) => {\n\t\tswitch (match) {\n\t\t\tcase '&amp;':\n\t\t\t\treturn '&';\n\t\t\tcase '&lt;':\n\t\t\t\treturn '<';\n\t\t\tcase '&gt;':\n\t\t\t\treturn '>';\n\t\t\tcase '&#39;':\n\t\t\t\treturn \"'\";\n\t\t\tcase '&quot;':\n\t\t\t\treturn '\"';\n\t\t\tdefault:\n\t\t\t\treturn match;\n\t\t}\n\t});\n}\n\n/**\n * Sorts each item json's keys by a priority list\n *\n * @param {INodeExecutionData[]} data The array of items which keys will be sorted\n * @param {string[]} priorityList The priority list, keys of item.json will be sorted in this order first then alphabetically\n */\nexport function sortItemKeysByPriorityList(data: INodeExecutionData[], priorityList: string[]) {\n\treturn data.map((item) => {\n\t\tconst itemKeys = Object.keys(item.json);\n\n\t\tconst updatedKeysOrder = itemKeys.sort((a, b) => {\n\t\t\tconst indexA = priorityList.indexOf(a);\n\t\t\tconst indexB = priorityList.indexOf(b);\n\n\t\t\tif (indexA !== -1 && indexB !== -1) {\n\t\t\t\treturn indexA - indexB;\n\t\t\t} else if (indexA !== -1) {\n\t\t\t\treturn -1;\n\t\t\t} else if (indexB !== -1) {\n\t\t\t\treturn 1;\n\t\t\t}\n\t\t\treturn a.localeCompare(b);\n\t\t});\n\n\t\tconst updatedItem: IDataObject = {};\n\t\tfor (const key of updatedKeysOrder) {\n\t\t\tupdatedItem[key] = item.json[key];\n\t\t}\n\n\t\titem.json = updatedItem;\n\t\treturn item;\n\t});\n}\n\nexport function createUtmCampaignLink(nodeType: string, instanceId?: string) {\n\treturn `https://n8n.io/?utm_source=n8n-internal&utm_medium=powered_by&utm_campaign=${encodeURIComponent(\n\t\tnodeType,\n\t)}${instanceId ? '_' + instanceId : ''}`;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA8D;AAQ9D,0BAAuD;AAkBhD,SAAS,MAAS,OAAY,OAAO,GAAG;AAC9C,QAAM,SAAS,UAAU,OAAO,IAAI,MAAM;AAC1C,MAAI,CAAC,UAAU,OAAO,GAAG;AACxB,WAAO,CAAC;AAAA,EACT;AACA,MAAI,QAAQ;AACZ,MAAI,WAAW;AACf,QAAM,SAAS,IAAI,MAAM,KAAK,KAAK,SAAS,IAAI,CAAC;AAEjD,SAAO,QAAQ,QAAQ;AACtB,WAAO,UAAU,IAAI,MAAM,MAAM,OAAQ,SAAS,IAAK;AAAA,EACxD;AACA,SAAO;AACR;AAMO,MAAM,eAAe,CAAI,UAAqB;AACpD,WAAS,IAAI,MAAM,SAAS,GAAG,IAAI,GAAG,KAAK;AAC1C,UAAM,QAAI,+BAAU,IAAI,CAAC;AACzB,KAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,EAC3C;AACD;AAOO,MAAM,cAAc,CAAC,KAAkB,SAAmB,CAAC,MAAmB;AACpF,SAAO,KAAC,wBAAS,GAAG,IACjB,EAAE,CAAC,OAAO,KAAK,GAAG,CAAC,GAAG,IAAI,QAC1B;AAAA,IACA;AAAA,IACA,CAAC,KAAK,MAAM,YAAQ,qBAAM,KAAK,YAAY,MAAqB,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC;AAAA,IACjF,CAAC;AAAA,EACF;AACH;AAaO,SAAS,QAAW,aAAoB;AAC9C,QAAM,SAAS,CAAC;AAEhB,GAAC,SAAS,KAAK,OAAoB;AAClC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,UAAI,MAAM,QAAQ,MAAM,CAAC,CAAC,GAAG;AAC5B,aAAK,MAAM,CAAC,CAAQ;AAAA,MACrB,OAAO;AACN,eAAO,KAAK,MAAM,CAAC,CAAC;AAAA,MACrB;AAAA,IACD;AAAA,EACD,GAAG,WAAW;AAId,SAAO;AACR;AAWO,MAAM,eAAe,CAC3B,MACA,MACA,MACA,qBAA8B,UACjB;AACb,MAAI,SAAS;AACb,aAAW,OAAO,MAAM;AACvB,QAAI,CAAC,oBAAoB;AACxB,UAAI,KAAC,2BAAQ,mBAAI,KAAK,MAAM,GAAG,OAAG,mBAAI,KAAK,MAAM,GAAG,CAAC,GAAG;AACvD,iBAAS;AACT;AAAA,MACD;AAAA,IACD,OAAO;AACN,UAAI,KAAC,uBAAQ,KAAK,KAAK,GAAG,GAAG,KAAK,KAAK,GAAG,CAAC,GAAG;AAC7C,iBAAS;AACT;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACA,SAAO;AACR;AAEO,SAAS,qBACf,gBACA,YACC;AACD,SAAO,WAAW,IAAI,CAAC,iBAAiB;AACvC,WAAO;AAAA,MACN,GAAG;AAAA,MACH,oBAAgB,qBAAM,CAAC,GAAG,aAAa,gBAAgB,cAAc;AAAA,IACtE;AAAA,EACD,CAAC;AACF;AAEO,SAAS,iBAAoB,UAAa,WAAoB;AACpE,MAAI;AACJ,QAAM,QAAQ,YAAY,IAAI,SAAS,OAAO;AAE9C,MAAI,OAAO,aAAa,UAAU;AACjC,QAAI;AACH,mBAAS,+BAAU,QAAQ;AAAA,IAC5B,SAAS,OAAO;AACf,YAAM,IAAI,qCAAiB,SAAS,KAAK,8BAA8B,EAAE,OAAO,UAAU,CAAC;AAAA,IAC5F;AAAA,EACD,WAAW,OAAO,aAAa,UAAU;AACxC,aAAS;AAAA,EACV,OAAO;AACN,UAAM,IAAI,qCAAiB,SAAS,KAAK,8BAA8B,EAAE,OAAO,UAAU,CAAC;AAAA,EAC5F;AAEA,SAAO;AACR;AAEA,SAAS,QAAW,OAAU;AAC7B,UAAI,sBAAO,KAAK,EAAG,QAAO;AAC1B,MAAI,OAAO,UAAU,YAAY,UAAU,GAAI,QAAO;AACtD,MAAI,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW,EAAG,QAAO;AACvD,SAAO;AACR;AAEA,MAAM,gCAAgC,CAAC,KAAa,QAAqB;AACxE,MAAI;AACH,UAAM,kBAAc,+BAAU,GAAG;AACjC,eAAO,uBAAQ,aAAa,GAAG;AAAA,EAChC,SAAS,OAAO;AACf,WAAO;AAAA,EACR;AACD;AAEO,MAAM,eAAe,CAAC,iBAA0B,iBAAiB,MAAM;AAC7E,MAAI,CAAC,iBAAiB;AAErB,WAAO,CAAO,OAAU,cAAa,uBAAQ,OAAO,KAAK;AAAA,EAC1D;AAEA,SAAO,CAAO,OAAU,UAAa;AAEpC,QAAI,KAAC,sBAAO,KAAK,KAAK,KAAC,sBAAO,KAAK,KAAK,OAAO,UAAU,OAAO,OAAO;AACtE,iBAAO,uBAAQ,OAAO,KAAK;AAAA,IAC5B;AAEA,QAAI,kBAAkB,GAAG;AAExB,cAAI,sBAAO,KAAK,UAAM,sBAAO,KAAK,KAAK,UAAU,KAAK,UAAU,MAAM;AACrE,eAAO;AAAA,MACR;AAEA,cAAI,sBAAO,KAAK,UAAM,sBAAO,KAAK,KAAK,UAAU,KAAK,UAAU,MAAM;AACrE,eAAO;AAAA,MACR;AAAA,IACD;AAGA,QAAI,QAAQ,KAAK,KAAK,QAAQ,KAAK,EAAG,QAAO;AAG7C,QAAI,QAAQ,KAAK,KAAK,UAAU,OAAW,QAAO;AAClD,QAAI,UAAU,UAAa,QAAQ,KAAK,EAAG,QAAO;AAGlD,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AAC3D,aAAO,MAAM,SAAS,MAAM;AAAA,IAC7B;AAEA,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AAC3D,aAAO,UAAU,MAAM,SAAS;AAAA,IACjC;AAGA,QAAI,KAAC,sBAAO,KAAK,KAAK,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AAC7E,aAAO,8BAA8B,OAAO,KAAoB;AAAA,IACjE;AAEA,QAAI,KAAC,sBAAO,KAAK,KAAK,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AAC7E,aAAO,8BAA8B,OAAO,KAAoB;AAAA,IACjE;AAGA,QAAI,OAAO,UAAU,aAAa,OAAO,UAAU,UAAU;AAC5D,UAAI,UAAU,QAAQ,MAAM,kBAAkB,MAAM,OAAQ,QAAO;AACnE,UAAI,UAAU,SAAS,MAAM,kBAAkB,MAAM,QAAS,QAAO;AAAA,IACtE;AAEA,QAAI,OAAO,UAAU,aAAa,OAAO,UAAU,UAAU;AAC5D,UAAI,UAAU,QAAQ,MAAM,kBAAkB,MAAM,OAAQ,QAAO;AACnE,UAAI,UAAU,SAAS,MAAM,kBAAkB,MAAM,QAAS,QAAO;AAAA,IACtE;AAGA,QAAI,OAAO,UAAU,aAAa,OAAO,UAAU,UAAU;AAC5D,UAAI,UAAU,QAAQ,UAAU,EAAG,QAAO;AAC1C,UAAI,UAAU,SAAS,UAAU,EAAG,QAAO;AAAA,IAC5C;AAEA,QAAI,OAAO,UAAU,aAAa,OAAO,UAAU,UAAU;AAC5D,UAAI,UAAU,QAAQ,UAAU,EAAG,QAAO;AAC1C,UAAI,UAAU,SAAS,UAAU,EAAG,QAAO;AAAA,IAC5C;AAEA,QAAI,OAAO,UAAU,aAAa,OAAO,UAAU,UAAU;AAC5D,UAAI,UAAU,QAAQ,UAAU,IAAK,QAAO;AAC5C,UAAI,UAAU,SAAS,UAAU,IAAK,QAAO;AAAA,IAC9C;AAEA,QAAI,OAAO,UAAU,aAAa,OAAO,UAAU,UAAU;AAC5D,UAAI,UAAU,QAAQ,UAAU,IAAK,QAAO;AAC5C,UAAI,UAAU,SAAS,UAAU,IAAK,QAAO;AAAA,IAC9C;AAEA,eAAO,uBAAQ,OAAO,KAAK;AAAA,EAC5B;AACD;AAEO,SAAS,SAAS,MAAyD;AACjF,MAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACzB,WAAO,CAAC,EAAE,MAAM,KAAK,CAAC;AAAA,EACvB;AACA,SAAO,KAAK,IAAI,CAAC,UAAU;AAAA,IAC1B,MAAM;AAAA,EACP,EAAE;AACH;AAEO,MAAM,kBAAkB,CAAI,YAAe;AACjD,MAAI,OAAO,YAAY,YAAY,MAAM,QAAQ,OAAO,KAAK,YAAY,KAAM,QAAO;AACtF,SAAO,OAAO,QAAQ,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AAC5D,QAAI,IAAI,YAAY,CAAC,IAAI;AACzB,WAAO;AAAA,EACR,GAAG,CAAC,CAAgB;AACrB;AAOO,SAAS,iBAAiB,YAAoB,cAAc,OAAe;AACjF,MAAI,QAAQ;AACZ,MAAI,aAAa;AAChB,YAAQ;AAAA,EACT;AACA,MAAI,CAAC,cAAc,KAAK,KAAK,UAAU,GAAG;AACzC,WAAO;AAAA,EACR;AACA,MAAI,sBAAsB;AAC1B,QAAM,QAAQ,WAAW,MAAM,OAAO,EAAE,OAAO,CAAC,SAAS,SAAS,EAAE;AACpE,QAAM,QAAQ,CAAC,SAAS;AACvB,QAAI,MAAM,KAAK,IAAI,GAAG;AACrB,6BAAuB,QAAQ,IAAI;AAAA,IACpC,OAAO;AACN,YAAM,YAAY;AAClB,UAAI,UAAU,KAAK,IAAI,GAAG;AACzB,eAAO,KAAK,QAAQ,SAAS,GAAG;AAChC,+BAAuB,KAAK,QAAQ,QAAQ,IAAI,EAAE,QAAQ,QAAQ,IAAI;AAAA,MACvE,OAAO;AACN,+BAAuB,KAAK,QAAQ,QAAQ,IAAI,EAAE,QAAQ,QAAQ,IAAI;AAAA,MACvE;AAAA,IACD;AAAA,EACD,CAAC;AACD,SAAO;AACR;AAKO,SAAS,eAAe,YAAoB;AAClD,MAAI,CAAC,WAAY,QAAO,CAAC;AAEzB,QAAM,cAAc,CAAC;AACrB,QAAM,kBAAkB;AAExB,MAAI;AAEJ,UAAQ,QAAQ,gBAAgB,KAAK,UAAU,OAAO,MAAM;AAC3D,QAAI,MAAM,CAAC,GAAG;AACb,kBAAY,KAAK,MAAM,CAAC,CAAC;AAAA,IAC1B;AAAA,EACD;AAEA,SAAO;AACR;AAOO,SAAS,cAAc,MAAmB;AAChD,QAAM,aAA0B,CAAC;AACjC,aAAW,QAAQ,OAAO,KAAK,IAAI,GAAG;AACrC,QAAI,KAAK,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,MAAM,UAAU;AAC1D,UAAI,KAAK,IAAI,aAAa,MAAM;AAC/B,mBAAW,IAAI,IAAI,KAAK,IAAI,GAAG,SAAS;AACxC;AAAA,MACD;AACA,YAAM,aAAa,cAAc,KAAK,IAAI,CAAgB;AAC1D,iBAAW,QAAQ,YAAY;AAC9B,YAAI,WAAW,IAAI,MAAM,QAAW;AACnC;AAAA,QACD;AACA,mBAAW,GAAG,IAAI,IAAI,IAAI,EAAE,IAAI,WAAW,IAAI;AAAA,MAChD;AAAA,IACD,OAAO;AACN,iBAAW,IAAI,IAAI,KAAK,IAAI;AAAA,IAC7B;AAAA,EACD;AACA,SAAO;AACR;AAOO,SAAS,WAAW,KAAqB;AAC/C,MAAI,CAAC,IAAK,QAAO;AAEjB,QAAM,QAAQ,IAAI,MAAM,EAAE;AAC1B,QAAM,CAAC,IAAI,MAAM,CAAC,EAAE,YAAY;AAEhC,SAAO,MAAM,KAAK,EAAE;AACrB;AAEO,SAAS,uBAAuB,QAAmC;AACzE,SAAO,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,GAAG,UAAU;AAAA,IAC3C;AAAA,EACD,EAAE;AACH;AAOO,SAAS,2BACf,YACoB;AACpB,MAAI,eAAe,OAAW,QAAO,CAAC;AACtC,MAAI,OAAO,eAAe,SAAU,QAAO,CAAC,EAAE,MAAM,WAAW,CAAC;AAChE,MAAI,MAAM,QAAQ,UAAU,EAAG,QAAO;AACtC,SAAO,CAAC,UAAU;AACnB;AAEO,MAAM,sBAAsB,CAAC,MAAmB,QAAgB;AACtE,MAAI,KAAK,GAAG,MAAM,QAAW;AAC5B,WAAO;AAAA,EACR;AAEA,MACE,IAAI,WAAW,IAAI,KAAK,IAAI,SAAS,IAAI,KACzC,IAAI,WAAW,IAAI,KAAK,IAAI,SAAS,IAAI,GACzC;AACD,UAAM,IAAI,MAAM,GAAG,EAAE;AACrB,QAAI,KAAK,GAAG,MAAM,QAAW;AAC5B,aAAO;AAAA,IACR;AAAA,EACD;AACA,SAAO;AACR;AAOO,SAAS,WAAW,MAAsB;AAChD,MAAI,CAAC,KAAM,QAAO;AAClB,SAAO,KAAK,QAAQ,iCAAiC,CAAC,UAAU;AAC/D,YAAQ,OAAO;AAAA,MACd,KAAK;AACJ,eAAO;AAAA,MACR,KAAK;AACJ,eAAO;AAAA,MACR,KAAK;AACJ,eAAO;AAAA,MACR,KAAK;AACJ,eAAO;AAAA,MACR,KAAK;AACJ,eAAO;AAAA,MACR;AACC,eAAO;AAAA,IACT;AAAA,EACD,CAAC;AACF;AAQO,SAAS,2BAA2B,MAA4B,cAAwB;AAC9F,SAAO,KAAK,IAAI,CAAC,SAAS;AACzB,UAAM,WAAW,OAAO,KAAK,KAAK,IAAI;AAEtC,UAAM,mBAAmB,SAAS,KAAK,CAAC,GAAG,MAAM;AAChD,YAAM,SAAS,aAAa,QAAQ,CAAC;AACrC,YAAM,SAAS,aAAa,QAAQ,CAAC;AAErC,UAAI,WAAW,MAAM,WAAW,IAAI;AACnC,eAAO,SAAS;AAAA,MACjB,WAAW,WAAW,IAAI;AACzB,eAAO;AAAA,MACR,WAAW,WAAW,IAAI;AACzB,eAAO;AAAA,MACR;AACA,aAAO,EAAE,cAAc,CAAC;AAAA,IACzB,CAAC;AAED,UAAM,cAA2B,CAAC;AAClC,eAAW,OAAO,kBAAkB;AACnC,kBAAY,GAAG,IAAI,KAAK,KAAK,GAAG;AAAA,IACjC;AAEA,SAAK,OAAO;AACZ,WAAO;AAAA,EACR,CAAC;AACF;AAEO,SAAS,sBAAsB,UAAkB,YAAqB;AAC5E,SAAO,8EAA8E;AAAA,IACpF;AAAA,EACD,CAAC,GAAG,aAAa,MAAM,aAAa,EAAE;AACvC;", "names": []}