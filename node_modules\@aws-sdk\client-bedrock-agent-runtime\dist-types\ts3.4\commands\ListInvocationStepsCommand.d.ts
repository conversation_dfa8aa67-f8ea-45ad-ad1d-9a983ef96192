import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  BedrockAgentRuntimeClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../BedrockAgentRuntimeClient";
import {
  ListInvocationStepsRequest,
  ListInvocationStepsResponse,
} from "../models/models_1";
export { __MetadataBearer };
export { $Command };
export interface ListInvocationStepsCommandInput
  extends ListInvocationStepsRequest {}
export interface ListInvocationStepsCommandOutput
  extends ListInvocationStepsResponse,
    __MetadataBearer {}
declare const ListInvocationStepsCommand_base: {
  new (
    input: ListInvocationStepsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListInvocationStepsCommandInput,
    ListInvocationStepsCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListInvocationStepsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListInvocationStepsCommandInput,
    ListInvocationStepsCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListInvocationStepsCommand extends ListInvocationStepsCommand_base {
  protected static __types: {
    api: {
      input: ListInvocationStepsRequest;
      output: ListInvocationStepsResponse;
    };
    sdk: {
      input: ListInvocationStepsCommandInput;
      output: ListInvocationStepsCommandOutput;
    };
  };
}
