{"version": 3, "sources": ["../../../nodes/Zulip/UserInterface.ts"], "sourcesContent": ["import type { IDataObject } from 'n8n-workflow';\n\nexport interface IUser {\n\tclient_gravatar?: boolean;\n\tinclude_custom_profile_fields?: boolean;\n\tfull_name?: string;\n\tis_admin?: boolean;\n\tis_guest?: boolean;\n\tprofile_data?: [IDataObject];\n\temail?: string;\n\tpassword?: string;\n\tshort_name?: string;\n\trole?: number;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}