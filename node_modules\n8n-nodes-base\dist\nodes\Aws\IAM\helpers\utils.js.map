{"version": 3, "sources": ["../../../../../nodes/Aws/IAM/helpers/utils.ts"], "sourcesContent": ["import type {\n\tIHttpRequestOptions,\n\tIDataObject,\n\tIExecuteSingleFunctions,\n\tIN8nHttpFullResponse,\n\tINodeExecutionData,\n\tJsonObject,\n} from 'n8n-workflow';\nimport { NodeApiError, NodeOperationError } from 'n8n-workflow';\n\nimport { CURRENT_VERSION } from './constants';\nimport type {\n\tGetAllGroupsResponseBody,\n\tGetAllUsersResponseBody,\n\tGetGroupResponseBody,\n\tTags,\n} from './types';\nimport { searchGroupsForUser } from '../methods/listSearch';\nimport { awsApiRequest } from '../transport';\n\nexport async function encodeBodyAsFormUrlEncoded(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tif (requestOptions.body) {\n\t\trequestOptions.body = new URLSearchParams(\n\t\t\trequestOptions.body as Record<string, string>,\n\t\t).toString();\n\t}\n\treturn requestOptions;\n}\n\nexport async function findUsersForGroup(\n\tthis: IExecuteSingleFunctions,\n\tgroupName: string,\n): Promise<IDataObject[]> {\n\tconst options: IHttpRequestOptions = {\n\t\tmethod: 'POST',\n\t\turl: '',\n\t\tbody: new URLSearchParams({\n\t\t\tAction: 'GetGroup',\n\t\t\tVersion: CURRENT_VERSION,\n\t\t\tGroupName: groupName,\n\t\t}).toString(),\n\t};\n\tconst responseData = (await awsApiRequest.call(this, options)) as GetGroupResponseBody;\n\treturn responseData?.GetGroupResponse?.GetGroupResult?.Users ?? [];\n}\n\nexport async function simplifyGetGroupsResponse(\n\tthis: IExecuteSingleFunctions,\n\t_: INodeExecutionData[],\n\tresponse: IN8nHttpFullResponse,\n): Promise<INodeExecutionData[]> {\n\tconst includeUsers = this.getNodeParameter('includeUsers', false);\n\tconst responseBody = response.body as GetGroupResponseBody;\n\tconst groupData = responseBody.GetGroupResponse.GetGroupResult;\n\tconst group = groupData.Group;\n\treturn [\n\t\t{ json: includeUsers ? { ...group, Users: groupData.Users ?? [] } : group },\n\t] as INodeExecutionData[];\n}\n\nexport async function simplifyGetAllGroupsResponse(\n\tthis: IExecuteSingleFunctions,\n\titems: INodeExecutionData[],\n\tresponse: IN8nHttpFullResponse,\n): Promise<INodeExecutionData[]> {\n\tconst includeUsers = this.getNodeParameter('includeUsers', false);\n\tconst responseBody = response.body as GetAllGroupsResponseBody;\n\tconst groups = responseBody.ListGroupsResponse.ListGroupsResult.Groups ?? [];\n\n\tif (groups.length === 0) {\n\t\treturn items;\n\t}\n\n\tif (!includeUsers) {\n\t\treturn this.helpers.returnJsonArray(groups);\n\t}\n\n\tconst processedItems: IDataObject[] = [];\n\tfor (const group of groups) {\n\t\tconst users = await findUsersForGroup.call(this, group.GroupName);\n\t\tprocessedItems.push({ ...group, Users: users });\n\t}\n\treturn this.helpers.returnJsonArray(processedItems);\n}\n\nexport async function simplifyGetAllUsersResponse(\n\tthis: IExecuteSingleFunctions,\n\t_items: INodeExecutionData[],\n\tresponse: IN8nHttpFullResponse,\n): Promise<INodeExecutionData[]> {\n\tif (!response.body) {\n\t\treturn [];\n\t}\n\tconst responseBody = response.body as GetAllUsersResponseBody;\n\tconst users = responseBody?.ListUsersResponse?.ListUsersResult?.Users ?? [];\n\treturn this.helpers.returnJsonArray(users);\n}\n\nexport async function deleteGroupMembers(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst groupName = this.getNodeParameter('group', undefined, { extractValue: true }) as string;\n\n\tconst users = await findUsersForGroup.call(this, groupName);\n\tif (!users.length) {\n\t\treturn requestOptions;\n\t}\n\n\tawait Promise.all(\n\t\tusers.map(async (user) => {\n\t\t\tconst userName = user.UserName as string;\n\t\t\tif (!user.UserName) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\tawait awsApiRequest.call(this, {\n\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\turl: '',\n\t\t\t\t\tbody: {\n\t\t\t\t\t\tAction: 'RemoveUserFromGroup',\n\t\t\t\t\t\tGroupName: groupName,\n\t\t\t\t\t\tUserName: userName,\n\t\t\t\t\t\tVersion: CURRENT_VERSION,\n\t\t\t\t\t},\n\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tthrow new NodeApiError(this.getNode(), error as JsonObject, {\n\t\t\t\t\tmessage: `Failed to remove user \"${userName}\" from \"${groupName}\"!`,\n\t\t\t\t});\n\t\t\t}\n\t\t}),\n\t);\n\n\treturn requestOptions;\n}\n\nexport async function validatePath(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst path = this.getNodeParameter('additionalFields.path') as string;\n\tif (path.length < 1 || path.length > 512) {\n\t\tthrow new NodeOperationError(\n\t\t\tthis.getNode(),\n\t\t\t'The \"Path\" parameter must be between 1 and 512 characters long.',\n\t\t);\n\t}\n\n\tconst validPathRegex = /^\\/[\\u0021-\\u007E]*\\/$/;\n\tif (!validPathRegex.test(path) && path !== '/') {\n\t\tthrow new NodeOperationError(\n\t\t\tthis.getNode(),\n\t\t\t'Ensure the path is structured correctly, e.g. /division_abc/subdivision_xyz/',\n\t\t);\n\t}\n\n\treturn requestOptions;\n}\n\nexport async function validateUserPath(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst prefix = this.getNodeParameter('additionalFields.pathPrefix') as string;\n\n\tlet formattedPrefix = prefix;\n\tif (!formattedPrefix.startsWith('/')) {\n\t\tformattedPrefix = '/' + formattedPrefix;\n\t}\n\tif (!formattedPrefix.endsWith('/') && formattedPrefix !== '/') {\n\t\tformattedPrefix = formattedPrefix + '/';\n\t}\n\n\tif (requestOptions.body && typeof requestOptions.body === 'object') {\n\t\tObject.assign(requestOptions.body, { PathPrefix: formattedPrefix });\n\t}\n\n\tconst options: IHttpRequestOptions = {\n\t\tmethod: 'POST',\n\t\turl: '',\n\t\tbody: {\n\t\t\tAction: 'ListUsers',\n\t\t\tVersion: CURRENT_VERSION,\n\t\t},\n\t};\n\tconst responseData = (await awsApiRequest.call(this, options)) as GetAllUsersResponseBody;\n\n\tconst users = responseData.ListUsersResponse.ListUsersResult.Users;\n\tif (!users || users.length === 0) {\n\t\tthrow new NodeOperationError(\n\t\t\tthis.getNode(),\n\t\t\t'No users found. Please adjust the \"Path\" parameter and try again.',\n\t\t);\n\t}\n\n\tconst userPaths = users.map((user) => user.Path).filter(Boolean);\n\tconst isPathValid = userPaths.some((path) => path?.startsWith(formattedPrefix));\n\tif (!isPathValid) {\n\t\tthrow new NodeOperationError(\n\t\t\tthis.getNode(),\n\t\t\t`The \"${formattedPrefix}\" path was not found in your users. Try entering a different path.`,\n\t\t);\n\t}\n\treturn requestOptions;\n}\n\nexport async function validateName(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst resource = this.getNodeParameter('resource') as string;\n\tconst nameParam = resource === 'user' ? 'userName' : 'groupName';\n\tconst name = this.getNodeParameter(nameParam) as string;\n\n\tconst maxLength = resource === 'user' ? 64 : 128;\n\tconst capitalizedResource = resource.replace(/^./, (c) => c.toUpperCase());\n\tconst validNamePattern = /^[a-zA-Z0-9-_]+$/;\n\n\tconst isInvalid = !validNamePattern.test(name) || name.length > maxLength;\n\n\tif (/\\s/.test(name)) {\n\t\tthrow new NodeOperationError(\n\t\t\tthis.getNode(),\n\t\t\t`${capitalizedResource} name should not contain spaces.`,\n\t\t);\n\t}\n\n\tif (isInvalid) {\n\t\tthrow new NodeOperationError(\n\t\t\tthis.getNode(),\n\t\t\t`${capitalizedResource} name can have up to ${maxLength} characters. Valid characters: letters, numbers, hyphens (-), and underscores (_).`,\n\t\t);\n\t}\n\n\treturn requestOptions;\n}\n\nexport async function validatePermissionsBoundary(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst permissionsBoundary = this.getNodeParameter(\n\t\t'additionalFields.permissionsBoundary',\n\t) as string;\n\n\tif (permissionsBoundary) {\n\t\tconst arnPattern = /^arn:aws:iam::\\d{12}:policy\\/[\\w\\-+\\/=._]+$/;\n\n\t\tif (!arnPattern.test(permissionsBoundary)) {\n\t\t\tthrow new NodeOperationError(\n\t\t\t\tthis.getNode(),\n\t\t\t\t'Permissions boundaries must be provided in ARN format (e.g. arn:aws:iam::123456789012:policy/ExampleBoundaryPolicy). These can be found at the top of the permissions boundary detail page in the IAM dashboard.',\n\t\t\t);\n\t\t}\n\n\t\tif (requestOptions.body) {\n\t\t\tObject.assign(requestOptions.body, { PermissionsBoundary: permissionsBoundary });\n\t\t} else {\n\t\t\trequestOptions.body = {\n\t\t\t\tPermissionsBoundary: permissionsBoundary,\n\t\t\t};\n\t\t}\n\t}\n\treturn requestOptions;\n}\n\nexport async function preprocessTags(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst tagsData = this.getNodeParameter('additionalFields.tags') as Tags;\n\tconst tags = tagsData?.tags || [];\n\n\tlet bodyObj: Record<string, string> = {};\n\tif (typeof requestOptions.body === 'string') {\n\t\tconst params = new URLSearchParams(requestOptions.body);\n\t\tbodyObj = Object.fromEntries(params.entries());\n\t}\n\n\ttags.forEach((tag, index) => {\n\t\tif (!tag.key || !tag.value) {\n\t\t\tthrow new NodeOperationError(\n\t\t\t\tthis.getNode(),\n\t\t\t\t`Tag at position ${index + 1} is missing '${!tag.key ? 'Key' : 'Value'}'. Both 'Key' and 'Value' are required.`,\n\t\t\t);\n\t\t}\n\t\tbodyObj[`Tags.member.${index + 1}.Key`] = tag.key;\n\t\tbodyObj[`Tags.member.${index + 1}.Value`] = tag.value;\n\t});\n\n\trequestOptions.body = new URLSearchParams(bodyObj).toString();\n\n\treturn requestOptions;\n}\n\nexport async function removeUserFromGroups(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst userName = this.getNodeParameter('user', undefined, { extractValue: true });\n\tconst userGroups = await searchGroupsForUser.call(this);\n\n\tfor (const group of userGroups.results) {\n\t\tawait awsApiRequest.call(this, {\n\t\t\tmethod: 'POST',\n\t\t\turl: '',\n\t\t\tbody: {\n\t\t\t\tAction: 'RemoveUserFromGroup',\n\t\t\t\tVersion: CURRENT_VERSION,\n\t\t\t\tGroupName: group.value,\n\t\t\t\tUserName: userName,\n\t\t\t},\n\t\t});\n\t}\n\n\treturn requestOptions;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,0BAAiD;AAEjD,uBAAgC;AAOhC,wBAAoC;AACpC,uBAA8B;AAE9B,eAAsB,2BAErB,gBAC+B;AAC/B,MAAI,eAAe,MAAM;AACxB,mBAAe,OAAO,IAAI;AAAA,MACzB,eAAe;AAAA,IAChB,EAAE,SAAS;AAAA,EACZ;AACA,SAAO;AACR;AAEA,eAAsB,kBAErB,WACyB;AACzB,QAAM,UAA+B;AAAA,IACpC,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM,IAAI,gBAAgB;AAAA,MACzB,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,IACZ,CAAC,EAAE,SAAS;AAAA,EACb;AACA,QAAM,eAAgB,MAAM,+BAAc,KAAK,MAAM,OAAO;AAC5D,SAAO,cAAc,kBAAkB,gBAAgB,SAAS,CAAC;AAClE;AAEA,eAAsB,0BAErB,GACA,UACgC;AAChC,QAAM,eAAe,KAAK,iBAAiB,gBAAgB,KAAK;AAChE,QAAM,eAAe,SAAS;AAC9B,QAAM,YAAY,aAAa,iBAAiB;AAChD,QAAM,QAAQ,UAAU;AACxB,SAAO;AAAA,IACN,EAAE,MAAM,eAAe,EAAE,GAAG,OAAO,OAAO,UAAU,SAAS,CAAC,EAAE,IAAI,MAAM;AAAA,EAC3E;AACD;AAEA,eAAsB,6BAErB,OACA,UACgC;AAChC,QAAM,eAAe,KAAK,iBAAiB,gBAAgB,KAAK;AAChE,QAAM,eAAe,SAAS;AAC9B,QAAM,SAAS,aAAa,mBAAmB,iBAAiB,UAAU,CAAC;AAE3E,MAAI,OAAO,WAAW,GAAG;AACxB,WAAO;AAAA,EACR;AAEA,MAAI,CAAC,cAAc;AAClB,WAAO,KAAK,QAAQ,gBAAgB,MAAM;AAAA,EAC3C;AAEA,QAAM,iBAAgC,CAAC;AACvC,aAAW,SAAS,QAAQ;AAC3B,UAAM,QAAQ,MAAM,kBAAkB,KAAK,MAAM,MAAM,SAAS;AAChE,mBAAe,KAAK,EAAE,GAAG,OAAO,OAAO,MAAM,CAAC;AAAA,EAC/C;AACA,SAAO,KAAK,QAAQ,gBAAgB,cAAc;AACnD;AAEA,eAAsB,4BAErB,QACA,UACgC;AAChC,MAAI,CAAC,SAAS,MAAM;AACnB,WAAO,CAAC;AAAA,EACT;AACA,QAAM,eAAe,SAAS;AAC9B,QAAM,QAAQ,cAAc,mBAAmB,iBAAiB,SAAS,CAAC;AAC1E,SAAO,KAAK,QAAQ,gBAAgB,KAAK;AAC1C;AAEA,eAAsB,mBAErB,gBAC+B;AAC/B,QAAM,YAAY,KAAK,iBAAiB,SAAS,QAAW,EAAE,cAAc,KAAK,CAAC;AAElF,QAAM,QAAQ,MAAM,kBAAkB,KAAK,MAAM,SAAS;AAC1D,MAAI,CAAC,MAAM,QAAQ;AAClB,WAAO;AAAA,EACR;AAEA,QAAM,QAAQ;AAAA,IACb,MAAM,IAAI,OAAO,SAAS;AACzB,YAAM,WAAW,KAAK;AACtB,UAAI,CAAC,KAAK,UAAU;AACnB;AAAA,MACD;AAEA,UAAI;AACH,cAAM,+BAAc,KAAK,MAAM;AAAA,UAC9B,QAAQ;AAAA,UACR,KAAK;AAAA,UACL,MAAM;AAAA,YACL,QAAQ;AAAA,YACR,WAAW;AAAA,YACX,UAAU;AAAA,YACV,SAAS;AAAA,UACV;AAAA,UACA,wBAAwB;AAAA,QACzB,CAAC;AAAA,MACF,SAAS,OAAO;AACf,cAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,OAAqB;AAAA,UAC3D,SAAS,0BAA0B,QAAQ,WAAW,SAAS;AAAA,QAChE,CAAC;AAAA,MACF;AAAA,IACD,CAAC;AAAA,EACF;AAEA,SAAO;AACR;AAEA,eAAsB,aAErB,gBAC+B;AAC/B,QAAM,OAAO,KAAK,iBAAiB,uBAAuB;AAC1D,MAAI,KAAK,SAAS,KAAK,KAAK,SAAS,KAAK;AACzC,UAAM,IAAI;AAAA,MACT,KAAK,QAAQ;AAAA,MACb;AAAA,IACD;AAAA,EACD;AAEA,QAAM,iBAAiB;AACvB,MAAI,CAAC,eAAe,KAAK,IAAI,KAAK,SAAS,KAAK;AAC/C,UAAM,IAAI;AAAA,MACT,KAAK,QAAQ;AAAA,MACb;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AAEA,eAAsB,iBAErB,gBAC+B;AAC/B,QAAM,SAAS,KAAK,iBAAiB,6BAA6B;AAElE,MAAI,kBAAkB;AACtB,MAAI,CAAC,gBAAgB,WAAW,GAAG,GAAG;AACrC,sBAAkB,MAAM;AAAA,EACzB;AACA,MAAI,CAAC,gBAAgB,SAAS,GAAG,KAAK,oBAAoB,KAAK;AAC9D,sBAAkB,kBAAkB;AAAA,EACrC;AAEA,MAAI,eAAe,QAAQ,OAAO,eAAe,SAAS,UAAU;AACnE,WAAO,OAAO,eAAe,MAAM,EAAE,YAAY,gBAAgB,CAAC;AAAA,EACnE;AAEA,QAAM,UAA+B;AAAA,IACpC,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM;AAAA,MACL,QAAQ;AAAA,MACR,SAAS;AAAA,IACV;AAAA,EACD;AACA,QAAM,eAAgB,MAAM,+BAAc,KAAK,MAAM,OAAO;AAE5D,QAAM,QAAQ,aAAa,kBAAkB,gBAAgB;AAC7D,MAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AACjC,UAAM,IAAI;AAAA,MACT,KAAK,QAAQ;AAAA,MACb;AAAA,IACD;AAAA,EACD;AAEA,QAAM,YAAY,MAAM,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE,OAAO,OAAO;AAC/D,QAAM,cAAc,UAAU,KAAK,CAAC,SAAS,MAAM,WAAW,eAAe,CAAC;AAC9E,MAAI,CAAC,aAAa;AACjB,UAAM,IAAI;AAAA,MACT,KAAK,QAAQ;AAAA,MACb,QAAQ,eAAe;AAAA,IACxB;AAAA,EACD;AACA,SAAO;AACR;AAEA,eAAsB,aAErB,gBAC+B;AAC/B,QAAM,WAAW,KAAK,iBAAiB,UAAU;AACjD,QAAM,YAAY,aAAa,SAAS,aAAa;AACrD,QAAM,OAAO,KAAK,iBAAiB,SAAS;AAE5C,QAAM,YAAY,aAAa,SAAS,KAAK;AAC7C,QAAM,sBAAsB,SAAS,QAAQ,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC;AACzE,QAAM,mBAAmB;AAEzB,QAAM,YAAY,CAAC,iBAAiB,KAAK,IAAI,KAAK,KAAK,SAAS;AAEhE,MAAI,KAAK,KAAK,IAAI,GAAG;AACpB,UAAM,IAAI;AAAA,MACT,KAAK,QAAQ;AAAA,MACb,GAAG,mBAAmB;AAAA,IACvB;AAAA,EACD;AAEA,MAAI,WAAW;AACd,UAAM,IAAI;AAAA,MACT,KAAK,QAAQ;AAAA,MACb,GAAG,mBAAmB,wBAAwB,SAAS;AAAA,IACxD;AAAA,EACD;AAEA,SAAO;AACR;AAEA,eAAsB,4BAErB,gBAC+B;AAC/B,QAAM,sBAAsB,KAAK;AAAA,IAChC;AAAA,EACD;AAEA,MAAI,qBAAqB;AACxB,UAAM,aAAa;AAEnB,QAAI,CAAC,WAAW,KAAK,mBAAmB,GAAG;AAC1C,YAAM,IAAI;AAAA,QACT,KAAK,QAAQ;AAAA,QACb;AAAA,MACD;AAAA,IACD;AAEA,QAAI,eAAe,MAAM;AACxB,aAAO,OAAO,eAAe,MAAM,EAAE,qBAAqB,oBAAoB,CAAC;AAAA,IAChF,OAAO;AACN,qBAAe,OAAO;AAAA,QACrB,qBAAqB;AAAA,MACtB;AAAA,IACD;AAAA,EACD;AACA,SAAO;AACR;AAEA,eAAsB,eAErB,gBAC+B;AAC/B,QAAM,WAAW,KAAK,iBAAiB,uBAAuB;AAC9D,QAAM,OAAO,UAAU,QAAQ,CAAC;AAEhC,MAAI,UAAkC,CAAC;AACvC,MAAI,OAAO,eAAe,SAAS,UAAU;AAC5C,UAAM,SAAS,IAAI,gBAAgB,eAAe,IAAI;AACtD,cAAU,OAAO,YAAY,OAAO,QAAQ,CAAC;AAAA,EAC9C;AAEA,OAAK,QAAQ,CAAC,KAAK,UAAU;AAC5B,QAAI,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO;AAC3B,YAAM,IAAI;AAAA,QACT,KAAK,QAAQ;AAAA,QACb,mBAAmB,QAAQ,CAAC,gBAAgB,CAAC,IAAI,MAAM,QAAQ,OAAO;AAAA,MACvE;AAAA,IACD;AACA,YAAQ,eAAe,QAAQ,CAAC,MAAM,IAAI,IAAI;AAC9C,YAAQ,eAAe,QAAQ,CAAC,QAAQ,IAAI,IAAI;AAAA,EACjD,CAAC;AAED,iBAAe,OAAO,IAAI,gBAAgB,OAAO,EAAE,SAAS;AAE5D,SAAO;AACR;AAEA,eAAsB,qBAErB,gBAC+B;AAC/B,QAAM,WAAW,KAAK,iBAAiB,QAAQ,QAAW,EAAE,cAAc,KAAK,CAAC;AAChF,QAAM,aAAa,MAAM,sCAAoB,KAAK,IAAI;AAEtD,aAAW,SAAS,WAAW,SAAS;AACvC,UAAM,+BAAc,KAAK,MAAM;AAAA,MAC9B,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,MAAM;AAAA,QACL,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW,MAAM;AAAA,QACjB,UAAU;AAAA,MACX;AAAA,IACD,CAAC;AAAA,EACF;AAEA,SAAO;AACR;", "names": []}