{"version": 3, "sources": ["../../../../nodes/Twitter/V2/TwitterV2.node.ts"], "sourcesContent": ["import ISO6391 from 'iso-639-1';\nimport { DateTime } from 'luxon';\nimport {\n\tNodeConnectionTypes,\n\ttype IDataObject,\n\ttype IExecuteFunctions,\n\ttype ILoadOptionsFunctions,\n\ttype INodeExecutionData,\n\ttype INodeParameterResourceLocator,\n\ttype INodePropertyOptions,\n\ttype INodeType,\n\ttype INodeTypeBaseDescription,\n\ttype INodeTypeDescription,\n\ttype JsonObject,\n} from 'n8n-workflow';\n\nimport { directMessageFields, directMessageOperations } from './DirectMessageDescription';\nimport {\n\treturnId,\n\treturnIdFromUsername,\n\ttwitterApiRequest,\n\ttwitterApiRequestAllItems,\n} from './GenericFunctions';\nimport { listFields, listOperations } from './ListDescription';\nimport { tweetFields, tweetOperations } from './TweetDescription';\nimport { userFields, userOperations } from './UserDescription';\n\nexport class TwitterV2 implements INodeType {\n\tdescription: INodeTypeDescription;\n\n\tconstructor(baseDescription: INodeTypeBaseDescription) {\n\t\tthis.description = {\n\t\t\t...baseDescription,\n\t\t\tversion: 2,\n\t\t\tdescription:\n\t\t\t\t'Post, like, and search tweets, send messages, search users, and add users to lists',\n\t\t\tsubtitle: '={{$parameter[\"operation\"] + \":\" + $parameter[\"resource\"]}}',\n\t\t\tdefaults: {\n\t\t\t\tname: 'X',\n\t\t\t},\n\t\t\tusableAsTool: true,\n\t\t\tinputs: [NodeConnectionTypes.Main],\n\t\t\toutputs: [NodeConnectionTypes.Main],\n\t\t\tcredentials: [\n\t\t\t\t{\n\t\t\t\t\tname: 'twitterOAuth2Api',\n\t\t\t\t\trequired: true,\n\t\t\t\t},\n\t\t\t],\n\t\t\tproperties: [\n\t\t\t\t{\n\t\t\t\t\tdisplayName: 'Resource',\n\t\t\t\t\tname: 'resource',\n\t\t\t\t\ttype: 'options',\n\t\t\t\t\tnoDataExpression: true,\n\t\t\t\t\toptions: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Direct Message',\n\t\t\t\t\t\t\tvalue: 'directMessage',\n\t\t\t\t\t\t\tdescription: 'Send a direct message to a user',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'List',\n\t\t\t\t\t\t\tvalue: 'list',\n\t\t\t\t\t\t\tdescription: 'Add a user to a list',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'Tweet',\n\t\t\t\t\t\t\tvalue: 'tweet',\n\t\t\t\t\t\t\tdescription: 'Create, like, search, or delete a tweet',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: 'User',\n\t\t\t\t\t\t\tvalue: 'user',\n\t\t\t\t\t\t\tdescription: 'Search users by username',\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t\tdefault: 'tweet',\n\t\t\t\t},\n\t\t\t\t// DIRECT MESSAGE\n\t\t\t\t...directMessageOperations,\n\t\t\t\t...directMessageFields,\n\t\t\t\t// LIST\n\t\t\t\t...listOperations,\n\t\t\t\t...listFields,\n\t\t\t\t// TWEET\n\t\t\t\t...tweetOperations,\n\t\t\t\t...tweetFields,\n\t\t\t\t// USER\n\t\t\t\t...userOperations,\n\t\t\t\t...userFields,\n\t\t\t],\n\t\t};\n\t}\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\t// Get all the available languages to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getLanguages(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst languages = ISO6391.getAllNames();\n\t\t\t\tfor (const language of languages) {\n\t\t\t\t\tconst languageName = language;\n\t\t\t\t\tconst languageId = ISO6391.getCode(language);\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: languageName,\n\t\t\t\t\t\tvalue: languageId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: INodeExecutionData[] = [];\n\t\tconst length = items.length;\n\t\tlet responseData;\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'user') {\n\t\t\t\t\tif (operation === 'searchUser') {\n\t\t\t\t\t\tconst me = this.getNodeParameter('me', i, false) as boolean;\n\t\t\t\t\t\tif (me) {\n\t\t\t\t\t\t\tresponseData = await twitterApiRequest.call(this, 'GET', '/users/me', {});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst userRlc = this.getNodeParameter(\n\t\t\t\t\t\t\t\t'user',\n\t\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t\tundefined,\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t) as INodeParameterResourceLocator;\n\t\t\t\t\t\t\tif (userRlc.mode === 'username') {\n\t\t\t\t\t\t\t\tuserRlc.value = (userRlc.value as string).includes('@')\n\t\t\t\t\t\t\t\t\t? (userRlc.value as string).replace('@', '')\n\t\t\t\t\t\t\t\t\t: userRlc.value;\n\t\t\t\t\t\t\t\tresponseData = await twitterApiRequest.call(\n\t\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t\t`/users/by/username/${userRlc.value}`,\n\t\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t} else if (userRlc.mode === 'id') {\n\t\t\t\t\t\t\t\tresponseData = await twitterApiRequest.call(\n\t\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t\t`/users/${userRlc.value}`,\n\t\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (resource === 'tweet') {\n\t\t\t\t\tif (operation === 'search') {\n\t\t\t\t\t\tconst searchText = this.getNodeParameter('searchText', i, '', {});\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tconst { sortOrder, startTime, endTime, tweetFieldsObject } = this.getNodeParameter(\n\t\t\t\t\t\t\t'additionalFields',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t) as {\n\t\t\t\t\t\t\tsortOrder: string;\n\t\t\t\t\t\t\tstartTime: string;\n\t\t\t\t\t\t\tendTime: string;\n\t\t\t\t\t\t\ttweetFieldsObject: string[];\n\t\t\t\t\t\t};\n\t\t\t\t\t\tconst qs: IDataObject = {\n\t\t\t\t\t\t\tquery: searchText,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tif (endTime) {\n\t\t\t\t\t\t\tconst endTimeISO = DateTime.fromISO(endTime).toISO();\n\t\t\t\t\t\t\tqs.end_time = endTimeISO;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (sortOrder) {\n\t\t\t\t\t\t\tqs.sort_order = sortOrder;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (startTime) {\n\t\t\t\t\t\t\tconst startTimeISO8601 = DateTime.fromISO(startTime).toISO();\n\t\t\t\t\t\t\tqs.start_time = startTimeISO8601;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (tweetFieldsObject) {\n\t\t\t\t\t\t\tif (tweetFieldsObject.length > 0) {\n\t\t\t\t\t\t\t\tqs['tweet.fields'] = tweetFieldsObject.join(',');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await twitterApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'data',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/tweets/search/recent',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tqs.max_results = limit;\n\t\t\t\t\t\t\tresponseData = await twitterApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/tweets/search/recent',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst text = this.getNodeParameter('text', i, '', {});\n\t\t\t\t\t\tconst { location, attachments, inQuoteToStatusId, inReplyToStatusId } =\n\t\t\t\t\t\t\tthis.getNodeParameter('additionalFields', i, {}) as {\n\t\t\t\t\t\t\t\tlocation: string;\n\t\t\t\t\t\t\t\tattachments: string;\n\t\t\t\t\t\t\t\tinQuoteToStatusId: INodeParameterResourceLocator;\n\t\t\t\t\t\t\t\tinReplyToStatusId: INodeParameterResourceLocator;\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\ttext,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tif (location) {\n\t\t\t\t\t\t\tbody.geo = { place_id: location };\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (attachments) {\n\t\t\t\t\t\t\tbody.media = { media_ids: [attachments] };\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (inQuoteToStatusId) {\n\t\t\t\t\t\t\tbody.quote_tweet_id = returnId(inQuoteToStatusId);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (inReplyToStatusId) {\n\t\t\t\t\t\t\tconst inReplyToStatusIdValue = { in_reply_to_tweet_id: returnId(inReplyToStatusId) };\n\t\t\t\t\t\t\tbody.reply = inReplyToStatusIdValue;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await twitterApiRequest.call(this, 'POST', '/tweets', body);\n\t\t\t\t\t}\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tconst tweetRLC = this.getNodeParameter(\n\t\t\t\t\t\t\t'tweetDeleteId',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t'',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t) as INodeParameterResourceLocator;\n\t\t\t\t\t\tconst tweetId = returnId(tweetRLC);\n\t\t\t\t\t\tresponseData = await twitterApiRequest.call(this, 'DELETE', `/tweets/${tweetId}`, {});\n\t\t\t\t\t}\n\t\t\t\t\tif (operation === 'like') {\n\t\t\t\t\t\tconst tweetRLC = this.getNodeParameter(\n\t\t\t\t\t\t\t'tweetId',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t'',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t) as INodeParameterResourceLocator;\n\t\t\t\t\t\tconst tweetId = returnId(tweetRLC);\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\ttweet_id: tweetId,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tconst user = (await twitterApiRequest.call(this, 'GET', '/users/me', {})) as {\n\t\t\t\t\t\t\tid: string;\n\t\t\t\t\t\t};\n\t\t\t\t\t\tresponseData = await twitterApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t`/users/${user.id}/likes`,\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\tif (operation === 'retweet') {\n\t\t\t\t\t\tconst tweetRLC = this.getNodeParameter(\n\t\t\t\t\t\t\t'tweetId',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t'',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t) as INodeParameterResourceLocator;\n\t\t\t\t\t\tconst tweetId = returnId(tweetRLC);\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\ttweet_id: tweetId,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tconst user = (await twitterApiRequest.call(this, 'GET', '/users/me', {})) as {\n\t\t\t\t\t\t\tid: string;\n\t\t\t\t\t\t};\n\t\t\t\t\t\tresponseData = await twitterApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t`/users/${user.id}/retweets`,\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (resource === 'list') {\n\t\t\t\t\tif (operation === 'add') {\n\t\t\t\t\t\tconst userRlc = this.getNodeParameter(\n\t\t\t\t\t\t\t'user',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t'',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t) as INodeParameterResourceLocator;\n\t\t\t\t\t\tconst userId =\n\t\t\t\t\t\t\tuserRlc.mode !== 'username'\n\t\t\t\t\t\t\t\t? returnId(userRlc)\n\t\t\t\t\t\t\t\t: await returnIdFromUsername.call(this, userRlc);\n\t\t\t\t\t\tconst listRlc = this.getNodeParameter(\n\t\t\t\t\t\t\t'list',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t'',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t) as INodeParameterResourceLocator;\n\t\t\t\t\t\tconst listId = returnId(listRlc);\n\t\t\t\t\t\tresponseData = await twitterApiRequest.call(this, 'POST', `/lists/${listId}/members`, {\n\t\t\t\t\t\t\tuser_id: userId,\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (resource === 'directMessage') {\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst userRlc = this.getNodeParameter(\n\t\t\t\t\t\t\t'user',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t'',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t) as INodeParameterResourceLocator;\n\t\t\t\t\t\tconst user = await returnIdFromUsername.call(this, userRlc);\n\t\t\t\t\t\tconst text = this.getNodeParameter('text', i, '', {});\n\t\t\t\t\t\tconst { attachments } = this.getNodeParameter('additionalFields', i, {}, {}) as {\n\t\t\t\t\t\t\tattachments: number;\n\t\t\t\t\t\t};\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\ttext,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (attachments) {\n\t\t\t\t\t\t\tbody.attachments = [{ media_id: attachments }];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await twitterApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t`/dm_conversations/with/${user}/messages`,\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\tthis.helpers.returnJsonArray(responseData as IDataObject[]),\n\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t);\n\t\t\t\treturnData.push(...executionData);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\tconst executionErrorData = {\n\t\t\t\t\t\tjson: {\n\t\t\t\t\t\t\terror: (error as JsonObject).message,\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\t\t\t\t\treturnData.push(executionErrorData);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAAoB;AACpB,mBAAyB;AACzB,0BAYO;AAEP,sCAA6D;AAC7D,8BAKO;AACP,6BAA2C;AAC3C,8BAA6C;AAC7C,6BAA2C;AAEpC,MAAM,UAA+B;AAAA,EAG3C,YAAY,iBAA2C;AAiEvD,mBAAU;AAAA,MACT,aAAa;AAAA;AAAA;AAAA,QAGZ,MAAM,eAA2E;AAChF,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,YAAY,iBAAAA,QAAQ,YAAY;AACtC,qBAAW,YAAY,WAAW;AACjC,kBAAM,eAAe;AACrB,kBAAM,aAAa,iBAAAA,QAAQ,QAAQ,QAAQ;AAC3C,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAlFC,SAAK,cAAc;AAAA,MAClB,GAAG;AAAA,MACH,SAAS;AAAA,MACT,aACC;AAAA,MACD,UAAU;AAAA,MACV,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA;AAAA,QAEA,GAAG;AAAA,QACH,GAAG;AAAA;AAAA,QAEH,GAAG;AAAA,QACH,GAAG;AAAA;AAAA,QAEH,GAAG;AAAA,QACH,GAAG;AAAA;AAAA,QAEH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAAA,EACD;AAAA,EAsBA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAAmC,CAAC;AAC1C,UAAM,SAAS,MAAM;AACrB,QAAI;AACJ,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,YAAI,aAAa,QAAQ;AACxB,cAAI,cAAc,cAAc;AAC/B,kBAAM,KAAK,KAAK,iBAAiB,MAAM,GAAG,KAAK;AAC/C,gBAAI,IAAI;AACP,6BAAe,MAAM,0CAAkB,KAAK,MAAM,OAAO,aAAa,CAAC,CAAC;AAAA,YACzE,OAAO;AACN,oBAAM,UAAU,KAAK;AAAA,gBACpB;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,cACF;AACA,kBAAI,QAAQ,SAAS,YAAY;AAChC,wBAAQ,QAAS,QAAQ,MAAiB,SAAS,GAAG,IAClD,QAAQ,MAAiB,QAAQ,KAAK,EAAE,IACzC,QAAQ;AACX,+BAAe,MAAM,0CAAkB;AAAA,kBACtC;AAAA,kBACA;AAAA,kBACA,sBAAsB,QAAQ,KAAK;AAAA,kBACnC,CAAC;AAAA,gBACF;AAAA,cACD,WAAW,QAAQ,SAAS,MAAM;AACjC,+BAAe,MAAM,0CAAkB;AAAA,kBACtC;AAAA,kBACA;AAAA,kBACA,UAAU,QAAQ,KAAK;AAAA,kBACvB,CAAC;AAAA,gBACF;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AACA,YAAI,aAAa,SAAS;AACzB,cAAI,cAAc,UAAU;AAC3B,kBAAM,aAAa,KAAK,iBAAiB,cAAc,GAAG,IAAI,CAAC,CAAC;AAChE,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,EAAE,WAAW,WAAW,SAAS,kBAAkB,IAAI,KAAK;AAAA,cACjE;AAAA,cACA;AAAA,cACA,CAAC;AAAA,YACF;AAMA,kBAAM,KAAkB;AAAA,cACvB,OAAO;AAAA,YACR;AACA,gBAAI,SAAS;AACZ,oBAAM,aAAa,sBAAS,QAAQ,OAAO,EAAE,MAAM;AACnD,iBAAG,WAAW;AAAA,YACf;AACA,gBAAI,WAAW;AACd,iBAAG,aAAa;AAAA,YACjB;AACA,gBAAI,WAAW;AACd,oBAAM,mBAAmB,sBAAS,QAAQ,SAAS,EAAE,MAAM;AAC3D,iBAAG,aAAa;AAAA,YACjB;AACA,gBAAI,mBAAmB;AACtB,kBAAI,kBAAkB,SAAS,GAAG;AACjC,mBAAG,cAAc,IAAI,kBAAkB,KAAK,GAAG;AAAA,cAChD;AAAA,YACD;AACA,gBAAI,WAAW;AACd,6BAAe,MAAM,kDAA0B;AAAA,gBAC9C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,oBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,iBAAG,cAAc;AACjB,6BAAe,MAAM,0CAAkB;AAAA,gBACtC;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAAA,UACD;AACA,cAAI,cAAc,UAAU;AAC3B,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,GAAG,IAAI,CAAC,CAAC;AACpD,kBAAM,EAAE,UAAU,aAAa,mBAAmB,kBAAkB,IACnE,KAAK,iBAAiB,oBAAoB,GAAG,CAAC,CAAC;AAMhD,kBAAM,OAAoB;AAAA,cACzB;AAAA,YACD;AACA,gBAAI,UAAU;AACb,mBAAK,MAAM,EAAE,UAAU,SAAS;AAAA,YACjC;AACA,gBAAI,aAAa;AAChB,mBAAK,QAAQ,EAAE,WAAW,CAAC,WAAW,EAAE;AAAA,YACzC;AACA,gBAAI,mBAAmB;AACtB,mBAAK,qBAAiB,kCAAS,iBAAiB;AAAA,YACjD;AACA,gBAAI,mBAAmB;AACtB,oBAAM,yBAAyB,EAAE,0BAAsB,kCAAS,iBAAiB,EAAE;AACnF,mBAAK,QAAQ;AAAA,YACd;AACA,2BAAe,MAAM,0CAAkB,KAAK,MAAM,QAAQ,WAAW,IAAI;AAAA,UAC1E;AACA,cAAI,cAAc,UAAU;AAC3B,kBAAM,WAAW,KAAK;AAAA,cACrB;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,YACF;AACA,kBAAM,cAAU,kCAAS,QAAQ;AACjC,2BAAe,MAAM,0CAAkB,KAAK,MAAM,UAAU,WAAW,OAAO,IAAI,CAAC,CAAC;AAAA,UACrF;AACA,cAAI,cAAc,QAAQ;AACzB,kBAAM,WAAW,KAAK;AAAA,cACrB;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,YACF;AACA,kBAAM,cAAU,kCAAS,QAAQ;AACjC,kBAAM,OAAoB;AAAA,cACzB,UAAU;AAAA,YACX;AACA,kBAAM,OAAQ,MAAM,0CAAkB,KAAK,MAAM,OAAO,aAAa,CAAC,CAAC;AAGvE,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA,UAAU,KAAK,EAAE;AAAA,cACjB;AAAA,YACD;AAAA,UACD;AACA,cAAI,cAAc,WAAW;AAC5B,kBAAM,WAAW,KAAK;AAAA,cACrB;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,YACF;AACA,kBAAM,cAAU,kCAAS,QAAQ;AACjC,kBAAM,OAAoB;AAAA,cACzB,UAAU;AAAA,YACX;AACA,kBAAM,OAAQ,MAAM,0CAAkB,KAAK,MAAM,OAAO,aAAa,CAAC,CAAC;AAGvE,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA,UAAU,KAAK,EAAE;AAAA,cACjB;AAAA,YACD;AAAA,UACD;AAAA,QACD;AACA,YAAI,aAAa,QAAQ;AACxB,cAAI,cAAc,OAAO;AACxB,kBAAM,UAAU,KAAK;AAAA,cACpB;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,YACF;AACA,kBAAM,SACL,QAAQ,SAAS,iBACd,kCAAS,OAAO,IAChB,MAAM,6CAAqB,KAAK,MAAM,OAAO;AACjD,kBAAM,UAAU,KAAK;AAAA,cACpB;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,YACF;AACA,kBAAM,aAAS,kCAAS,OAAO;AAC/B,2BAAe,MAAM,0CAAkB,KAAK,MAAM,QAAQ,UAAU,MAAM,YAAY;AAAA,cACrF,SAAS;AAAA,YACV,CAAC;AAAA,UACF;AAAA,QACD;AACA,YAAI,aAAa,iBAAiB;AACjC,cAAI,cAAc,UAAU;AAC3B,kBAAM,UAAU,KAAK;AAAA,cACpB;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,YACF;AACA,kBAAM,OAAO,MAAM,6CAAqB,KAAK,MAAM,OAAO;AAC1D,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,GAAG,IAAI,CAAC,CAAC;AACpD,kBAAM,EAAE,YAAY,IAAI,KAAK,iBAAiB,oBAAoB,GAAG,CAAC,GAAG,CAAC,CAAC;AAG3E,kBAAM,OAAoB;AAAA,cACzB;AAAA,YACD;AAEA,gBAAI,aAAa;AAChB,mBAAK,cAAc,CAAC,EAAE,UAAU,YAAY,CAAC;AAAA,YAC9C;AAEA,2BAAe,MAAM,0CAAkB;AAAA,cACtC;AAAA,cACA;AAAA,cACA,0BAA0B,IAAI;AAAA,cAC9B;AAAA,YACD;AAAA,UACD;AAAA,QACD;AACA,cAAM,gBAAgB,KAAK,QAAQ;AAAA,UAClC,KAAK,QAAQ,gBAAgB,YAA6B;AAAA,UAC1D,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,QACzB;AACA,mBAAW,KAAK,GAAG,aAAa;AAAA,MACjC,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,gBAAM,qBAAqB;AAAA,YAC1B,MAAM;AAAA,cACL,OAAQ,MAAqB;AAAA,YAC9B;AAAA,UACD;AACA,qBAAW,KAAK,kBAAkB;AAClC;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AAEA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": ["ISO6391"]}