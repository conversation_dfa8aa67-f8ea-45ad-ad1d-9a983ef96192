{"version": 3, "sources": ["../../../../../../nodes/Microsoft/Outlook/v2/helpers/utils.ts"], "sourcesContent": ["import type {\n\tIDataObject,\n\tIExecuteFunctions,\n\tIExecuteSingleFunctions,\n\tILoadOptionsFunctions,\n\tIPollFunctions,\n\tJsonObject,\n} from 'n8n-workflow';\nimport { ApplicationError, jsonParse, NodeApiError } from 'n8n-workflow';\n\nexport const messageFields = [\n\t'bccRecipients',\n\t'body',\n\t'bodyPreview',\n\t'categories',\n\t'ccRecipients',\n\t'changeKey',\n\t'conversationId',\n\t'createdDateTime',\n\t'flag',\n\t'from',\n\t'hasAttachments',\n\t'importance',\n\t'inferenceClassification',\n\t'internetMessageId',\n\t'isDeliveryReceiptRequested',\n\t'isDraft',\n\t'isRead',\n\t'isReadReceiptRequested',\n\t'lastModifiedDateTime',\n\t'parentFolderId',\n\t'receivedDateTime',\n\t'replyTo',\n\t'sender',\n\t'sentDateTime',\n\t'subject',\n\t'toRecipients',\n\t'webLink',\n].map((field) => ({ name: field, value: field }));\n\nexport const eventfields = [\n\t'allowNewTimeProposals',\n\t'attendees',\n\t'body',\n\t'bodyPreview',\n\t'categories',\n\t'changeKey',\n\t'createdDateTime',\n\t'end',\n\t'hasAttachments',\n\t'hideAttendees',\n\t'iCalUId',\n\t'importance',\n\t'isAllDay',\n\t'isCancelled',\n\t'isDraft',\n\t'isOnlineMeeting',\n\t'isOrganizer',\n\t'isReminderOn',\n\t'lastModifiedDateTime',\n\t'location',\n\t'locations',\n\t'onlineMeeting',\n\t'onlineMeetingProvider',\n\t'onlineMeetingUrl',\n\t'organizer',\n\t'originalEndTimeZone',\n\t'originalStartTimeZone',\n\t'recurrence',\n\t'reminderMinutesBeforeStart',\n\t'responseRequested',\n\t'responseStatus',\n\t'sensitivity',\n\t'seriesMasterId',\n\t'showAs',\n\t'start',\n\t'subject',\n\t'transactionId',\n\t'type',\n\t'webLink',\n].map((field) => ({ name: field, value: field }));\n\nexport const contactFields = [\n\t'createdDateTime',\n\t'lastModifiedDateTime',\n\t'changeKey',\n\t'categories',\n\t'parentFolderId',\n\t'birthday',\n\t'fileAs',\n\t'displayName',\n\t'givenName',\n\t'initials',\n\t'middleName',\n\t'nickName',\n\t'surname',\n\t'title',\n\t'yomiGivenName',\n\t'yomiSurname',\n\t'yomiCompanyName',\n\t'generation',\n\t'imAddresses',\n\t'jobTitle',\n\t'companyName',\n\t'department',\n\t'officeLocation',\n\t'profession',\n\t'businessHomePage',\n\t'assistantName',\n\t'manager',\n\t'homePhones',\n\t'mobilePhone',\n\t'businessPhones',\n\t'spouseName',\n\t'personalNotes',\n\t'children',\n\t'emailAddresses',\n\t'homeAddress',\n\t'businessAddress',\n\t'otherAddress',\n].map((field) => ({ name: field, value: field }));\n\nexport function makeRecipient(email: string) {\n\treturn {\n\t\temailAddress: {\n\t\t\taddress: email,\n\t\t},\n\t};\n}\n\nexport function createMessage(fields: IDataObject) {\n\tconst message: IDataObject = {};\n\n\t// Create body object\n\tif (fields.bodyContent || fields.bodyContentType) {\n\t\tconst bodyObject = {\n\t\t\tcontent: fields.bodyContent,\n\t\t\tcontentType: fields.bodyContentType,\n\t\t};\n\n\t\tmessage.body = bodyObject;\n\t\tdelete fields.bodyContent;\n\t\tdelete fields.bodyContentType;\n\t}\n\n\t// Handle custom headers\n\tif (\n\t\t'internetMessageHeaders' in fields &&\n\t\t'headers' in (fields.internetMessageHeaders as IDataObject)\n\t) {\n\t\tfields.internetMessageHeaders = (fields.internetMessageHeaders as IDataObject).headers;\n\t}\n\n\tfor (const [key, value] of Object.entries(fields)) {\n\t\tif (['bccRecipients', 'ccRecipients', 'replyTo', 'sender', 'toRecipients'].includes(key)) {\n\t\t\tif (Array.isArray(value)) {\n\t\t\t\tmessage[key] = (value as string[]).map((email) => makeRecipient(email));\n\t\t\t} else if (typeof value === 'string') {\n\t\t\t\tmessage[key] = value.split(',').map((recipient: string) => makeRecipient(recipient.trim()));\n\t\t\t} else {\n\t\t\t\tthrow new ApplicationError(`The \"${key}\" field must be a string or an array of strings`, {\n\t\t\t\t\tlevel: 'warning',\n\t\t\t\t});\n\t\t\t}\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (['from', 'sender'].includes(key)) {\n\t\t\tif (value) {\n\t\t\t\tmessage[key] = makeRecipient(value as string);\n\t\t\t}\n\t\t\tcontinue;\n\t\t}\n\n\t\tmessage[key] = value;\n\t}\n\n\treturn message;\n}\n\nexport function simplifyOutputMessages(data: IDataObject[]) {\n\treturn data.map((item: IDataObject) => {\n\t\treturn {\n\t\t\tid: item.id,\n\t\t\tconversationId: item.conversationId,\n\t\t\tsubject: item.subject,\n\t\t\tbodyPreview: item.bodyPreview,\n\t\t\tfrom: ((item.from as IDataObject)?.emailAddress as IDataObject)?.address,\n\t\t\tto: (item.toRecipients as IDataObject[]).map(\n\t\t\t\t(recipient: IDataObject) => (recipient.emailAddress as IDataObject)?.address,\n\t\t\t),\n\t\t\tcategories: item.categories,\n\t\t\thasAttachments: item.hasAttachments,\n\t\t};\n\t});\n}\n\nexport function prepareContactFields(fields: IDataObject) {\n\tconst returnData: IDataObject = {};\n\n\tconst typeStringCollection = [\n\t\t'businessPhones',\n\t\t'categories',\n\t\t'children',\n\t\t'homePhones',\n\t\t'imAddresses',\n\t];\n\tconst typeValuesToExtract = ['businessAddress', 'emailAddresses', 'homePhones', 'otherAddress'];\n\n\tfor (const [key, value] of Object.entries(fields)) {\n\t\tif (value === undefined || value === '') {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (typeStringCollection.includes(key) && !Array.isArray(value)) {\n\t\t\treturnData[key] = (value as string).split(',').map((item) => item.trim());\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (typeValuesToExtract.includes(key)) {\n\t\t\tif ((value as IDataObject).values === undefined) continue;\n\t\t\treturnData[key] = (value as IDataObject).values;\n\t\t\tcontinue;\n\t\t}\n\n\t\treturnData[key] = value;\n\t}\n\n\treturn returnData;\n}\n\nexport function prepareFilterString(filters: IDataObject) {\n\tconst selectedFilters = filters.filters as IDataObject;\n\tconst filterString: string[] = [];\n\n\tif (selectedFilters.foldersToInclude) {\n\t\tconst folders = (selectedFilters.foldersToInclude as string[])\n\t\t\t.filter((folder) => folder !== '')\n\t\t\t.map((folder) => `parentFolderId eq '${folder}'`)\n\t\t\t.join(' or ');\n\n\t\tfilterString.push(folders);\n\t}\n\n\tif (selectedFilters.foldersToExclude) {\n\t\tfor (const folder of selectedFilters.foldersToExclude as string[]) {\n\t\t\tfilterString.push(`parentFolderId ne '${folder}'`);\n\t\t}\n\t}\n\n\tif (selectedFilters.sender) {\n\t\tconst sender = selectedFilters.sender as string;\n\t\tconst byMailAddress = `from/emailAddress/address eq '${sender}'`;\n\t\tconst byName = `from/emailAddress/name eq '${sender}'`;\n\t\tfilterString.push(`(${byMailAddress} or ${byName})`);\n\t}\n\n\tif (selectedFilters.hasAttachments) {\n\t\tfilterString.push(`hasAttachments eq ${selectedFilters.hasAttachments}`);\n\t}\n\n\tif (selectedFilters.readStatus && selectedFilters.readStatus !== 'both') {\n\t\tfilterString.push(`isRead eq ${selectedFilters.readStatus === 'read'}`);\n\t}\n\n\tif (selectedFilters.receivedAfter) {\n\t\tfilterString.push(`receivedDateTime ge ${selectedFilters.receivedAfter}`);\n\t}\n\n\tif (selectedFilters.receivedBefore) {\n\t\tfilterString.push(`receivedDateTime le ${selectedFilters.receivedBefore}`);\n\t}\n\n\tif (selectedFilters.custom) {\n\t\tfilterString.push(selectedFilters.custom as string);\n\t}\n\n\treturn filterString.length ? filterString.join(' and ') : undefined;\n}\n\nexport function prepareApiError(\n\tthis: IExecuteFunctions | IExecuteSingleFunctions | ILoadOptionsFunctions | IPollFunctions,\n\terror: IDataObject,\n\titemIndex = 0,\n) {\n\tconst [httpCode, err, message] = (error.description as string).split(' - ');\n\tconst json = jsonParse(err);\n\treturn new NodeApiError(this.getNode(), json as JsonObject, {\n\t\titemIndex,\n\t\thttpCode,\n\t\t//In UI we are replacing some of the field names to make them more user friendly, updating error message to reflect that\n\t\tmessage: message\n\t\t\t.replace(/toRecipients/g, 'toRecipients (To)')\n\t\t\t.replace(/bodyContent/g, 'bodyContent (Message)')\n\t\t\t.replace(/bodyContentType/g, 'bodyContentType (Message Type)'),\n\t});\n}\n\nexport const encodeOutlookId = (id: string) => {\n\treturn id.replace(/-/g, '%2F').replace(/=/g, '%3D').replace(/\\+/g, '%2B');\n};\n\nexport const decodeOutlookId = (id: string) => {\n\treturn id.replace(/%2F/g, '-').replace(/%3D/g, '=').replace(/%2B/g, '+');\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,0BAA0D;AAEnD,MAAM,gBAAgB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,EAAE,IAAI,CAAC,WAAW,EAAE,MAAM,OAAO,OAAO,MAAM,EAAE;AAEzC,MAAM,cAAc;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,EAAE,IAAI,CAAC,WAAW,EAAE,MAAM,OAAO,OAAO,MAAM,EAAE;AAEzC,MAAM,gBAAgB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,EAAE,IAAI,CAAC,WAAW,EAAE,MAAM,OAAO,OAAO,MAAM,EAAE;AAEzC,SAAS,cAAc,OAAe;AAC5C,SAAO;AAAA,IACN,cAAc;AAAA,MACb,SAAS;AAAA,IACV;AAAA,EACD;AACD;AAEO,SAAS,cAAc,QAAqB;AAClD,QAAM,UAAuB,CAAC;AAG9B,MAAI,OAAO,eAAe,OAAO,iBAAiB;AACjD,UAAM,aAAa;AAAA,MAClB,SAAS,OAAO;AAAA,MAChB,aAAa,OAAO;AAAA,IACrB;AAEA,YAAQ,OAAO;AACf,WAAO,OAAO;AACd,WAAO,OAAO;AAAA,EACf;AAGA,MACC,4BAA4B,UAC5B,aAAc,OAAO,wBACpB;AACD,WAAO,yBAA0B,OAAO,uBAAuC;AAAA,EAChF;AAEA,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AAClD,QAAI,CAAC,iBAAiB,gBAAgB,WAAW,UAAU,cAAc,EAAE,SAAS,GAAG,GAAG;AACzF,UAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,gBAAQ,GAAG,IAAK,MAAmB,IAAI,CAAC,UAAU,cAAc,KAAK,CAAC;AAAA,MACvE,WAAW,OAAO,UAAU,UAAU;AACrC,gBAAQ,GAAG,IAAI,MAAM,MAAM,GAAG,EAAE,IAAI,CAAC,cAAsB,cAAc,UAAU,KAAK,CAAC,CAAC;AAAA,MAC3F,OAAO;AACN,cAAM,IAAI,qCAAiB,QAAQ,GAAG,mDAAmD;AAAA,UACxF,OAAO;AAAA,QACR,CAAC;AAAA,MACF;AACA;AAAA,IACD;AAEA,QAAI,CAAC,QAAQ,QAAQ,EAAE,SAAS,GAAG,GAAG;AACrC,UAAI,OAAO;AACV,gBAAQ,GAAG,IAAI,cAAc,KAAe;AAAA,MAC7C;AACA;AAAA,IACD;AAEA,YAAQ,GAAG,IAAI;AAAA,EAChB;AAEA,SAAO;AACR;AAEO,SAAS,uBAAuB,MAAqB;AAC3D,SAAO,KAAK,IAAI,CAAC,SAAsB;AACtC,WAAO;AAAA,MACN,IAAI,KAAK;AAAA,MACT,gBAAgB,KAAK;AAAA,MACrB,SAAS,KAAK;AAAA,MACd,aAAa,KAAK;AAAA,MAClB,MAAQ,KAAK,MAAsB,cAA8B;AAAA,MACjE,IAAK,KAAK,aAA+B;AAAA,QACxC,CAAC,cAA4B,UAAU,cAA8B;AAAA,MACtE;AAAA,MACA,YAAY,KAAK;AAAA,MACjB,gBAAgB,KAAK;AAAA,IACtB;AAAA,EACD,CAAC;AACF;AAEO,SAAS,qBAAqB,QAAqB;AACzD,QAAM,aAA0B,CAAC;AAEjC,QAAM,uBAAuB;AAAA,IAC5B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACA,QAAM,sBAAsB,CAAC,mBAAmB,kBAAkB,cAAc,cAAc;AAE9F,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AAClD,QAAI,UAAU,UAAa,UAAU,IAAI;AACxC;AAAA,IACD;AAEA,QAAI,qBAAqB,SAAS,GAAG,KAAK,CAAC,MAAM,QAAQ,KAAK,GAAG;AAChE,iBAAW,GAAG,IAAK,MAAiB,MAAM,GAAG,EAAE,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC;AACxE;AAAA,IACD;AAEA,QAAI,oBAAoB,SAAS,GAAG,GAAG;AACtC,UAAK,MAAsB,WAAW,OAAW;AACjD,iBAAW,GAAG,IAAK,MAAsB;AACzC;AAAA,IACD;AAEA,eAAW,GAAG,IAAI;AAAA,EACnB;AAEA,SAAO;AACR;AAEO,SAAS,oBAAoB,SAAsB;AACzD,QAAM,kBAAkB,QAAQ;AAChC,QAAM,eAAyB,CAAC;AAEhC,MAAI,gBAAgB,kBAAkB;AACrC,UAAM,UAAW,gBAAgB,iBAC/B,OAAO,CAAC,WAAW,WAAW,EAAE,EAChC,IAAI,CAAC,WAAW,sBAAsB,MAAM,GAAG,EAC/C,KAAK,MAAM;AAEb,iBAAa,KAAK,OAAO;AAAA,EAC1B;AAEA,MAAI,gBAAgB,kBAAkB;AACrC,eAAW,UAAU,gBAAgB,kBAA8B;AAClE,mBAAa,KAAK,sBAAsB,MAAM,GAAG;AAAA,IAClD;AAAA,EACD;AAEA,MAAI,gBAAgB,QAAQ;AAC3B,UAAM,SAAS,gBAAgB;AAC/B,UAAM,gBAAgB,iCAAiC,MAAM;AAC7D,UAAM,SAAS,8BAA8B,MAAM;AACnD,iBAAa,KAAK,IAAI,aAAa,OAAO,MAAM,GAAG;AAAA,EACpD;AAEA,MAAI,gBAAgB,gBAAgB;AACnC,iBAAa,KAAK,qBAAqB,gBAAgB,cAAc,EAAE;AAAA,EACxE;AAEA,MAAI,gBAAgB,cAAc,gBAAgB,eAAe,QAAQ;AACxE,iBAAa,KAAK,aAAa,gBAAgB,eAAe,MAAM,EAAE;AAAA,EACvE;AAEA,MAAI,gBAAgB,eAAe;AAClC,iBAAa,KAAK,uBAAuB,gBAAgB,aAAa,EAAE;AAAA,EACzE;AAEA,MAAI,gBAAgB,gBAAgB;AACnC,iBAAa,KAAK,uBAAuB,gBAAgB,cAAc,EAAE;AAAA,EAC1E;AAEA,MAAI,gBAAgB,QAAQ;AAC3B,iBAAa,KAAK,gBAAgB,MAAgB;AAAA,EACnD;AAEA,SAAO,aAAa,SAAS,aAAa,KAAK,OAAO,IAAI;AAC3D;AAEO,SAAS,gBAEf,OACA,YAAY,GACX;AACD,QAAM,CAAC,UAAU,KAAK,OAAO,IAAK,MAAM,YAAuB,MAAM,KAAK;AAC1E,QAAM,WAAO,+BAAU,GAAG;AAC1B,SAAO,IAAI,iCAAa,KAAK,QAAQ,GAAG,MAAoB;AAAA,IAC3D;AAAA,IACA;AAAA;AAAA,IAEA,SAAS,QACP,QAAQ,iBAAiB,mBAAmB,EAC5C,QAAQ,gBAAgB,uBAAuB,EAC/C,QAAQ,oBAAoB,gCAAgC;AAAA,EAC/D,CAAC;AACF;AAEO,MAAM,kBAAkB,CAAC,OAAe;AAC9C,SAAO,GAAG,QAAQ,MAAM,KAAK,EAAE,QAAQ,MAAM,KAAK,EAAE,QAAQ,OAAO,KAAK;AACzE;AAEO,MAAM,kBAAkB,CAAC,OAAe;AAC9C,SAAO,GAAG,QAAQ,QAAQ,GAAG,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,QAAQ,GAAG;AACxE;", "names": []}