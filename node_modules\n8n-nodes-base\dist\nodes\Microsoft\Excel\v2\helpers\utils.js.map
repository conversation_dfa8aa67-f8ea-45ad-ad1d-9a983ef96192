{"version": 3, "sources": ["../../../../../../nodes/Microsoft/Excel/v2/helpers/utils.ts"], "sourcesContent": ["import type { IDataObject, IExecuteFunctions, INode, INodeExecutionData } from 'n8n-workflow';\nimport { NodeOperationError } from 'n8n-workflow';\n\nimport { generatePairedItemData, wrapData } from '@utils/utilities';\n\nimport type { ExcelResponse, SheetData, UpdateSummary } from './interfaces';\n\ntype PrepareOutputConfig = {\n\trawData: boolean;\n\tdataProperty?: string;\n\tkeyRow?: number;\n\tfirstDataRow?: number;\n\tcolumnsRow?: string[];\n\tupdatedRows?: number[];\n};\n\nexport function prepareOutput(\n\tthis: IExecuteFunctions,\n\tnode: INode,\n\tresponseData: ExcelResponse,\n\tconfig: PrepareOutputConfig,\n) {\n\tconst returnData: INodeExecutionData[] = [];\n\n\tconst { rawData, keyRow, firstDataRow, columnsRow, updatedRows } = {\n\t\tkeyRow: 0,\n\t\tfirstDataRow: 1,\n\t\tcolumnsRow: undefined,\n\t\tupdatedRows: undefined,\n\t\t...config,\n\t};\n\n\tif (!rawData) {\n\t\tlet values = responseData.values;\n\t\tif (values === null) {\n\t\t\tthrow new NodeOperationError(node, 'Operation did not return data');\n\t\t}\n\n\t\tlet columns = [];\n\n\t\tif (columnsRow?.length) {\n\t\t\tcolumns = columnsRow;\n\t\t\tvalues = [columns, ...values];\n\t\t} else {\n\t\t\tcolumns = values[keyRow];\n\t\t}\n\n\t\tif (updatedRows) {\n\t\t\tvalues = values.filter((_, index) => updatedRows.includes(index));\n\t\t}\n\n\t\tfor (let rowIndex = firstDataRow; rowIndex < values.length; rowIndex++) {\n\t\t\tif (rowIndex === keyRow) continue;\n\t\t\tconst data: IDataObject = {};\n\t\t\tfor (let columnIndex = 0; columnIndex < columns.length; columnIndex++) {\n\t\t\t\tdata[columns[columnIndex] as string] = values[rowIndex][columnIndex];\n\t\t\t}\n\t\t\tconst executionData = this.helpers.constructExecutionMetaData(wrapData({ ...data }), {\n\t\t\t\titemData: { item: rowIndex },\n\t\t\t});\n\n\t\t\treturnData.push(...executionData);\n\t\t}\n\t} else {\n\t\tconst itemData = generatePairedItemData(this.getInputData().length);\n\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\twrapData({ [config.dataProperty || 'data']: responseData }),\n\t\t\t{ itemData },\n\t\t);\n\n\t\treturnData.push(...executionData);\n\t}\n\n\treturn returnData;\n}\n// update values of spreadsheet when update mode is 'define'\nexport function updateByDefinedValues(\n\tthis: IExecuteFunctions,\n\titemslength: number,\n\tsheetData: SheetData,\n\tupdateAllOccurences: boolean,\n): UpdateSummary {\n\tconst [columns, ...originalValues] = sheetData;\n\tconst updateValues: SheetData = originalValues.map((row) => row.map(() => null));\n\n\tconst updatedRowsIndexes = new Set<number>();\n\tconst appendData: IDataObject[] = [];\n\n\tfor (let itemIndex = 0; itemIndex < itemslength; itemIndex++) {\n\t\tconst columnToMatchOn = this.getNodeParameter('columnToMatchOn', itemIndex) as string;\n\t\tconst valueToMatchOn = this.getNodeParameter('valueToMatchOn', itemIndex) as string;\n\n\t\tconst definedFields = this.getNodeParameter('fieldsUi.values', itemIndex, []) as Array<{\n\t\t\tcolumn: string;\n\t\t\tfieldValue: string;\n\t\t}>;\n\n\t\tconst columnToMatchOnIndex = columns.indexOf(columnToMatchOn);\n\n\t\tconst rowIndexes: number[] = [];\n\t\tif (updateAllOccurences) {\n\t\t\tfor (const [index, row] of originalValues.entries()) {\n\t\t\t\tif (\n\t\t\t\t\trow[columnToMatchOnIndex] === valueToMatchOn ||\n\t\t\t\t\tNumber(row[columnToMatchOnIndex]) === Number(valueToMatchOn)\n\t\t\t\t) {\n\t\t\t\t\trowIndexes.push(index);\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tconst rowIndex = originalValues.findIndex(\n\t\t\t\t(row) =>\n\t\t\t\t\trow[columnToMatchOnIndex] === valueToMatchOn ||\n\t\t\t\t\tNumber(row[columnToMatchOnIndex]) === Number(valueToMatchOn),\n\t\t\t);\n\n\t\t\tif (rowIndex !== -1) {\n\t\t\t\trowIndexes.push(rowIndex);\n\t\t\t}\n\t\t}\n\n\t\tif (!rowIndexes.length) {\n\t\t\tconst appendItem: IDataObject = {};\n\t\t\tappendItem[columnToMatchOn] = valueToMatchOn;\n\n\t\t\tfor (const entry of definedFields) {\n\t\t\t\tappendItem[entry.column] = entry.fieldValue;\n\t\t\t}\n\t\t\tappendData.push(appendItem);\n\t\t\tcontinue;\n\t\t}\n\n\t\tfor (const rowIndex of rowIndexes) {\n\t\t\tfor (const entry of definedFields) {\n\t\t\t\tconst columnIndex = columns.indexOf(entry.column);\n\t\t\t\tif (rowIndex === -1) continue;\n\t\t\t\tupdateValues[rowIndex][columnIndex] = entry.fieldValue;\n\t\t\t\t//add rows index and shift by 1 to account for header row\n\t\t\t\tupdatedRowsIndexes.add(rowIndex + 1);\n\t\t\t}\n\t\t}\n\t}\n\n\tconst updatedData = [columns, ...updateValues];\n\tconst updatedRows = [0, ...Array.from(updatedRowsIndexes)];\n\n\tconst summary: UpdateSummary = { updatedData, appendData, updatedRows };\n\n\treturn summary;\n}\n\n// update values of spreadsheet when update mode is 'autoMap'\nexport function updateByAutoMaping(\n\titems: INodeExecutionData[],\n\tsheetData: SheetData,\n\tcolumnToMatchOn: string,\n\tupdateAllOccurences = false,\n): UpdateSummary {\n\tconst [columns, ...values] = sheetData;\n\tconst matchColumnIndex = columns.indexOf(columnToMatchOn);\n\tconst matchValuesMap = values.map((row) => row[matchColumnIndex]);\n\n\tconst updatedRowsIndexes = new Set<number>();\n\tconst appendData: IDataObject[] = [];\n\n\tfor (const { json } of items) {\n\t\tconst columnValue = json[columnToMatchOn] as string;\n\t\tif (columnValue === undefined) continue;\n\n\t\tconst rowIndexes: number[] = [];\n\t\tif (updateAllOccurences) {\n\t\t\tmatchValuesMap.forEach((value, index) => {\n\t\t\t\tif (value === columnValue || Number(value) === Number(columnValue)) {\n\t\t\t\t\trowIndexes.push(index);\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\tconst rowIndex = matchValuesMap.findIndex(\n\t\t\t\t(value) => value === columnValue || Number(value) === Number(columnValue),\n\t\t\t);\n\n\t\t\tif (rowIndex !== -1) rowIndexes.push(rowIndex);\n\t\t}\n\n\t\tif (!rowIndexes.length) {\n\t\t\tappendData.push(json);\n\t\t\tcontinue;\n\t\t}\n\n\t\tconst updatedRow: Array<string | null> = [];\n\n\t\tfor (const columnName of columns as string[]) {\n\t\t\tconst updateValue = json[columnName] === undefined ? null : (json[columnName] as string);\n\t\t\tupdatedRow.push(updateValue);\n\t\t}\n\n\t\tfor (const rowIndex of rowIndexes) {\n\t\t\tvalues[rowIndex] = updatedRow as string[];\n\t\t\t//add rows index and shift by 1 to account for header row\n\t\t\tupdatedRowsIndexes.add(rowIndex + 1);\n\t\t}\n\t}\n\n\tconst updatedData = [columns, ...values];\n\tconst updatedRows = [0, ...Array.from(updatedRowsIndexes)];\n\n\tconst summary: UpdateSummary = { updatedData, appendData, updatedRows };\n\n\treturn summary;\n}\n\nexport const checkRange = (node: INode, range: string) => {\n\tconst rangeRegex = /^[A-Z]+:[A-Z]+$/i;\n\n\tif (rangeRegex.test(range)) {\n\t\tthrow new NodeOperationError(\n\t\t\tnode,\n\t\t\t`Specify the range more precisely e.g. A1:B5, generic ranges like ${range} are not supported`,\n\t\t);\n\t}\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAAmC;AAEnC,uBAAiD;AAa1C,SAAS,cAEf,MACA,cACA,QACC;AACD,QAAM,aAAmC,CAAC;AAE1C,QAAM,EAAE,SAAS,QAAQ,cAAc,YAAY,YAAY,IAAI;AAAA,IAClE,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,GAAG;AAAA,EACJ;AAEA,MAAI,CAAC,SAAS;AACb,QAAI,SAAS,aAAa;AAC1B,QAAI,WAAW,MAAM;AACpB,YAAM,IAAI,uCAAmB,MAAM,+BAA+B;AAAA,IACnE;AAEA,QAAI,UAAU,CAAC;AAEf,QAAI,YAAY,QAAQ;AACvB,gBAAU;AACV,eAAS,CAAC,SAAS,GAAG,MAAM;AAAA,IAC7B,OAAO;AACN,gBAAU,OAAO,MAAM;AAAA,IACxB;AAEA,QAAI,aAAa;AAChB,eAAS,OAAO,OAAO,CAAC,GAAG,UAAU,YAAY,SAAS,KAAK,CAAC;AAAA,IACjE;AAEA,aAAS,WAAW,cAAc,WAAW,OAAO,QAAQ,YAAY;AACvE,UAAI,aAAa,OAAQ;AACzB,YAAM,OAAoB,CAAC;AAC3B,eAAS,cAAc,GAAG,cAAc,QAAQ,QAAQ,eAAe;AACtE,aAAK,QAAQ,WAAW,CAAW,IAAI,OAAO,QAAQ,EAAE,WAAW;AAAA,MACpE;AACA,YAAM,gBAAgB,KAAK,QAAQ,+BAA2B,2BAAS,EAAE,GAAG,KAAK,CAAC,GAAG;AAAA,QACpF,UAAU,EAAE,MAAM,SAAS;AAAA,MAC5B,CAAC;AAED,iBAAW,KAAK,GAAG,aAAa;AAAA,IACjC;AAAA,EACD,OAAO;AACN,UAAM,eAAW,yCAAuB,KAAK,aAAa,EAAE,MAAM;AAClE,UAAM,gBAAgB,KAAK,QAAQ;AAAA,UAClC,2BAAS,EAAE,CAAC,OAAO,gBAAgB,MAAM,GAAG,aAAa,CAAC;AAAA,MAC1D,EAAE,SAAS;AAAA,IACZ;AAEA,eAAW,KAAK,GAAG,aAAa;AAAA,EACjC;AAEA,SAAO;AACR;AAEO,SAAS,sBAEf,aACA,WACA,qBACgB;AAChB,QAAM,CAAC,SAAS,GAAG,cAAc,IAAI;AACrC,QAAM,eAA0B,eAAe,IAAI,CAAC,QAAQ,IAAI,IAAI,MAAM,IAAI,CAAC;AAE/E,QAAM,qBAAqB,oBAAI,IAAY;AAC3C,QAAM,aAA4B,CAAC;AAEnC,WAAS,YAAY,GAAG,YAAY,aAAa,aAAa;AAC7D,UAAM,kBAAkB,KAAK,iBAAiB,mBAAmB,SAAS;AAC1E,UAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,SAAS;AAExE,UAAM,gBAAgB,KAAK,iBAAiB,mBAAmB,WAAW,CAAC,CAAC;AAK5E,UAAM,uBAAuB,QAAQ,QAAQ,eAAe;AAE5D,UAAM,aAAuB,CAAC;AAC9B,QAAI,qBAAqB;AACxB,iBAAW,CAAC,OAAO,GAAG,KAAK,eAAe,QAAQ,GAAG;AACpD,YACC,IAAI,oBAAoB,MAAM,kBAC9B,OAAO,IAAI,oBAAoB,CAAC,MAAM,OAAO,cAAc,GAC1D;AACD,qBAAW,KAAK,KAAK;AAAA,QACtB;AAAA,MACD;AAAA,IACD,OAAO;AACN,YAAM,WAAW,eAAe;AAAA,QAC/B,CAAC,QACA,IAAI,oBAAoB,MAAM,kBAC9B,OAAO,IAAI,oBAAoB,CAAC,MAAM,OAAO,cAAc;AAAA,MAC7D;AAEA,UAAI,aAAa,IAAI;AACpB,mBAAW,KAAK,QAAQ;AAAA,MACzB;AAAA,IACD;AAEA,QAAI,CAAC,WAAW,QAAQ;AACvB,YAAM,aAA0B,CAAC;AACjC,iBAAW,eAAe,IAAI;AAE9B,iBAAW,SAAS,eAAe;AAClC,mBAAW,MAAM,MAAM,IAAI,MAAM;AAAA,MAClC;AACA,iBAAW,KAAK,UAAU;AAC1B;AAAA,IACD;AAEA,eAAW,YAAY,YAAY;AAClC,iBAAW,SAAS,eAAe;AAClC,cAAM,cAAc,QAAQ,QAAQ,MAAM,MAAM;AAChD,YAAI,aAAa,GAAI;AACrB,qBAAa,QAAQ,EAAE,WAAW,IAAI,MAAM;AAE5C,2BAAmB,IAAI,WAAW,CAAC;AAAA,MACpC;AAAA,IACD;AAAA,EACD;AAEA,QAAM,cAAc,CAAC,SAAS,GAAG,YAAY;AAC7C,QAAM,cAAc,CAAC,GAAG,GAAG,MAAM,KAAK,kBAAkB,CAAC;AAEzD,QAAM,UAAyB,EAAE,aAAa,YAAY,YAAY;AAEtE,SAAO;AACR;AAGO,SAAS,mBACf,OACA,WACA,iBACA,sBAAsB,OACN;AAChB,QAAM,CAAC,SAAS,GAAG,MAAM,IAAI;AAC7B,QAAM,mBAAmB,QAAQ,QAAQ,eAAe;AACxD,QAAM,iBAAiB,OAAO,IAAI,CAAC,QAAQ,IAAI,gBAAgB,CAAC;AAEhE,QAAM,qBAAqB,oBAAI,IAAY;AAC3C,QAAM,aAA4B,CAAC;AAEnC,aAAW,EAAE,KAAK,KAAK,OAAO;AAC7B,UAAM,cAAc,KAAK,eAAe;AACxC,QAAI,gBAAgB,OAAW;AAE/B,UAAM,aAAuB,CAAC;AAC9B,QAAI,qBAAqB;AACxB,qBAAe,QAAQ,CAAC,OAAO,UAAU;AACxC,YAAI,UAAU,eAAe,OAAO,KAAK,MAAM,OAAO,WAAW,GAAG;AACnE,qBAAW,KAAK,KAAK;AAAA,QACtB;AAAA,MACD,CAAC;AAAA,IACF,OAAO;AACN,YAAM,WAAW,eAAe;AAAA,QAC/B,CAAC,UAAU,UAAU,eAAe,OAAO,KAAK,MAAM,OAAO,WAAW;AAAA,MACzE;AAEA,UAAI,aAAa,GAAI,YAAW,KAAK,QAAQ;AAAA,IAC9C;AAEA,QAAI,CAAC,WAAW,QAAQ;AACvB,iBAAW,KAAK,IAAI;AACpB;AAAA,IACD;AAEA,UAAM,aAAmC,CAAC;AAE1C,eAAW,cAAc,SAAqB;AAC7C,YAAM,cAAc,KAAK,UAAU,MAAM,SAAY,OAAQ,KAAK,UAAU;AAC5E,iBAAW,KAAK,WAAW;AAAA,IAC5B;AAEA,eAAW,YAAY,YAAY;AAClC,aAAO,QAAQ,IAAI;AAEnB,yBAAmB,IAAI,WAAW,CAAC;AAAA,IACpC;AAAA,EACD;AAEA,QAAM,cAAc,CAAC,SAAS,GAAG,MAAM;AACvC,QAAM,cAAc,CAAC,GAAG,GAAG,MAAM,KAAK,kBAAkB,CAAC;AAEzD,QAAM,UAAyB,EAAE,aAAa,YAAY,YAAY;AAEtE,SAAO;AACR;AAEO,MAAM,aAAa,CAAC,MAAa,UAAkB;AACzD,QAAM,aAAa;AAEnB,MAAI,WAAW,KAAK,KAAK,GAAG;AAC3B,UAAM,IAAI;AAAA,MACT;AAAA,MACA,oEAAoE,KAAK;AAAA,IAC1E;AAAA,EACD;AACD;", "names": []}