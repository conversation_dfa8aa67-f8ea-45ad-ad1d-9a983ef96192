{"version": 3, "sources": ["../../../../../../nodes/Microsoft/Teams/v2/helpers/utils.ts"], "sourcesContent": ["import type { IExecuteFunctions, ILoadOptionsFunctions, INodeListSearchItems } from 'n8n-workflow';\n\nexport function prepareMessage(\n\tthis: IExecuteFunctions | ILoadOptionsFunctions,\n\tmessage: string,\n\tcontentType: string,\n\tincludeLinkToWorkflow: boolean,\n\tinstanceId?: string,\n) {\n\tif (includeLinkToWorkflow) {\n\t\tconst { id } = this.getWorkflow();\n\t\tconst link = `${this.getInstanceBaseUrl()}workflow/${id}?utm_source=n8n-internal&utm_medium=powered_by&utm_campaign=${encodeURIComponent(\n\t\t\t'n8n-nodes-base.microsoftTeams',\n\t\t)}${instanceId ? '_' + instanceId : ''}`;\n\t\tcontentType = 'html';\n\t\tmessage = `${message}<br><br><em> Powered by <a href=\"${link}\">this n8n workflow</a> </em>`;\n\t}\n\n\treturn {\n\t\tbody: {\n\t\t\tcontentType,\n\t\t\tcontent: message,\n\t\t},\n\t};\n}\n\nexport function filterSortSearchListItems(items: INodeListSearchItems[], filter?: string) {\n\treturn items\n\t\t.filter(\n\t\t\t(item) =>\n\t\t\t\t!filter ||\n\t\t\t\titem.name.toLowerCase().includes(filter.toLowerCase()) ||\n\t\t\t\titem.value.toString().toLowerCase().includes(filter.toLowerCase()),\n\t\t)\n\t\t.sort((a, b) => {\n\t\t\tif (a.name.toLocaleLowerCase() < b.name.toLocaleLowerCase()) {\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t\tif (a.name.toLocaleLowerCase() > b.name.toLocaleLowerCase()) {\n\t\t\t\treturn 1;\n\t\t\t}\n\t\t\treturn 0;\n\t\t});\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,SAAS,eAEf,SACA,aACA,uBACA,YACC;AACD,MAAI,uBAAuB;AAC1B,UAAM,EAAE,GAAG,IAAI,KAAK,YAAY;AAChC,UAAM,OAAO,GAAG,KAAK,mBAAmB,CAAC,YAAY,EAAE,+DAA+D;AAAA,MACrH;AAAA,IACD,CAAC,GAAG,aAAa,MAAM,aAAa,EAAE;AACtC,kBAAc;AACd,cAAU,GAAG,OAAO,oCAAoC,IAAI;AAAA,EAC7D;AAEA,SAAO;AAAA,IACN,MAAM;AAAA,MACL;AAAA,MACA,SAAS;AAAA,IACV;AAAA,EACD;AACD;AAEO,SAAS,0BAA0B,OAA+B,QAAiB;AACzF,SAAO,MACL;AAAA,IACA,CAAC,SACA,CAAC,UACD,KAAK,KAAK,YAAY,EAAE,SAAS,OAAO,YAAY,CAAC,KACrD,KAAK,MAAM,SAAS,EAAE,YAAY,EAAE,SAAS,OAAO,YAAY,CAAC;AAAA,EACnE,EACC,KAAK,CAAC,GAAG,MAAM;AACf,QAAI,EAAE,KAAK,kBAAkB,IAAI,EAAE,KAAK,kBAAkB,GAAG;AAC5D,aAAO;AAAA,IACR;AACA,QAAI,EAAE,KAAK,kBAAkB,IAAI,EAAE,KAAK,kBAAkB,GAAG;AAC5D,aAAO;AAAA,IACR;AACA,WAAO;AAAA,EACR,CAAC;AACH;", "names": []}