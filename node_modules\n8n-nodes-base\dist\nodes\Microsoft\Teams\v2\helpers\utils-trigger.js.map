{"version": 3, "sources": ["../../../../../../nodes/Microsoft/Teams/v2/helpers/utils-trigger.ts"], "sourcesContent": ["import type { IHookFunctions, IDataObject } from 'n8n-workflow';\nimport { NodeOperationError } from 'n8n-workflow';\n\nimport type { TeamResponse, ChannelResponse, SubscriptionResponse } from './types';\nimport { microsoftApiRequest } from '../transport';\n\nexport async function fetchAllTeams(this: IHookFunctions): Promise<TeamResponse[]> {\n\tconst { value: teams } = (await microsoftApiRequest.call(\n\t\tthis,\n\t\t'GET',\n\t\t'/v1.0/me/joinedTeams',\n\t)) as { value: TeamResponse[] };\n\treturn teams;\n}\n\nexport async function fetchAllChannels(\n\tthis: IHookFunctions,\n\tteamId: string,\n): Promise<ChannelResponse[]> {\n\tconst { value: channels } = (await microsoftApiRequest.call(\n\t\tthis,\n\t\t'GET',\n\t\t`/v1.0/teams/${teamId}/channels`,\n\t)) as { value: ChannelResponse[] };\n\treturn channels;\n}\n\nexport async function createSubscription(\n\tthis: IHookFunctions,\n\twebhookUrl: string,\n\tresourcePath: string,\n): Promise<SubscriptionResponse> {\n\tconst expirationTime = new Date(Date.now() + 4318 * 60 * 1000).toISOString();\n\tconst body: IDataObject = {\n\t\tchangeType: 'created',\n\t\tnotificationUrl: webhookUrl,\n\t\tresource: resourcePath,\n\t\texpirationDateTime: expirationTime,\n\t\tlatestSupportedTlsVersion: 'v1_2',\n\t\tlifecycleNotificationUrl: webhookUrl,\n\t};\n\n\tconst response = (await microsoftApiRequest.call(\n\t\tthis,\n\t\t'POST',\n\t\t'/v1.0/subscriptions',\n\t\tbody,\n\t)) as SubscriptionResponse;\n\n\treturn response;\n}\n\nexport async function getResourcePath(\n\tthis: IHookFunctions,\n\tevent: string,\n): Promise<string | string[]> {\n\tswitch (event) {\n\t\tcase 'newChat': {\n\t\t\treturn '/me/chats';\n\t\t}\n\n\t\tcase 'newChatMessage': {\n\t\t\tconst watchAllChats = this.getNodeParameter('watchAllChats', false, {\n\t\t\t\textractValue: true,\n\t\t\t}) as boolean;\n\n\t\t\tif (watchAllChats) {\n\t\t\t\treturn '/me/chats/getAllMessages';\n\t\t\t} else {\n\t\t\t\tconst chatId = this.getNodeParameter('chatId', undefined, { extractValue: true }) as string;\n\t\t\t\treturn `/chats/${decodeURIComponent(chatId)}/messages`;\n\t\t\t}\n\t\t}\n\n\t\tcase 'newChannel': {\n\t\t\tconst watchAllTeams = this.getNodeParameter('watchAllTeams', false, {\n\t\t\t\textractValue: true,\n\t\t\t}) as boolean;\n\n\t\t\tif (watchAllTeams) {\n\t\t\t\tconst teams = await fetchAllTeams.call(this);\n\t\t\t\treturn teams.map((team) => `/teams/${team.id}/channels`);\n\t\t\t} else {\n\t\t\t\tconst teamId = this.getNodeParameter('teamId', undefined, { extractValue: true }) as string;\n\t\t\t\treturn `/teams/${teamId}/channels`;\n\t\t\t}\n\t\t}\n\n\t\tcase 'newChannelMessage': {\n\t\t\tconst watchAllTeams = this.getNodeParameter('watchAllTeams', false, {\n\t\t\t\textractValue: true,\n\t\t\t}) as boolean;\n\n\t\t\tif (watchAllTeams) {\n\t\t\t\tconst teams = await fetchAllTeams.call(this);\n\t\t\t\tconst teamChannels = await Promise.all(\n\t\t\t\t\tteams.map(async (team) => {\n\t\t\t\t\t\tconst channels = await fetchAllChannels.call(this, team.id);\n\t\t\t\t\t\treturn channels.map((channel) => `/teams/${team.id}/channels/${channel.id}/messages`);\n\t\t\t\t\t}),\n\t\t\t\t);\n\t\t\t\treturn teamChannels.flat();\n\t\t\t} else {\n\t\t\t\tconst teamId = this.getNodeParameter('teamId', undefined, { extractValue: true }) as string;\n\t\t\t\tconst watchAllChannels = this.getNodeParameter('watchAllChannels', false, {\n\t\t\t\t\textractValue: true,\n\t\t\t\t}) as boolean;\n\n\t\t\t\tif (watchAllChannels) {\n\t\t\t\t\tconst channels = await fetchAllChannels.call(this, teamId);\n\t\t\t\t\treturn channels.map((channel) => `/teams/${teamId}/channels/${channel.id}/messages`);\n\t\t\t\t} else {\n\t\t\t\t\tconst channelId = this.getNodeParameter('channelId', undefined, {\n\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t}) as string;\n\t\t\t\t\treturn `/teams/${teamId}/channels/${decodeURIComponent(channelId)}/messages`;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tcase 'newTeamMember': {\n\t\t\tconst watchAllTeams = this.getNodeParameter('watchAllTeams', false, {\n\t\t\t\textractValue: true,\n\t\t\t}) as boolean;\n\n\t\t\tif (watchAllTeams) {\n\t\t\t\tconst teams = await fetchAllTeams.call(this);\n\t\t\t\treturn teams.map((team) => `/teams/${team.id}/members`);\n\t\t\t} else {\n\t\t\t\tconst teamId = this.getNodeParameter('teamId', undefined, { extractValue: true }) as string;\n\t\t\t\treturn `/teams/${teamId}/members`;\n\t\t\t}\n\t\t}\n\n\t\tdefault: {\n\t\t\tthrow new NodeOperationError(this.getNode(), {\n\t\t\t\tmessage: `Invalid event: ${event}`,\n\t\t\t\tdescription: `The selected event \"${event}\" is not recognized.`,\n\t\t\t});\n\t\t}\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAAmC;AAGnC,uBAAoC;AAEpC,eAAsB,gBAA6D;AAClF,QAAM,EAAE,OAAO,MAAM,IAAK,MAAM,qCAAoB;AAAA,IACnD;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACA,SAAO;AACR;AAEA,eAAsB,iBAErB,QAC6B;AAC7B,QAAM,EAAE,OAAO,SAAS,IAAK,MAAM,qCAAoB;AAAA,IACtD;AAAA,IACA;AAAA,IACA,eAAe,MAAM;AAAA,EACtB;AACA,SAAO;AACR;AAEA,eAAsB,mBAErB,YACA,cACgC;AAChC,QAAM,iBAAiB,IAAI,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,GAAI,EAAE,YAAY;AAC3E,QAAM,OAAoB;AAAA,IACzB,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,oBAAoB;AAAA,IACpB,2BAA2B;AAAA,IAC3B,0BAA0B;AAAA,EAC3B;AAEA,QAAM,WAAY,MAAM,qCAAoB;AAAA,IAC3C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAEA,SAAO;AACR;AAEA,eAAsB,gBAErB,OAC6B;AAC7B,UAAQ,OAAO;AAAA,IACd,KAAK,WAAW;AACf,aAAO;AAAA,IACR;AAAA,IAEA,KAAK,kBAAkB;AACtB,YAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,OAAO;AAAA,QACnE,cAAc;AAAA,MACf,CAAC;AAED,UAAI,eAAe;AAClB,eAAO;AAAA,MACR,OAAO;AACN,cAAM,SAAS,KAAK,iBAAiB,UAAU,QAAW,EAAE,cAAc,KAAK,CAAC;AAChF,eAAO,UAAU,mBAAmB,MAAM,CAAC;AAAA,MAC5C;AAAA,IACD;AAAA,IAEA,KAAK,cAAc;AAClB,YAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,OAAO;AAAA,QACnE,cAAc;AAAA,MACf,CAAC;AAED,UAAI,eAAe;AAClB,cAAM,QAAQ,MAAM,cAAc,KAAK,IAAI;AAC3C,eAAO,MAAM,IAAI,CAAC,SAAS,UAAU,KAAK,EAAE,WAAW;AAAA,MACxD,OAAO;AACN,cAAM,SAAS,KAAK,iBAAiB,UAAU,QAAW,EAAE,cAAc,KAAK,CAAC;AAChF,eAAO,UAAU,MAAM;AAAA,MACxB;AAAA,IACD;AAAA,IAEA,KAAK,qBAAqB;AACzB,YAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,OAAO;AAAA,QACnE,cAAc;AAAA,MACf,CAAC;AAED,UAAI,eAAe;AAClB,cAAM,QAAQ,MAAM,cAAc,KAAK,IAAI;AAC3C,cAAM,eAAe,MAAM,QAAQ;AAAA,UAClC,MAAM,IAAI,OAAO,SAAS;AACzB,kBAAM,WAAW,MAAM,iBAAiB,KAAK,MAAM,KAAK,EAAE;AAC1D,mBAAO,SAAS,IAAI,CAAC,YAAY,UAAU,KAAK,EAAE,aAAa,QAAQ,EAAE,WAAW;AAAA,UACrF,CAAC;AAAA,QACF;AACA,eAAO,aAAa,KAAK;AAAA,MAC1B,OAAO;AACN,cAAM,SAAS,KAAK,iBAAiB,UAAU,QAAW,EAAE,cAAc,KAAK,CAAC;AAChF,cAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,OAAO;AAAA,UACzE,cAAc;AAAA,QACf,CAAC;AAED,YAAI,kBAAkB;AACrB,gBAAM,WAAW,MAAM,iBAAiB,KAAK,MAAM,MAAM;AACzD,iBAAO,SAAS,IAAI,CAAC,YAAY,UAAU,MAAM,aAAa,QAAQ,EAAE,WAAW;AAAA,QACpF,OAAO;AACN,gBAAM,YAAY,KAAK,iBAAiB,aAAa,QAAW;AAAA,YAC/D,cAAc;AAAA,UACf,CAAC;AACD,iBAAO,UAAU,MAAM,aAAa,mBAAmB,SAAS,CAAC;AAAA,QAClE;AAAA,MACD;AAAA,IACD;AAAA,IAEA,KAAK,iBAAiB;AACrB,YAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,OAAO;AAAA,QACnE,cAAc;AAAA,MACf,CAAC;AAED,UAAI,eAAe;AAClB,cAAM,QAAQ,MAAM,cAAc,KAAK,IAAI;AAC3C,eAAO,MAAM,IAAI,CAAC,SAAS,UAAU,KAAK,EAAE,UAAU;AAAA,MACvD,OAAO;AACN,cAAM,SAAS,KAAK,iBAAiB,UAAU,QAAW,EAAE,cAAc,KAAK,CAAC;AAChF,eAAO,UAAU,MAAM;AAAA,MACxB;AAAA,IACD;AAAA,IAEA,SAAS;AACR,YAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG;AAAA,QAC5C,SAAS,kBAAkB,KAAK;AAAA,QAChC,aAAa,uBAAuB,KAAK;AAAA,MAC1C,CAAC;AAAA,IACF;AAAA,EACD;AACD;", "names": []}