{"version": 3, "sources": ["../../../../../nodes/Splunk/v1/descriptions/UserDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const userOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Create',\n\t\t\t\tvalue: 'create',\n\t\t\t\tdescription: 'Create an user',\n\t\t\t\taction: 'Create a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'delete',\n\t\t\t\tdescription: 'Delete an user',\n\t\t\t\taction: 'Delete a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\tdescription: 'Retrieve an user',\n\t\t\t\taction: 'Get a user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Retrieve many users',\n\t\t\t\taction: 'Get many users',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Update',\n\t\t\t\tvalue: 'update',\n\t\t\t\tdescription: 'Update an user',\n\t\t\t\taction: 'Update a user',\n\t\t\t},\n\t\t],\n\t\tdefault: 'create',\n\t},\n];\n\nexport const userFields: INodeProperties[] = [\n\t// ----------------------------------------\n\t//               user: create\n\t// ----------------------------------------\n\t{\n\t\tdisplayName: 'Name',\n\t\tname: 'name',\n\t\tdescription: 'Login name of the user',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Role Names or IDs',\n\t\tname: 'roles',\n\t\ttype: 'multiOptions',\n\t\tdescription:\n\t\t\t'Comma-separated list of roles to assign to the user. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\trequired: true,\n\t\tdefault: [],\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getRoles',\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Password',\n\t\tname: 'password',\n\t\ttype: 'string',\n\t\ttypeOptions: { password: true },\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Email',\n\t\t\t\tname: 'email',\n\t\t\t\ttype: 'string',\n\t\t\t\tplaceholder: '<EMAIL>',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Full Name',\n\t\t\t\tname: 'realname',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Full name of the user',\n\t\t\t},\n\t\t],\n\t},\n\n\t// ----------------------------------------\n\t//               user: delete\n\t// ----------------------------------------\n\t{\n\t\tdisplayName: 'User ID',\n\t\tname: 'userId',\n\t\tdescription: 'ID of the user to delete',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['delete'],\n\t\t\t},\n\t\t},\n\t},\n\n\t// ----------------------------------------\n\t//                user: get\n\t// ----------------------------------------\n\t{\n\t\tdisplayName: 'User ID',\n\t\tname: 'userId',\n\t\tdescription: 'ID of the user to retrieve',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['get'],\n\t\t\t},\n\t\t},\n\t},\n\n\t// ----------------------------------------\n\t//               user: getAll\n\t// ----------------------------------------\n\t{\n\t\tdisplayName: 'Return All',\n\t\tname: 'returnAll',\n\t\ttype: 'boolean',\n\t\tdefault: false,\n\t\tdescription: 'Whether to return all results or only up to a given limit',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Limit',\n\t\tname: 'limit',\n\t\ttype: 'number',\n\t\tdefault: 50,\n\t\tdescription: 'Max number of results to return',\n\t\ttypeOptions: {\n\t\t\tminValue: 1,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['getAll'],\n\t\t\t\treturnAll: [false],\n\t\t\t},\n\t\t},\n\t},\n\n\t// ----------------------------------------\n\t//               user: update\n\t// ----------------------------------------\n\t{\n\t\tdisplayName: 'User ID',\n\t\tname: 'userId',\n\t\tdescription: 'ID of the user to update',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Update Fields',\n\t\tname: 'updateFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t\toperation: ['update'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Email',\n\t\t\t\tname: 'email',\n\t\t\t\ttype: 'string',\n\t\t\t\tplaceholder: '<EMAIL>',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Full Name',\n\t\t\t\tname: 'realname',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Full name of the user',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Password',\n\t\t\t\tname: 'password',\n\t\t\t\ttype: 'string',\n\t\t\t\ttypeOptions: { password: true },\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Role Names or IDs',\n\t\t\t\tname: 'roles',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\tdescription:\n\t\t\t\t\t'Comma-separated list of roles to assign to the user. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t\tdefault: [],\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getRoles',\n\t\t\t\t},\n\t\t\t},\n\t\t],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,iBAAoC;AAAA,EAChD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,aAAgC;AAAA;AAAA;AAAA;AAAA,EAI5C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aACC;AAAA,IACD,UAAU;AAAA,IACV,SAAS,CAAC;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,EAAE,UAAU,KAAK;AAAA,IAC9B,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,MACZ,UAAU;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,QACpB,WAAW,CAAC,KAAK;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,QACjB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aACC;AAAA,QACD,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,UACZ,mBAAmB;AAAA,QACpB;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;", "names": []}