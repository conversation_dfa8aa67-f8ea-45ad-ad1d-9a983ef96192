{"version": 3, "sources": ["../../../nodes/Trello/Trello.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tILoadOptionsFunctions,\n\tIDataObject,\n\tINodeExecutionData,\n\tINodeListSearchResult,\n\tINodeType,\n\tINodeTypeDescription,\n\tIHttpRequestMethods,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes, NodeOperationError } from 'n8n-workflow';\n\nimport { attachmentFields, attachmentOperations } from './AttachmentDescription';\nimport { boardFields, boardOperations } from './BoardDescription';\nimport { boardMemberFields, boardMemberOperations } from './BoardMemberDescription';\nimport { cardCommentFields, cardCommentOperations } from './CardCommentDescription';\nimport { cardFields, cardOperations } from './CardDescription';\nimport { checklistFields, checklistOperations } from './ChecklistDescription';\nimport { apiRequest, apiRequestAllItems } from './GenericFunctions';\nimport { labelFields, labelOperations } from './LabelDescription';\nimport { listFields, listOperations } from './ListDescription';\n\ninterface TrelloBoardType {\n\tid: string;\n\tname: string;\n\turl: string;\n\tdesc: string;\n}\n\nexport class Trello implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Trello',\n\t\tname: 'trello',\n\t\ticon: 'file:trello.svg',\n\t\tgroup: ['transform'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Create, change and delete boards and cards',\n\t\tdefaults: {\n\t\t\tname: 'Trello',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'trelloApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Attachment',\n\t\t\t\t\t\tvalue: 'attachment',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Board',\n\t\t\t\t\t\tvalue: 'board',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Board Member',\n\t\t\t\t\t\tvalue: 'boardMember',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Card',\n\t\t\t\t\t\tvalue: 'card',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Card Comment',\n\t\t\t\t\t\tvalue: 'cardComment',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Checklist',\n\t\t\t\t\t\tvalue: 'checklist',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Label',\n\t\t\t\t\t\tvalue: 'label',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'List',\n\t\t\t\t\t\tvalue: 'list',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'card',\n\t\t\t},\n\n\t\t\t// ----------------------------------\n\t\t\t//         operations\n\t\t\t// ----------------------------------\n\t\t\t...attachmentOperations,\n\t\t\t...boardOperations,\n\t\t\t...boardMemberOperations,\n\t\t\t...cardOperations,\n\t\t\t...cardCommentOperations,\n\t\t\t...checklistOperations,\n\t\t\t...labelOperations,\n\t\t\t...listOperations,\n\n\t\t\t// ----------------------------------\n\t\t\t//         fields\n\t\t\t// ----------------------------------\n\t\t\t...attachmentFields,\n\t\t\t...boardFields,\n\t\t\t...boardMemberFields,\n\t\t\t...cardFields,\n\t\t\t...cardCommentFields,\n\t\t\t...checklistFields,\n\t\t\t...labelFields,\n\t\t\t...listFields,\n\t\t],\n\t};\n\n\tmethods = {\n\t\tlistSearch: {\n\t\t\tasync searchBoards(\n\t\t\t\tthis: ILoadOptionsFunctions,\n\t\t\t\tquery?: string,\n\t\t\t): Promise<INodeListSearchResult> {\n\t\t\t\tif (!query) {\n\t\t\t\t\tthrow new NodeOperationError(this.getNode(), 'Query required for Trello search');\n\t\t\t\t}\n\t\t\t\tconst searchResults = await apiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t'search',\n\t\t\t\t\t{},\n\t\t\t\t\t{\n\t\t\t\t\t\tquery,\n\t\t\t\t\t\tmodelTypes: 'boards',\n\t\t\t\t\t\tboard_fields: 'name,url,desc',\n\t\t\t\t\t\t// Enables partial word searching, only for the start of words though\n\t\t\t\t\t\tpartial: true,\n\t\t\t\t\t\t// Seems like a good number since it isn't paginated. Default is 10.\n\t\t\t\t\t\tboards_limit: 50,\n\t\t\t\t\t},\n\t\t\t\t);\n\t\t\t\treturn {\n\t\t\t\t\tresults: searchResults.boards.map((b: TrelloBoardType) => ({\n\t\t\t\t\t\tname: b.name,\n\t\t\t\t\t\tvalue: b.id,\n\t\t\t\t\t\turl: b.url,\n\t\t\t\t\t\tdescription: b.desc,\n\t\t\t\t\t})),\n\t\t\t\t};\n\t\t\t},\n\t\t\tasync searchCards(\n\t\t\t\tthis: ILoadOptionsFunctions,\n\t\t\t\tquery?: string,\n\t\t\t): Promise<INodeListSearchResult> {\n\t\t\t\tif (!query) {\n\t\t\t\t\tthrow new NodeOperationError(this.getNode(), 'Query required for Trello search');\n\t\t\t\t}\n\t\t\t\tconst searchResults = await apiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t'search',\n\t\t\t\t\t{},\n\t\t\t\t\t{\n\t\t\t\t\t\tquery,\n\t\t\t\t\t\tmodelTypes: 'cards',\n\t\t\t\t\t\tboard_fields: 'name,url,desc',\n\t\t\t\t\t\t// Enables partial word searching, only for the start of words though\n\t\t\t\t\t\tpartial: true,\n\t\t\t\t\t\t// Seems like a good number since it isn't paginated. Default is 10.\n\t\t\t\t\t\tcards_limit: 50,\n\t\t\t\t\t},\n\t\t\t\t);\n\t\t\t\treturn {\n\t\t\t\t\tresults: searchResults.cards.map((b: TrelloBoardType) => ({\n\t\t\t\t\t\tname: b.name,\n\t\t\t\t\t\tvalue: b.id,\n\t\t\t\t\t\turl: b.url,\n\t\t\t\t\t\tdescription: b.desc,\n\t\t\t\t\t})),\n\t\t\t\t};\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: INodeExecutionData[] = [];\n\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\n\t\t// For Post\n\t\tlet body: IDataObject;\n\t\t// For Query string\n\t\tlet qs: IDataObject;\n\n\t\tlet requestMethod: IHttpRequestMethods;\n\t\tlet endpoint: string;\n\t\tlet returnAll = false;\n\t\tlet responseData;\n\n\t\tfor (let i = 0; i < items.length; i++) {\n\t\t\ttry {\n\t\t\t\trequestMethod = 'GET';\n\t\t\t\tendpoint = '';\n\t\t\t\tbody = {};\n\t\t\t\tqs = {};\n\n\t\t\t\tif (resource === 'board') {\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'POST';\n\t\t\t\t\t\tendpoint = 'boards';\n\n\t\t\t\t\t\tbody.name = this.getNodeParameter('name', i) as string;\n\t\t\t\t\t\tbody.desc = this.getNodeParameter('description', i) as string;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'DELETE';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${id}`;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i, undefined, { extractValue: true });\n\n\t\t\t\t\t\tendpoint = `boards/${id}`;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(qs, additionalFields);\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         update\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'PUT';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i, undefined, { extractValue: true });\n\n\t\t\t\t\t\tendpoint = `boards/${id}`;\n\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\t\tObject.assign(qs, updateFields);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t`The operation \"${operation}\" is not known!`,\n\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'boardMember') {\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\treturnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tif (!returnAll) {\n\t\t\t\t\t\t\tqs.limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tendpoint = `boards/${id}/members`;\n\t\t\t\t\t} else if (operation === 'add') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//               add\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'PUT';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tconst idMember = this.getNodeParameter('idMember', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${id}/members/${idMember}`;\n\n\t\t\t\t\t\tqs.type = this.getNodeParameter('type', i) as string;\n\t\t\t\t\t\tqs.allowBillableGuest = this.getNodeParameter(\n\t\t\t\t\t\t\t'additionalFields.allowBillableGuest',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\tfalse,\n\t\t\t\t\t\t) as boolean;\n\t\t\t\t\t} else if (operation === 'invite') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//              invite\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'PUT';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${id}/members`;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tqs.email = this.getNodeParameter('email', i) as string;\n\t\t\t\t\t\tqs.type = additionalFields.type as string;\n\t\t\t\t\t\tbody.fullName = additionalFields.fullName as string;\n\t\t\t\t\t} else if (operation === 'remove') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//              remove\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'DELETE';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tconst idMember = this.getNodeParameter('idMember', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${id}/members/${idMember}`;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t`The operation \"${operation}\" is not known!`,\n\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'card') {\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'POST';\n\t\t\t\t\t\tendpoint = 'cards';\n\n\t\t\t\t\t\tbody.idList = this.getNodeParameter('listId', i) as string;\n\n\t\t\t\t\t\tbody.name = this.getNodeParameter('name', i) as string;\n\t\t\t\t\t\tbody.desc = this.getNodeParameter('description', i) as string;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'DELETE';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i, undefined, { extractValue: true });\n\n\t\t\t\t\t\tendpoint = `cards/${id}`;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i, undefined, { extractValue: true });\n\n\t\t\t\t\t\tendpoint = `cards/${id}`;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(qs, additionalFields);\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         update\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'PUT';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i, undefined, { extractValue: true });\n\n\t\t\t\t\t\tendpoint = `cards/${id}`;\n\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\t\tObject.assign(qs, updateFields);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t`The operation \"${operation}\" is not known!`,\n\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'cardComment') {\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tbody.text = this.getNodeParameter('text', i) as string;\n\n\t\t\t\t\t\trequestMethod = 'POST';\n\n\t\t\t\t\t\tendpoint = `cards/${cardId}/actions/comments`;\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'DELETE';\n\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tconst commentId = this.getNodeParameter('commentId', i) as string;\n\n\t\t\t\t\t\tendpoint = `/cards/${cardId}/actions/${commentId}/comments`;\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         update\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'PUT';\n\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tconst commentId = this.getNodeParameter('commentId', i) as string;\n\n\t\t\t\t\t\tqs.text = this.getNodeParameter('text', i) as string;\n\n\t\t\t\t\t\tendpoint = `cards/${cardId}/actions/${commentId}/comments`;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t`The operation \"${operation}\" is not known!`,\n\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'list') {\n\t\t\t\t\tif (operation === 'archive') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         archive\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'PUT';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tqs.value = this.getNodeParameter('archive', i) as boolean;\n\n\t\t\t\t\t\tendpoint = `lists/${id}/closed`;\n\t\t\t\t\t} else if (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'POST';\n\t\t\t\t\t\tendpoint = 'lists';\n\n\t\t\t\t\t\tbody.idBoard = this.getNodeParameter('idBoard', i) as string;\n\n\t\t\t\t\t\tbody.name = this.getNodeParameter('name', i) as string;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tendpoint = `lists/${id}`;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(qs, additionalFields);\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\treturnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tif (!returnAll) {\n\t\t\t\t\t\t\tqs.limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tendpoint = `boards/${id}/lists`;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(qs, additionalFields);\n\t\t\t\t\t} else if (operation === 'getCards') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         getCards\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\treturnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tif (!returnAll) {\n\t\t\t\t\t\t\tqs.limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tendpoint = `lists/${id}/cards`;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(qs, additionalFields);\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         update\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'PUT';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tendpoint = `lists/${id}`;\n\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\t\tObject.assign(qs, updateFields);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t`The operation \"${operation}\" is not known!`,\n\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'attachment') {\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'POST';\n\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tconst url = this.getNodeParameter('url', i) as string;\n\n\t\t\t\t\t\tObject.assign(body, {\n\t\t\t\t\t\t\turl,\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\n\t\t\t\t\t\tendpoint = `cards/${cardId}/attachments`;\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'DELETE';\n\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tendpoint = `cards/${cardId}/attachments/${id}`;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tendpoint = `cards/${cardId}/attachments/${id}`;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(qs, additionalFields);\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tendpoint = `cards/${cardId}/attachments`;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(qs, additionalFields);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t`The operation \"${operation}\" is not known!`,\n\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'checklist') {\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'POST';\n\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tconst name = this.getNodeParameter('name', i) as string;\n\n\t\t\t\t\t\tObject.assign(body, { name });\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\n\t\t\t\t\t\tendpoint = `cards/${cardId}/checklists`;\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'DELETE';\n\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tendpoint = `cards/${cardId}/checklists/${id}`;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tendpoint = `checklists/${id}`;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(qs, additionalFields);\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tendpoint = `cards/${cardId}/checklists`;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(qs, additionalFields);\n\t\t\t\t\t} else if (operation === 'getCheckItem') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         getCheckItem\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tconst checkItemId = this.getNodeParameter('checkItemId', i) as string;\n\n\t\t\t\t\t\tendpoint = `cards/${cardId}/checkItem/${checkItemId}`;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(qs, additionalFields);\n\t\t\t\t\t} else if (operation === 'createCheckItem') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         createCheckItem\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'POST';\n\n\t\t\t\t\t\tconst checklistId = this.getNodeParameter('checklistId', i) as string;\n\n\t\t\t\t\t\tendpoint = `checklists/${checklistId}/checkItems`;\n\n\t\t\t\t\t\tconst name = this.getNodeParameter('name', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(body, { name, ...additionalFields });\n\t\t\t\t\t} else if (operation === 'deleteCheckItem') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         deleteCheckItem\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'DELETE';\n\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tconst checkItemId = this.getNodeParameter('checkItemId', i) as string;\n\n\t\t\t\t\t\tendpoint = `cards/${cardId}/checkItem/${checkItemId}`;\n\t\t\t\t\t} else if (operation === 'updateCheckItem') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         updateCheckItem\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'PUT';\n\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tconst checkItemId = this.getNodeParameter('checkItemId', i) as string;\n\n\t\t\t\t\t\tendpoint = `cards/${cardId}/checkItem/${checkItemId}`;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(qs, additionalFields);\n\t\t\t\t\t} else if (operation === 'completedCheckItems') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         completedCheckItems\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tendpoint = `cards/${cardId}/checkItemStates`;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(qs, additionalFields);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t`The operation \"${operation}\" is not known!`,\n\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'label') {\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'POST';\n\n\t\t\t\t\t\tconst idBoard = this.getNodeParameter('boardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tconst name = this.getNodeParameter('name', i) as string;\n\t\t\t\t\t\tconst color = this.getNodeParameter('color', i) as string;\n\n\t\t\t\t\t\tObject.assign(body, {\n\t\t\t\t\t\t\tidBoard,\n\t\t\t\t\t\t\tname,\n\t\t\t\t\t\t\tcolor,\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tendpoint = 'labels';\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'DELETE';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tendpoint = `labels/${id}`;\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tendpoint = `labels/${id}`;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tObject.assign(qs, additionalFields);\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'GET';\n\n\t\t\t\t\t\tconst idBoard = this.getNodeParameter('boardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tendpoint = `board/${idBoard}/labels`;\n\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tObject.assign(qs, additionalFields);\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         update\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'PUT';\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tendpoint = `labels/${id}`;\n\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\t\tObject.assign(qs, updateFields);\n\t\t\t\t\t} else if (operation === 'addLabel') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         addLabel\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'POST';\n\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tbody.value = id;\n\n\t\t\t\t\t\tendpoint = `/cards/${cardId}/idLabels`;\n\t\t\t\t\t} else if (operation === 'removeLabel') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         removeLabel\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\trequestMethod = 'DELETE';\n\n\t\t\t\t\t\tconst cardId = this.getNodeParameter('cardId', i, undefined, {\n\t\t\t\t\t\t\textractValue: true,\n\t\t\t\t\t\t}) as string;\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tendpoint = `/cards/${cardId}/idLabels/${id}`;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t`The operation \"${operation}\" is not known!`,\n\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthrow new NodeOperationError(this.getNode(), `The resource \"${resource}\" is not known!`, {\n\t\t\t\t\t\titemIndex: i,\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\t// resources listed here do not support pagination so\n\t\t\t\t// paginate them 'manually'\n\t\t\t\tconst skipPagination = ['list:getAll'];\n\n\t\t\t\tif (returnAll && !skipPagination.includes(`${resource}:${operation}`)) {\n\t\t\t\t\tresponseData = await apiRequestAllItems.call(this, requestMethod, endpoint, body, qs);\n\t\t\t\t} else {\n\t\t\t\t\tresponseData = await apiRequest.call(this, requestMethod, endpoint, body, qs);\n\t\t\t\t\tif (!returnAll && qs.limit) {\n\t\t\t\t\t\tresponseData = responseData.splice(0, qs.limit);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\tthis.helpers.returnJsonArray(responseData as IDataObject[]),\n\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t);\n\t\t\t\treturnData.push(...executionData);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\t\tthis.helpers.returnJsonArray({ error: error.message }),\n\t\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t\t);\n\t\t\t\t\treturnData.push(...executionData);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,0BAAwD;AAExD,mCAAuD;AACvD,8BAA6C;AAC7C,oCAAyD;AACzD,oCAAyD;AACzD,6BAA2C;AAC3C,kCAAqD;AACrD,8BAA+C;AAC/C,8BAA6C;AAC7C,6BAA2C;AASpC,MAAM,OAA4B;AAAA,EAAlC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,WAAW;AAAA,MACnB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA;AAAA;AAAA;AAAA,QAKA,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA;AAAA;AAAA;AAAA,QAKH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,YAAY;AAAA,QACX,MAAM,aAEL,OACiC;AACjC,cAAI,CAAC,OAAO;AACX,kBAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,kCAAkC;AAAA,UAChF;AACA,gBAAM,gBAAgB,MAAM,mCAAW;AAAA,YACtC;AAAA,YACA;AAAA,YACA;AAAA,YACA,CAAC;AAAA,YACD;AAAA,cACC;AAAA,cACA,YAAY;AAAA,cACZ,cAAc;AAAA;AAAA,cAEd,SAAS;AAAA;AAAA,cAET,cAAc;AAAA,YACf;AAAA,UACD;AACA,iBAAO;AAAA,YACN,SAAS,cAAc,OAAO,IAAI,CAAC,OAAwB;AAAA,cAC1D,MAAM,EAAE;AAAA,cACR,OAAO,EAAE;AAAA,cACT,KAAK,EAAE;AAAA,cACP,aAAa,EAAE;AAAA,YAChB,EAAE;AAAA,UACH;AAAA,QACD;AAAA,QACA,MAAM,YAEL,OACiC;AACjC,cAAI,CAAC,OAAO;AACX,kBAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,kCAAkC;AAAA,UAChF;AACA,gBAAM,gBAAgB,MAAM,mCAAW;AAAA,YACtC;AAAA,YACA;AAAA,YACA;AAAA,YACA,CAAC;AAAA,YACD;AAAA,cACC;AAAA,cACA,YAAY;AAAA,cACZ,cAAc;AAAA;AAAA,cAEd,SAAS;AAAA;AAAA,cAET,aAAa;AAAA,YACd;AAAA,UACD;AACA,iBAAO;AAAA,YACN,SAAS,cAAc,MAAM,IAAI,CAAC,OAAwB;AAAA,cACzD,MAAM,EAAE;AAAA,cACR,OAAO,EAAE;AAAA,cACT,KAAK,EAAE;AAAA,cACP,aAAa,EAAE;AAAA,YAChB,EAAE;AAAA,UACH;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAAmC,CAAC;AAE1C,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAGpD,QAAI;AAEJ,QAAI;AAEJ,QAAI;AACJ,QAAI;AACJ,QAAI,YAAY;AAChB,QAAI;AAEJ,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,UAAI;AACH,wBAAgB;AAChB,mBAAW;AACX,eAAO,CAAC;AACR,aAAK,CAAC;AAEN,YAAI,aAAa,SAAS;AACzB,cAAI,cAAc,UAAU;AAK3B,4BAAgB;AAChB,uBAAW;AAEX,iBAAK,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC3C,iBAAK,OAAO,KAAK,iBAAiB,eAAe,CAAC;AAElD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,MAAM,gBAAgB;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,GAAG,QAAW;AAAA,cACpD,cAAc;AAAA,YACf,CAAC;AAED,uBAAW,UAAU,EAAE;AAAA,UACxB,WAAW,cAAc,OAAO;AAK/B,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,GAAG,QAAW,EAAE,cAAc,KAAK,CAAC;AAE3E,uBAAW,UAAU,EAAE;AAEvB,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,IAAI,gBAAgB;AAAA,UACnC,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,GAAG,QAAW,EAAE,cAAc,KAAK,CAAC;AAE3E,uBAAW,UAAU,EAAE;AAEvB,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,mBAAO,OAAO,IAAI,YAAY;AAAA,UAC/B,OAAO;AACN,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,kBAAkB,SAAS;AAAA,cAC3B,EAAE,WAAW,EAAE;AAAA,YAChB;AAAA,UACD;AAAA,QACD,WAAW,aAAa,eAAe;AACtC,cAAI,cAAc,UAAU;AAK3B,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AACxC,wBAAY,KAAK,iBAAiB,aAAa,CAAC;AAChD,gBAAI,CAAC,WAAW;AACf,iBAAG,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAAA,YAC5C;AAEA,uBAAW,UAAU,EAAE;AAAA,UACxB,WAAW,cAAc,OAAO;AAK/B,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AACxC,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAEpD,uBAAW,UAAU,EAAE,YAAY,QAAQ;AAE3C,eAAG,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AACzC,eAAG,qBAAqB,KAAK;AAAA,cAC5B;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAAA,UACD,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,uBAAW,UAAU,EAAE;AAEvB,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,eAAG,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC3C,eAAG,OAAO,iBAAiB;AAC3B,iBAAK,WAAW,iBAAiB;AAAA,UAClC,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AACxC,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAEpD,uBAAW,UAAU,EAAE,YAAY,QAAQ;AAAA,UAC5C,OAAO;AACN,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,kBAAkB,SAAS;AAAA,cAC3B,EAAE,WAAW,EAAE;AAAA,YAChB;AAAA,UACD;AAAA,QACD,WAAW,aAAa,QAAQ;AAC/B,cAAI,cAAc,UAAU;AAK3B,4BAAgB;AAChB,uBAAW;AAEX,iBAAK,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAE/C,iBAAK,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC3C,iBAAK,OAAO,KAAK,iBAAiB,eAAe,CAAC;AAElD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,MAAM,gBAAgB;AAAA,UACrC,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,GAAG,QAAW,EAAE,cAAc,KAAK,CAAC;AAE3E,uBAAW,SAAS,EAAE;AAAA,UACvB,WAAW,cAAc,OAAO;AAK/B,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,GAAG,QAAW,EAAE,cAAc,KAAK,CAAC;AAE3E,uBAAW,SAAS,EAAE;AAEtB,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,IAAI,gBAAgB;AAAA,UACnC,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,GAAG,QAAW,EAAE,cAAc,KAAK,CAAC;AAE3E,uBAAW,SAAS,EAAE;AAEtB,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,mBAAO,OAAO,IAAI,YAAY;AAAA,UAC/B,OAAO;AACN,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,kBAAkB,SAAS;AAAA,cAC3B,EAAE,WAAW,EAAE;AAAA,YAChB;AAAA,UACD;AAAA,QACD,WAAW,aAAa,eAAe;AACtC,cAAI,cAAc,UAAU;AAK3B,kBAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,cAC5D,cAAc;AAAA,YACf,CAAC;AAED,iBAAK,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAE3C,4BAAgB;AAEhB,uBAAW,SAAS,MAAM;AAAA,UAC3B,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,cAC5D,cAAc;AAAA,YACf,CAAC;AAED,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,uBAAW,UAAU,MAAM,YAAY,SAAS;AAAA,UACjD,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,cAC5D,cAAc;AAAA,YACf,CAAC;AAED,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,eAAG,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAEzC,uBAAW,SAAS,MAAM,YAAY,SAAS;AAAA,UAChD,OAAO;AACN,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,kBAAkB,SAAS;AAAA,cAC3B,EAAE,WAAW,EAAE;AAAA,YAChB;AAAA,UACD;AAAA,QACD,WAAW,aAAa,QAAQ;AAC/B,cAAI,cAAc,WAAW;AAK5B,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AACxC,eAAG,QAAQ,KAAK,iBAAiB,WAAW,CAAC;AAE7C,uBAAW,SAAS,EAAE;AAAA,UACvB,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAChB,uBAAW;AAEX,iBAAK,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAEjD,iBAAK,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAE3C,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,MAAM,gBAAgB;AAAA,UACrC,WAAW,cAAc,OAAO;AAK/B,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,uBAAW,SAAS,EAAE;AAEtB,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,IAAI,gBAAgB;AAAA,UACnC,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,wBAAY,KAAK,iBAAiB,aAAa,CAAC;AAEhD,gBAAI,CAAC,WAAW;AACf,iBAAG,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAAA,YAC5C;AAEA,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,uBAAW,UAAU,EAAE;AAEvB,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,IAAI,gBAAgB;AAAA,UACnC,WAAW,cAAc,YAAY;AAKpC,4BAAgB;AAEhB,wBAAY,KAAK,iBAAiB,aAAa,CAAC;AAEhD,gBAAI,CAAC,WAAW;AACf,iBAAG,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAAA,YAC5C;AAEA,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,uBAAW,SAAS,EAAE;AAEtB,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,IAAI,gBAAgB;AAAA,UACnC,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,uBAAW,SAAS,EAAE;AAEtB,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,mBAAO,OAAO,IAAI,YAAY;AAAA,UAC/B,OAAO;AACN,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,kBAAkB,SAAS;AAAA,cAC3B,EAAE,WAAW,EAAE;AAAA,YAChB;AAAA,UACD;AAAA,QACD,WAAW,aAAa,cAAc;AACrC,cAAI,cAAc,UAAU;AAK3B,4BAAgB;AAEhB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,cAC5D,cAAc;AAAA,YACf,CAAC;AAED,kBAAM,MAAM,KAAK,iBAAiB,OAAO,CAAC;AAE1C,mBAAO,OAAO,MAAM;AAAA,cACnB;AAAA,YACD,CAAC;AAED,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,MAAM,gBAAgB;AAEpC,uBAAW,SAAS,MAAM;AAAA,UAC3B,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,cAC5D,cAAc;AAAA,YACf,CAAC;AAED,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,uBAAW,SAAS,MAAM,gBAAgB,EAAE;AAAA,UAC7C,WAAW,cAAc,OAAO;AAK/B,4BAAgB;AAEhB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,cAC5D,cAAc;AAAA,YACf,CAAC;AAED,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,uBAAW,SAAS,MAAM,gBAAgB,EAAE;AAE5C,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,IAAI,gBAAgB;AAAA,UACnC,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,cAC5D,cAAc;AAAA,YACf,CAAC;AAED,uBAAW,SAAS,MAAM;AAE1B,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,IAAI,gBAAgB;AAAA,UACnC,OAAO;AACN,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,kBAAkB,SAAS;AAAA,cAC3B,EAAE,WAAW,EAAE;AAAA,YAChB;AAAA,UACD;AAAA,QACD,WAAW,aAAa,aAAa;AACpC,cAAI,cAAc,UAAU;AAK3B,4BAAgB;AAEhB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,cAC5D,cAAc;AAAA,YACf,CAAC;AAED,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAE5C,mBAAO,OAAO,MAAM,EAAE,KAAK,CAAC;AAE5B,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,MAAM,gBAAgB;AAEpC,uBAAW,SAAS,MAAM;AAAA,UAC3B,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,cAC5D,cAAc;AAAA,YACf,CAAC;AAED,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,uBAAW,SAAS,MAAM,eAAe,EAAE;AAAA,UAC5C,WAAW,cAAc,OAAO;AAK/B,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,uBAAW,cAAc,EAAE;AAE3B,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,IAAI,gBAAgB;AAAA,UACnC,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,cAC5D,cAAc;AAAA,YACf,CAAC;AAED,uBAAW,SAAS,MAAM;AAE1B,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,IAAI,gBAAgB;AAAA,UACnC,WAAW,cAAc,gBAAgB;AAKxC,4BAAgB;AAEhB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,cAC5D,cAAc;AAAA,YACf,CAAC;AAED,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAE1D,uBAAW,SAAS,MAAM,cAAc,WAAW;AAEnD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,IAAI,gBAAgB;AAAA,UACnC,WAAW,cAAc,mBAAmB;AAK3C,4BAAgB;AAEhB,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAE1D,uBAAW,cAAc,WAAW;AAEpC,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC5C,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,MAAM,EAAE,MAAM,GAAG,iBAAiB,CAAC;AAAA,UAClD,WAAW,cAAc,mBAAmB;AAK3C,4BAAgB;AAEhB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,cAC5D,cAAc;AAAA,YACf,CAAC;AAED,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAE1D,uBAAW,SAAS,MAAM,cAAc,WAAW;AAAA,UACpD,WAAW,cAAc,mBAAmB;AAK3C,4BAAgB;AAEhB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,cAC5D,cAAc;AAAA,YACf,CAAC;AAED,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAE1D,uBAAW,SAAS,MAAM,cAAc,WAAW;AAEnD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,IAAI,gBAAgB;AAAA,UACnC,WAAW,cAAc,uBAAuB;AAK/C,4BAAgB;AAEhB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,cAC5D,cAAc;AAAA,YACf,CAAC;AAED,uBAAW,SAAS,MAAM;AAE1B,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,IAAI,gBAAgB;AAAA,UACnC,OAAO;AACN,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,kBAAkB,SAAS;AAAA,cAC3B,EAAE,WAAW,EAAE;AAAA,YAChB;AAAA,UACD;AAAA,QACD,WAAW,aAAa,SAAS;AAChC,cAAI,cAAc,UAAU;AAK3B,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,GAAG,QAAW;AAAA,cAC9D,cAAc;AAAA,YACf,CAAC;AAED,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC5C,kBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAE9C,mBAAO,OAAO,MAAM;AAAA,cACnB;AAAA,cACA;AAAA,cACA;AAAA,YACD,CAAC;AAED,uBAAW;AAAA,UACZ,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,uBAAW,UAAU,EAAE;AAAA,UACxB,WAAW,cAAc,OAAO;AAK/B,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,uBAAW,UAAU,EAAE;AAEvB,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,mBAAO,OAAO,IAAI,gBAAgB;AAAA,UACnC,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,GAAG,QAAW;AAAA,cAC9D,cAAc;AAAA,YACf,CAAC;AAED,uBAAW,SAAS,OAAO;AAE3B,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,mBAAO,OAAO,IAAI,gBAAgB;AAAA,UACnC,WAAW,cAAc,UAAU;AAKlC,4BAAgB;AAEhB,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,uBAAW,UAAU,EAAE;AAEvB,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,mBAAO,OAAO,IAAI,YAAY;AAAA,UAC/B,WAAW,cAAc,YAAY;AAKpC,4BAAgB;AAEhB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,cAC5D,cAAc;AAAA,YACf,CAAC;AAED,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,iBAAK,QAAQ;AAEb,uBAAW,UAAU,MAAM;AAAA,UAC5B,WAAW,cAAc,eAAe;AAKvC,4BAAgB;AAEhB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,cAC5D,cAAc;AAAA,YACf,CAAC;AAED,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,uBAAW,UAAU,MAAM,aAAa,EAAE;AAAA,UAC3C,OAAO;AACN,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,kBAAkB,SAAS;AAAA,cAC3B,EAAE,WAAW,EAAE;AAAA,YAChB;AAAA,UACD;AAAA,QACD,OAAO;AACN,gBAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,iBAAiB,QAAQ,mBAAmB;AAAA,YACxF,WAAW;AAAA,UACZ,CAAC;AAAA,QACF;AAIA,cAAM,iBAAiB,CAAC,aAAa;AAErC,YAAI,aAAa,CAAC,eAAe,SAAS,GAAG,QAAQ,IAAI,SAAS,EAAE,GAAG;AACtE,yBAAe,MAAM,2CAAmB,KAAK,MAAM,eAAe,UAAU,MAAM,EAAE;AAAA,QACrF,OAAO;AACN,yBAAe,MAAM,mCAAW,KAAK,MAAM,eAAe,UAAU,MAAM,EAAE;AAC5E,cAAI,CAAC,aAAa,GAAG,OAAO;AAC3B,2BAAe,aAAa,OAAO,GAAG,GAAG,KAAK;AAAA,UAC/C;AAAA,QACD;AAEA,cAAM,gBAAgB,KAAK,QAAQ;AAAA,UAClC,KAAK,QAAQ,gBAAgB,YAA6B;AAAA,UAC1D,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,QACzB;AACA,mBAAW,KAAK,GAAG,aAAa;AAAA,MACjC,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,gBAAM,gBAAgB,KAAK,QAAQ;AAAA,YAClC,KAAK,QAAQ,gBAAgB,EAAE,OAAO,MAAM,QAAQ,CAAC;AAAA,YACrD,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,UACzB;AACA,qBAAW,KAAK,GAAG,aAAa;AAChC;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AAEA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": []}