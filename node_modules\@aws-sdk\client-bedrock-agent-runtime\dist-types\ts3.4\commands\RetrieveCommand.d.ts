import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  BedrockAgentRuntimeClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../BedrockAgentRuntimeClient";
import { RetrieveRequest, RetrieveResponse } from "../models/models_1";
export { __MetadataBearer };
export { $Command };
export interface RetrieveCommandInput extends RetrieveRequest {}
export interface RetrieveCommandOutput
  extends RetrieveResponse,
    __MetadataBearer {}
declare const RetrieveCommand_base: {
  new (
    input: RetrieveCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    RetrieveCommandInput,
    RetrieveCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: RetrieveCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    RetrieveCommandInput,
    RetrieveCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class RetrieveCommand extends RetrieveCommand_base {
  protected static __types: {
    api: {
      input: RetrieveRequest;
      output: RetrieveResponse;
    };
    sdk: {
      input: RetrieveCommandInput;
      output: RetrieveCommandOutput;
    };
  };
}
