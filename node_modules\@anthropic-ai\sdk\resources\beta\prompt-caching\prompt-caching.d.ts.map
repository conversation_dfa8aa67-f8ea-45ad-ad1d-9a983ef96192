{"version": 3, "file": "prompt-caching.d.ts", "sourceRoot": "", "sources": ["../../../src/resources/beta/prompt-caching/prompt-caching.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,KAAK,WAAW,MAAM,YAAY,CAAC;AAC1C,OAAO,EACL,mBAAmB,EACnB,+BAA+B,EAC/B,4BAA4B,EAC5B,QAAQ,EACR,sCAAsC,EACtC,gCAAgC,EAChC,wBAAwB,EACxB,6BAA6B,EAC7B,+BAA+B,EAC/B,qBAAqB,EACrB,qCAAqC,EACrC,kCAAkC,EAClC,sBAAsB,EACtB,qCAAqC,EACrC,sCAAsC,EACvC,MAAM,YAAY,CAAC;AAEpB,qBAAa,aAAc,SAAQ,WAAW;IAC5C,QAAQ,EAAE,WAAW,CAAC,QAAQ,CAA0C;CACzE;AAID,MAAM,CAAC,OAAO,WAAW,aAAa,CAAC;IACrC,OAAO,EACL,QAAQ,IAAI,QAAQ,EACpB,KAAK,sCAAsC,IAAI,sCAAsC,EACrF,KAAK,gCAAgC,IAAI,gCAAgC,EACzE,KAAK,wBAAwB,IAAI,wBAAwB,EACzD,KAAK,6BAA6B,IAAI,6BAA6B,EACnE,KAAK,+BAA+B,IAAI,+BAA+B,EACvE,KAAK,qBAAqB,IAAI,qBAAqB,EACnD,KAAK,qCAAqC,IAAI,qCAAqC,EACnF,KAAK,kCAAkC,IAAI,kCAAkC,EAC7E,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,qCAAqC,IAAI,qCAAqC,EACnF,KAAK,sCAAsC,IAAI,sCAAsC,EACrF,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,+BAA+B,IAAI,+BAA+B,EACvE,KAAK,4BAA4B,IAAI,4BAA4B,GAClE,CAAC;CACH"}