import { Paginator } from "@smithy/types";
import { ListSessionsCommandInput, ListSessionsCommandOutput } from "../commands/ListSessionsCommand";
import { BedrockAgentRuntimePaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListSessions: (config: BedrockAgentRuntimePaginationConfiguration, input: ListSessionsCommandInput, ...rest: any[]) => Paginator<ListSessionsCommandOutput>;
