{"version": 3, "sources": ["../../../../../nodes/Airtop/actions/window/Window.resource.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nimport * as close from './close.operation';\nimport * as create from './create.operation';\nimport * as load from './load.operation';\nimport * as takeScreenshot from './takeScreenshot.operation';\nimport { sessionIdField, windowIdField } from '../common/fields';\n\nexport { create, close, takeScreenshot, load };\n\nexport const description: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\ttypeOptions: {\n\t\t\tsortable: false,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['window'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Create a New Browser Window',\n\t\t\t\tvalue: 'create',\n\t\t\t\tdescription: 'Create a new browser window inside a session. Can load a URL when created.',\n\t\t\t\taction: 'Create a window',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Load URL',\n\t\t\t\tvalue: 'load',\n\t\t\t\tdescription: 'Load a URL in an existing window',\n\t\t\t\taction: 'Load a page',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Take Screenshot',\n\t\t\t\tvalue: 'takeScreenshot',\n\t\t\t\tdescription: 'Take a screenshot of the current window',\n\t\t\t\taction: 'Take screenshot',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Close Window',\n\t\t\t\tvalue: 'close',\n\t\t\t\tdescription: 'Close a window inside a session',\n\t\t\t\taction: 'Close a window',\n\t\t\t},\n\t\t],\n\t\tdefault: 'create',\n\t},\n\t{\n\t\t...sessionIdField,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['window'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\t...windowIdField,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['window'],\n\t\t\t\toperation: ['close', 'takeScreenshot', 'load'],\n\t\t\t},\n\t\t},\n\t},\n\t...create.description,\n\t...load.description,\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,YAAuB;AACvB,aAAwB;AACxB,WAAsB;AACtB,qBAAgC;AAChC,oBAA8C;AAIvC,MAAM,cAAiC;AAAA,EAC7C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,aAAa;AAAA,MACZ,UAAU;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,MACpB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,GAAG;AAAA,IACH,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,MACpB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,GAAG;AAAA,IACH,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,QAAQ;AAAA,QACnB,WAAW,CAAC,SAAS,kBAAkB,MAAM;AAAA,MAC9C;AAAA,IACD;AAAA,EACD;AAAA,EACA,GAAG,OAAO;AAAA,EACV,GAAG,KAAK;AACT;", "names": []}