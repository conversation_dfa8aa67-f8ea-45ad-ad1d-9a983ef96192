{"version": 3, "sources": ["../../../../../nodes/Microsoft/AzureCosmosDb/helpers/utils.ts"], "sourcesContent": ["import type {\n\tIDataObject,\n\tIExecuteSingleFunctions,\n\tIHttpRequestOptions,\n\tIN8nHttpFullResponse,\n\tINodeExecutionData,\n\tNodeApiError,\n} from 'n8n-workflow';\nimport { jsonParse, NodeOperationError, OperationalError } from 'n8n-workflow';\n\nimport { HeaderConstants } from './constants';\nimport { ErrorMap } from './errorHandler';\nimport type { IContainer } from './interfaces';\nimport { azureCosmosDbApiRequest } from '../transport';\n\nexport async function getPartitionKey(this: IExecuteSingleFunctions): Promise<string> {\n\tconst container = this.getNodeParameter('container', undefined, {\n\t\textractValue: true,\n\t}) as string;\n\n\tlet partitionKeyField: string | undefined = undefined;\n\ttry {\n\t\tconst responseData = (await azureCosmosDbApiRequest.call(\n\t\t\tthis,\n\t\t\t'GET',\n\t\t\t`/colls/${container}`,\n\t\t)) as IContainer;\n\t\tpartitionKeyField = responseData.partitionKey?.paths[0]?.replace('/', '');\n\t} catch (error) {\n\t\tconst err = error as NodeApiError;\n\t\tif (err.httpCode === '404') {\n\t\t\terr.message = ErrorMap.Container.NotFound.getMessage(container);\n\t\t\terr.description = ErrorMap.Container.NotFound.description;\n\t\t}\n\t\tthrow err;\n\t}\n\n\tif (!partitionKeyField) {\n\t\tthrow new NodeOperationError(this.getNode(), 'Partition key not found', {\n\t\t\tdescription: 'Failed to determine the partition key for this collection',\n\t\t});\n\t}\n\n\treturn partitionKeyField;\n}\n\nexport async function simplifyData(\n\tthis: IExecuteSingleFunctions,\n\titems: INodeExecutionData[],\n\t_response: IN8nHttpFullResponse,\n): Promise<INodeExecutionData[]> {\n\tconst simple = this.getNodeParameter('simple') as boolean;\n\n\tif (!simple) {\n\t\treturn items;\n\t}\n\n\tconst simplifyFields = (data: IDataObject): IDataObject => {\n\t\tconst simplifiedData = Object.keys(data)\n\t\t\t.filter((key) => !key.startsWith('_'))\n\t\t\t.reduce((acc, key) => {\n\t\t\t\tacc[key] = data[key];\n\t\t\t\treturn acc;\n\t\t\t}, {} as IDataObject);\n\n\t\treturn simplifiedData;\n\t};\n\n\treturn items.map((item) => {\n\t\tconst simplifiedData = simplifyFields(item.json);\n\t\treturn { json: simplifiedData } as INodeExecutionData;\n\t});\n}\n\nexport async function validateQueryParameters(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst query = this.getNodeParameter('query', '') as string;\n\tconst queryOptions = this.getNodeParameter('options.queryOptions') as IDataObject;\n\n\tconst parameterNames = query.replace(/\\$(\\d+)/g, '@Param$1').match(/@\\w+/g) ?? [];\n\n\tconst queryParamsString = queryOptions?.queryParameters as string;\n\tconst parameterValues = queryParamsString\n\t\t? queryParamsString.split(',').map((param) => param.trim())\n\t\t: [];\n\n\tif (parameterNames.length !== parameterValues.length) {\n\t\tthrow new NodeOperationError(this.getNode(), 'Empty parameter value provided', {\n\t\t\tdescription: 'Please provide non-empty values for the query parameters',\n\t\t});\n\t}\n\n\trequestOptions.body = {\n\t\t...(requestOptions.body as IDataObject),\n\t\tparameters: parameterNames.map((name, index) => ({\n\t\t\tname,\n\t\t\tvalue: parameterValues[index],\n\t\t})),\n\t};\n\n\treturn requestOptions;\n}\n\nexport function processJsonInput<T>(\n\tjsonData: T,\n\tinputName?: string,\n\tfallbackValue: T | undefined = undefined,\n\tdisallowSpacesIn?: string[],\n): Record<string, unknown> {\n\tlet values: Record<string, unknown> = {};\n\n\tconst input = inputName ? `'${inputName}' ` : '';\n\n\tif (typeof jsonData === 'string') {\n\t\ttry {\n\t\t\tvalues = jsonParse(jsonData, { fallbackValue }) as Record<string, unknown>;\n\t\t} catch (error) {\n\t\t\tthrow new OperationalError(`Input ${input}must contain a valid JSON`, { level: 'warning' });\n\t\t}\n\t} else if (jsonData && typeof jsonData === 'object') {\n\t\tvalues = jsonData as Record<string, unknown>;\n\t} else {\n\t\tthrow new OperationalError(`Input ${input}must contain a valid JSON`, { level: 'warning' });\n\t}\n\n\tdisallowSpacesIn?.forEach((key) => {\n\t\tconst value = values[key];\n\t\tif (typeof value === 'string' && value.includes(' ')) {\n\t\t\tthrow new OperationalError(\n\t\t\t\t`${inputName ? `'${inputName}'` : ''} property '${key}' should not contain spaces (received \"${value}\")`,\n\t\t\t\t{ level: 'warning' },\n\t\t\t);\n\t\t}\n\t});\n\n\treturn values;\n}\n\nexport async function validatePartitionKey(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst operation = this.getNodeParameter('operation') as string;\n\tlet customProperties = this.getNodeParameter('customProperties', {}) as IDataObject;\n\n\tconst partitionKey = await getPartitionKey.call(this);\n\n\tif (typeof customProperties === 'string') {\n\t\ttry {\n\t\t\tcustomProperties = jsonParse(customProperties);\n\t\t} catch (error) {\n\t\t\tthrow new NodeOperationError(this.getNode(), 'Invalid JSON format in \"Item Contents\"', {\n\t\t\t\tdescription: 'Ensure the \"Item Contents\" field contains a valid JSON object',\n\t\t\t});\n\t\t}\n\t}\n\n\tlet partitionKeyValue: string = '';\n\tconst needsPartitionKey = ['update', 'delete', 'get'].includes(operation);\n\n\tif (operation === 'create') {\n\t\tif (!(partitionKey in customProperties) || !customProperties[partitionKey]) {\n\t\t\tthrow new NodeOperationError(this.getNode(), \"Partition key not found in 'Item Contents'\", {\n\t\t\t\tdescription: `Partition key '${partitionKey}' must be present and have a valid, non-empty value in 'Item Contents'.`,\n\t\t\t});\n\t\t}\n\t\tpartitionKeyValue = customProperties[partitionKey] as string;\n\t} else if (needsPartitionKey) {\n\t\ttry {\n\t\t\tpartitionKeyValue =\n\t\t\t\tpartitionKey === 'id'\n\t\t\t\t\t? String(this.getNodeParameter('item', undefined, { extractValue: true }) ?? '')\n\t\t\t\t\t: String(this.getNodeParameter('additionalFields.partitionKey', undefined) ?? '');\n\n\t\t\tif (!partitionKeyValue) {\n\t\t\t\tthrow new NodeOperationError(this.getNode(), 'Partition key is empty', {\n\t\t\t\t\tdescription: 'Ensure the \"Partition Key\" field has a valid, non-empty value.',\n\t\t\t\t});\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tthrow new NodeOperationError(this.getNode(), 'Partition key is missing or empty', {\n\t\t\t\tdescription: 'Ensure the \"Partition Key\" field exists and has a valid, non-empty value.',\n\t\t\t});\n\t\t}\n\n\t\tif (operation === 'update') {\n\t\t\tconst idValue = String(\n\t\t\t\tthis.getNodeParameter('item', undefined, { extractValue: true }) ?? '',\n\t\t\t);\n\n\t\t\t(requestOptions.body as IDataObject).id = idValue;\n\t\t\t(requestOptions.body as IDataObject)[partitionKey] = partitionKeyValue;\n\t\t}\n\t}\n\n\trequestOptions.headers = {\n\t\t...requestOptions.headers,\n\t\t[HeaderConstants.X_MS_DOCUMENTDB_PARTITIONKEY]: `[\"${partitionKeyValue}\"]`,\n\t};\n\n\treturn requestOptions;\n}\n\nexport async function validateCustomProperties(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst rawCustomProperties = this.getNodeParameter('customProperties') as IDataObject;\n\tconst customProperties = processJsonInput(rawCustomProperties, 'Item Contents');\n\tif (\n\t\tObject.keys(customProperties).length === 0 ||\n\t\tObject.values(customProperties).every((val) => val === undefined || val === null || val === '')\n\t) {\n\t\tthrow new NodeOperationError(this.getNode(), 'Item contents are empty', {\n\t\t\tdescription: 'Ensure the \"Item Contents\" field contains at least one valid property.',\n\t\t});\n\t}\n\trequestOptions.body = {\n\t\t...(requestOptions.body as IDataObject),\n\t\t...customProperties,\n\t};\n\treturn requestOptions;\n}\n\nexport const untilContainerSelected = { container: [''] };\n\nexport const untilItemSelected = { item: [''] };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,0BAAgE;AAEhE,uBAAgC;AAChC,0BAAyB;AAEzB,uBAAwC;AAExC,eAAsB,kBAAgE;AACrF,QAAM,YAAY,KAAK,iBAAiB,aAAa,QAAW;AAAA,IAC/D,cAAc;AAAA,EACf,CAAC;AAED,MAAI,oBAAwC;AAC5C,MAAI;AACH,UAAM,eAAgB,MAAM,yCAAwB;AAAA,MACnD;AAAA,MACA;AAAA,MACA,UAAU,SAAS;AAAA,IACpB;AACA,wBAAoB,aAAa,cAAc,MAAM,CAAC,GAAG,QAAQ,KAAK,EAAE;AAAA,EACzE,SAAS,OAAO;AACf,UAAM,MAAM;AACZ,QAAI,IAAI,aAAa,OAAO;AAC3B,UAAI,UAAU,6BAAS,UAAU,SAAS,WAAW,SAAS;AAC9D,UAAI,cAAc,6BAAS,UAAU,SAAS;AAAA,IAC/C;AACA,UAAM;AAAA,EACP;AAEA,MAAI,CAAC,mBAAmB;AACvB,UAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,2BAA2B;AAAA,MACvE,aAAa;AAAA,IACd,CAAC;AAAA,EACF;AAEA,SAAO;AACR;AAEA,eAAsB,aAErB,OACA,WACgC;AAChC,QAAM,SAAS,KAAK,iBAAiB,QAAQ;AAE7C,MAAI,CAAC,QAAQ;AACZ,WAAO;AAAA,EACR;AAEA,QAAM,iBAAiB,CAAC,SAAmC;AAC1D,UAAM,iBAAiB,OAAO,KAAK,IAAI,EACrC,OAAO,CAAC,QAAQ,CAAC,IAAI,WAAW,GAAG,CAAC,EACpC,OAAO,CAAC,KAAK,QAAQ;AACrB,UAAI,GAAG,IAAI,KAAK,GAAG;AACnB,aAAO;AAAA,IACR,GAAG,CAAC,CAAgB;AAErB,WAAO;AAAA,EACR;AAEA,SAAO,MAAM,IAAI,CAAC,SAAS;AAC1B,UAAM,iBAAiB,eAAe,KAAK,IAAI;AAC/C,WAAO,EAAE,MAAM,eAAe;AAAA,EAC/B,CAAC;AACF;AAEA,eAAsB,wBAErB,gBAC+B;AAC/B,QAAM,QAAQ,KAAK,iBAAiB,SAAS,EAAE;AAC/C,QAAM,eAAe,KAAK,iBAAiB,sBAAsB;AAEjE,QAAM,iBAAiB,MAAM,QAAQ,YAAY,UAAU,EAAE,MAAM,OAAO,KAAK,CAAC;AAEhF,QAAM,oBAAoB,cAAc;AACxC,QAAM,kBAAkB,oBACrB,kBAAkB,MAAM,GAAG,EAAE,IAAI,CAAC,UAAU,MAAM,KAAK,CAAC,IACxD,CAAC;AAEJ,MAAI,eAAe,WAAW,gBAAgB,QAAQ;AACrD,UAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,kCAAkC;AAAA,MAC9E,aAAa;AAAA,IACd,CAAC;AAAA,EACF;AAEA,iBAAe,OAAO;AAAA,IACrB,GAAI,eAAe;AAAA,IACnB,YAAY,eAAe,IAAI,CAAC,MAAM,WAAW;AAAA,MAChD;AAAA,MACA,OAAO,gBAAgB,KAAK;AAAA,IAC7B,EAAE;AAAA,EACH;AAEA,SAAO;AACR;AAEO,SAAS,iBACf,UACA,WACA,gBAA+B,QAC/B,kBAC0B;AAC1B,MAAI,SAAkC,CAAC;AAEvC,QAAM,QAAQ,YAAY,IAAI,SAAS,OAAO;AAE9C,MAAI,OAAO,aAAa,UAAU;AACjC,QAAI;AACH,mBAAS,+BAAU,UAAU,EAAE,cAAc,CAAC;AAAA,IAC/C,SAAS,OAAO;AACf,YAAM,IAAI,qCAAiB,SAAS,KAAK,6BAA6B,EAAE,OAAO,UAAU,CAAC;AAAA,IAC3F;AAAA,EACD,WAAW,YAAY,OAAO,aAAa,UAAU;AACpD,aAAS;AAAA,EACV,OAAO;AACN,UAAM,IAAI,qCAAiB,SAAS,KAAK,6BAA6B,EAAE,OAAO,UAAU,CAAC;AAAA,EAC3F;AAEA,oBAAkB,QAAQ,CAAC,QAAQ;AAClC,UAAM,QAAQ,OAAO,GAAG;AACxB,QAAI,OAAO,UAAU,YAAY,MAAM,SAAS,GAAG,GAAG;AACrD,YAAM,IAAI;AAAA,QACT,GAAG,YAAY,IAAI,SAAS,MAAM,EAAE,cAAc,GAAG,0CAA0C,KAAK;AAAA,QACpG,EAAE,OAAO,UAAU;AAAA,MACpB;AAAA,IACD;AAAA,EACD,CAAC;AAED,SAAO;AACR;AAEA,eAAsB,qBAErB,gBAC+B;AAC/B,QAAM,YAAY,KAAK,iBAAiB,WAAW;AACnD,MAAI,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC,CAAC;AAEnE,QAAM,eAAe,MAAM,gBAAgB,KAAK,IAAI;AAEpD,MAAI,OAAO,qBAAqB,UAAU;AACzC,QAAI;AACH,6BAAmB,+BAAU,gBAAgB;AAAA,IAC9C,SAAS,OAAO;AACf,YAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,0CAA0C;AAAA,QACtF,aAAa;AAAA,MACd,CAAC;AAAA,IACF;AAAA,EACD;AAEA,MAAI,oBAA4B;AAChC,QAAM,oBAAoB,CAAC,UAAU,UAAU,KAAK,EAAE,SAAS,SAAS;AAExE,MAAI,cAAc,UAAU;AAC3B,QAAI,EAAE,gBAAgB,qBAAqB,CAAC,iBAAiB,YAAY,GAAG;AAC3E,YAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,8CAA8C;AAAA,QAC1F,aAAa,kBAAkB,YAAY;AAAA,MAC5C,CAAC;AAAA,IACF;AACA,wBAAoB,iBAAiB,YAAY;AAAA,EAClD,WAAW,mBAAmB;AAC7B,QAAI;AACH,0BACC,iBAAiB,OACd,OAAO,KAAK,iBAAiB,QAAQ,QAAW,EAAE,cAAc,KAAK,CAAC,KAAK,EAAE,IAC7E,OAAO,KAAK,iBAAiB,iCAAiC,MAAS,KAAK,EAAE;AAElF,UAAI,CAAC,mBAAmB;AACvB,cAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,0BAA0B;AAAA,UACtE,aAAa;AAAA,QACd,CAAC;AAAA,MACF;AAAA,IACD,SAAS,OAAO;AACf,YAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,qCAAqC;AAAA,QACjF,aAAa;AAAA,MACd,CAAC;AAAA,IACF;AAEA,QAAI,cAAc,UAAU;AAC3B,YAAM,UAAU;AAAA,QACf,KAAK,iBAAiB,QAAQ,QAAW,EAAE,cAAc,KAAK,CAAC,KAAK;AAAA,MACrE;AAEA,MAAC,eAAe,KAAqB,KAAK;AAC1C,MAAC,eAAe,KAAqB,YAAY,IAAI;AAAA,IACtD;AAAA,EACD;AAEA,iBAAe,UAAU;AAAA,IACxB,GAAG,eAAe;AAAA,IAClB,CAAC,iCAAgB,4BAA4B,GAAG,KAAK,iBAAiB;AAAA,EACvE;AAEA,SAAO;AACR;AAEA,eAAsB,yBAErB,gBAC+B;AAC/B,QAAM,sBAAsB,KAAK,iBAAiB,kBAAkB;AACpE,QAAM,mBAAmB,iBAAiB,qBAAqB,eAAe;AAC9E,MACC,OAAO,KAAK,gBAAgB,EAAE,WAAW,KACzC,OAAO,OAAO,gBAAgB,EAAE,MAAM,CAAC,QAAQ,QAAQ,UAAa,QAAQ,QAAQ,QAAQ,EAAE,GAC7F;AACD,UAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,2BAA2B;AAAA,MACvE,aAAa;AAAA,IACd,CAAC;AAAA,EACF;AACA,iBAAe,OAAO;AAAA,IACrB,GAAI,eAAe;AAAA,IACnB,GAAG;AAAA,EACJ;AACA,SAAO;AACR;AAEO,MAAM,yBAAyB,EAAE,WAAW,CAAC,EAAE,EAAE;AAEjD,MAAM,oBAAoB,EAAE,MAAM,CAAC,EAAE,EAAE;", "names": []}