{"version": 3, "sources": ["../../../../../nodes/TheHiveProject/actions/page/update.operation.ts"], "sourcesContent": ["import type {\n\tIDataObject,\n\tIExecuteFunctions,\n\tINodeExecutionData,\n\tINodeProperties,\n} from 'n8n-workflow';\n\nimport { updateDisplayOptions, wrapData } from '@utils/utilities';\n\nimport { caseRLC, pageRLC } from '../../descriptions';\nimport { theHiveApiRequest } from '../../transport';\n\nconst properties: INodeProperties[] = [\n\t{\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-miscased\n\t\tdisplayName: 'Update in',\n\t\tname: 'location',\n\t\ttype: 'options',\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Case',\n\t\t\t\tvalue: 'case',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Knowledge Base',\n\t\t\t\tvalue: 'knowledgeBase',\n\t\t\t},\n\t\t],\n\t\tdefault: 'case',\n\t},\n\t{\n\t\t...caseRLC,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tlocation: ['case'],\n\t\t\t},\n\t\t},\n\t},\n\tpageRLC,\n\t{\n\t\tdisplayName: 'Content',\n\t\tname: 'content',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\ttypeOptions: {\n\t\t\trows: 2,\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Options',\n\t\tname: 'options',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add option',\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Category',\n\t\t\t\tname: 'category',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Title',\n\t\t\t\tname: 'title',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Order',\n\t\t\t\tname: 'order',\n\t\t\t\ttype: 'number',\n\t\t\t\tdefault: 0,\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tminValue: 0,\n\t\t\t\t},\n\t\t\t},\n\t\t],\n\t},\n];\n\nconst displayOptions = {\n\tshow: {\n\t\tresource: ['page'],\n\t\toperation: ['update'],\n\t},\n};\n\nexport const description = updateDisplayOptions(displayOptions, properties);\n\nexport async function execute(this: IExecuteFunctions, i: number): Promise<INodeExecutionData[]> {\n\tlet responseData: IDataObject | IDataObject[] = [];\n\n\tconst location = this.getNodeParameter('location', i) as string;\n\tconst pageId = this.getNodeParameter('pageId', i, '', { extractValue: true }) as string;\n\tconst content = this.getNodeParameter('content', i, '') as string;\n\tconst options = this.getNodeParameter('options', i, {});\n\n\tlet endpoint;\n\n\tif (location === 'case') {\n\t\tconst caseId = this.getNodeParameter('caseId', i, '', { extractValue: true }) as string;\n\t\tendpoint = `/v1/case/${caseId}/page/${pageId}`;\n\t} else {\n\t\tendpoint = `/v1/page/${pageId}`;\n\t}\n\n\tconst body: IDataObject = options;\n\n\tif (content) {\n\t\tbody.content = content;\n\t}\n\n\tresponseData = await theHiveApiRequest.call(this, 'PATCH', endpoint, body);\n\n\tconst executionData = this.helpers.constructExecutionMetaData(wrapData(responseData), {\n\t\titemData: { item: i },\n\t});\n\n\treturn executionData;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,uBAA+C;AAE/C,0BAAiC;AACjC,uBAAkC;AAElC,MAAM,aAAgC;AAAA,EACrC;AAAA;AAAA,IAEC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,GAAG;AAAA,IACH,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,MACZ,MAAM;AAAA,IACP;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AAEA,MAAM,iBAAiB;AAAA,EACtB,MAAM;AAAA,IACL,UAAU,CAAC,MAAM;AAAA,IACjB,WAAW,CAAC,QAAQ;AAAA,EACrB;AACD;AAEO,MAAM,kBAAc,uCAAqB,gBAAgB,UAAU;AAE1E,eAAsB,QAAiC,GAA0C;AAChG,MAAI,eAA4C,CAAC;AAEjD,QAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,QAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,IAAI,EAAE,cAAc,KAAK,CAAC;AAC5E,QAAM,UAAU,KAAK,iBAAiB,WAAW,GAAG,EAAE;AACtD,QAAM,UAAU,KAAK,iBAAiB,WAAW,GAAG,CAAC,CAAC;AAEtD,MAAI;AAEJ,MAAI,aAAa,QAAQ;AACxB,UAAM,SAAS,KAAK,iBAAiB,UAAU,GAAG,IAAI,EAAE,cAAc,KAAK,CAAC;AAC5E,eAAW,YAAY,MAAM,SAAS,MAAM;AAAA,EAC7C,OAAO;AACN,eAAW,YAAY,MAAM;AAAA,EAC9B;AAEA,QAAM,OAAoB;AAE1B,MAAI,SAAS;AACZ,SAAK,UAAU;AAAA,EAChB;AAEA,iBAAe,MAAM,mCAAkB,KAAK,MAAM,SAAS,UAAU,IAAI;AAEzE,QAAM,gBAAgB,KAAK,QAAQ,+BAA2B,2BAAS,YAAY,GAAG;AAAA,IACrF,UAAU,EAAE,MAAM,EAAE;AAAA,EACrB,CAAC;AAED,SAAO;AACR;", "names": []}