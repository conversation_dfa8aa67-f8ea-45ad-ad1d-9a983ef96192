{"version": 3, "sources": ["../../../../nodes/Splunk/v1/types.ts"], "sourcesContent": ["export type SplunkCredentials = {\n\tauthToken: string;\n\tbaseUrl: string;\n\tallowUnauthorizedCerts: boolean;\n};\n\nexport type SplunkFeedResponse = {\n\tfeed: {\n\t\tentry: { title: string };\n\t};\n};\n\nexport type SplunkSearchResponse = {\n\tentry: { title: string };\n};\n\nexport type SplunkResultResponse = {\n\tresults: { result: Array<{ field: string }> } | { result: { field: string } };\n};\n\nexport type SplunkError = {\n\tresponse?: {\n\t\tmessages?: {\n\t\t\tmsg: {\n\t\t\t\t$: { type: string };\n\t\t\t\t_: string;\n\t\t\t};\n\t\t};\n\t};\n};\n\nexport const SPLUNK = {\n\tDICT: 's:dict',\n\tLIST: 's:list',\n\tITEM: 's:item',\n\tKEY: 's:key',\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AA+BO,MAAM,SAAS;AAAA,EACrB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK;AACN;", "names": []}