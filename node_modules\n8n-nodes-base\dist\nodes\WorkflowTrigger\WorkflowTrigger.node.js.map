{"version": 3, "sources": ["../../../nodes/WorkflowTrigger/WorkflowTrigger.node.ts"], "sourcesContent": ["import type {\n\tITriggerFunctions,\n\tINodeType,\n\tINodeTypeDescription,\n\tITriggerResponse,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes } from 'n8n-workflow';\n\ntype eventType = 'Workflow activated' | 'Workflow updated' | undefined;\ntype activationType = 'activate' | 'update';\n\nexport class WorkflowTrigger implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Workflow Trigger',\n\t\thidden: true,\n\t\tname: 'workflowTrigger',\n\t\ticon: 'fa:network-wired',\n\t\ticonColor: 'orange-red',\n\t\tgroup: ['trigger'],\n\t\tversion: 1,\n\t\tdescription: 'Triggers based on various lifecycle events, like when a workflow is activated',\n\t\teventTriggerDescription: '',\n\t\tmockManualExecution: true,\n\t\tactivationMessage: 'Your workflow will now trigger executions on the event you have defined.',\n\t\tdefaults: {\n\t\t\tname: 'Workflow Trigger',\n\t\t\tcolor: '#ff6d5a',\n\t\t},\n\t\tinputs: [],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName:\n\t\t\t\t\t\"This node is deprecated and would not be updated in the future. Please use 'n8n Trigger' node instead.\",\n\t\t\t\tname: 'oldVersionNotice',\n\t\t\t\ttype: 'notice',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Events',\n\t\t\t\tname: 'events',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\trequired: true,\n\t\t\t\tdefault: [],\n\t\t\t\tdescription: `Specifies under which conditions an execution should happen:\n\t\t\t\t\t<ul>\n\t\t\t\t\t\t<li><b>Active Workflow Updated</b>: Triggers when this workflow is updated</li>\n\t\t\t\t\t\t<li><b>Workflow Activated</b>: Triggers when this workflow is activated</li>\n\t\t\t\t\t</ul>`,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Active Workflow Updated',\n\t\t\t\t\t\tvalue: 'update',\n\t\t\t\t\t\tdescription: 'Triggers when this workflow is updated',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Workflow Activated',\n\t\t\t\t\t\tvalue: 'activate',\n\t\t\t\t\t\tdescription: 'Triggers when this workflow is activated',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t};\n\n\tasync trigger(this: ITriggerFunctions): Promise<ITriggerResponse> {\n\t\tconst events = this.getNodeParameter('events', []) as activationType[];\n\n\t\tconst activationMode = this.getActivationMode() as activationType;\n\n\t\tif (events.includes(activationMode)) {\n\t\t\tlet event: eventType;\n\t\t\tif (activationMode === 'activate') {\n\t\t\t\tevent = 'Workflow activated';\n\t\t\t}\n\t\t\tif (activationMode === 'update') {\n\t\t\t\tevent = 'Workflow updated';\n\t\t\t}\n\t\t\tthis.emit([\n\t\t\t\tthis.helpers.returnJsonArray([\n\t\t\t\t\t{ event, timestamp: new Date().toISOString(), workflow_id: this.getWorkflow().id },\n\t\t\t\t]),\n\t\t\t]);\n\t\t}\n\n\t\tconst manualTriggerFunction = async () => {\n\t\t\tthis.emit([\n\t\t\t\tthis.helpers.returnJsonArray([\n\t\t\t\t\t{\n\t\t\t\t\t\tevent: 'Manual execution',\n\t\t\t\t\t\ttimestamp: new Date().toISOString(),\n\t\t\t\t\t\tworkflow_id: this.getWorkflow().id,\n\t\t\t\t\t},\n\t\t\t\t]),\n\t\t\t]);\n\t\t};\n\n\t\treturn {\n\t\t\tmanualTriggerFunction,\n\t\t};\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,0BAAoC;AAK7B,MAAM,gBAAqC;AAAA,EAA3C;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,MACX,OAAO,CAAC,SAAS;AAAA,MACjB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,yBAAyB;AAAA,MACzB,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,UAAU;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,YAAY;AAAA,QACX;AAAA,UACC,aACC;AAAA,UACD,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,CAAC;AAAA,UACV,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,UAKb,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAA4D;AACjE,UAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC,CAAC;AAEjD,UAAM,iBAAiB,KAAK,kBAAkB;AAE9C,QAAI,OAAO,SAAS,cAAc,GAAG;AACpC,UAAI;AACJ,UAAI,mBAAmB,YAAY;AAClC,gBAAQ;AAAA,MACT;AACA,UAAI,mBAAmB,UAAU;AAChC,gBAAQ;AAAA,MACT;AACA,WAAK,KAAK;AAAA,QACT,KAAK,QAAQ,gBAAgB;AAAA,UAC5B,EAAE,OAAO,YAAW,oBAAI,KAAK,GAAE,YAAY,GAAG,aAAa,KAAK,YAAY,EAAE,GAAG;AAAA,QAClF,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAEA,UAAM,wBAAwB,YAAY;AACzC,WAAK,KAAK;AAAA,QACT,KAAK,QAAQ,gBAAgB;AAAA,UAC5B;AAAA,YACC,OAAO;AAAA,YACP,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,YAClC,aAAa,KAAK,YAAY,EAAE;AAAA,UACjC;AAAA,QACD,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAEA,WAAO;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;", "names": []}