import { DocumentType as __DocumentType } from "@smithy/types";
import {
  AccessDeniedException,
  AgentActionGroup,
  AgentCollaboration,
  BadGatewayException,
  BedrockModelConfigurations,
  CitationEvent,
  CollaboratorConfiguration,
  ConflictException,
  ConversationHistory,
  CustomOrchestration,
  DependencyFailedException,
  ExternalSourcesRetrieveAndGenerateConfiguration,
  FilterAttribute,
  GenerationConfiguration,
  GuadrailAction,
  GuardrailConfiguration,
  GuardrailConfigurationWithArn,
  GuardrailEvent,
  ImplicitFilterConfiguration,
  InlineBedrockModelConfigurations,
  InlineSessionState,
  InputFile,
  InternalServerException,
  InvocationResultMember,
  OrchestrationConfiguration,
  OrchestrationType,
  PromptOverrideConfiguration,
  ResourceNotFoundException,
  RetrievalResultContent,
  RetrievalResultLocation,
  RetrieveAndGenerateInput,
  RetrieveAndGenerateOutputEvent,
  RetrieveAndGenerateSessionConfiguration,
  RetrieveAndGenerateType,
  SearchType,
  ServiceQuotaExceededException,
  StreamingConfigurations,
  ThrottlingException,
  ValidationException,
  VectorSearchRerankingConfiguration,
} from "./models_0";
export type RetrieveAndGenerateStreamResponseOutput =
  | RetrieveAndGenerateStreamResponseOutput.AccessDeniedExceptionMember
  | RetrieveAndGenerateStreamResponseOutput.BadGatewayExceptionMember
  | RetrieveAndGenerateStreamResponseOutput.CitationMember
  | RetrieveAndGenerateStreamResponseOutput.ConflictExceptionMember
  | RetrieveAndGenerateStreamResponseOutput.DependencyFailedExceptionMember
  | RetrieveAndGenerateStreamResponseOutput.GuardrailMember
  | RetrieveAndGenerateStreamResponseOutput.InternalServerExceptionMember
  | RetrieveAndGenerateStreamResponseOutput.OutputMember
  | RetrieveAndGenerateStreamResponseOutput.ResourceNotFoundExceptionMember
  | RetrieveAndGenerateStreamResponseOutput.ServiceQuotaExceededExceptionMember
  | RetrieveAndGenerateStreamResponseOutput.ThrottlingExceptionMember
  | RetrieveAndGenerateStreamResponseOutput.ValidationExceptionMember
  | RetrieveAndGenerateStreamResponseOutput.$UnknownMember;
export declare namespace RetrieveAndGenerateStreamResponseOutput {
  interface OutputMember {
    output: RetrieveAndGenerateOutputEvent;
    citation?: never;
    guardrail?: never;
    internalServerException?: never;
    validationException?: never;
    resourceNotFoundException?: never;
    serviceQuotaExceededException?: never;
    throttlingException?: never;
    accessDeniedException?: never;
    conflictException?: never;
    dependencyFailedException?: never;
    badGatewayException?: never;
    $unknown?: never;
  }
  interface CitationMember {
    output?: never;
    citation: CitationEvent;
    guardrail?: never;
    internalServerException?: never;
    validationException?: never;
    resourceNotFoundException?: never;
    serviceQuotaExceededException?: never;
    throttlingException?: never;
    accessDeniedException?: never;
    conflictException?: never;
    dependencyFailedException?: never;
    badGatewayException?: never;
    $unknown?: never;
  }
  interface GuardrailMember {
    output?: never;
    citation?: never;
    guardrail: GuardrailEvent;
    internalServerException?: never;
    validationException?: never;
    resourceNotFoundException?: never;
    serviceQuotaExceededException?: never;
    throttlingException?: never;
    accessDeniedException?: never;
    conflictException?: never;
    dependencyFailedException?: never;
    badGatewayException?: never;
    $unknown?: never;
  }
  interface InternalServerExceptionMember {
    output?: never;
    citation?: never;
    guardrail?: never;
    internalServerException: InternalServerException;
    validationException?: never;
    resourceNotFoundException?: never;
    serviceQuotaExceededException?: never;
    throttlingException?: never;
    accessDeniedException?: never;
    conflictException?: never;
    dependencyFailedException?: never;
    badGatewayException?: never;
    $unknown?: never;
  }
  interface ValidationExceptionMember {
    output?: never;
    citation?: never;
    guardrail?: never;
    internalServerException?: never;
    validationException: ValidationException;
    resourceNotFoundException?: never;
    serviceQuotaExceededException?: never;
    throttlingException?: never;
    accessDeniedException?: never;
    conflictException?: never;
    dependencyFailedException?: never;
    badGatewayException?: never;
    $unknown?: never;
  }
  interface ResourceNotFoundExceptionMember {
    output?: never;
    citation?: never;
    guardrail?: never;
    internalServerException?: never;
    validationException?: never;
    resourceNotFoundException: ResourceNotFoundException;
    serviceQuotaExceededException?: never;
    throttlingException?: never;
    accessDeniedException?: never;
    conflictException?: never;
    dependencyFailedException?: never;
    badGatewayException?: never;
    $unknown?: never;
  }
  interface ServiceQuotaExceededExceptionMember {
    output?: never;
    citation?: never;
    guardrail?: never;
    internalServerException?: never;
    validationException?: never;
    resourceNotFoundException?: never;
    serviceQuotaExceededException: ServiceQuotaExceededException;
    throttlingException?: never;
    accessDeniedException?: never;
    conflictException?: never;
    dependencyFailedException?: never;
    badGatewayException?: never;
    $unknown?: never;
  }
  interface ThrottlingExceptionMember {
    output?: never;
    citation?: never;
    guardrail?: never;
    internalServerException?: never;
    validationException?: never;
    resourceNotFoundException?: never;
    serviceQuotaExceededException?: never;
    throttlingException: ThrottlingException;
    accessDeniedException?: never;
    conflictException?: never;
    dependencyFailedException?: never;
    badGatewayException?: never;
    $unknown?: never;
  }
  interface AccessDeniedExceptionMember {
    output?: never;
    citation?: never;
    guardrail?: never;
    internalServerException?: never;
    validationException?: never;
    resourceNotFoundException?: never;
    serviceQuotaExceededException?: never;
    throttlingException?: never;
    accessDeniedException: AccessDeniedException;
    conflictException?: never;
    dependencyFailedException?: never;
    badGatewayException?: never;
    $unknown?: never;
  }
  interface ConflictExceptionMember {
    output?: never;
    citation?: never;
    guardrail?: never;
    internalServerException?: never;
    validationException?: never;
    resourceNotFoundException?: never;
    serviceQuotaExceededException?: never;
    throttlingException?: never;
    accessDeniedException?: never;
    conflictException: ConflictException;
    dependencyFailedException?: never;
    badGatewayException?: never;
    $unknown?: never;
  }
  interface DependencyFailedExceptionMember {
    output?: never;
    citation?: never;
    guardrail?: never;
    internalServerException?: never;
    validationException?: never;
    resourceNotFoundException?: never;
    serviceQuotaExceededException?: never;
    throttlingException?: never;
    accessDeniedException?: never;
    conflictException?: never;
    dependencyFailedException: DependencyFailedException;
    badGatewayException?: never;
    $unknown?: never;
  }
  interface BadGatewayExceptionMember {
    output?: never;
    citation?: never;
    guardrail?: never;
    internalServerException?: never;
    validationException?: never;
    resourceNotFoundException?: never;
    serviceQuotaExceededException?: never;
    throttlingException?: never;
    accessDeniedException?: never;
    conflictException?: never;
    dependencyFailedException?: never;
    badGatewayException: BadGatewayException;
    $unknown?: never;
  }
  interface $UnknownMember {
    output?: never;
    citation?: never;
    guardrail?: never;
    internalServerException?: never;
    validationException?: never;
    resourceNotFoundException?: never;
    serviceQuotaExceededException?: never;
    throttlingException?: never;
    accessDeniedException?: never;
    conflictException?: never;
    dependencyFailedException?: never;
    badGatewayException?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    output: (value: RetrieveAndGenerateOutputEvent) => T;
    citation: (value: CitationEvent) => T;
    guardrail: (value: GuardrailEvent) => T;
    internalServerException: (value: InternalServerException) => T;
    validationException: (value: ValidationException) => T;
    resourceNotFoundException: (value: ResourceNotFoundException) => T;
    serviceQuotaExceededException: (value: ServiceQuotaExceededException) => T;
    throttlingException: (value: ThrottlingException) => T;
    accessDeniedException: (value: AccessDeniedException) => T;
    conflictException: (value: ConflictException) => T;
    dependencyFailedException: (value: DependencyFailedException) => T;
    badGatewayException: (value: BadGatewayException) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: RetrieveAndGenerateStreamResponseOutput,
    visitor: Visitor<T>
  ) => T;
}
export interface RetrieveAndGenerateStreamResponse {
  stream: AsyncIterable<RetrieveAndGenerateStreamResponseOutput> | undefined;
  sessionId: string | undefined;
}
export interface KnowledgeBaseQuery {
  text: string | undefined;
}
export interface KnowledgeBaseRetrievalResult {
  content: RetrievalResultContent | undefined;
  location?: RetrievalResultLocation | undefined;
  score?: number | undefined;
  metadata?: Record<string, __DocumentType> | undefined;
}
export interface RetrieveResponse {
  retrievalResults: KnowledgeBaseRetrievalResult[] | undefined;
  guardrailAction?: GuadrailAction | undefined;
  nextToken?: string | undefined;
}
export interface CreateSessionRequest {
  sessionMetadata?: Record<string, string> | undefined;
  encryptionKeyArn?: string | undefined;
  tags?: Record<string, string> | undefined;
}
export declare const SessionStatus: {
  readonly ACTIVE: "ACTIVE";
  readonly ENDED: "ENDED";
  readonly EXPIRED: "EXPIRED";
};
export type SessionStatus = (typeof SessionStatus)[keyof typeof SessionStatus];
export interface CreateSessionResponse {
  sessionId: string | undefined;
  sessionArn: string | undefined;
  sessionStatus: SessionStatus | undefined;
  createdAt: Date | undefined;
}
export interface DeleteSessionRequest {
  sessionIdentifier: string | undefined;
}
export interface DeleteSessionResponse {}
export interface EndSessionRequest {
  sessionIdentifier: string | undefined;
}
export interface EndSessionResponse {
  sessionId: string | undefined;
  sessionArn: string | undefined;
  sessionStatus: SessionStatus | undefined;
}
export interface GetSessionRequest {
  sessionIdentifier: string | undefined;
}
export interface GetSessionResponse {
  sessionId: string | undefined;
  sessionArn: string | undefined;
  sessionStatus: SessionStatus | undefined;
  createdAt: Date | undefined;
  lastUpdatedAt: Date | undefined;
  sessionMetadata?: Record<string, string> | undefined;
  encryptionKeyArn?: string | undefined;
}
export interface CreateInvocationRequest {
  invocationId?: string | undefined;
  description?: string | undefined;
  sessionIdentifier: string | undefined;
}
export interface CreateInvocationResponse {
  sessionId: string | undefined;
  invocationId: string | undefined;
  createdAt: Date | undefined;
}
export interface ListInvocationsRequest {
  nextToken?: string | undefined;
  maxResults?: number | undefined;
  sessionIdentifier: string | undefined;
}
export interface InvocationSummary {
  sessionId: string | undefined;
  invocationId: string | undefined;
  createdAt: Date | undefined;
}
export interface ListInvocationsResponse {
  invocationSummaries: InvocationSummary[] | undefined;
  nextToken?: string | undefined;
}
export interface GetInvocationStepRequest {
  invocationIdentifier: string | undefined;
  invocationStepId: string | undefined;
  sessionIdentifier: string | undefined;
}
export declare const ImageFormat: {
  readonly GIF: "gif";
  readonly JPEG: "jpeg";
  readonly PNG: "png";
  readonly WEBP: "webp";
};
export type ImageFormat = (typeof ImageFormat)[keyof typeof ImageFormat];
export interface S3Location {
  uri: string | undefined;
}
export type ImageSource =
  | ImageSource.BytesMember
  | ImageSource.S3LocationMember
  | ImageSource.$UnknownMember;
export declare namespace ImageSource {
  interface BytesMember {
    bytes: Uint8Array;
    s3Location?: never;
    $unknown?: never;
  }
  interface S3LocationMember {
    bytes?: never;
    s3Location: S3Location;
    $unknown?: never;
  }
  interface $UnknownMember {
    bytes?: never;
    s3Location?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    bytes: (value: Uint8Array) => T;
    s3Location: (value: S3Location) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: ImageSource, visitor: Visitor<T>) => T;
}
export interface ImageBlock {
  format: ImageFormat | undefined;
  source: ImageSource | undefined;
}
export type BedrockSessionContentBlock =
  | BedrockSessionContentBlock.ImageMember
  | BedrockSessionContentBlock.TextMember
  | BedrockSessionContentBlock.$UnknownMember;
export declare namespace BedrockSessionContentBlock {
  interface TextMember {
    text: string;
    image?: never;
    $unknown?: never;
  }
  interface ImageMember {
    text?: never;
    image: ImageBlock;
    $unknown?: never;
  }
  interface $UnknownMember {
    text?: never;
    image?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    text: (value: string) => T;
    image: (value: ImageBlock) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: BedrockSessionContentBlock, visitor: Visitor<T>) => T;
}
export type InvocationStepPayload =
  | InvocationStepPayload.ContentBlocksMember
  | InvocationStepPayload.$UnknownMember;
export declare namespace InvocationStepPayload {
  interface ContentBlocksMember {
    contentBlocks: BedrockSessionContentBlock[];
    $unknown?: never;
  }
  interface $UnknownMember {
    contentBlocks?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    contentBlocks: (value: BedrockSessionContentBlock[]) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: InvocationStepPayload, visitor: Visitor<T>) => T;
}
export interface InvocationStep {
  sessionId: string | undefined;
  invocationId: string | undefined;
  invocationStepId: string | undefined;
  invocationStepTime: Date | undefined;
  payload: InvocationStepPayload | undefined;
}
export interface GetInvocationStepResponse {
  invocationStep: InvocationStep | undefined;
}
export interface ListInvocationStepsRequest {
  invocationIdentifier?: string | undefined;
  nextToken?: string | undefined;
  maxResults?: number | undefined;
  sessionIdentifier: string | undefined;
}
export interface InvocationStepSummary {
  sessionId: string | undefined;
  invocationId: string | undefined;
  invocationStepId: string | undefined;
  invocationStepTime: Date | undefined;
}
export interface ListInvocationStepsResponse {
  invocationStepSummaries: InvocationStepSummary[] | undefined;
  nextToken?: string | undefined;
}
export interface PutInvocationStepRequest {
  sessionIdentifier: string | undefined;
  invocationIdentifier: string | undefined;
  invocationStepTime: Date | undefined;
  payload: InvocationStepPayload | undefined;
  invocationStepId?: string | undefined;
}
export interface PutInvocationStepResponse {
  invocationStepId: string | undefined;
}
export interface ListSessionsRequest {
  maxResults?: number | undefined;
  nextToken?: string | undefined;
}
export interface SessionSummary {
  sessionId: string | undefined;
  sessionArn: string | undefined;
  sessionStatus: SessionStatus | undefined;
  createdAt: Date | undefined;
  lastUpdatedAt: Date | undefined;
}
export interface ListSessionsResponse {
  sessionSummaries: SessionSummary[] | undefined;
  nextToken?: string | undefined;
}
export interface UpdateSessionRequest {
  sessionMetadata?: Record<string, string> | undefined;
  sessionIdentifier: string | undefined;
}
export interface UpdateSessionResponse {
  sessionId: string | undefined;
  sessionArn: string | undefined;
  sessionStatus: SessionStatus | undefined;
  createdAt: Date | undefined;
  lastUpdatedAt: Date | undefined;
}
export interface ListTagsForResourceRequest {
  resourceArn: string | undefined;
}
export interface ListTagsForResourceResponse {
  tags?: Record<string, string> | undefined;
}
export interface TagResourceRequest {
  resourceArn: string | undefined;
  tags: Record<string, string> | undefined;
}
export interface TagResourceResponse {}
export interface UntagResourceRequest {
  resourceArn: string | undefined;
  tagKeys: string[] | undefined;
}
export interface UntagResourceResponse {}
export type RetrievalFilter =
  | RetrievalFilter.AndAllMember
  | RetrievalFilter.EqualsMember
  | RetrievalFilter.GreaterThanMember
  | RetrievalFilter.GreaterThanOrEqualsMember
  | RetrievalFilter.InMember
  | RetrievalFilter.LessThanMember
  | RetrievalFilter.LessThanOrEqualsMember
  | RetrievalFilter.ListContainsMember
  | RetrievalFilter.NotEqualsMember
  | RetrievalFilter.NotInMember
  | RetrievalFilter.OrAllMember
  | RetrievalFilter.StartsWithMember
  | RetrievalFilter.StringContainsMember
  | RetrievalFilter.$UnknownMember;
export declare namespace RetrievalFilter {
  interface EqualsMember {
    equals: FilterAttribute;
    notEquals?: never;
    greaterThan?: never;
    greaterThanOrEquals?: never;
    lessThan?: never;
    lessThanOrEquals?: never;
    in?: never;
    notIn?: never;
    startsWith?: never;
    listContains?: never;
    stringContains?: never;
    andAll?: never;
    orAll?: never;
    $unknown?: never;
  }
  interface NotEqualsMember {
    equals?: never;
    notEquals: FilterAttribute;
    greaterThan?: never;
    greaterThanOrEquals?: never;
    lessThan?: never;
    lessThanOrEquals?: never;
    in?: never;
    notIn?: never;
    startsWith?: never;
    listContains?: never;
    stringContains?: never;
    andAll?: never;
    orAll?: never;
    $unknown?: never;
  }
  interface GreaterThanMember {
    equals?: never;
    notEquals?: never;
    greaterThan: FilterAttribute;
    greaterThanOrEquals?: never;
    lessThan?: never;
    lessThanOrEquals?: never;
    in?: never;
    notIn?: never;
    startsWith?: never;
    listContains?: never;
    stringContains?: never;
    andAll?: never;
    orAll?: never;
    $unknown?: never;
  }
  interface GreaterThanOrEqualsMember {
    equals?: never;
    notEquals?: never;
    greaterThan?: never;
    greaterThanOrEquals: FilterAttribute;
    lessThan?: never;
    lessThanOrEquals?: never;
    in?: never;
    notIn?: never;
    startsWith?: never;
    listContains?: never;
    stringContains?: never;
    andAll?: never;
    orAll?: never;
    $unknown?: never;
  }
  interface LessThanMember {
    equals?: never;
    notEquals?: never;
    greaterThan?: never;
    greaterThanOrEquals?: never;
    lessThan: FilterAttribute;
    lessThanOrEquals?: never;
    in?: never;
    notIn?: never;
    startsWith?: never;
    listContains?: never;
    stringContains?: never;
    andAll?: never;
    orAll?: never;
    $unknown?: never;
  }
  interface LessThanOrEqualsMember {
    equals?: never;
    notEquals?: never;
    greaterThan?: never;
    greaterThanOrEquals?: never;
    lessThan?: never;
    lessThanOrEquals: FilterAttribute;
    in?: never;
    notIn?: never;
    startsWith?: never;
    listContains?: never;
    stringContains?: never;
    andAll?: never;
    orAll?: never;
    $unknown?: never;
  }
  interface InMember {
    equals?: never;
    notEquals?: never;
    greaterThan?: never;
    greaterThanOrEquals?: never;
    lessThan?: never;
    lessThanOrEquals?: never;
    in: FilterAttribute;
    notIn?: never;
    startsWith?: never;
    listContains?: never;
    stringContains?: never;
    andAll?: never;
    orAll?: never;
    $unknown?: never;
  }
  interface NotInMember {
    equals?: never;
    notEquals?: never;
    greaterThan?: never;
    greaterThanOrEquals?: never;
    lessThan?: never;
    lessThanOrEquals?: never;
    in?: never;
    notIn: FilterAttribute;
    startsWith?: never;
    listContains?: never;
    stringContains?: never;
    andAll?: never;
    orAll?: never;
    $unknown?: never;
  }
  interface StartsWithMember {
    equals?: never;
    notEquals?: never;
    greaterThan?: never;
    greaterThanOrEquals?: never;
    lessThan?: never;
    lessThanOrEquals?: never;
    in?: never;
    notIn?: never;
    startsWith: FilterAttribute;
    listContains?: never;
    stringContains?: never;
    andAll?: never;
    orAll?: never;
    $unknown?: never;
  }
  interface ListContainsMember {
    equals?: never;
    notEquals?: never;
    greaterThan?: never;
    greaterThanOrEquals?: never;
    lessThan?: never;
    lessThanOrEquals?: never;
    in?: never;
    notIn?: never;
    startsWith?: never;
    listContains: FilterAttribute;
    stringContains?: never;
    andAll?: never;
    orAll?: never;
    $unknown?: never;
  }
  interface StringContainsMember {
    equals?: never;
    notEquals?: never;
    greaterThan?: never;
    greaterThanOrEquals?: never;
    lessThan?: never;
    lessThanOrEquals?: never;
    in?: never;
    notIn?: never;
    startsWith?: never;
    listContains?: never;
    stringContains: FilterAttribute;
    andAll?: never;
    orAll?: never;
    $unknown?: never;
  }
  interface AndAllMember {
    equals?: never;
    notEquals?: never;
    greaterThan?: never;
    greaterThanOrEquals?: never;
    lessThan?: never;
    lessThanOrEquals?: never;
    in?: never;
    notIn?: never;
    startsWith?: never;
    listContains?: never;
    stringContains?: never;
    andAll: RetrievalFilter[];
    orAll?: never;
    $unknown?: never;
  }
  interface OrAllMember {
    equals?: never;
    notEquals?: never;
    greaterThan?: never;
    greaterThanOrEquals?: never;
    lessThan?: never;
    lessThanOrEquals?: never;
    in?: never;
    notIn?: never;
    startsWith?: never;
    listContains?: never;
    stringContains?: never;
    andAll?: never;
    orAll: RetrievalFilter[];
    $unknown?: never;
  }
  interface $UnknownMember {
    equals?: never;
    notEquals?: never;
    greaterThan?: never;
    greaterThanOrEquals?: never;
    lessThan?: never;
    lessThanOrEquals?: never;
    in?: never;
    notIn?: never;
    startsWith?: never;
    listContains?: never;
    stringContains?: never;
    andAll?: never;
    orAll?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    equals: (value: FilterAttribute) => T;
    notEquals: (value: FilterAttribute) => T;
    greaterThan: (value: FilterAttribute) => T;
    greaterThanOrEquals: (value: FilterAttribute) => T;
    lessThan: (value: FilterAttribute) => T;
    lessThanOrEquals: (value: FilterAttribute) => T;
    in: (value: FilterAttribute) => T;
    notIn: (value: FilterAttribute) => T;
    startsWith: (value: FilterAttribute) => T;
    listContains: (value: FilterAttribute) => T;
    stringContains: (value: FilterAttribute) => T;
    andAll: (value: RetrievalFilter[]) => T;
    orAll: (value: RetrievalFilter[]) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: RetrievalFilter, visitor: Visitor<T>) => T;
}
export interface KnowledgeBaseVectorSearchConfiguration {
  numberOfResults?: number | undefined;
  overrideSearchType?: SearchType | undefined;
  filter?: RetrievalFilter | undefined;
  rerankingConfiguration?: VectorSearchRerankingConfiguration | undefined;
  implicitFilterConfiguration?: ImplicitFilterConfiguration | undefined;
}
export interface KnowledgeBaseRetrievalConfiguration {
  vectorSearchConfiguration: KnowledgeBaseVectorSearchConfiguration | undefined;
}
export interface KnowledgeBase {
  knowledgeBaseId: string | undefined;
  description: string | undefined;
  retrievalConfiguration?: KnowledgeBaseRetrievalConfiguration | undefined;
}
export interface KnowledgeBaseConfiguration {
  knowledgeBaseId: string | undefined;
  retrievalConfiguration: KnowledgeBaseRetrievalConfiguration | undefined;
}
export interface KnowledgeBaseRetrieveAndGenerateConfiguration {
  knowledgeBaseId: string | undefined;
  modelArn: string | undefined;
  retrievalConfiguration?: KnowledgeBaseRetrievalConfiguration | undefined;
  generationConfiguration?: GenerationConfiguration | undefined;
  orchestrationConfiguration?: OrchestrationConfiguration | undefined;
}
export interface RetrieveRequest {
  knowledgeBaseId: string | undefined;
  retrievalQuery: KnowledgeBaseQuery | undefined;
  retrievalConfiguration?: KnowledgeBaseRetrievalConfiguration | undefined;
  guardrailConfiguration?: GuardrailConfiguration | undefined;
  nextToken?: string | undefined;
}
export interface RetrieveAndGenerateConfiguration {
  type: RetrieveAndGenerateType | undefined;
  knowledgeBaseConfiguration?:
    | KnowledgeBaseRetrieveAndGenerateConfiguration
    | undefined;
  externalSourcesConfiguration?:
    | ExternalSourcesRetrieveAndGenerateConfiguration
    | undefined;
}
export interface Collaborator {
  customerEncryptionKeyArn?: string | undefined;
  foundationModel: string | undefined;
  instruction: string | undefined;
  idleSessionTTLInSeconds?: number | undefined;
  actionGroups?: AgentActionGroup[] | undefined;
  knowledgeBases?: KnowledgeBase[] | undefined;
  guardrailConfiguration?: GuardrailConfigurationWithArn | undefined;
  promptOverrideConfiguration?: PromptOverrideConfiguration | undefined;
  agentCollaboration?: AgentCollaboration | undefined;
  collaboratorConfigurations?: CollaboratorConfiguration[] | undefined;
  agentName?: string | undefined;
}
export interface RetrieveAndGenerateRequest {
  sessionId?: string | undefined;
  input: RetrieveAndGenerateInput | undefined;
  retrieveAndGenerateConfiguration?:
    | RetrieveAndGenerateConfiguration
    | undefined;
  sessionConfiguration?: RetrieveAndGenerateSessionConfiguration | undefined;
}
export interface RetrieveAndGenerateStreamRequest {
  sessionId?: string | undefined;
  input: RetrieveAndGenerateInput | undefined;
  retrieveAndGenerateConfiguration?:
    | RetrieveAndGenerateConfiguration
    | undefined;
  sessionConfiguration?: RetrieveAndGenerateSessionConfiguration | undefined;
}
export interface SessionState {
  sessionAttributes?: Record<string, string> | undefined;
  promptSessionAttributes?: Record<string, string> | undefined;
  returnControlInvocationResults?: InvocationResultMember[] | undefined;
  invocationId?: string | undefined;
  files?: InputFile[] | undefined;
  knowledgeBaseConfigurations?: KnowledgeBaseConfiguration[] | undefined;
  conversationHistory?: ConversationHistory | undefined;
}
export interface InvokeAgentRequest {
  sessionState?: SessionState | undefined;
  agentId: string | undefined;
  agentAliasId: string | undefined;
  sessionId: string | undefined;
  endSession?: boolean | undefined;
  enableTrace?: boolean | undefined;
  inputText?: string | undefined;
  memoryId?: string | undefined;
  bedrockModelConfigurations?: BedrockModelConfigurations | undefined;
  streamingConfigurations?: StreamingConfigurations | undefined;
  sourceArn?: string | undefined;
}
export interface InvokeInlineAgentRequest {
  customerEncryptionKeyArn?: string | undefined;
  foundationModel: string | undefined;
  instruction: string | undefined;
  idleSessionTTLInSeconds?: number | undefined;
  actionGroups?: AgentActionGroup[] | undefined;
  knowledgeBases?: KnowledgeBase[] | undefined;
  guardrailConfiguration?: GuardrailConfigurationWithArn | undefined;
  promptOverrideConfiguration?: PromptOverrideConfiguration | undefined;
  agentCollaboration?: AgentCollaboration | undefined;
  collaboratorConfigurations?: CollaboratorConfiguration[] | undefined;
  agentName?: string | undefined;
  sessionId: string | undefined;
  endSession?: boolean | undefined;
  enableTrace?: boolean | undefined;
  inputText?: string | undefined;
  streamingConfigurations?: StreamingConfigurations | undefined;
  inlineSessionState?: InlineSessionState | undefined;
  collaborators?: Collaborator[] | undefined;
  bedrockModelConfigurations?: InlineBedrockModelConfigurations | undefined;
  orchestrationType?: OrchestrationType | undefined;
  customOrchestration?: CustomOrchestration | undefined;
}
export declare const RetrieveAndGenerateStreamResponseOutputFilterSensitiveLog: (
  obj: RetrieveAndGenerateStreamResponseOutput
) => any;
export declare const RetrieveAndGenerateStreamResponseFilterSensitiveLog: (
  obj: RetrieveAndGenerateStreamResponse
) => any;
export declare const KnowledgeBaseQueryFilterSensitiveLog: (
  obj: KnowledgeBaseQuery
) => any;
export declare const KnowledgeBaseRetrievalResultFilterSensitiveLog: (
  obj: KnowledgeBaseRetrievalResult
) => any;
export declare const RetrieveResponseFilterSensitiveLog: (
  obj: RetrieveResponse
) => any;
export declare const BedrockSessionContentBlockFilterSensitiveLog: (
  obj: BedrockSessionContentBlock
) => any;
export declare const InvocationStepPayloadFilterSensitiveLog: (
  obj: InvocationStepPayload
) => any;
export declare const InvocationStepFilterSensitiveLog: (
  obj: InvocationStep
) => any;
export declare const GetInvocationStepResponseFilterSensitiveLog: (
  obj: GetInvocationStepResponse
) => any;
export declare const PutInvocationStepRequestFilterSensitiveLog: (
  obj: PutInvocationStepRequest
) => any;
export declare const RetrievalFilterFilterSensitiveLog: (
  obj: RetrievalFilter
) => any;
export declare const KnowledgeBaseVectorSearchConfigurationFilterSensitiveLog: (
  obj: KnowledgeBaseVectorSearchConfiguration
) => any;
export declare const KnowledgeBaseRetrievalConfigurationFilterSensitiveLog: (
  obj: KnowledgeBaseRetrievalConfiguration
) => any;
export declare const KnowledgeBaseFilterSensitiveLog: (
  obj: KnowledgeBase
) => any;
export declare const KnowledgeBaseConfigurationFilterSensitiveLog: (
  obj: KnowledgeBaseConfiguration
) => any;
export declare const KnowledgeBaseRetrieveAndGenerateConfigurationFilterSensitiveLog: (
  obj: KnowledgeBaseRetrieveAndGenerateConfiguration
) => any;
export declare const RetrieveRequestFilterSensitiveLog: (
  obj: RetrieveRequest
) => any;
export declare const RetrieveAndGenerateConfigurationFilterSensitiveLog: (
  obj: RetrieveAndGenerateConfiguration
) => any;
export declare const CollaboratorFilterSensitiveLog: (obj: Collaborator) => any;
export declare const RetrieveAndGenerateRequestFilterSensitiveLog: (
  obj: RetrieveAndGenerateRequest
) => any;
export declare const RetrieveAndGenerateStreamRequestFilterSensitiveLog: (
  obj: RetrieveAndGenerateStreamRequest
) => any;
export declare const SessionStateFilterSensitiveLog: (obj: SessionState) => any;
export declare const InvokeAgentRequestFilterSensitiveLog: (
  obj: InvokeAgentRequest
) => any;
export declare const InvokeInlineAgentRequestFilterSensitiveLog: (
  obj: InvokeInlineAgentRequest
) => any;
