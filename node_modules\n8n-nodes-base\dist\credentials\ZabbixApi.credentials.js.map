{"version": 3, "sources": ["../../credentials/ZabbixApi.credentials.ts"], "sourcesContent": ["import type {\n\tIAuthenticateGeneric,\n\tIcon,\n\tICredentialTestRequest,\n\tICredentialType,\n\tINodeProperties,\n} from 'n8n-workflow';\n\nexport class ZabbixApi implements ICredentialType {\n\tname = 'zabbixApi';\n\n\tdisplayName = 'Zabbix API';\n\n\tdocumentationUrl = 'zabbix';\n\n\ticon: Icon = 'file:icons/Zabbix.svg';\n\n\thttpRequestNode = {\n\t\tname: 'Zabbix',\n\t\tdocsUrl: 'https://www.zabbix.com/documentation/current/en/manual/api',\n\t\tapiBaseUrl: '',\n\t};\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'URL',\n\t\t\tname: 'url',\n\t\t\trequired: true,\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'API Token',\n\t\t\tname: 'apiToken',\n\t\t\trequired: true,\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\tdefault: '',\n\t\t},\n\t];\n\n\tauthenticate: IAuthenticateGeneric = {\n\t\ttype: 'generic',\n\t\tproperties: {\n\t\t\theaders: {\n\t\t\t\tAuthorization: '=Bearer {{$credentials.apiToken}}',\n\t\t\t\t'Content-Type': 'application/json-rpc',\n\t\t\t},\n\t\t},\n\t};\n\n\ttest: ICredentialTestRequest = {\n\t\trequest: {\n\t\t\tbaseURL: '={{$credentials.url}}'.replace(/\\/$/, ''),\n\t\t\turl: '/api_jsonrpc.php',\n\t\t\tmethod: 'POST',\n\t\t\tbody: {\n\t\t\t\tjsonrpc: '2.0',\n\t\t\t\tmethod: 'host.get',\n\t\t\t\tparams: {\n\t\t\t\t\toutput: ['hostid', 'host'],\n\t\t\t\t\tselectInterfaces: ['interfaceid', 'ip'],\n\t\t\t\t},\n\t\t\t\tid: 2,\n\t\t\t},\n\t\t},\n\t\trules: [\n\t\t\t{\n\t\t\t\ttype: 'responseSuccessBody',\n\t\t\t\tproperties: {\n\t\t\t\t\tkey: 'result',\n\t\t\t\t\tvalue: undefined,\n\t\t\t\t\tmessage: 'Invalid access token',\n\t\t\t\t},\n\t\t\t},\n\t\t],\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAQO,MAAM,UAAqC;AAAA,EAA3C;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,gBAAa;AAEb,2BAAkB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAEA,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,MACV;AAAA,IACD;AAEA,wBAAqC;AAAA,MACpC,MAAM;AAAA,MACN,YAAY;AAAA,QACX,SAAS;AAAA,UACR,eAAe;AAAA,UACf,gBAAgB;AAAA,QACjB;AAAA,MACD;AAAA,IACD;AAEA,gBAA+B;AAAA,MAC9B,SAAS;AAAA,QACR,SAAS,wBAAwB,QAAQ,OAAO,EAAE;AAAA,QAClD,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,UACL,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACP,QAAQ,CAAC,UAAU,MAAM;AAAA,YACzB,kBAAkB,CAAC,eAAe,IAAI;AAAA,UACvC;AAAA,UACA,IAAI;AAAA,QACL;AAAA,MACD;AAAA,MACA,OAAO;AAAA,QACN;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,YACX,KAAK;AAAA,YACL,OAAO;AAAA,YACP,SAAS;AAAA,UACV;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA;AACD;", "names": []}