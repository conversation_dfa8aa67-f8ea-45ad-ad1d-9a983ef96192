{"version": 3, "sources": ["../../../../../../nodes/SeaTable/v2/actions/asset/upload.operation.ts"], "sourcesContent": ["import {\n\ttype IDataObject,\n\ttype INodeExecutionData,\n\ttype INodeProperties,\n\ttype IExecuteFunctions,\n\tupdateDisplayOptions,\n} from 'n8n-workflow';\n\nimport { seaTableApiRequest } from '../../GenericFunctions';\nimport type { IUploadLink, IRowObject } from '../Interfaces';\n\nconst properties: INodeProperties[] = [\n\t{\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-wrong-for-dynamic-options\n\t\tdisplayName: 'Table Name',\n\t\tname: 'tableName',\n\t\ttype: 'options',\n\t\tplaceholder: 'Select a table',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getTableNames',\n\t\t},\n\t\tdefault: '',\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-description-wrong-for-dynamic-options\n\t\tdescription:\n\t\t\t'Choose from the list, or specify a name using an <a href=\"https://docs.n8n.io/code-examples/expressions/\">expression</a>',\n\t},\n\t{\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-wrong-for-dynamic-options\n\t\tdisplayName: 'Column Name',\n\t\tname: 'uploadColumn',\n\t\ttype: 'options',\n\t\ttypeOptions: {\n\t\t\tloadOptionsDependsOn: ['tableName'],\n\t\t\tloadOptionsMethod: 'getAssetColumns',\n\t\t},\n\t\trequired: true,\n\t\tdefault: '',\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-description-wrong-for-dynamic-options\n\t\tdescription:\n\t\t\t'Choose from the list, or specify the name using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t},\n\t{\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-wrong-for-dynamic-options\n\t\tdisplayName: 'Row ID',\n\t\tname: 'rowId',\n\t\ttype: 'options',\n\t\tdescription:\n\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>',\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tloadOptionsDependsOn: ['tableName'],\n\t\t\tloadOptionsMethod: 'getRowIds',\n\t\t},\n\t\tdefault: '',\n\t},\n\t{\n\t\tdisplayName: 'Property Name',\n\t\tname: 'dataPropertyName',\n\t\ttype: 'string',\n\t\tdefault: 'data',\n\t\trequired: true,\n\t\tdescription: 'Name of the binary property which contains the data for the file to be written',\n\t},\n\t{\n\t\tdisplayName: 'Options',\n\t\tname: 'options',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Option',\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Replace Existing File',\n\t\t\t\tname: 'replace',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: true,\n\t\t\t\tdescription:\n\t\t\t\t\t'Whether to replace the existing asset with the same name (true). Otherwise, a new version with a different name (numeral in parentheses) will be uploaded (false).',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Append to Column',\n\t\t\t\tname: 'append',\n\t\t\t\ttype: 'boolean',\n\t\t\t\tdefault: true,\n\t\t\t\tdescription:\n\t\t\t\t\t'Whether to keep existing files/images in the column and append the new asset (true). Otherwise, the existing files/images are removed from the column (false).',\n\t\t\t},\n\t\t],\n\t},\n];\n\nconst displayOptions = {\n\tshow: {\n\t\tresource: ['asset'],\n\t\toperation: ['upload'],\n\t},\n};\n\nexport const description = updateDisplayOptions(displayOptions, properties);\n\nexport async function execute(\n\tthis: IExecuteFunctions,\n\tindex: number,\n): Promise<INodeExecutionData[]> {\n\tconst uploadColumn = this.getNodeParameter('uploadColumn', index) as string;\n\tconst uploadColumnType = uploadColumn.split(':::')[1];\n\tconst uploadColumnName = uploadColumn.split(':::')[0];\n\tconst dataPropertyName = this.getNodeParameter('dataPropertyName', index);\n\tconst tableName = this.getNodeParameter('tableName', index) as string;\n\tconst rowId = this.getNodeParameter('rowId', index) as string;\n\tconst uploadLink = (await seaTableApiRequest.call(\n\t\tthis,\n\t\t{},\n\t\t'GET',\n\t\t'/api/v2.1/dtable/app-upload-link/',\n\t)) as IUploadLink;\n\tconst relativePath =\n\t\tuploadColumnType === 'image' ? uploadLink.img_relative_path : uploadLink.file_relative_path;\n\n\tconst options = this.getNodeParameter('options', index);\n\n\t// get server url\n\tconst credentials: any = await this.getCredentials('seaTableApi');\n\tconst serverURL: string = credentials.domain\n\t\t? credentials.domain.replace(/\\/$/, '')\n\t\t: 'https://cloud.seatable.io';\n\n\t// get workspaceId\n\tconst workspaceId = (\n\t\tawait this.helpers.httpRequest({\n\t\t\theaders: {\n\t\t\t\tAuthorization: `Token ${credentials.token}`,\n\t\t\t},\n\t\t\turl: `${serverURL}/api/v2.1/dtable/app-access-token/`,\n\t\t\tjson: true,\n\t\t})\n\t).workspace_id;\n\n\t// if there are already assets attached to the column\n\tlet existingAssetArray = [];\n\tconst append = options.append ?? true;\n\tif (append) {\n\t\tconst rowToUpdate = await seaTableApiRequest.call(\n\t\t\tthis,\n\t\t\t{},\n\t\t\t'GET',\n\t\t\t'/api-gateway/api/v2/dtables/{{dtable_uuid}}/rows/' + rowId,\n\t\t\t{},\n\t\t\t{\n\t\t\t\ttable_name: tableName,\n\t\t\t\tconvert_keys: true,\n\t\t\t},\n\t\t);\n\t\texistingAssetArray = rowToUpdate[uploadColumnName] ?? [];\n\t}\n\n\t// Get the binary data and prepare asset for upload\n\tconst fileBufferData = await this.helpers.getBinaryDataBuffer(index, dataPropertyName);\n\tconst binaryData = this.helpers.assertBinaryData(index, dataPropertyName);\n\tconst requestOptions = {\n\t\tformData: {\n\t\t\tfile: {\n\t\t\t\tvalue: fileBufferData,\n\t\t\t\toptions: {\n\t\t\t\t\tfilename: binaryData.fileName,\n\t\t\t\t\tcontentType: binaryData.mimeType,\n\t\t\t\t},\n\t\t\t},\n\t\t\tparent_dir: uploadLink.parent_path,\n\t\t\treplace: options.replace ? '1' : '0',\n\t\t\trelative_path: relativePath,\n\t\t},\n\t};\n\n\t// Send the upload request\n\tconst uploadAsset = await seaTableApiRequest.call(\n\t\tthis,\n\t\t{},\n\t\t'POST',\n\t\t`/seafhttp/upload-api/${uploadLink.upload_link.split('seafhttp/upload-api/')[1]}?ret-json=true`,\n\t\t{},\n\t\t{},\n\t\t'',\n\t\trequestOptions,\n\t);\n\n\t// attach the asset to a column in a base\n\tfor (let c = 0; c < uploadAsset.length; c++) {\n\t\tconst rowInput = {} as IRowObject;\n\n\t\tconst filePath = `${serverURL}/workspace/${workspaceId}${uploadLink.parent_path}/${relativePath}/${uploadAsset[c].name}`;\n\n\t\tif (uploadColumnType === 'image') {\n\t\t\trowInput[uploadColumnName] = [filePath];\n\t\t} else if (uploadColumnType === 'file') {\n\t\t\trowInput[uploadColumnName] = uploadAsset;\n\t\t\tuploadAsset[c].type = 'file';\n\t\t\tuploadAsset[c].url = filePath;\n\t\t}\n\n\t\t// merge with existing assets in this column or with [] and remove duplicates\n\t\tconst mergedArray = existingAssetArray.concat(rowInput[uploadColumnName]);\n\n\t\t// Remove duplicates from input, keeping the last one\n\t\tconst uniqueAssets = Array.from(new Set(mergedArray));\n\n\t\t// Update the rowInput with the unique assets and store into body.row.\n\t\trowInput[uploadColumnName] = uniqueAssets;\n\t\tconst body = {\n\t\t\ttable_name: tableName,\n\t\t\tupdates: [\n\t\t\t\t{\n\t\t\t\t\trow_id: rowId,\n\t\t\t\t\trow: rowInput,\n\t\t\t\t},\n\t\t\t],\n\t\t} as IDataObject;\n\n\t\t// attach assets to table row\n\t\tconst responseData = await seaTableApiRequest.call(\n\t\t\tthis,\n\t\t\t{},\n\t\t\t'PUT',\n\t\t\t'/api-gateway/api/v2/dtables/{{dtable_uuid}}/rows/',\n\t\t\tbody,\n\t\t);\n\n\t\tuploadAsset[c].upload_successful = responseData.success;\n\t}\n\n\treturn this.helpers.returnJsonArray(uploadAsset as IDataObject[]);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAMO;AAEP,8BAAmC;AAGnC,MAAM,aAAgC;AAAA,EACrC;AAAA;AAAA,IAEC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA;AAAA,IAET,aACC;AAAA,EACF;AAAA,EACA;AAAA;AAAA,IAEC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,sBAAsB,CAAC,WAAW;AAAA,MAClC,mBAAmB;AAAA,IACpB;AAAA,IACA,UAAU;AAAA,IACV,SAAS;AAAA;AAAA,IAET,aACC;AAAA,EACF;AAAA,EACA;AAAA;AAAA,IAEC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aACC;AAAA,IACD,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,sBAAsB,CAAC,WAAW;AAAA,MAClC,mBAAmB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AACD;AAEA,MAAM,iBAAiB;AAAA,EACtB,MAAM;AAAA,IACL,UAAU,CAAC,OAAO;AAAA,IAClB,WAAW,CAAC,QAAQ;AAAA,EACrB;AACD;AAEO,MAAM,kBAAc,0CAAqB,gBAAgB,UAAU;AAE1E,eAAsB,QAErB,OACgC;AAChC,QAAM,eAAe,KAAK,iBAAiB,gBAAgB,KAAK;AAChE,QAAM,mBAAmB,aAAa,MAAM,KAAK,EAAE,CAAC;AACpD,QAAM,mBAAmB,aAAa,MAAM,KAAK,EAAE,CAAC;AACpD,QAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,KAAK;AACxE,QAAM,YAAY,KAAK,iBAAiB,aAAa,KAAK;AAC1D,QAAM,QAAQ,KAAK,iBAAiB,SAAS,KAAK;AAClD,QAAM,aAAc,MAAM,2CAAmB;AAAA,IAC5C;AAAA,IACA,CAAC;AAAA,IACD;AAAA,IACA;AAAA,EACD;AACA,QAAM,eACL,qBAAqB,UAAU,WAAW,oBAAoB,WAAW;AAE1E,QAAM,UAAU,KAAK,iBAAiB,WAAW,KAAK;AAGtD,QAAM,cAAmB,MAAM,KAAK,eAAe,aAAa;AAChE,QAAM,YAAoB,YAAY,SACnC,YAAY,OAAO,QAAQ,OAAO,EAAE,IACpC;AAGH,QAAM,eACL,MAAM,KAAK,QAAQ,YAAY;AAAA,IAC9B,SAAS;AAAA,MACR,eAAe,SAAS,YAAY,KAAK;AAAA,IAC1C;AAAA,IACA,KAAK,GAAG,SAAS;AAAA,IACjB,MAAM;AAAA,EACP,CAAC,GACA;AAGF,MAAI,qBAAqB,CAAC;AAC1B,QAAM,SAAS,QAAQ,UAAU;AACjC,MAAI,QAAQ;AACX,UAAM,cAAc,MAAM,2CAAmB;AAAA,MAC5C;AAAA,MACA,CAAC;AAAA,MACD;AAAA,MACA,sDAAsD;AAAA,MACtD,CAAC;AAAA,MACD;AAAA,QACC,YAAY;AAAA,QACZ,cAAc;AAAA,MACf;AAAA,IACD;AACA,yBAAqB,YAAY,gBAAgB,KAAK,CAAC;AAAA,EACxD;AAGA,QAAM,iBAAiB,MAAM,KAAK,QAAQ,oBAAoB,OAAO,gBAAgB;AACrF,QAAM,aAAa,KAAK,QAAQ,iBAAiB,OAAO,gBAAgB;AACxE,QAAM,iBAAiB;AAAA,IACtB,UAAU;AAAA,MACT,MAAM;AAAA,QACL,OAAO;AAAA,QACP,SAAS;AAAA,UACR,UAAU,WAAW;AAAA,UACrB,aAAa,WAAW;AAAA,QACzB;AAAA,MACD;AAAA,MACA,YAAY,WAAW;AAAA,MACvB,SAAS,QAAQ,UAAU,MAAM;AAAA,MACjC,eAAe;AAAA,IAChB;AAAA,EACD;AAGA,QAAM,cAAc,MAAM,2CAAmB;AAAA,IAC5C;AAAA,IACA,CAAC;AAAA,IACD;AAAA,IACA,wBAAwB,WAAW,YAAY,MAAM,sBAAsB,EAAE,CAAC,CAAC;AAAA,IAC/E,CAAC;AAAA,IACD,CAAC;AAAA,IACD;AAAA,IACA;AAAA,EACD;AAGA,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC5C,UAAM,WAAW,CAAC;AAElB,UAAM,WAAW,GAAG,SAAS,cAAc,WAAW,GAAG,WAAW,WAAW,IAAI,YAAY,IAAI,YAAY,CAAC,EAAE,IAAI;AAEtH,QAAI,qBAAqB,SAAS;AACjC,eAAS,gBAAgB,IAAI,CAAC,QAAQ;AAAA,IACvC,WAAW,qBAAqB,QAAQ;AACvC,eAAS,gBAAgB,IAAI;AAC7B,kBAAY,CAAC,EAAE,OAAO;AACtB,kBAAY,CAAC,EAAE,MAAM;AAAA,IACtB;AAGA,UAAM,cAAc,mBAAmB,OAAO,SAAS,gBAAgB,CAAC;AAGxE,UAAM,eAAe,MAAM,KAAK,IAAI,IAAI,WAAW,CAAC;AAGpD,aAAS,gBAAgB,IAAI;AAC7B,UAAM,OAAO;AAAA,MACZ,YAAY;AAAA,MACZ,SAAS;AAAA,QACR;AAAA,UACC,QAAQ;AAAA,UACR,KAAK;AAAA,QACN;AAAA,MACD;AAAA,IACD;AAGA,UAAM,eAAe,MAAM,2CAAmB;AAAA,MAC7C;AAAA,MACA,CAAC;AAAA,MACD;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAEA,gBAAY,CAAC,EAAE,oBAAoB,aAAa;AAAA,EACjD;AAEA,SAAO,KAAK,QAAQ,gBAAgB,WAA4B;AACjE;", "names": []}