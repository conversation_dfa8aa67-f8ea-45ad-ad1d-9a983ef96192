{"version": 3, "sources": ["../../../nodes/Zammad/Zammad.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tICredentialsDecrypted,\n\tICredentialTestFunctions,\n\tIDataObject,\n\tILoadOptionsFunctions,\n\tINodeCredentialTestResult,\n\tINodeExecutionData,\n\tINodeType,\n\tINodeTypeDescription,\n\tIRequestOptions,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes, NodeOperationError } from 'n8n-workflow';\n\nimport {\n\tgroupDescription,\n\torganizationDescription,\n\tticketDescription,\n\tuserDescription,\n} from './descriptions';\nimport {\n\tdoesNotBelongToZammad,\n\tfieldToLoadOption,\n\tgetAllFields,\n\tgetGroupCustomFields,\n\tgetGroupFields,\n\tgetOrganizationCustomFields,\n\tgetOrganizationFields,\n\tgetTicketCustomFields,\n\tgetTicketFields,\n\tgetUserCustomFields,\n\tgetUserFields,\n\tisCustomer,\n\tisNotZammadFoundation,\n\tthrowOnEmptyUpdate,\n\ttolerateTrailingSlash,\n\tzammadApiRequest,\n\tzammadApiRequestAllItems,\n} from './GenericFunctions';\nimport type { Zammad as ZammadTypes } from './types';\n\nexport class Zammad implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Zammad',\n\t\tname: 'zammad',\n\t\ticon: 'file:zammad.svg',\n\t\tgroup: ['input'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume the Zammad API',\n\t\tdefaults: {\n\t\t\tname: 'Zammad',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'zammadBasicAuthApi',\n\t\t\t\trequired: true,\n\t\t\t\ttestedBy: 'zammadBasicAuthApiTest',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tauthentication: ['basicAuth'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'zammadTokenAuthApi',\n\t\t\t\trequired: true,\n\t\t\t\ttestedBy: 'zammadTokenAuthApiTest',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tauthentication: ['tokenAuth'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Authentication',\n\t\t\t\tname: 'authentication',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Basic Auth',\n\t\t\t\t\t\tvalue: 'basicAuth',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Token Auth',\n\t\t\t\t\t\tvalue: 'tokenAuth',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'tokenAuth',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Group',\n\t\t\t\t\t\tvalue: 'group',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Organization',\n\t\t\t\t\t\tvalue: 'organization',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Ticket',\n\t\t\t\t\t\tvalue: 'ticket',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'User',\n\t\t\t\t\t\tvalue: 'user',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'user',\n\t\t\t},\n\n\t\t\t...groupDescription,\n\t\t\t...organizationDescription,\n\t\t\t...ticketDescription,\n\t\t\t...userDescription,\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\t// ----------------------------------\n\t\t\t//          custom fields\n\t\t\t// ----------------------------------\n\n\t\t\tasync loadGroupCustomFields(this: ILoadOptionsFunctions) {\n\t\t\t\tconst allFields = await getAllFields.call(this);\n\n\t\t\t\treturn getGroupCustomFields(allFields).map(fieldToLoadOption);\n\t\t\t},\n\n\t\t\tasync loadOrganizationCustomFields(this: ILoadOptionsFunctions) {\n\t\t\t\tconst allFields = await getAllFields.call(this);\n\n\t\t\t\treturn getOrganizationCustomFields(allFields).map(fieldToLoadOption);\n\t\t\t},\n\n\t\t\tasync loadUserCustomFields(this: ILoadOptionsFunctions) {\n\t\t\t\tconst allFields = await getAllFields.call(this);\n\n\t\t\t\treturn getUserCustomFields(allFields).map(fieldToLoadOption);\n\t\t\t},\n\n\t\t\tasync loadTicketCustomFields(this: ILoadOptionsFunctions) {\n\t\t\t\tconst allFields = await getAllFields.call(this);\n\n\t\t\t\treturn getTicketCustomFields(allFields).map((i) => ({ name: i.name, value: i.id }));\n\t\t\t},\n\n\t\t\t// ----------------------------------\n\t\t\t//          built-in fields\n\t\t\t// ----------------------------------\n\n\t\t\tasync loadGroupFields(this: ILoadOptionsFunctions) {\n\t\t\t\tconst allFields = await getAllFields.call(this);\n\n\t\t\t\treturn getGroupFields(allFields).map(fieldToLoadOption);\n\t\t\t},\n\n\t\t\tasync loadOrganizationFields(this: ILoadOptionsFunctions) {\n\t\t\t\tconst allFields = await getAllFields.call(this);\n\n\t\t\t\treturn getOrganizationFields(allFields).map(fieldToLoadOption);\n\t\t\t},\n\n\t\t\tasync loadTicketFields(this: ILoadOptionsFunctions) {\n\t\t\t\tconst allFields = await getAllFields.call(this);\n\n\t\t\t\treturn getTicketFields(allFields).map(fieldToLoadOption);\n\t\t\t},\n\n\t\t\tasync loadUserFields(this: ILoadOptionsFunctions) {\n\t\t\t\tconst allFields = await getAllFields.call(this);\n\n\t\t\t\treturn getUserFields(allFields).map(fieldToLoadOption);\n\t\t\t},\n\n\t\t\t// ----------------------------------\n\t\t\t//             resources\n\t\t\t// ----------------------------------\n\n\t\t\t// by non-ID attribute\n\n\t\t\t/**\n\t\t\t * POST /tickets requires group name instead of group ID.\n\t\t\t */\n\t\t\tasync loadGroupNames(this: ILoadOptionsFunctions) {\n\t\t\t\tconst groups = (await zammadApiRequest.call(this, 'GET', '/groups')) as ZammadTypes.Group[];\n\n\t\t\t\treturn groups.map((i) => ({ name: i.name, value: i.name }));\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * PUT /users requires organization name instead of organization ID.\n\t\t\t */\n\t\t\tasync loadOrganizationNames(this: ILoadOptionsFunctions) {\n\t\t\t\tconst orgs = (await zammadApiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/organizations',\n\t\t\t\t)) as ZammadTypes.Group[];\n\n\t\t\t\treturn orgs.filter(isNotZammadFoundation).map((i) => ({ name: i.name, value: i.name }));\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * POST & PUT /tickets requires customer email instead of customer ID.\n\t\t\t */\n\t\t\tasync loadCustomerEmails(this: ILoadOptionsFunctions) {\n\t\t\t\tconst users = (await zammadApiRequest.call(this, 'GET', '/users')) as ZammadTypes.User[];\n\n\t\t\t\treturn users.filter(isCustomer).map((i) => ({ name: i.email, value: i.email }));\n\t\t\t},\n\n\t\t\t// by ID\n\n\t\t\tasync loadGroups(this: ILoadOptionsFunctions) {\n\t\t\t\tconst groups = (await zammadApiRequest.call(this, 'GET', '/groups')) as ZammadTypes.Group[];\n\n\t\t\t\treturn groups.map((i) => ({ name: i.name, value: i.id }));\n\t\t\t},\n\n\t\t\tasync loadOrganizations(this: ILoadOptionsFunctions) {\n\t\t\t\tconst orgs = (await zammadApiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/organizations',\n\t\t\t\t)) as ZammadTypes.Organization[];\n\n\t\t\t\treturn orgs.filter(isNotZammadFoundation).map((i) => ({ name: i.name, value: i.id }));\n\t\t\t},\n\n\t\t\tasync loadUsers(this: ILoadOptionsFunctions) {\n\t\t\t\tconst users = (await zammadApiRequest.call(this, 'GET', '/users')) as ZammadTypes.User[];\n\n\t\t\t\treturn users.filter(doesNotBelongToZammad).map((i) => ({ name: i.login, value: i.id }));\n\t\t\t},\n\t\t},\n\t\tcredentialTest: {\n\t\t\tasync zammadBasicAuthApiTest(\n\t\t\t\tthis: ICredentialTestFunctions,\n\t\t\t\tcredential: ICredentialsDecrypted,\n\t\t\t): Promise<INodeCredentialTestResult> {\n\t\t\t\tconst credentials = credential.data as ZammadTypes.BasicAuthCredentials;\n\n\t\t\t\tconst baseUrl = tolerateTrailingSlash(credentials.baseUrl);\n\n\t\t\t\tconst options: IRequestOptions = {\n\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\turi: `${baseUrl}/api/v1/users/me`,\n\t\t\t\t\tjson: true,\n\t\t\t\t\trejectUnauthorized: !credentials.allowUnauthorizedCerts,\n\t\t\t\t\tauth: {\n\t\t\t\t\t\tuser: credentials.username,\n\t\t\t\t\t\tpass: credentials.password,\n\t\t\t\t\t},\n\t\t\t\t};\n\n\t\t\t\ttry {\n\t\t\t\t\tawait this.helpers.request(options);\n\t\t\t\t\treturn {\n\t\t\t\t\t\tstatus: 'OK',\n\t\t\t\t\t\tmessage: 'Authentication successful',\n\t\t\t\t\t};\n\t\t\t\t} catch (error) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tstatus: 'Error',\n\t\t\t\t\t\tmessage: error.message,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tasync zammadTokenAuthApiTest(\n\t\t\t\tthis: ICredentialTestFunctions,\n\t\t\t\tcredential: ICredentialsDecrypted,\n\t\t\t): Promise<INodeCredentialTestResult> {\n\t\t\t\tconst credentials = credential.data as ZammadTypes.TokenAuthCredentials;\n\n\t\t\t\tconst baseUrl = tolerateTrailingSlash(credentials.baseUrl);\n\n\t\t\t\tconst options: IRequestOptions = {\n\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\turi: `${baseUrl}/api/v1/users/me`,\n\t\t\t\t\tjson: true,\n\t\t\t\t\trejectUnauthorized: !credentials.allowUnauthorizedCerts,\n\t\t\t\t\theaders: {\n\t\t\t\t\t\tAuthorization: `Token token=${credentials.accessToken}`,\n\t\t\t\t\t},\n\t\t\t\t};\n\n\t\t\t\ttry {\n\t\t\t\t\tawait this.helpers.request(options);\n\t\t\t\t\treturn {\n\t\t\t\t\t\tstatus: 'OK',\n\t\t\t\t\t\tmessage: 'Authentication successful',\n\t\t\t\t\t};\n\t\t\t\t} catch (error) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tstatus: 'Error',\n\t\t\t\t\t\tmessage: error.message,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\n\t\tconst resource = this.getNodeParameter('resource', 0) as ZammadTypes.Resource;\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\n\t\tlet responseData;\n\t\tconst returnData: INodeExecutionData[] = [];\n\n\t\tfor (let i = 0; i < items.length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'user') {\n\t\t\t\t\t// **********************************************************************\n\t\t\t\t\t//                                  user\n\t\t\t\t\t// **********************************************************************\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//           user:create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/user.html#create\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tfirstname: this.getNodeParameter('firstname', i),\n\t\t\t\t\t\t\tlastname: this.getNodeParameter('lastname', i),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst { addressUi, customFieldsUi, ...rest } = this.getNodeParameter(\n\t\t\t\t\t\t\t'additionalFields',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t) as ZammadTypes.UserAdditionalFields;\n\n\t\t\t\t\t\tObject.assign(body, addressUi?.addressDetails);\n\n\t\t\t\t\t\tcustomFieldsUi?.customFieldPairs.forEach((pair) => {\n\t\t\t\t\t\t\tbody[pair.name] = pair.value;\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tObject.assign(body, rest);\n\n\t\t\t\t\t\tresponseData = await zammadApiRequest.call(this, 'POST', '/users', body);\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//            user:update\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/user.html#update\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i);\n\n\t\t\t\t\t\tconst body: IDataObject = {};\n\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter(\n\t\t\t\t\t\t\t'updateFields',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t) as ZammadTypes.UserUpdateFields;\n\n\t\t\t\t\t\tif (!Object.keys(updateFields).length) {\n\t\t\t\t\t\t\tthrowOnEmptyUpdate.call(this, resource);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst { addressUi, customFieldsUi, ...rest } = updateFields;\n\n\t\t\t\t\t\tObject.assign(body, addressUi?.addressDetails);\n\n\t\t\t\t\t\tcustomFieldsUi?.customFieldPairs.forEach((pair) => {\n\t\t\t\t\t\t\tbody[pair.name] = pair.value;\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tObject.assign(body, rest);\n\n\t\t\t\t\t\tresponseData = await zammadApiRequest.call(this, 'PUT', `/users/${id}`, body);\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//            user:delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/user.html#delete\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tawait zammadApiRequest.call(this, 'DELETE', `/users/${id}`);\n\n\t\t\t\t\t\tresponseData = { success: true };\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//            user:get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/user.html#show\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tresponseData = await zammadApiRequest.call(this, 'GET', `/users/${id}`);\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//           user:getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/user.html#list\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/user.html#search\n\n\t\t\t\t\t\tconst qs: IDataObject = {};\n\n\t\t\t\t\t\tconst { sortUi, ...rest } = this.getNodeParameter(\n\t\t\t\t\t\t\t'filters',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t) as ZammadTypes.UserFilterFields;\n\n\t\t\t\t\t\tObject.assign(qs, sortUi?.sortDetails);\n\n\t\t\t\t\t\tObject.assign(qs, rest);\n\n\t\t\t\t\t\tqs.query ||= ''; // otherwise triggers 500\n\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tconst limit = returnAll ? 0 : this.getNodeParameter('limit', i);\n\n\t\t\t\t\t\tresponseData = await zammadApiRequestAllItems\n\t\t\t\t\t\t\t.call(this, 'GET', '/users/search', {}, qs, limit)\n\t\t\t\t\t\t\t.then((response) => {\n\t\t\t\t\t\t\t\treturn response.map((user) => {\n\t\t\t\t\t\t\t\t\tconst { _preferences, ...data } = user;\n\t\t\t\t\t\t\t\t\treturn data;\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t});\n\t\t\t\t\t} else if (operation === 'getSelf') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//             user:me\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/user.html#me-current-user\n\n\t\t\t\t\t\tresponseData = await zammadApiRequest.call(this, 'GET', '/users/me');\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'organization') {\n\t\t\t\t\t// **********************************************************************\n\t\t\t\t\t//                             organization\n\t\t\t\t\t// **********************************************************************\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//        organization:create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/organization.html#create\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tname: this.getNodeParameter('name', i),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst { customFieldsUi, ...rest } = this.getNodeParameter(\n\t\t\t\t\t\t\t'additionalFields',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t) as ZammadTypes.UserAdditionalFields;\n\n\t\t\t\t\t\tcustomFieldsUi?.customFieldPairs.forEach((pair) => {\n\t\t\t\t\t\t\tbody[pair.name] = pair.value;\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tObject.assign(body, rest);\n\n\t\t\t\t\t\tresponseData = await zammadApiRequest.call(this, 'POST', '/organizations', body);\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//       organization:update\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/organization.html#update\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i);\n\n\t\t\t\t\t\tconst body: IDataObject = {};\n\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter(\n\t\t\t\t\t\t\t'updateFields',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t) as ZammadTypes.UserUpdateFields;\n\n\t\t\t\t\t\tif (!Object.keys(updateFields).length) {\n\t\t\t\t\t\t\tthrowOnEmptyUpdate.call(this, resource);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst { customFieldsUi, ...rest } = updateFields;\n\n\t\t\t\t\t\tcustomFieldsUi?.customFieldPairs.forEach((pair) => {\n\t\t\t\t\t\t\tbody[pair.name] = pair.value;\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tObject.assign(body, rest);\n\n\t\t\t\t\t\tresponseData = await zammadApiRequest.call(this, 'PUT', `/organizations/${id}`, body);\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         organization:delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/organization.html#delete\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tawait zammadApiRequest.call(this, 'DELETE', `/organizations/${id}`);\n\n\t\t\t\t\t\tresponseData = { success: true };\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         organization:get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/organization.html#show\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tresponseData = await zammadApiRequest.call(this, 'GET', `/organizations/${id}`);\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//         organization:getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/organization.html#list\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/organization.html#search - returning empty always\n\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tconst limit = returnAll ? 0 : this.getNodeParameter('limit', i);\n\n\t\t\t\t\t\tresponseData = await zammadApiRequestAllItems.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t'/organizations',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tlimit,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'group') {\n\t\t\t\t\t// **********************************************************************\n\t\t\t\t\t//                                  group\n\t\t\t\t\t// **********************************************************************\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//           group:create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/group.html#create\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tname: this.getNodeParameter('name', i) as string,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst { customFieldsUi, ...rest } = this.getNodeParameter(\n\t\t\t\t\t\t\t'additionalFields',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t) as ZammadTypes.UserAdditionalFields;\n\n\t\t\t\t\t\tcustomFieldsUi?.customFieldPairs.forEach((pair) => {\n\t\t\t\t\t\t\tbody[pair.name] = pair.value;\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tObject.assign(body, rest);\n\n\t\t\t\t\t\tresponseData = await zammadApiRequest.call(this, 'POST', '/groups', body);\n\t\t\t\t\t} else if (operation === 'update') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//            group:update\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/group.html#update\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tconst body: IDataObject = {};\n\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter(\n\t\t\t\t\t\t\t'updateFields',\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t) as ZammadTypes.GroupUpdateFields;\n\n\t\t\t\t\t\tif (!Object.keys(updateFields).length) {\n\t\t\t\t\t\t\tthrowOnEmptyUpdate.call(this, resource);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst { customFieldsUi, ...rest } = updateFields;\n\n\t\t\t\t\t\tcustomFieldsUi?.customFieldPairs.forEach((pair) => {\n\t\t\t\t\t\t\tbody[pair.name] = pair.value;\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tObject.assign(body, rest);\n\n\t\t\t\t\t\tresponseData = await zammadApiRequest.call(this, 'PUT', `/groups/${id}`, body);\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//            group:delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/group.html#delete\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tawait zammadApiRequest.call(this, 'DELETE', `/groups/${id}`);\n\n\t\t\t\t\t\tresponseData = { success: true };\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//             group:get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/group.html#show\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tresponseData = await zammadApiRequest.call(this, 'GET', `/groups/${id}`);\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//           group:getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/group.html#list\n\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tconst limit = returnAll ? 0 : this.getNodeParameter('limit', i);\n\n\t\t\t\t\t\tresponseData = await zammadApiRequestAllItems.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t'/groups',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tlimit,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else if (resource === 'ticket') {\n\t\t\t\t\t// **********************************************************************\n\t\t\t\t\t//                                  ticket\n\t\t\t\t\t// **********************************************************************\n\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//           ticket:create\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/ticket/index.html#create\n\n\t\t\t\t\t\tconst body = {\n\t\t\t\t\t\t\tarticle: {},\n\t\t\t\t\t\t\ttitle: this.getNodeParameter('title', i) as string,\n\t\t\t\t\t\t\tgroup: this.getNodeParameter('group', i) as string,\n\t\t\t\t\t\t\tcustomer: this.getNodeParameter('customer', i) as string,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst article = this.getNodeParameter('article', i) as ZammadTypes.Article;\n\n\t\t\t\t\t\tif (!Object.keys(article).length) {\n\t\t\t\t\t\t\tthrow new NodeOperationError(this.getNode(), 'Article is required', { itemIndex: i });\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst {\n\t\t\t\t\t\t\tarticleDetails: { visibility, ...rest },\n\t\t\t\t\t\t} = article;\n\n\t\t\t\t\t\tbody.article = {\n\t\t\t\t\t\t\t...rest,\n\t\t\t\t\t\t\tinternal: visibility === 'internal',\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tresponseData = await zammadApiRequest.call(this, 'POST', '/tickets', body);\n\n\t\t\t\t\t\tconst { id } = responseData;\n\n\t\t\t\t\t\tresponseData.articles = await zammadApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t`/ticket_articles/by_ticket/${id}`,\n\t\t\t\t\t\t);\n\t\t\t\t\t} else if (operation === 'delete') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//          ticket:delete\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/ticket/index.html#delete\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tawait zammadApiRequest.call(this, 'DELETE', `/tickets/${id}`);\n\n\t\t\t\t\t\tresponseData = { success: true };\n\t\t\t\t\t} else if (operation === 'get') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//            ticket:get\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/ticket/index.html#show\n\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tresponseData = await zammadApiRequest.call(this, 'GET', `/tickets/${id}`);\n\t\t\t\t\t\tresponseData.articles = await zammadApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t`/ticket_articles/by_ticket/${id}`,\n\t\t\t\t\t\t);\n\t\t\t\t\t} else if (operation === 'getAll') {\n\t\t\t\t\t\t// ----------------------------------\n\t\t\t\t\t\t//           ticket:getAll\n\t\t\t\t\t\t// ----------------------------------\n\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/ticket/index.html#list\n\t\t\t\t\t\t// https://docs.zammad.org/en/latest/api/ticket/index.html#search - returning empty always\n\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tconst limit = returnAll ? 0 : this.getNodeParameter('limit', i);\n\n\t\t\t\t\t\tresponseData = await zammadApiRequestAllItems.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t'/tickets',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tlimit,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\tthis.helpers.returnJsonArray(responseData as IDataObject),\n\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t);\n\t\t\t\treturnData.push(...executionData);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ json: { error: error.message } });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,0BAAwD;AAExD,0BAKO;AACP,8BAkBO;AAGA,MAAM,OAA4B;AAAA,EAAlC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,OAAO;AAAA,MACf,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,gBAAgB,CAAC,WAAW;AAAA,YAC7B;AAAA,UACD;AAAA,QACD;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,gBAAgB,CAAC,WAAW;AAAA,YAC7B;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QAEA,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA;AAAA;AAAA;AAAA,QAKZ,MAAM,wBAAmD;AACxD,gBAAM,YAAY,MAAM,qCAAa,KAAK,IAAI;AAE9C,qBAAO,8CAAqB,SAAS,EAAE,IAAI,yCAAiB;AAAA,QAC7D;AAAA,QAEA,MAAM,+BAA0D;AAC/D,gBAAM,YAAY,MAAM,qCAAa,KAAK,IAAI;AAE9C,qBAAO,qDAA4B,SAAS,EAAE,IAAI,yCAAiB;AAAA,QACpE;AAAA,QAEA,MAAM,uBAAkD;AACvD,gBAAM,YAAY,MAAM,qCAAa,KAAK,IAAI;AAE9C,qBAAO,6CAAoB,SAAS,EAAE,IAAI,yCAAiB;AAAA,QAC5D;AAAA,QAEA,MAAM,yBAAoD;AACzD,gBAAM,YAAY,MAAM,qCAAa,KAAK,IAAI;AAE9C,qBAAO,+CAAsB,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,EAAE;AAAA,QACnF;AAAA;AAAA;AAAA;AAAA,QAMA,MAAM,kBAA6C;AAClD,gBAAM,YAAY,MAAM,qCAAa,KAAK,IAAI;AAE9C,qBAAO,wCAAe,SAAS,EAAE,IAAI,yCAAiB;AAAA,QACvD;AAAA,QAEA,MAAM,yBAAoD;AACzD,gBAAM,YAAY,MAAM,qCAAa,KAAK,IAAI;AAE9C,qBAAO,+CAAsB,SAAS,EAAE,IAAI,yCAAiB;AAAA,QAC9D;AAAA,QAEA,MAAM,mBAA8C;AACnD,gBAAM,YAAY,MAAM,qCAAa,KAAK,IAAI;AAE9C,qBAAO,yCAAgB,SAAS,EAAE,IAAI,yCAAiB;AAAA,QACxD;AAAA,QAEA,MAAM,iBAA4C;AACjD,gBAAM,YAAY,MAAM,qCAAa,KAAK,IAAI;AAE9C,qBAAO,uCAAc,SAAS,EAAE,IAAI,yCAAiB;AAAA,QACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAWA,MAAM,iBAA4C;AACjD,gBAAM,SAAU,MAAM,yCAAiB,KAAK,MAAM,OAAO,SAAS;AAElE,iBAAO,OAAO,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE;AAAA,QAC3D;AAAA;AAAA;AAAA;AAAA,QAKA,MAAM,wBAAmD;AACxD,gBAAM,OAAQ,MAAM,yCAAiB;AAAA,YACpC;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAEA,iBAAO,KAAK,OAAO,6CAAqB,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE;AAAA,QACvF;AAAA;AAAA;AAAA;AAAA,QAKA,MAAM,qBAAgD;AACrD,gBAAM,QAAS,MAAM,yCAAiB,KAAK,MAAM,OAAO,QAAQ;AAEhE,iBAAO,MAAM,OAAO,kCAAU,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,OAAO,EAAE,MAAM,EAAE;AAAA,QAC/E;AAAA;AAAA,QAIA,MAAM,aAAwC;AAC7C,gBAAM,SAAU,MAAM,yCAAiB,KAAK,MAAM,OAAO,SAAS;AAElE,iBAAO,OAAO,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,EAAE;AAAA,QACzD;AAAA,QAEA,MAAM,oBAA+C;AACpD,gBAAM,OAAQ,MAAM,yCAAiB;AAAA,YACpC;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAEA,iBAAO,KAAK,OAAO,6CAAqB,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,EAAE;AAAA,QACrF;AAAA,QAEA,MAAM,YAAuC;AAC5C,gBAAM,QAAS,MAAM,yCAAiB,KAAK,MAAM,OAAO,QAAQ;AAEhE,iBAAO,MAAM,OAAO,6CAAqB,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,OAAO,EAAE,GAAG,EAAE;AAAA,QACvF;AAAA,MACD;AAAA,MACA,gBAAgB;AAAA,QACf,MAAM,uBAEL,YACqC;AACrC,gBAAM,cAAc,WAAW;AAE/B,gBAAM,cAAU,+CAAsB,YAAY,OAAO;AAEzD,gBAAM,UAA2B;AAAA,YAChC,QAAQ;AAAA,YACR,KAAK,GAAG,OAAO;AAAA,YACf,MAAM;AAAA,YACN,oBAAoB,CAAC,YAAY;AAAA,YACjC,MAAM;AAAA,cACL,MAAM,YAAY;AAAA,cAClB,MAAM,YAAY;AAAA,YACnB;AAAA,UACD;AAEA,cAAI;AACH,kBAAM,KAAK,QAAQ,QAAQ,OAAO;AAClC,mBAAO;AAAA,cACN,QAAQ;AAAA,cACR,SAAS;AAAA,YACV;AAAA,UACD,SAAS,OAAO;AACf,mBAAO;AAAA,cACN,QAAQ;AAAA,cACR,SAAS,MAAM;AAAA,YAChB;AAAA,UACD;AAAA,QACD;AAAA,QAEA,MAAM,uBAEL,YACqC;AACrC,gBAAM,cAAc,WAAW;AAE/B,gBAAM,cAAU,+CAAsB,YAAY,OAAO;AAEzD,gBAAM,UAA2B;AAAA,YAChC,QAAQ;AAAA,YACR,KAAK,GAAG,OAAO;AAAA,YACf,MAAM;AAAA,YACN,oBAAoB,CAAC,YAAY;AAAA,YACjC,SAAS;AAAA,cACR,eAAe,eAAe,YAAY,WAAW;AAAA,YACtD;AAAA,UACD;AAEA,cAAI;AACH,kBAAM,KAAK,QAAQ,QAAQ,OAAO;AAClC,mBAAO;AAAA,cACN,QAAQ;AAAA,cACR,SAAS;AAAA,YACV;AAAA,UACD,SAAS,OAAO;AACf,mBAAO;AAAA,cACN,QAAQ;AAAA,cACR,SAAS,MAAM;AAAA,YAChB;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAEhC,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,QAAI;AACJ,UAAM,aAAmC,CAAC;AAE1C,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,UAAI;AACH,YAAI,aAAa,QAAQ;AAKxB,cAAI,cAAc,UAAU;AAO3B,kBAAM,OAAoB;AAAA,cACzB,WAAW,KAAK,iBAAiB,aAAa,CAAC;AAAA,cAC/C,UAAU,KAAK,iBAAiB,YAAY,CAAC;AAAA,YAC9C;AAEA,kBAAM,EAAE,WAAW,gBAAgB,GAAG,KAAK,IAAI,KAAK;AAAA,cACnD;AAAA,cACA;AAAA,YACD;AAEA,mBAAO,OAAO,MAAM,WAAW,cAAc;AAE7C,4BAAgB,iBAAiB,QAAQ,CAAC,SAAS;AAClD,mBAAK,KAAK,IAAI,IAAI,KAAK;AAAA,YACxB,CAAC;AAED,mBAAO,OAAO,MAAM,IAAI;AAExB,2BAAe,MAAM,yCAAiB,KAAK,MAAM,QAAQ,UAAU,IAAI;AAAA,UACxE,WAAW,cAAc,UAAU;AAOlC,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,kBAAM,OAAoB,CAAC;AAE3B,kBAAM,eAAe,KAAK;AAAA,cACzB;AAAA,cACA;AAAA,YACD;AAEA,gBAAI,CAAC,OAAO,KAAK,YAAY,EAAE,QAAQ;AACtC,yDAAmB,KAAK,MAAM,QAAQ;AAAA,YACvC;AAEA,kBAAM,EAAE,WAAW,gBAAgB,GAAG,KAAK,IAAI;AAE/C,mBAAO,OAAO,MAAM,WAAW,cAAc;AAE7C,4BAAgB,iBAAiB,QAAQ,CAAC,SAAS;AAClD,mBAAK,KAAK,IAAI,IAAI,KAAK;AAAA,YACxB,CAAC;AAED,mBAAO,OAAO,MAAM,IAAI;AAExB,2BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,UAAU,EAAE,IAAI,IAAI;AAAA,UAC7E,WAAW,cAAc,UAAU;AAOlC,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,kBAAM,yCAAiB,KAAK,MAAM,UAAU,UAAU,EAAE,EAAE;AAE1D,2BAAe,EAAE,SAAS,KAAK;AAAA,UAChC,WAAW,cAAc,OAAO;AAO/B,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,2BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,UAAU,EAAE,EAAE;AAAA,UACvE,WAAW,cAAc,UAAU;AAQlC,kBAAM,KAAkB,CAAC;AAEzB,kBAAM,EAAE,QAAQ,GAAG,KAAK,IAAI,KAAK;AAAA,cAChC;AAAA,cACA;AAAA,YACD;AAEA,mBAAO,OAAO,IAAI,QAAQ,WAAW;AAErC,mBAAO,OAAO,IAAI,IAAI;AAEtB,eAAG,UAAU;AAEb,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,QAAQ,YAAY,IAAI,KAAK,iBAAiB,SAAS,CAAC;AAE9D,2BAAe,MAAM,iDACnB,KAAK,MAAM,OAAO,iBAAiB,CAAC,GAAG,IAAI,KAAK,EAChD,KAAK,CAAC,aAAa;AACnB,qBAAO,SAAS,IAAI,CAAC,SAAS;AAC7B,sBAAM,EAAE,cAAc,GAAG,KAAK,IAAI;AAClC,uBAAO;AAAA,cACR,CAAC;AAAA,YACF,CAAC;AAAA,UACH,WAAW,cAAc,WAAW;AAOnC,2BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,WAAW;AAAA,UACpE;AAAA,QACD,WAAW,aAAa,gBAAgB;AAKvC,cAAI,cAAc,UAAU;AAO3B,kBAAM,OAAoB;AAAA,cACzB,MAAM,KAAK,iBAAiB,QAAQ,CAAC;AAAA,YACtC;AAEA,kBAAM,EAAE,gBAAgB,GAAG,KAAK,IAAI,KAAK;AAAA,cACxC;AAAA,cACA;AAAA,YACD;AAEA,4BAAgB,iBAAiB,QAAQ,CAAC,SAAS;AAClD,mBAAK,KAAK,IAAI,IAAI,KAAK;AAAA,YACxB,CAAC;AAED,mBAAO,OAAO,MAAM,IAAI;AAExB,2BAAe,MAAM,yCAAiB,KAAK,MAAM,QAAQ,kBAAkB,IAAI;AAAA,UAChF,WAAW,cAAc,UAAU;AAOlC,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,kBAAM,OAAoB,CAAC;AAE3B,kBAAM,eAAe,KAAK;AAAA,cACzB;AAAA,cACA;AAAA,YACD;AAEA,gBAAI,CAAC,OAAO,KAAK,YAAY,EAAE,QAAQ;AACtC,yDAAmB,KAAK,MAAM,QAAQ;AAAA,YACvC;AAEA,kBAAM,EAAE,gBAAgB,GAAG,KAAK,IAAI;AAEpC,4BAAgB,iBAAiB,QAAQ,CAAC,SAAS;AAClD,mBAAK,KAAK,IAAI,IAAI,KAAK;AAAA,YACxB,CAAC;AAED,mBAAO,OAAO,MAAM,IAAI;AAExB,2BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,kBAAkB,EAAE,IAAI,IAAI;AAAA,UACrF,WAAW,cAAc,UAAU;AAOlC,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,kBAAM,yCAAiB,KAAK,MAAM,UAAU,kBAAkB,EAAE,EAAE;AAElE,2BAAe,EAAE,SAAS,KAAK;AAAA,UAChC,WAAW,cAAc,OAAO;AAO/B,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,2BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,kBAAkB,EAAE,EAAE;AAAA,UAC/E,WAAW,cAAc,UAAU;AAQlC,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,QAAQ,YAAY,IAAI,KAAK,iBAAiB,SAAS,CAAC;AAE9D,2BAAe,MAAM,iDAAyB;AAAA,cAC7C;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD,WAAW,aAAa,SAAS;AAKhC,cAAI,cAAc,UAAU;AAO3B,kBAAM,OAAoB;AAAA,cACzB,MAAM,KAAK,iBAAiB,QAAQ,CAAC;AAAA,YACtC;AAEA,kBAAM,EAAE,gBAAgB,GAAG,KAAK,IAAI,KAAK;AAAA,cACxC;AAAA,cACA;AAAA,YACD;AAEA,4BAAgB,iBAAiB,QAAQ,CAAC,SAAS;AAClD,mBAAK,KAAK,IAAI,IAAI,KAAK;AAAA,YACxB,CAAC;AAED,mBAAO,OAAO,MAAM,IAAI;AAExB,2BAAe,MAAM,yCAAiB,KAAK,MAAM,QAAQ,WAAW,IAAI;AAAA,UACzE,WAAW,cAAc,UAAU;AAOlC,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,kBAAM,OAAoB,CAAC;AAE3B,kBAAM,eAAe,KAAK;AAAA,cACzB;AAAA,cACA;AAAA,YACD;AAEA,gBAAI,CAAC,OAAO,KAAK,YAAY,EAAE,QAAQ;AACtC,yDAAmB,KAAK,MAAM,QAAQ;AAAA,YACvC;AAEA,kBAAM,EAAE,gBAAgB,GAAG,KAAK,IAAI;AAEpC,4BAAgB,iBAAiB,QAAQ,CAAC,SAAS;AAClD,mBAAK,KAAK,IAAI,IAAI,KAAK;AAAA,YACxB,CAAC;AAED,mBAAO,OAAO,MAAM,IAAI;AAExB,2BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,WAAW,EAAE,IAAI,IAAI;AAAA,UAC9E,WAAW,cAAc,UAAU;AAOlC,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,kBAAM,yCAAiB,KAAK,MAAM,UAAU,WAAW,EAAE,EAAE;AAE3D,2BAAe,EAAE,SAAS,KAAK;AAAA,UAChC,WAAW,cAAc,OAAO;AAO/B,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,2BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,WAAW,EAAE,EAAE;AAAA,UACxE,WAAW,cAAc,UAAU;AAOlC,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,QAAQ,YAAY,IAAI,KAAK,iBAAiB,SAAS,CAAC;AAE9D,2BAAe,MAAM,iDAAyB;AAAA,cAC7C;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD,WAAW,aAAa,UAAU;AAKjC,cAAI,cAAc,UAAU;AAO3B,kBAAM,OAAO;AAAA,cACZ,SAAS,CAAC;AAAA,cACV,OAAO,KAAK,iBAAiB,SAAS,CAAC;AAAA,cACvC,OAAO,KAAK,iBAAiB,SAAS,CAAC;AAAA,cACvC,UAAU,KAAK,iBAAiB,YAAY,CAAC;AAAA,YAC9C;AAEA,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gBAAI,CAAC,OAAO,KAAK,OAAO,EAAE,QAAQ;AACjC,oBAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,uBAAuB,EAAE,WAAW,EAAE,CAAC;AAAA,YACrF;AAEA,kBAAM;AAAA,cACL,gBAAgB,EAAE,YAAY,GAAG,KAAK;AAAA,YACvC,IAAI;AAEJ,iBAAK,UAAU;AAAA,cACd,GAAG;AAAA,cACH,UAAU,eAAe;AAAA,YAC1B;AAEA,2BAAe,MAAM,yCAAiB,KAAK,MAAM,QAAQ,YAAY,IAAI;AAEzE,kBAAM,EAAE,GAAG,IAAI;AAEf,yBAAa,WAAW,MAAM,yCAAiB;AAAA,cAC9C;AAAA,cACA;AAAA,cACA,8BAA8B,EAAE;AAAA,YACjC;AAAA,UACD,WAAW,cAAc,UAAU;AAOlC,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,kBAAM,yCAAiB,KAAK,MAAM,UAAU,YAAY,EAAE,EAAE;AAE5D,2BAAe,EAAE,SAAS,KAAK;AAAA,UAChC,WAAW,cAAc,OAAO;AAO/B,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAExC,2BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,YAAY,EAAE,EAAE;AACxE,yBAAa,WAAW,MAAM,yCAAiB;AAAA,cAC9C;AAAA,cACA;AAAA,cACA,8BAA8B,EAAE;AAAA,YACjC;AAAA,UACD,WAAW,cAAc,UAAU;AAQlC,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,QAAQ,YAAY,IAAI,KAAK,iBAAiB,SAAS,CAAC;AAE9D,2BAAe,MAAM,iDAAyB;AAAA,cAC7C;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAEA,cAAM,gBAAgB,KAAK,QAAQ;AAAA,UAClC,KAAK,QAAQ,gBAAgB,YAA2B;AAAA,UACxD,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,QACzB;AACA,mBAAW,KAAK,GAAG,aAAa;AAAA,MACjC,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,MAAM,EAAE,OAAO,MAAM,QAAQ,EAAE,CAAC;AAClD;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AACA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": []}