{"version": 3, "sources": ["../../../../nodes/EmailSend/v2/utils.ts"], "sourcesContent": ["import type {\n\tIDataObject,\n\tICredentialsDecrypted,\n\tICredentialTestFunctions,\n\tINodeCredentialTestResult,\n} from 'n8n-workflow';\nimport { createTransport } from 'nodemailer';\nimport type SMTPTransport from 'nodemailer/lib/smtp-transport';\n\nexport type EmailSendOptions = {\n\tappendAttribution?: boolean;\n\tallowUnauthorizedCerts?: boolean;\n\tattachments?: string;\n\tccEmail?: string;\n\tbccEmail?: string;\n\treplyTo?: string;\n};\n\nexport function configureTransport(credentials: IDataObject, options: EmailSendOptions) {\n\tconst connectionOptions: SMTPTransport.Options = {\n\t\thost: credentials.host as string,\n\t\tport: credentials.port as number,\n\t\tsecure: credentials.secure as boolean,\n\t};\n\n\tif (credentials.secure === false) {\n\t\tconnectionOptions.ignoreTLS = credentials.disableStartTls as boolean;\n\t}\n\n\tif (typeof credentials.hostName === 'string' && credentials.hostName) {\n\t\tconnectionOptions.name = credentials.hostName;\n\t}\n\n\tif (credentials.user || credentials.password) {\n\t\tconnectionOptions.auth = {\n\t\t\tuser: credentials.user as string,\n\t\t\tpass: credentials.password as string,\n\t\t};\n\t}\n\n\tif (options.allowUnauthorizedCerts === true) {\n\t\tconnectionOptions.tls = {\n\t\t\trejectUnauthorized: false,\n\t\t};\n\t}\n\n\treturn createTransport(connectionOptions);\n}\n\nexport async function smtpConnectionTest(\n\tthis: ICredentialTestFunctions,\n\tcredential: ICredentialsDecrypted,\n): Promise<INodeCredentialTestResult> {\n\tconst credentials = credential.data!;\n\tconst transporter = configureTransport(credentials, {});\n\ttry {\n\t\tawait transporter.verify();\n\t\treturn {\n\t\t\tstatus: 'OK',\n\t\t\tmessage: 'Connection successful!',\n\t\t};\n\t} catch (error) {\n\t\treturn {\n\t\t\tstatus: 'Error',\n\t\t\tmessage: error.message,\n\t\t};\n\t} finally {\n\t\ttransporter.close();\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,wBAAgC;AAYzB,SAAS,mBAAmB,aAA0B,SAA2B;AACvF,QAAM,oBAA2C;AAAA,IAChD,MAAM,YAAY;AAAA,IAClB,MAAM,YAAY;AAAA,IAClB,QAAQ,YAAY;AAAA,EACrB;AAEA,MAAI,YAAY,WAAW,OAAO;AACjC,sBAAkB,YAAY,YAAY;AAAA,EAC3C;AAEA,MAAI,OAAO,YAAY,aAAa,YAAY,YAAY,UAAU;AACrE,sBAAkB,OAAO,YAAY;AAAA,EACtC;AAEA,MAAI,YAAY,QAAQ,YAAY,UAAU;AAC7C,sBAAkB,OAAO;AAAA,MACxB,MAAM,YAAY;AAAA,MAClB,MAAM,YAAY;AAAA,IACnB;AAAA,EACD;AAEA,MAAI,QAAQ,2BAA2B,MAAM;AAC5C,sBAAkB,MAAM;AAAA,MACvB,oBAAoB;AAAA,IACrB;AAAA,EACD;AAEA,aAAO,mCAAgB,iBAAiB;AACzC;AAEA,eAAsB,mBAErB,YACqC;AACrC,QAAM,cAAc,WAAW;AAC/B,QAAM,cAAc,mBAAmB,aAAa,CAAC,CAAC;AACtD,MAAI;AACH,UAAM,YAAY,OAAO;AACzB,WAAO;AAAA,MACN,QAAQ;AAAA,MACR,SAAS;AAAA,IACV;AAAA,EACD,SAAS,OAAO;AACf,WAAO;AAAA,MACN,QAAQ;AAAA,MACR,SAAS,MAAM;AAAA,IAChB;AAAA,EACD,UAAE;AACD,gBAAY,MAAM;AAAA,EACnB;AACD;", "names": []}