{"version": 3, "sources": ["../../../../../../../nodes/Google/Analytics/v2/actions/userActivity/UserActivity.resource.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nimport * as search from './search.operation';\n\nexport { search };\n\nexport const description: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['userActivity'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Search',\n\t\t\t\tvalue: 'search',\n\t\t\t\tdescription: 'Return user activity data',\n\t\t\t\taction: 'Search user activity data',\n\t\t\t},\n\t\t],\n\t\tdefault: 'search',\n\t},\n\t...search.description,\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,aAAwB;AAIjB,MAAM,cAAiC;AAAA,EAC7C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,cAAc;AAAA,MAC1B;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AAAA,EACA,GAAG,OAAO;AACX;", "names": []}