{"version": 3, "sources": ["../../../nodes/Yourls/UrlDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const urlOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['url'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Expand',\n\t\t\t\tvalue: 'expand',\n\t\t\t\tdescription: 'Expand a URL',\n\t\t\t\taction: 'Expand a URL',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Shorten',\n\t\t\t\tvalue: 'shorten',\n\t\t\t\tdescription: 'Shorten a URL',\n\t\t\t\taction: 'Shorten a URL',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Stats',\n\t\t\t\tvalue: 'stats',\n\t\t\t\tdescription: 'Get stats about one short URL',\n\t\t\t\taction: 'Get stats for a URL',\n\t\t\t},\n\t\t],\n\t\tdefault: 'shorten',\n\t},\n];\n\nexport const urlFields: INodeProperties[] = [\n\t/* -------------------------------------------------------------------------- */\n\t/*                                url:shorten                                 */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'URL',\n\t\tname: 'url',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['url'],\n\t\t\t\toperation: ['shorten'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription: 'The URL to shorten',\n\t},\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['url'],\n\t\t\t\toperation: ['shorten'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Keyword',\n\t\t\t\tname: 'keyword',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Title',\n\t\t\t\tname: 'title',\n\t\t\t\ttype: 'string',\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'Title for custom short URLs',\n\t\t\t},\n\t\t],\n\t},\n\t/* -------------------------------------------------------------------------- */\n\t/*                                url:expand                                  */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Short URL',\n\t\tname: 'shortUrl',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['url'],\n\t\t\t\toperation: ['expand'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription: 'The short URL to expand',\n\t},\n\n\t/* -------------------------------------------------------------------------- */\n\t/*                                url:stats                                   */\n\t/* -------------------------------------------------------------------------- */\n\t{\n\t\tdisplayName: 'Short URL',\n\t\tname: 'shortUrl',\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['url'],\n\t\t\t\toperation: ['stats'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription: 'The short URL for which to get stats',\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,gBAAmC;AAAA,EAC/C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,KAAK;AAAA,MACjB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEO,MAAM,YAA+B;AAAA;AAAA;AAAA;AAAA,EAI3C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,KAAK;AAAA,QAChB,WAAW,CAAC,SAAS;AAAA,MACtB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,KAAK;AAAA,QAChB,WAAW,CAAC,SAAS;AAAA,MACtB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,KAAK;AAAA,QAChB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,KAAK;AAAA,QAChB,WAAW,CAAC,OAAO;AAAA,MACpB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AACD;", "names": []}