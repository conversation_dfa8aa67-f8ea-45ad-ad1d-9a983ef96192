{"version": 3, "sources": ["../../../../../../nodes/MySql/v2/actions/database/upsert.operation.ts"], "sourcesContent": ["import type {\n\tIDataObject,\n\tIExecuteFunctions,\n\tINodeExecutionData,\n\tINodeProperties,\n} from 'n8n-workflow';\n\nimport { updateDisplayOptions } from '@utils/utilities';\n\nimport type { QueryRunner, QueryValues, QueryWithValues } from '../../helpers/interfaces';\nimport { AUTO_MAP, DATA_MODE } from '../../helpers/interfaces';\nimport { escapeSqlIdentifier, replaceEmptyStringsByNulls } from '../../helpers/utils';\nimport { optionsCollection } from '../common.descriptions';\n\nconst properties: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Data Mode',\n\t\tname: 'dataMode',\n\t\ttype: 'options',\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Auto-Map Input Data to Columns',\n\t\t\t\tvalue: DATA_MODE.AUTO_MAP,\n\t\t\t\tdescription: 'Use when node input properties names exactly match the table column names',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Map Each Column Below',\n\t\t\t\tvalue: DATA_MODE.MANUAL,\n\t\t\t\tdescription: 'Set the value for each destination column manually',\n\t\t\t},\n\t\t],\n\t\tdefault: AUTO_MAP,\n\t\tdescription:\n\t\t\t'Whether to map node input properties and the table data automatically or manually',\n\t},\n\t{\n\t\tdisplayName: `\n\t\tIn this mode, make sure incoming data fields are named the same as the columns in your table. If needed, use an 'Edit Fields' node before this node to change the field names.\n\t\t`,\n\t\tname: 'notice',\n\t\ttype: 'notice',\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tdataMode: [DATA_MODE.AUTO_MAP],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-wrong-for-dynamic-options\n\t\tdisplayName: 'Column to Match On',\n\t\tname: 'columnToMatchOn',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-description-wrong-for-dynamic-options\n\t\tdescription:\n\t\t\t'The column to compare when finding the rows to update. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\" target=\"_blank\">expression</a>.',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getColumns',\n\t\t\tloadOptionsDependsOn: ['schema.value', 'table.value'],\n\t\t},\n\t\tdefault: '',\n\t\thint: \"Used to find the correct row to update. Doesn't get changed. Has to be unique.\",\n\t},\n\t{\n\t\tdisplayName: 'Value of Column to Match On',\n\t\tname: 'valueToMatchOn',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\tdescription:\n\t\t\t'Rows with a value in the specified \"Column to Match On\" that corresponds to the value in this field will be updated. New rows will be created for non-matching items.',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tdataMode: [DATA_MODE.MANUAL],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Values to Send',\n\t\tname: 'valuesToSend',\n\t\tplaceholder: 'Add Value',\n\t\ttype: 'fixedCollection',\n\t\ttypeOptions: {\n\t\t\tmultipleValueButtonText: 'Add Value',\n\t\t\tmultipleValues: true,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tdataMode: [DATA_MODE.MANUAL],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Values',\n\t\t\t\tname: 'values',\n\t\t\t\tvalues: [\n\t\t\t\t\t{\n\t\t\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-wrong-for-dynamic-options\n\t\t\t\t\t\tdisplayName: 'Column',\n\t\t\t\t\t\tname: 'column',\n\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-description-wrong-for-dynamic-options\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\" target=\"_blank\">expression</a>',\n\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\tloadOptionsMethod: 'getColumnsWithoutColumnToMatchOn',\n\t\t\t\t\t\t\tloadOptionsDependsOn: ['schema.value', 'table.value'],\n\t\t\t\t\t\t},\n\t\t\t\t\t\tdefault: [],\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Value',\n\t\t\t\t\t\tname: 'value',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n\toptionsCollection,\n];\n\nconst displayOptions = {\n\tshow: {\n\t\tresource: ['database'],\n\t\toperation: ['upsert'],\n\t},\n\thide: {\n\t\ttable: [''],\n\t},\n};\n\nexport const description = updateDisplayOptions(displayOptions, properties);\n\nexport async function execute(\n\tthis: IExecuteFunctions,\n\tinputItems: INodeExecutionData[],\n\trunQueries: QueryRunner,\n\tnodeOptions: IDataObject,\n): Promise<INodeExecutionData[]> {\n\tlet returnData: INodeExecutionData[] = [];\n\n\tconst items = replaceEmptyStringsByNulls(inputItems, nodeOptions.replaceEmptyStrings as boolean);\n\n\tconst queries: QueryWithValues[] = [];\n\n\tfor (let i = 0; i < items.length; i++) {\n\t\tconst table = this.getNodeParameter('table', i, undefined, {\n\t\t\textractValue: true,\n\t\t}) as string;\n\n\t\tconst columnToMatchOn = this.getNodeParameter('columnToMatchOn', i) as string;\n\n\t\tconst dataMode = this.getNodeParameter('dataMode', i) as string;\n\n\t\tlet item: IDataObject = {};\n\n\t\tif (dataMode === DATA_MODE.AUTO_MAP) {\n\t\t\titem = items[i].json;\n\t\t}\n\n\t\tif (dataMode === DATA_MODE.MANUAL) {\n\t\t\tconst valuesToSend = (this.getNodeParameter('valuesToSend', i, []) as IDataObject)\n\t\t\t\t.values as IDataObject[];\n\n\t\t\titem = valuesToSend.reduce((acc, { column, value }) => {\n\t\t\t\tacc[column as string] = value;\n\t\t\t\treturn acc;\n\t\t\t}, {} as IDataObject);\n\n\t\t\titem[columnToMatchOn] = this.getNodeParameter('valueToMatchOn', i) as string;\n\t\t}\n\n\t\tconst onConflict = 'ON DUPLICATE KEY UPDATE';\n\n\t\tconst columns = Object.keys(item);\n\t\tconst escapedColumns = columns.map(escapeSqlIdentifier).join(', ');\n\t\tconst placeholder = `${columns.map(() => '?').join(',')}`;\n\n\t\tconst insertQuery = `INSERT INTO ${escapeSqlIdentifier(\n\t\t\ttable,\n\t\t)}(${escapedColumns}) VALUES(${placeholder})`;\n\n\t\tconst values = Object.values(item) as QueryValues;\n\n\t\tconst updateColumns = Object.keys(item).filter((column) => column !== columnToMatchOn);\n\n\t\tconst updates: string[] = [];\n\n\t\tfor (const column of updateColumns) {\n\t\t\tupdates.push(`${escapeSqlIdentifier(column)} = ?`);\n\t\t\tvalues.push(item[column] as string);\n\t\t}\n\n\t\tconst query = `${insertQuery} ${onConflict} ${updates.join(', ')}`;\n\n\t\tqueries.push({ query, values });\n\t}\n\n\treturnData = await runQueries(queries);\n\n\treturn returnData;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,uBAAqC;AAGrC,wBAAoC;AACpC,mBAAgE;AAChE,oBAAkC;AAElC,MAAM,aAAgC;AAAA,EACrC;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO,4BAAU;AAAA,QACjB,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO,4BAAU;AAAA,QACjB,aAAa;AAAA,MACd;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,aAAa;AAAA;AAAA;AAAA,IAGb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,4BAAU,QAAQ;AAAA,MAC9B;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA;AAAA,IAEC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA;AAAA,IAEV,aACC;AAAA,IACD,aAAa;AAAA,MACZ,mBAAmB;AAAA,MACnB,sBAAsB,CAAC,gBAAgB,aAAa;AAAA,IACrD;AAAA,IACA,SAAS;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aACC;AAAA,IACD,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,4BAAU,MAAM;AAAA,MAC5B;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,yBAAyB;AAAA,MACzB,gBAAgB;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,4BAAU,MAAM;AAAA,MAC5B;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,QAAQ;AAAA,UACP;AAAA;AAAA,YAEC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA;AAAA,YAEN,aACC;AAAA,YACD,aAAa;AAAA,cACZ,mBAAmB;AAAA,cACnB,sBAAsB,CAAC,gBAAgB,aAAa;AAAA,YACrD;AAAA,YACA,SAAS,CAAC;AAAA,UACX;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA;AACD;AAEA,MAAM,iBAAiB;AAAA,EACtB,MAAM;AAAA,IACL,UAAU,CAAC,UAAU;AAAA,IACrB,WAAW,CAAC,QAAQ;AAAA,EACrB;AAAA,EACA,MAAM;AAAA,IACL,OAAO,CAAC,EAAE;AAAA,EACX;AACD;AAEO,MAAM,kBAAc,uCAAqB,gBAAgB,UAAU;AAE1E,eAAsB,QAErB,YACA,YACA,aACgC;AAChC,MAAI,aAAmC,CAAC;AAExC,QAAM,YAAQ,yCAA2B,YAAY,YAAY,mBAA8B;AAE/F,QAAM,UAA6B,CAAC;AAEpC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,UAAM,QAAQ,KAAK,iBAAiB,SAAS,GAAG,QAAW;AAAA,MAC1D,cAAc;AAAA,IACf,CAAC;AAED,UAAM,kBAAkB,KAAK,iBAAiB,mBAAmB,CAAC;AAElE,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AAEpD,QAAI,OAAoB,CAAC;AAEzB,QAAI,aAAa,4BAAU,UAAU;AACpC,aAAO,MAAM,CAAC,EAAE;AAAA,IACjB;AAEA,QAAI,aAAa,4BAAU,QAAQ;AAClC,YAAM,eAAgB,KAAK,iBAAiB,gBAAgB,GAAG,CAAC,CAAC,EAC/D;AAEF,aAAO,aAAa,OAAO,CAAC,KAAK,EAAE,QAAQ,MAAM,MAAM;AACtD,YAAI,MAAgB,IAAI;AACxB,eAAO;AAAA,MACR,GAAG,CAAC,CAAgB;AAEpB,WAAK,eAAe,IAAI,KAAK,iBAAiB,kBAAkB,CAAC;AAAA,IAClE;AAEA,UAAM,aAAa;AAEnB,UAAM,UAAU,OAAO,KAAK,IAAI;AAChC,UAAM,iBAAiB,QAAQ,IAAI,gCAAmB,EAAE,KAAK,IAAI;AACjE,UAAM,cAAc,GAAG,QAAQ,IAAI,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC;AAEvD,UAAM,cAAc,mBAAe;AAAA,MAClC;AAAA,IACD,CAAC,IAAI,cAAc,YAAY,WAAW;AAE1C,UAAM,SAAS,OAAO,OAAO,IAAI;AAEjC,UAAM,gBAAgB,OAAO,KAAK,IAAI,EAAE,OAAO,CAAC,WAAW,WAAW,eAAe;AAErF,UAAM,UAAoB,CAAC;AAE3B,eAAW,UAAU,eAAe;AACnC,cAAQ,KAAK,OAAG,kCAAoB,MAAM,CAAC,MAAM;AACjD,aAAO,KAAK,KAAK,MAAM,CAAW;AAAA,IACnC;AAEA,UAAM,QAAQ,GAAG,WAAW,IAAI,UAAU,IAAI,QAAQ,KAAK,IAAI,CAAC;AAEhE,YAAQ,KAAK,EAAE,OAAO,OAAO,CAAC;AAAA,EAC/B;AAEA,eAAa,MAAM,WAAW,OAAO;AAErC,SAAO;AACR;", "names": []}