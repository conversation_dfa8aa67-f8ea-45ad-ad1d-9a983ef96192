{"version": 3, "sources": ["../../../nodes/Zoom/Zoom.node.ts"], "sourcesContent": ["import moment from 'moment-timezone';\nimport {\n\ttype IExecuteFunctions,\n\ttype IDataObject,\n\ttype ILoadOptionsFunctions,\n\ttype INodeExecutionData,\n\ttype INodePropertyOptions,\n\ttype INodeType,\n\ttype INodeTypeDescription,\n\tNodeConnectionTypes,\n} from 'n8n-workflow';\n\nimport { zoomApiRequest, zoomApiRequestAllItems } from './GenericFunctions';\nimport { meetingFields, meetingOperations } from './MeetingDescription';\n\n// import {\n// \tmeetingRegistrantOperations,\n// \tmeetingRegistrantFields,\n// } from './MeetingRegistrantDescription';\n\n// import {\n// \twebinarOperations,\n// \twebinarFields,\n// } from './WebinarDescription';\n\ninterface Settings {\n\thost_video?: boolean;\n\tparticipant_video?: boolean;\n\tpanelists_video?: boolean;\n\tcn_meeting?: boolean;\n\tin_meeting?: boolean;\n\tjoin_before_host?: boolean;\n\tmute_upon_entry?: boolean;\n\twatermark?: boolean;\n\twaiting_room?: boolean;\n\taudio?: string;\n\talternative_hosts?: string;\n\tauto_recording?: string;\n\tregistration_type?: number;\n\tapproval_type?: number;\n\tpractice_session?: boolean;\n}\n\nexport class Zoom implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Zoom',\n\t\tname: 'zoom',\n\t\tgroup: ['input'],\n\t\tversion: 1,\n\t\tdescription: 'Consume Zoom API',\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdefaults: {\n\t\t\tname: 'Zoom',\n\t\t},\n\t\ticon: 'file:zoom.svg',\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\t// create a JWT app on Zoom Marketplace\n\t\t\t\t//https://marketplace.zoom.us/develop/create\n\t\t\t\t//get the JWT token as access token\n\t\t\t\tname: 'zoomApi',\n\t\t\t\trequired: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tauthentication: ['accessToken'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\t//create a account level OAuth app\n\t\t\t\t//https://marketplace.zoom.us/develop/create\n\t\t\t\tname: 'zoomOAuth2Api',\n\t\t\t\trequired: true,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tauthentication: ['oAuth2'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Authentication',\n\t\t\t\tname: 'authentication',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Access Token',\n\t\t\t\t\t\tvalue: 'accessToken',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'OAuth2',\n\t\t\t\t\t\tvalue: 'oAuth2',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'accessToken',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Meeting',\n\t\t\t\t\t\tvalue: 'meeting',\n\t\t\t\t\t},\n\t\t\t\t\t// {\n\t\t\t\t\t// \tname: 'Meeting Registrant',\n\t\t\t\t\t// \tvalue: 'meetingRegistrant'\n\t\t\t\t\t// },\n\t\t\t\t\t// {\n\t\t\t\t\t// \tname: 'Webinar',\n\t\t\t\t\t// \tvalue: 'webinar'\n\t\t\t\t\t// }\n\t\t\t\t],\n\t\t\t\tdefault: 'meeting',\n\t\t\t},\n\t\t\t//MEETINGS\n\t\t\t...meetingOperations,\n\t\t\t...meetingFields,\n\n\t\t\t// \t//MEETING REGISTRANTS\n\t\t\t// \t...meetingRegistrantOperations,\n\t\t\t// \t...meetingRegistrantFields,\n\n\t\t\t// \t//WEBINARS\n\t\t\t// \t...webinarOperations,\n\t\t\t// \t...webinarFields,\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\t// Get all the timezones to display them to user so that they can select them easily\n\t\t\tasync getTimezones(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tfor (const timezone of moment.tz.names()) {\n\t\t\t\t\tconst timezoneName = timezone;\n\t\t\t\t\tconst timezoneId = timezone;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: timezoneName,\n\t\t\t\t\t\tvalue: timezoneId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: INodeExecutionData[] = [];\n\t\tlet qs: IDataObject = {};\n\t\tlet responseData;\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\n\t\tfor (let i = 0; i < items.length; i++) {\n\t\t\ttry {\n\t\t\t\tqs = {};\n\t\t\t\t//https://marketplace.zoom.us/docs/api-reference/zoom-api/\n\t\t\t\tif (resource === 'meeting') {\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\t//https://marketplace.zoom.us/docs/api-reference/zoom-api/meetings/meeting\n\t\t\t\t\t\tconst meetingId = this.getNodeParameter('meetingId', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tif (additionalFields.showPreviousOccurrences) {\n\t\t\t\t\t\t\tqs.show_previous_occurrences = additionalFields.showPreviousOccurrences as boolean;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.occurrenceId) {\n\t\t\t\t\t\t\tqs.occurrence_id = additionalFields.occurrenceId as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zoomApiRequest.call(this, 'GET', `/meetings/${meetingId}`, {}, qs);\n\t\t\t\t\t}\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\t//https://marketplace.zoom.us/docs/api-reference/zoom-api/meetings/meetings\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\n\t\t\t\t\t\tconst filters = this.getNodeParameter('filters', i);\n\t\t\t\t\t\tif (filters.type) {\n\t\t\t\t\t\t\tqs.type = filters.type as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await zoomApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'meetings',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/users/me/meetings',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tqs.page_size = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = await zoomApiRequest.call(this, 'GET', '/users/me/meetings', {}, qs);\n\t\t\t\t\t\t\tresponseData = responseData.meetings;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\t//https://marketplace.zoom.us/docs/api-reference/zoom-api/meetings/meetingdelete\n\t\t\t\t\t\tconst meetingId = this.getNodeParameter('meetingId', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tif (additionalFields.scheduleForReminder) {\n\t\t\t\t\t\t\tqs.schedule_for_reminder = additionalFields.scheduleForReminder as boolean;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.occurrenceId) {\n\t\t\t\t\t\t\tqs.occurrence_id = additionalFields.occurrenceId;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zoomApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'DELETE',\n\t\t\t\t\t\t\t`/meetings/${meetingId}`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t\tresponseData = { success: true };\n\t\t\t\t\t}\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\t//https://marketplace.zoom.us/docs/api-reference/zoom-api/meetings/meetingcreate\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\n\t\t\t\t\t\tconst body: IDataObject = {};\n\n\t\t\t\t\t\tif (additionalFields.settings) {\n\t\t\t\t\t\t\tconst settingValues: Settings = {};\n\t\t\t\t\t\t\tconst settings = additionalFields.settings as IDataObject;\n\n\t\t\t\t\t\t\tif (settings.cnMeeting) {\n\t\t\t\t\t\t\t\tsettingValues.cn_meeting = settings.cnMeeting as boolean;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.inMeeting) {\n\t\t\t\t\t\t\t\tsettingValues.in_meeting = settings.inMeeting as boolean;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.joinBeforeHost) {\n\t\t\t\t\t\t\t\tsettingValues.join_before_host = settings.joinBeforeHost as boolean;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.muteUponEntry) {\n\t\t\t\t\t\t\t\tsettingValues.mute_upon_entry = settings.muteUponEntry as boolean;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.watermark) {\n\t\t\t\t\t\t\t\tsettingValues.watermark = settings.watermark as boolean;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.audio) {\n\t\t\t\t\t\t\t\tsettingValues.audio = settings.audio as string;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.alternativeHosts) {\n\t\t\t\t\t\t\t\tsettingValues.alternative_hosts = settings.alternativeHosts as string;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.participantVideo) {\n\t\t\t\t\t\t\t\tsettingValues.participant_video = settings.participantVideo as boolean;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.hostVideo) {\n\t\t\t\t\t\t\t\tsettingValues.host_video = settings.hostVideo as boolean;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.autoRecording) {\n\t\t\t\t\t\t\t\tsettingValues.auto_recording = settings.autoRecording as string;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.registrationType) {\n\t\t\t\t\t\t\t\tsettingValues.registration_type = settings.registrationType as number;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tbody.settings = settingValues;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tbody.topic = this.getNodeParameter('topic', i) as string;\n\n\t\t\t\t\t\tif (additionalFields.type) {\n\t\t\t\t\t\t\tbody.type = additionalFields.type as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.startTime) {\n\t\t\t\t\t\t\tif (additionalFields.timeZone) {\n\t\t\t\t\t\t\t\tbody.start_time = moment(additionalFields.startTime as string).format(\n\t\t\t\t\t\t\t\t\t'YYYY-MM-DDTHH:mm:ss',\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// if none timezone it's defined used n8n timezone\n\t\t\t\t\t\t\t\tbody.start_time = moment\n\t\t\t\t\t\t\t\t\t.tz(additionalFields.startTime as string, this.getTimezone())\n\t\t\t\t\t\t\t\t\t.format();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.duration) {\n\t\t\t\t\t\t\tbody.duration = additionalFields.duration as number;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.scheduleFor) {\n\t\t\t\t\t\t\tbody.schedule_for = additionalFields.scheduleFor as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.timeZone) {\n\t\t\t\t\t\t\tbody.timezone = additionalFields.timeZone as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.password) {\n\t\t\t\t\t\t\tbody.password = additionalFields.password as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.agenda) {\n\t\t\t\t\t\t\tbody.agenda = additionalFields.agenda as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zoomApiRequest.call(this, 'POST', '/users/me/meetings', body, qs);\n\t\t\t\t\t}\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\t//https://marketplace.zoom.us/docs/api-reference/zoom-api/meetings/meetingupdate\n\t\t\t\t\t\tconst meetingId = this.getNodeParameter('meetingId', i) as string;\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\tconst body: IDataObject = {};\n\n\t\t\t\t\t\tif (updateFields.settings) {\n\t\t\t\t\t\t\tconst settingValues: Settings = {};\n\t\t\t\t\t\t\tconst settings = updateFields.settings as IDataObject;\n\n\t\t\t\t\t\t\tif (settings.cnMeeting) {\n\t\t\t\t\t\t\t\tsettingValues.cn_meeting = settings.cnMeeting as boolean;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.inMeeting) {\n\t\t\t\t\t\t\t\tsettingValues.in_meeting = settings.inMeeting as boolean;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.joinBeforeHost) {\n\t\t\t\t\t\t\t\tsettingValues.join_before_host = settings.joinBeforeHost as boolean;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.muteUponEntry) {\n\t\t\t\t\t\t\t\tsettingValues.mute_upon_entry = settings.muteUponEntry as boolean;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.watermark) {\n\t\t\t\t\t\t\t\tsettingValues.watermark = settings.watermark as boolean;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.audio) {\n\t\t\t\t\t\t\t\tsettingValues.audio = settings.audio as string;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.alternativeHosts) {\n\t\t\t\t\t\t\t\tsettingValues.alternative_hosts = settings.alternativeHosts as string;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.participantVideo) {\n\t\t\t\t\t\t\t\tsettingValues.participant_video = settings.participantVideo as boolean;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.hostVideo) {\n\t\t\t\t\t\t\t\tsettingValues.host_video = settings.hostVideo as boolean;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.autoRecording) {\n\t\t\t\t\t\t\t\tsettingValues.auto_recording = settings.autoRecording as string;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (settings.registrationType) {\n\t\t\t\t\t\t\t\tsettingValues.registration_type = settings.registrationType as number;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tbody.settings = settingValues;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.topic) {\n\t\t\t\t\t\t\tbody.topic = updateFields.topic as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.type) {\n\t\t\t\t\t\t\tbody.type = updateFields.type as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.startTime) {\n\t\t\t\t\t\t\tbody.start_time = updateFields.startTime as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.duration) {\n\t\t\t\t\t\t\tbody.duration = updateFields.duration as number;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.scheduleFor) {\n\t\t\t\t\t\t\tbody.schedule_for = updateFields.scheduleFor as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.timeZone) {\n\t\t\t\t\t\t\tbody.timezone = updateFields.timeZone as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.password) {\n\t\t\t\t\t\t\tbody.password = updateFields.password as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.agenda) {\n\t\t\t\t\t\t\tbody.agenda = updateFields.agenda as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await zoomApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'PATCH',\n\t\t\t\t\t\t\t`/meetings/${meetingId}`,\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tresponseData = { success: true };\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// if (resource === 'meetingRegistrant') {\n\t\t\t\t// \tif (operation === 'create') {\n\t\t\t\t// \t\t//https://marketplace.zoom.us/docs/api-reference/zoom-api/meetings/meetingregistrantcreate\n\t\t\t\t// \t\tconst meetingId = this.getNodeParameter('meetingId', i) as string;\n\t\t\t\t// \t\tconst emailId = this.getNodeParameter('email', i) as string;\n\t\t\t\t// \t\tbody.email = emailId;\n\t\t\t\t// \t\tconst firstName = this.getNodeParameter('firstName', i) as string;\n\t\t\t\t// \t\tbody.first_name = firstName;\n\t\t\t\t// \t\tconst additionalFields = this.getNodeParameter(\n\t\t\t\t// \t\t\t'additionalFields',\n\t\t\t\t// \t\t\ti\n\t\t\t\t// \t\t) as IDataObject;\n\t\t\t\t// \t\tif (additionalFields.occurrenceId) {\n\t\t\t\t// \t\t\tqs.occurrence_ids = additionalFields.occurrenceId as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.lastName) {\n\t\t\t\t// \t\t\tbody.last_name = additionalFields.lastName as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.address) {\n\t\t\t\t// \t\t\tbody.address = additionalFields.address as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.city) {\n\t\t\t\t// \t\t\tbody.city = additionalFields.city as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.state) {\n\t\t\t\t// \t\t\tbody.state = additionalFields.state as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.country) {\n\t\t\t\t// \t\t\tbody.country = additionalFields.country as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.zip) {\n\t\t\t\t// \t\t\tbody.zip = additionalFields.zip as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.phone) {\n\t\t\t\t// \t\t\tbody.phone = additionalFields.phone as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.comments) {\n\t\t\t\t// \t\t\tbody.comments = additionalFields.comments as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.org) {\n\t\t\t\t// \t\t\tbody.org = additionalFields.org as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.jobTitle) {\n\t\t\t\t// \t\t\tbody.job_title = additionalFields.jobTitle as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.purchasingTimeFrame) {\n\t\t\t\t// \t\t\tbody.purchasing_time_frame = additionalFields.purchasingTimeFrame as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.roleInPurchaseProcess) {\n\t\t\t\t// \t\t\tbody.role_in_purchase_process = additionalFields.roleInPurchaseProcess as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tresponseData = await zoomApiRequest.call(\n\t\t\t\t// \t\t\tthis,\n\t\t\t\t// \t\t\t'POST',\n\t\t\t\t// \t\t\t`/meetings/${meetingId}/registrants`,\n\t\t\t\t// \t\t\tbody,\n\t\t\t\t// \t\t\tqs\n\t\t\t\t// \t\t);\n\t\t\t\t// \t}\n\t\t\t\t// \tif (operation === 'getAll') {\n\t\t\t\t// \t\t//https://marketplace.zoom.us/docs/api-reference/zoom-api/meetings/meetingregistrants\n\t\t\t\t// \t\tconst meetingId = this.getNodeParameter('meetingId', i) as string;\n\t\t\t\t// \t\tconst additionalFields = this.getNodeParameter(\n\t\t\t\t// \t\t\t'additionalFields',\n\t\t\t\t// \t\t\ti\n\t\t\t\t// \t\t) as IDataObject;\n\t\t\t\t// \t\tif (additionalFields.occurrenceId) {\n\t\t\t\t// \t\t\tqs.occurrence_id = additionalFields.occurrenceId as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.status) {\n\t\t\t\t// \t\t\tqs.status = additionalFields.status as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t// \t\tif (returnAll) {\n\t\t\t\t// \t\t\tresponseData = await zoomApiRequestAllItems.call(this, 'results', 'GET', `/meetings/${meetingId}/registrants`, {}, qs);\n\t\t\t\t// \t\t} else {\n\t\t\t\t// \t\t\tqs.page_size = this.getNodeParameter('limit', i);\n\t\t\t\t// \t\t\tresponseData = await zoomApiRequest.call(this, 'GET', `/meetings/${meetingId}/registrants`, {}, qs);\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t}\n\t\t\t\t// \tif (operation === 'update') {\n\t\t\t\t// \t\t//https://marketplace.zoom.us/docs/api-reference/zoom-api/meetings/meetingregistrantstatus\n\t\t\t\t// \t\tconst meetingId = this.getNodeParameter('meetingId', i) as string;\n\t\t\t\t// \t\tconst additionalFields = this.getNodeParameter(\n\t\t\t\t// \t\t\t'additionalFields',\n\t\t\t\t// \t\t\ti\n\t\t\t\t// \t\t) as IDataObject;\n\t\t\t\t// \t\tif (additionalFields.occurrenceId) {\n\t\t\t\t// \t\t\tqs.occurrence_id = additionalFields.occurrenceId as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.action) {\n\t\t\t\t// \t\t\tbody.action = additionalFields.action as string;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tresponseData = await zoomApiRequest.call(\n\t\t\t\t// \t\t\tthis,\n\t\t\t\t// \t\t\t'PUT',\n\t\t\t\t// \t\t\t`/meetings/${meetingId}/registrants/status`,\n\t\t\t\t// \t\t\tbody,\n\t\t\t\t// \t\t\tqs\n\t\t\t\t// \t\t);\n\t\t\t\t// \t}\n\t\t\t\t// }\n\t\t\t\t// if (resource === 'webinar') {\n\t\t\t\t// \tif (operation === 'create') {\n\t\t\t\t// \t\t//https://marketplace.zoom.us/docs/api-reference/zoom-api/webinars/webinarcreate\n\t\t\t\t// \t\tconst userId = this.getNodeParameter('userId', i) as string;\n\t\t\t\t// \t\tconst additionalFields = this.getNodeParameter(\n\t\t\t\t// \t\t\t'additionalFields',\n\t\t\t\t// \t\t\ti\n\t\t\t\t// \t\t) as IDataObject;\n\t\t\t\t// \t\tconst settings: Settings = {};\n\n\t\t\t\t// \t\tif (additionalFields.audio) {\n\t\t\t\t// \t\t\tsettings.audio = additionalFields.audio as string;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.alternativeHosts) {\n\t\t\t\t// \t\t\tsettings.alternative_hosts = additionalFields.alternativeHosts as string;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.panelistsVideo) {\n\t\t\t\t// \t\t\tsettings.panelists_video = additionalFields.panelistsVideo as boolean;\n\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.hostVideo) {\n\t\t\t\t// \t\t\tsettings.host_video = additionalFields.hostVideo as boolean;\n\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.practiceSession) {\n\t\t\t\t// \t\t\tsettings.practice_session = additionalFields.practiceSession as boolean;\n\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.autoRecording) {\n\t\t\t\t// \t\t\tsettings.auto_recording = additionalFields.autoRecording as string;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.registrationType) {\n\t\t\t\t// \t\t\tsettings.registration_type = additionalFields.registrationType as number;\n\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.approvalType) {\n\t\t\t\t// \t\t\tsettings.approval_type = additionalFields.approvalType as number;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tbody = {\n\t\t\t\t// \t\t\tsettings,\n\t\t\t\t// \t\t};\n\n\t\t\t\t// \t\tif (additionalFields.topic) {\n\t\t\t\t// \t\t\tbody.topic = additionalFields.topic as string;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.type) {\n\t\t\t\t// \t\t\tbody.type = additionalFields.type as string;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.startTime) {\n\t\t\t\t// \t\t\tbody.start_time = additionalFields.startTime as string;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.duration) {\n\t\t\t\t// \t\t\tbody.duration = additionalFields.duration as number;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.timeZone) {\n\t\t\t\t// \t\t\tbody.timezone = additionalFields.timeZone as string;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.password) {\n\t\t\t\t// \t\t\tbody.password = additionalFields.password as string;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.agenda) {\n\t\t\t\t// \t\t\tbody.agenda = additionalFields.agenda as string;\n\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tresponseData = await zoomApiRequest.call(\n\t\t\t\t// \t\t\tthis,\n\t\t\t\t// \t\t\t'POST',\n\t\t\t\t// \t\t\t`/users/${userId}/webinars`,\n\t\t\t\t// \t\t\tbody,\n\t\t\t\t// \t\t\tqs\n\t\t\t\t// \t\t);\n\t\t\t\t// \t}\n\t\t\t\t// \tif (operation === 'get') {\n\t\t\t\t// \t\t//https://marketplace.zoom.us/docs/api-reference/zoom-api/webinars/webinar\n\t\t\t\t// \t\tconst webinarId = this.getNodeParameter('webinarId', i) as string;\n\n\t\t\t\t// \t\tconst additionalFields = this.getNodeParameter(\n\t\t\t\t// \t\t\t'additionalFields',\n\t\t\t\t// \t\t\ti\n\t\t\t\t// \t\t) as IDataObject;\n\t\t\t\t// \t\tif (additionalFields.showPreviousOccurrences) {\n\t\t\t\t// \t\t\tqs.show_previous_occurrences = additionalFields.showPreviousOccurrences as boolean;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.occurrenceId) {\n\t\t\t\t// \t\t\tqs.occurrence_id = additionalFields.occurrenceId as string;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tresponseData = await zoomApiRequest.call(\n\t\t\t\t// \t\t\tthis,\n\t\t\t\t// \t\t\t'GET',\n\t\t\t\t// \t\t\t`/webinars/${webinarId}`,\n\t\t\t\t// \t\t\t{},\n\t\t\t\t// \t\t\tqs\n\t\t\t\t// \t\t);\n\t\t\t\t// \t}\n\t\t\t\t// \tif (operation === 'getAll') {\n\t\t\t\t// \t\t//https://marketplace.zoom.us/docs/api-reference/zoom-api/webinars/webinars\n\t\t\t\t// \t\tconst userId = this.getNodeParameter('userId', i) as string;\n\t\t\t\t// \t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t// \t\tif (returnAll) {\n\t\t\t\t// \t\t\tresponseData = await zoomApiRequestAllItems.call(this, 'results', 'GET', `/users/${userId}/webinars`, {}, qs);\n\t\t\t\t// \t\t} else {\n\t\t\t\t// \t\t\tqs.page_size = this.getNodeParameter('limit', i);\n\t\t\t\t// \t\t\tresponseData = await zoomApiRequest.call(this, 'GET', `/users/${userId}/webinars`, {}, qs);\n\n\t\t\t\t// \t\t}\n\t\t\t\t// \t}\n\t\t\t\t// \tif (operation === 'delete') {\n\t\t\t\t// \t\t//https://marketplace.zoom.us/docs/api-reference/zoom-api/webinars/webinardelete\n\t\t\t\t// \t\tconst webinarId = this.getNodeParameter('webinarId', i) as string;\n\t\t\t\t// \t\tconst additionalFields = this.getNodeParameter(\n\t\t\t\t// \t\t\t'additionalFields',\n\t\t\t\t// \t\t\ti\n\t\t\t\t// \t\t) as IDataObject;\n\n\t\t\t\t// \t\tif (additionalFields.occurrenceId) {\n\t\t\t\t// \t\t\tqs.occurrence_id = additionalFields.occurrenceId;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tresponseData = await zoomApiRequest.call(\n\t\t\t\t// \t\t\tthis,\n\t\t\t\t// \t\t\t'DELETE',\n\t\t\t\t// \t\t\t`/webinars/${webinarId}`,\n\t\t\t\t// \t\t\t{},\n\t\t\t\t// \t\t\tqs\n\t\t\t\t// \t\t);\n\t\t\t\t// \t\tresponseData = { success: true };\n\t\t\t\t// \t}\n\t\t\t\t// \tif (operation === 'update') {\n\t\t\t\t// \t\t//https://marketplace.zoom.us/docs/api-reference/zoom-api/webinars/webinarupdate\n\t\t\t\t// \t\tconst webinarId = this.getNodeParameter('webinarId', i) as string;\n\t\t\t\t// \t\tconst additionalFields = this.getNodeParameter(\n\t\t\t\t// \t\t\t'additionalFields',\n\t\t\t\t// \t\t\ti\n\t\t\t\t// \t\t) as IDataObject;\n\t\t\t\t// \t\tif (additionalFields.occurrenceId) {\n\t\t\t\t// \t\t\tqs.occurrence_id = additionalFields.occurrenceId as string;\n\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tconst settings: Settings = {};\n\t\t\t\t// \t\tif (additionalFields.audio) {\n\t\t\t\t// \t\t\tsettings.audio = additionalFields.audio as string;\n\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.alternativeHosts) {\n\t\t\t\t// \t\t\tsettings.alternative_hosts = additionalFields.alternativeHosts as string;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.panelistsVideo) {\n\t\t\t\t// \t\t\tsettings.panelists_video = additionalFields.panelistsVideo as boolean;\n\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.hostVideo) {\n\t\t\t\t// \t\t\tsettings.host_video = additionalFields.hostVideo as boolean;\n\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.practiceSession) {\n\t\t\t\t// \t\t\tsettings.practice_session = additionalFields.practiceSession as boolean;\n\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.autoRecording) {\n\t\t\t\t// \t\t\tsettings.auto_recording = additionalFields.autoRecording as string;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.registrationType) {\n\t\t\t\t// \t\t\tsettings.registration_type = additionalFields.registrationType as number;\n\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (additionalFields.approvalType) {\n\t\t\t\t// \t\t\tsettings.approval_type = additionalFields.approvalType as number;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tbody = {\n\t\t\t\t// \t\t\tsettings,\n\t\t\t\t// \t\t};\n\n\t\t\t\t// \t\tif (additionalFields.topic) {\n\t\t\t\t// \t\t\tbody.topic = additionalFields.topic as string;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.type) {\n\t\t\t\t// \t\t\tbody.type = additionalFields.type as string;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.startTime) {\n\t\t\t\t// \t\t\tbody.start_time = additionalFields.startTime as string;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.duration) {\n\t\t\t\t// \t\t\tbody.duration = additionalFields.duration as number;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.timeZone) {\n\t\t\t\t// \t\t\tbody.timezone = additionalFields.timeZone as string;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.password) {\n\t\t\t\t// \t\t\tbody.password = additionalFields.password as string;\n\n\t\t\t\t// \t\t}\n\n\t\t\t\t// \t\tif (additionalFields.agenda) {\n\t\t\t\t// \t\t\tbody.agenda = additionalFields.agenda as string;\n\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tresponseData = await zoomApiRequest.call(\n\t\t\t\t// \t\t\tthis,\n\t\t\t\t// \t\t\t'PATCH',\n\t\t\t\t// \t\t\t`webinars/${webinarId}`,\n\t\t\t\t// \t\t\tbody,\n\t\t\t\t// \t\t\tqs\n\t\t\t\t// \t\t);\n\t\t\t\t// \t}\n\t\t\t\t// }\n\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\tthis.helpers.returnJsonArray(responseData as IDataObject),\n\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t);\n\t\t\t\treturnData.push(...executionData);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\tconst executionErrorData = {\n\t\t\t\t\t\tjson: {} as IDataObject,\n\t\t\t\t\t\terror: error.message,\n\t\t\t\t\t\titemIndex: i,\n\t\t\t\t\t};\n\t\t\t\t\treturnData.push(executionErrorData as INodeExecutionData);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6BAAmB;AACnB,0BASO;AAEP,8BAAuD;AACvD,gCAAiD;AA8B1C,MAAM,KAA0B;AAAA,EAAhC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO,CAAC,OAAO;AAAA,MACf,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,MAAM;AAAA,MACN,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA;AAAA;AAAA;AAAA,UAIC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,gBAAgB,CAAC,aAAa;AAAA,YAC/B;AAAA,UACD;AAAA,QACD;AAAA,QACA;AAAA;AAAA;AAAA,UAGC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,gBAAgB,CAAC,QAAQ;AAAA,YAC1B;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UASD;AAAA,UACA,SAAS;AAAA,QACV;AAAA;AAAA,QAEA,GAAG;AAAA,QACH,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASJ;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA;AAAA,QAEZ,MAAM,eAA2E;AAChF,gBAAM,aAAqC,CAAC;AAC5C,qBAAW,YAAY,uBAAAA,QAAO,GAAG,MAAM,GAAG;AACzC,kBAAM,eAAe;AACrB,kBAAM,aAAa;AACnB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAAmC,CAAC;AAC1C,QAAI,KAAkB,CAAC;AACvB,QAAI;AACJ,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,UAAI;AACH,aAAK,CAAC;AAEN,YAAI,aAAa,WAAW;AAC3B,cAAI,cAAc,OAAO;AAExB,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,gBAAI,iBAAiB,yBAAyB;AAC7C,iBAAG,4BAA4B,iBAAiB;AAAA,YACjD;AAEA,gBAAI,iBAAiB,cAAc;AAClC,iBAAG,gBAAgB,iBAAiB;AAAA,YACrC;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,aAAa,SAAS,IAAI,CAAC,GAAG,EAAE;AAAA,UACvF;AACA,cAAI,cAAc,UAAU;AAE3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,gBAAI,QAAQ,MAAM;AACjB,iBAAG,OAAO,QAAQ;AAAA,YACnB;AAEA,gBAAI,WAAW;AACd,6BAAe,MAAM,+CAAuB;AAAA,gBAC3C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,iBAAG,YAAY,KAAK,iBAAiB,SAAS,CAAC;AAC/C,6BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,sBAAsB,CAAC,GAAG,EAAE;AAClF,6BAAe,aAAa;AAAA,YAC7B;AAAA,UACD;AACA,cAAI,cAAc,UAAU;AAE3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,gBAAI,iBAAiB,qBAAqB;AACzC,iBAAG,wBAAwB,iBAAiB;AAAA,YAC7C;AAEA,gBAAI,iBAAiB,cAAc;AAClC,iBAAG,gBAAgB,iBAAiB;AAAA,YACrC;AAEA,2BAAe,MAAM,uCAAe;AAAA,cACnC;AAAA,cACA;AAAA,cACA,aAAa,SAAS;AAAA,cACtB,CAAC;AAAA,cACD;AAAA,YACD;AACA,2BAAe,EAAE,SAAS,KAAK;AAAA,UAChC;AACA,cAAI,cAAc,UAAU;AAE3B,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AAEpE,kBAAM,OAAoB,CAAC;AAE3B,gBAAI,iBAAiB,UAAU;AAC9B,oBAAM,gBAA0B,CAAC;AACjC,oBAAM,WAAW,iBAAiB;AAElC,kBAAI,SAAS,WAAW;AACvB,8BAAc,aAAa,SAAS;AAAA,cACrC;AAEA,kBAAI,SAAS,WAAW;AACvB,8BAAc,aAAa,SAAS;AAAA,cACrC;AAEA,kBAAI,SAAS,gBAAgB;AAC5B,8BAAc,mBAAmB,SAAS;AAAA,cAC3C;AAEA,kBAAI,SAAS,eAAe;AAC3B,8BAAc,kBAAkB,SAAS;AAAA,cAC1C;AAEA,kBAAI,SAAS,WAAW;AACvB,8BAAc,YAAY,SAAS;AAAA,cACpC;AAEA,kBAAI,SAAS,OAAO;AACnB,8BAAc,QAAQ,SAAS;AAAA,cAChC;AAEA,kBAAI,SAAS,kBAAkB;AAC9B,8BAAc,oBAAoB,SAAS;AAAA,cAC5C;AAEA,kBAAI,SAAS,kBAAkB;AAC9B,8BAAc,oBAAoB,SAAS;AAAA,cAC5C;AAEA,kBAAI,SAAS,WAAW;AACvB,8BAAc,aAAa,SAAS;AAAA,cACrC;AAEA,kBAAI,SAAS,eAAe;AAC3B,8BAAc,iBAAiB,SAAS;AAAA,cACzC;AAEA,kBAAI,SAAS,kBAAkB;AAC9B,8BAAc,oBAAoB,SAAS;AAAA,cAC5C;AAEA,mBAAK,WAAW;AAAA,YACjB;AAEA,iBAAK,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAE7C,gBAAI,iBAAiB,MAAM;AAC1B,mBAAK,OAAO,iBAAiB;AAAA,YAC9B;AAEA,gBAAI,iBAAiB,WAAW;AAC/B,kBAAI,iBAAiB,UAAU;AAC9B,qBAAK,iBAAa,uBAAAA,SAAO,iBAAiB,SAAmB,EAAE;AAAA,kBAC9D;AAAA,gBACD;AAAA,cACD,OAAO;AAEN,qBAAK,aAAa,uBAAAA,QAChB,GAAG,iBAAiB,WAAqB,KAAK,YAAY,CAAC,EAC3D,OAAO;AAAA,cACV;AAAA,YACD;AAEA,gBAAI,iBAAiB,UAAU;AAC9B,mBAAK,WAAW,iBAAiB;AAAA,YAClC;AAEA,gBAAI,iBAAiB,aAAa;AACjC,mBAAK,eAAe,iBAAiB;AAAA,YACtC;AAEA,gBAAI,iBAAiB,UAAU;AAC9B,mBAAK,WAAW,iBAAiB;AAAA,YAClC;AAEA,gBAAI,iBAAiB,UAAU;AAC9B,mBAAK,WAAW,iBAAiB;AAAA,YAClC;AAEA,gBAAI,iBAAiB,QAAQ;AAC5B,mBAAK,SAAS,iBAAiB;AAAA,YAChC;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,sBAAsB,MAAM,EAAE;AAAA,UACtF;AACA,cAAI,cAAc,UAAU;AAE3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,kBAAM,OAAoB,CAAC;AAE3B,gBAAI,aAAa,UAAU;AAC1B,oBAAM,gBAA0B,CAAC;AACjC,oBAAM,WAAW,aAAa;AAE9B,kBAAI,SAAS,WAAW;AACvB,8BAAc,aAAa,SAAS;AAAA,cACrC;AAEA,kBAAI,SAAS,WAAW;AACvB,8BAAc,aAAa,SAAS;AAAA,cACrC;AAEA,kBAAI,SAAS,gBAAgB;AAC5B,8BAAc,mBAAmB,SAAS;AAAA,cAC3C;AAEA,kBAAI,SAAS,eAAe;AAC3B,8BAAc,kBAAkB,SAAS;AAAA,cAC1C;AAEA,kBAAI,SAAS,WAAW;AACvB,8BAAc,YAAY,SAAS;AAAA,cACpC;AAEA,kBAAI,SAAS,OAAO;AACnB,8BAAc,QAAQ,SAAS;AAAA,cAChC;AAEA,kBAAI,SAAS,kBAAkB;AAC9B,8BAAc,oBAAoB,SAAS;AAAA,cAC5C;AAEA,kBAAI,SAAS,kBAAkB;AAC9B,8BAAc,oBAAoB,SAAS;AAAA,cAC5C;AAEA,kBAAI,SAAS,WAAW;AACvB,8BAAc,aAAa,SAAS;AAAA,cACrC;AAEA,kBAAI,SAAS,eAAe;AAC3B,8BAAc,iBAAiB,SAAS;AAAA,cACzC;AAEA,kBAAI,SAAS,kBAAkB;AAC9B,8BAAc,oBAAoB,SAAS;AAAA,cAC5C;AAEA,mBAAK,WAAW;AAAA,YACjB;AAEA,gBAAI,aAAa,OAAO;AACvB,mBAAK,QAAQ,aAAa;AAAA,YAC3B;AAEA,gBAAI,aAAa,MAAM;AACtB,mBAAK,OAAO,aAAa;AAAA,YAC1B;AAEA,gBAAI,aAAa,WAAW;AAC3B,mBAAK,aAAa,aAAa;AAAA,YAChC;AAEA,gBAAI,aAAa,UAAU;AAC1B,mBAAK,WAAW,aAAa;AAAA,YAC9B;AAEA,gBAAI,aAAa,aAAa;AAC7B,mBAAK,eAAe,aAAa;AAAA,YAClC;AAEA,gBAAI,aAAa,UAAU;AAC1B,mBAAK,WAAW,aAAa;AAAA,YAC9B;AAEA,gBAAI,aAAa,UAAU;AAC1B,mBAAK,WAAW,aAAa;AAAA,YAC9B;AAEA,gBAAI,aAAa,QAAQ;AACxB,mBAAK,SAAS,aAAa;AAAA,YAC5B;AAEA,2BAAe,MAAM,uCAAe;AAAA,cACnC;AAAA,cACA;AAAA,cACA,aAAa,SAAS;AAAA,cACtB;AAAA,cACA;AAAA,YACD;AAEA,2BAAe,EAAE,SAAS,KAAK;AAAA,UAChC;AAAA,QACD;AA+VA,cAAM,gBAAgB,KAAK,QAAQ;AAAA,UAClC,KAAK,QAAQ,gBAAgB,YAA2B;AAAA,UACxD,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,QACzB;AACA,mBAAW,KAAK,GAAG,aAAa;AAAA,MACjC,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,gBAAM,qBAAqB;AAAA,YAC1B,MAAM,CAAC;AAAA,YACP,OAAO,MAAM;AAAA,YACb,WAAW;AAAA,UACZ;AACA,qBAAW,KAAK,kBAAwC;AACxD;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AAEA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": ["moment"]}