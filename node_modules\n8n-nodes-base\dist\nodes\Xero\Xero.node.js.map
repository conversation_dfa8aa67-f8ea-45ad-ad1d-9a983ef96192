{"version": 3, "sources": ["../../../nodes/Xero/Xero.node.ts"], "sourcesContent": ["import {\n\ttype IExecuteFunctions,\n\ttype IDataObject,\n\ttype ILoadOptionsFunctions,\n\ttype INodeExecutionData,\n\ttype INodePropertyOptions,\n\ttype INodeType,\n\ttype INodeTypeDescription,\n\ttype JsonObject,\n\tNodeConnectionTypes,\n} from 'n8n-workflow';\n\nimport { contactFields, contactOperations } from './ContactDescription';\nimport { xeroApiRequest, xeroApiRequestAllItems } from './GenericFunctions';\nimport type { IAddress, IContact, IPhone } from './IContactInterface';\nimport { invoiceFields, invoiceOperations } from './InvoiceDescription';\nimport type { IInvoice, ILineItem } from './InvoiceInterface';\n\nexport class Xero implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Xero',\n\t\tname: 'xero',\n\t\ticon: 'file:xero.svg',\n\t\tgroup: ['output'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume Xero API',\n\t\tdefaults: {\n\t\t\tname: 'Xero',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'xeroOAuth2Api',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Contact',\n\t\t\t\t\t\tvalue: 'contact',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Invoice',\n\t\t\t\t\t\tvalue: 'invoice',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'invoice',\n\t\t\t},\n\t\t\t// CONTACT\n\t\t\t...contactOperations,\n\t\t\t...contactFields,\n\t\t\t// INVOICE\n\t\t\t...invoiceOperations,\n\t\t\t...invoiceFields,\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\t// Get all the item codes to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getItemCodes(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst organizationId = this.getCurrentNodeParameter('organizationId');\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst { Items: items } = await xeroApiRequest.call(this, 'GET', '/items', {\n\t\t\t\t\torganizationId,\n\t\t\t\t});\n\t\t\t\tfor (const item of items) {\n\t\t\t\t\tconst itemName = item.Description;\n\t\t\t\t\tconst itemId = item.Code;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: itemName,\n\t\t\t\t\t\tvalue: itemId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the account codes to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getAccountCodes(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst organizationId = this.getCurrentNodeParameter('organizationId');\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst { Accounts: accounts } = await xeroApiRequest.call(this, 'GET', '/Accounts', {\n\t\t\t\t\torganizationId,\n\t\t\t\t});\n\t\t\t\tfor (const account of accounts) {\n\t\t\t\t\tconst accountName = account.Name;\n\t\t\t\t\tconst accountId = account.Code;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: accountName,\n\t\t\t\t\t\tvalue: accountId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the tenants to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getTenants(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst tenants = await xeroApiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t'',\n\t\t\t\t\t{},\n\t\t\t\t\t{},\n\t\t\t\t\t'https://api.xero.com/connections',\n\t\t\t\t);\n\t\t\t\tfor (const tenant of tenants) {\n\t\t\t\t\tconst tenantName = tenant.tenantName;\n\t\t\t\t\tconst tenantId = tenant.tenantId;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: tenantName,\n\t\t\t\t\t\tvalue: tenantId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the brading themes to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getBrandingThemes(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst organizationId = this.getCurrentNodeParameter('organizationId');\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst { BrandingThemes: themes } = await xeroApiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/BrandingThemes',\n\t\t\t\t\t{ organizationId },\n\t\t\t\t);\n\t\t\t\tfor (const theme of themes) {\n\t\t\t\t\tconst themeName = theme.Name;\n\t\t\t\t\tconst themeId = theme.BrandingThemeID;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: themeName,\n\t\t\t\t\t\tvalue: themeId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the brading themes to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getCurrencies(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst organizationId = this.getCurrentNodeParameter('organizationId');\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst { Currencies: currencies } = await xeroApiRequest.call(this, 'GET', '/Currencies', {\n\t\t\t\t\torganizationId,\n\t\t\t\t});\n\t\t\t\tfor (const currency of currencies) {\n\t\t\t\t\tconst currencyName = currency.Code;\n\t\t\t\t\tconst currencyId = currency.Description;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: currencyName,\n\t\t\t\t\t\tvalue: currencyId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the tracking categories to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getTrakingCategories(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst organizationId = this.getCurrentNodeParameter('organizationId');\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst { TrackingCategories: categories } = await xeroApiRequest.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/TrackingCategories',\n\t\t\t\t\t{ organizationId },\n\t\t\t\t);\n\t\t\t\tfor (const category of categories) {\n\t\t\t\t\tconst categoryName = category.Name;\n\t\t\t\t\tconst categoryId = category.TrackingCategoryID;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: categoryName,\n\t\t\t\t\t\tvalue: categoryId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// // Get all the tracking categories to display them to user so that they can\n\t\t\t// // select them easily\n\t\t\t// async getTrakingOptions(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t// \tconst organizationId = this.getCurrentNodeParameter('organizationId');\n\t\t\t// \tconst name = this.getCurrentNodeParameter('name');\n\t\t\t// \tconst returnData: INodePropertyOptions[] = [];\n\t\t\t// \tconst { TrackingCategories: categories } = await xeroApiRequest.call(this, 'GET', '/TrackingCategories', { organizationId });\n\t\t\t// \tconst { Options: options } = categories.filter((category: IDataObject) => category.Name === name)[0];\n\t\t\t// \tfor (const option of options) {\n\t\t\t// \t\tconst optionName = option.Name;\n\t\t\t// \t\tconst optionId = option.TrackingOptionID;\n\t\t\t// \t\treturnData.push({\n\t\t\t// \t\t\tname: optionName,\n\t\t\t// \t\t\tvalue: optionId,\n\t\t\t// \t\t});\n\t\t\t// \t}\n\t\t\t// \treturn returnData;\n\t\t\t// },\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: INodeExecutionData[] = [];\n\t\tconst length = items.length;\n\t\tconst qs: IDataObject = {};\n\t\tlet responseData;\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\t\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\t\t\t//https://developer.xero.com/documentation/api/invoices\n\t\t\t\tif (resource === 'invoice') {\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst organizationId = this.getNodeParameter('organizationId', i) as string;\n\t\t\t\t\t\tconst type = this.getNodeParameter('type', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tconst contactId = this.getNodeParameter('contactId', i) as string;\n\t\t\t\t\t\tconst lineItemsValues = (this.getNodeParameter('lineItemsUi', i) as IDataObject)\n\t\t\t\t\t\t\t.lineItemsValues as IDataObject[];\n\n\t\t\t\t\t\tconst body: IInvoice = {\n\t\t\t\t\t\t\torganizationId,\n\t\t\t\t\t\t\tType: type,\n\t\t\t\t\t\t\tContact: { ContactID: contactId },\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (lineItemsValues) {\n\t\t\t\t\t\t\tconst lineItems: ILineItem[] = [];\n\t\t\t\t\t\t\tfor (const lineItemValue of lineItemsValues) {\n\t\t\t\t\t\t\t\tconst lineItem: ILineItem = {\n\t\t\t\t\t\t\t\t\tTracking: [],\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\tlineItem.AccountCode = lineItemValue.accountCode as string;\n\t\t\t\t\t\t\t\tlineItem.Description = lineItemValue.description as string;\n\t\t\t\t\t\t\t\tlineItem.DiscountRate = lineItemValue.discountRate as string;\n\t\t\t\t\t\t\t\tlineItem.ItemCode = lineItemValue.itemCode as string;\n\t\t\t\t\t\t\t\tlineItem.LineAmount = lineItemValue.lineAmount as string;\n\t\t\t\t\t\t\t\tlineItem.Quantity = (lineItemValue.quantity as number).toString();\n\t\t\t\t\t\t\t\tlineItem.TaxAmount = lineItemValue.taxAmount as string;\n\t\t\t\t\t\t\t\tlineItem.TaxType = lineItemValue.taxType as string;\n\t\t\t\t\t\t\t\tlineItem.UnitAmount = lineItemValue.unitAmount as string;\n\t\t\t\t\t\t\t\t// if (lineItemValue.trackingUi) {\n\t\t\t\t\t\t\t\t// \t//@ts-ignore\n\t\t\t\t\t\t\t\t// \tconst { trackingValues } = lineItemValue.trackingUi as IDataObject[];\n\t\t\t\t\t\t\t\t// \tif (trackingValues) {\n\t\t\t\t\t\t\t\t// \t\tfor (const trackingValue of trackingValues) {\n\t\t\t\t\t\t\t\t// \t\t\tconst tracking: IDataObject = {};\n\t\t\t\t\t\t\t\t// \t\t\ttracking.Name = trackingValue.name as string;\n\t\t\t\t\t\t\t\t// \t\t\ttracking.Option = trackingValue.option as string;\n\t\t\t\t\t\t\t\t// \t\t\tlineItem.Tracking!.push(tracking);\n\t\t\t\t\t\t\t\t// \t\t}\n\t\t\t\t\t\t\t\t// \t}\n\t\t\t\t\t\t\t\t// }\n\t\t\t\t\t\t\t\tlineItems.push(lineItem);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbody.LineItems = lineItems;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.brandingThemeId) {\n\t\t\t\t\t\t\tbody.BrandingThemeID = additionalFields.brandingThemeId as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.currency) {\n\t\t\t\t\t\t\tbody.CurrencyCode = additionalFields.currency as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.currencyRate) {\n\t\t\t\t\t\t\tbody.CurrencyRate = additionalFields.currencyRate as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.date) {\n\t\t\t\t\t\t\tbody.Date = additionalFields.date as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.dueDate) {\n\t\t\t\t\t\t\tbody.DueDate = additionalFields.dueDate as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.dueDate) {\n\t\t\t\t\t\t\tbody.DueDate = additionalFields.dueDate as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.expectedPaymentDate) {\n\t\t\t\t\t\t\tbody.ExpectedPaymentDate = additionalFields.expectedPaymentDate as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.invoiceNumber) {\n\t\t\t\t\t\t\tbody.InvoiceNumber = additionalFields.invoiceNumber as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.lineAmountType) {\n\t\t\t\t\t\t\tbody.LineAmountTypes = additionalFields.lineAmountType as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.plannedPaymentDate) {\n\t\t\t\t\t\t\tbody.PlannedPaymentDate = additionalFields.plannedPaymentDate as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.reference) {\n\t\t\t\t\t\t\tbody.Reference = additionalFields.reference as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.sendToContact) {\n\t\t\t\t\t\t\tbody.SentToContact = additionalFields.sendToContact as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.status) {\n\t\t\t\t\t\t\tbody.Status = additionalFields.status as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.url) {\n\t\t\t\t\t\t\tbody.Url = additionalFields.url as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await xeroApiRequest.call(this, 'POST', '/Invoices', body);\n\t\t\t\t\t\tresponseData = responseData.Invoices;\n\t\t\t\t\t}\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst invoiceId = this.getNodeParameter('invoiceId', i) as string;\n\t\t\t\t\t\tconst organizationId = this.getNodeParameter('organizationId', i) as string;\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\tconst body: IInvoice = {\n\t\t\t\t\t\t\torganizationId,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (updateFields.lineItemsUi) {\n\t\t\t\t\t\t\tconst lineItemsValues = (updateFields.lineItemsUi as IDataObject)\n\t\t\t\t\t\t\t\t.lineItemsValues as IDataObject[];\n\t\t\t\t\t\t\tif (lineItemsValues) {\n\t\t\t\t\t\t\t\tconst lineItems: ILineItem[] = [];\n\t\t\t\t\t\t\t\tfor (const lineItemValue of lineItemsValues) {\n\t\t\t\t\t\t\t\t\tconst lineItem: ILineItem = {\n\t\t\t\t\t\t\t\t\t\tTracking: [],\n\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\tlineItem.AccountCode = lineItemValue.accountCode as string;\n\t\t\t\t\t\t\t\t\tlineItem.Description = lineItemValue.description as string;\n\t\t\t\t\t\t\t\t\tlineItem.DiscountRate = lineItemValue.discountRate as string;\n\t\t\t\t\t\t\t\t\tlineItem.ItemCode = lineItemValue.itemCode as string;\n\t\t\t\t\t\t\t\t\tlineItem.LineAmount = lineItemValue.lineAmount as string;\n\t\t\t\t\t\t\t\t\tlineItem.Quantity = (lineItemValue.quantity as number).toString();\n\t\t\t\t\t\t\t\t\tlineItem.TaxAmount = lineItemValue.taxAmount as string;\n\t\t\t\t\t\t\t\t\tlineItem.TaxType = lineItemValue.taxType as string;\n\t\t\t\t\t\t\t\t\tlineItem.UnitAmount = lineItemValue.unitAmount as string;\n\t\t\t\t\t\t\t\t\t// if (lineItemValue.trackingUi) {\n\t\t\t\t\t\t\t\t\t// \t//@ts-ignore\n\t\t\t\t\t\t\t\t\t// \tconst { trackingValues } = lineItemValue.trackingUi as IDataObject[];\n\t\t\t\t\t\t\t\t\t// \tif (trackingValues) {\n\t\t\t\t\t\t\t\t\t// \t\tfor (const trackingValue of trackingValues) {\n\t\t\t\t\t\t\t\t\t// \t\t\tconst tracking: IDataObject = {};\n\t\t\t\t\t\t\t\t\t// \t\t\ttracking.Name = trackingValue.name as string;\n\t\t\t\t\t\t\t\t\t// \t\t\ttracking.Option = trackingValue.option as string;\n\t\t\t\t\t\t\t\t\t// \t\t\tlineItem.Tracking!.push(tracking);\n\t\t\t\t\t\t\t\t\t// \t\t}\n\t\t\t\t\t\t\t\t\t// \t}\n\t\t\t\t\t\t\t\t\t// }\n\t\t\t\t\t\t\t\t\tlineItems.push(lineItem);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbody.LineItems = lineItems;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.type) {\n\t\t\t\t\t\t\tbody.Type = updateFields.type as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.Contact) {\n\t\t\t\t\t\t\tbody.Contact = { ContactID: updateFields.contactId as string };\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.brandingThemeId) {\n\t\t\t\t\t\t\tbody.BrandingThemeID = updateFields.brandingThemeId as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.currency) {\n\t\t\t\t\t\t\tbody.CurrencyCode = updateFields.currency as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.currencyRate) {\n\t\t\t\t\t\t\tbody.CurrencyRate = updateFields.currencyRate as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.date) {\n\t\t\t\t\t\t\tbody.Date = updateFields.date as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.dueDate) {\n\t\t\t\t\t\t\tbody.DueDate = updateFields.dueDate as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.dueDate) {\n\t\t\t\t\t\t\tbody.DueDate = updateFields.dueDate as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.expectedPaymentDate) {\n\t\t\t\t\t\t\tbody.ExpectedPaymentDate = updateFields.expectedPaymentDate as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.invoiceNumber) {\n\t\t\t\t\t\t\tbody.InvoiceNumber = updateFields.invoiceNumber as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.lineAmountType) {\n\t\t\t\t\t\t\tbody.LineAmountTypes = updateFields.lineAmountType as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.plannedPaymentDate) {\n\t\t\t\t\t\t\tbody.PlannedPaymentDate = updateFields.plannedPaymentDate as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.reference) {\n\t\t\t\t\t\t\tbody.Reference = updateFields.reference as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.sendToContact) {\n\t\t\t\t\t\t\tbody.SentToContact = updateFields.sendToContact as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.status) {\n\t\t\t\t\t\t\tbody.Status = updateFields.status as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.url) {\n\t\t\t\t\t\t\tbody.Url = updateFields.url as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await xeroApiRequest.call(this, 'POST', `/Invoices/${invoiceId}`, body);\n\t\t\t\t\t\tresponseData = responseData.Invoices;\n\t\t\t\t\t}\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tconst organizationId = this.getNodeParameter('organizationId', i) as string;\n\t\t\t\t\t\tconst invoiceId = this.getNodeParameter('invoiceId', i) as string;\n\t\t\t\t\t\tresponseData = await xeroApiRequest.call(this, 'GET', `/Invoices/${invoiceId}`, {\n\t\t\t\t\t\t\torganizationId,\n\t\t\t\t\t\t});\n\t\t\t\t\t\tresponseData = responseData.Invoices;\n\t\t\t\t\t}\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst organizationId = this.getNodeParameter('organizationId', i) as string;\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tif (options.statuses) {\n\t\t\t\t\t\t\tqs.statuses = (options.statuses as string[]).join(',');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.orderBy) {\n\t\t\t\t\t\t\tqs.order = `${options.orderBy} ${\n\t\t\t\t\t\t\t\toptions.sortOrder === undefined ? 'DESC' : options.sortOrder\n\t\t\t\t\t\t\t}`;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.where) {\n\t\t\t\t\t\t\tqs.where = options.where;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.createdByMyApp) {\n\t\t\t\t\t\t\tqs.createdByMyApp = options.createdByMyApp as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await xeroApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'Invoices',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/Invoices',\n\t\t\t\t\t\t\t\t{ organizationId },\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = await xeroApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/Invoices',\n\t\t\t\t\t\t\t\t{ organizationId },\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tresponseData = responseData.Invoices;\n\t\t\t\t\t\t\tresponseData = responseData.splice(0, limit);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (resource === 'contact') {\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst organizationId = this.getNodeParameter('organizationId', i) as string;\n\t\t\t\t\t\tconst name = this.getNodeParameter('name', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tconst addressesUi = additionalFields.addressesUi as IDataObject;\n\t\t\t\t\t\tconst phonesUi = additionalFields.phonesUi as IDataObject;\n\n\t\t\t\t\t\tconst body: IContact = {\n\t\t\t\t\t\t\tName: name,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (additionalFields.accountNumber) {\n\t\t\t\t\t\t\tbody.AccountNumber = additionalFields.accountNumber as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.bankAccountDetails) {\n\t\t\t\t\t\t\tbody.BankAccountDetails = additionalFields.bankAccountDetails as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.contactNumber) {\n\t\t\t\t\t\t\tbody.ContactNumber = additionalFields.contactNumber as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.contactStatus) {\n\t\t\t\t\t\t\tbody.ContactStatus = additionalFields.contactStatus as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.defaultCurrency) {\n\t\t\t\t\t\t\tbody.DefaultCurrency = additionalFields.defaultCurrency as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.emailAddress) {\n\t\t\t\t\t\t\tbody.EmailAddress = additionalFields.emailAddress as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.firstName) {\n\t\t\t\t\t\t\tbody.FirstName = additionalFields.firstName as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.lastName) {\n\t\t\t\t\t\t\tbody.LastName = additionalFields.lastName as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.purchasesDefaultAccountCode) {\n\t\t\t\t\t\t\tbody.PurchasesDefaultAccountCode =\n\t\t\t\t\t\t\t\tadditionalFields.purchasesDefaultAccountCode as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.salesDefaultAccountCode) {\n\t\t\t\t\t\t\tbody.SalesDefaultAccountCode = additionalFields.salesDefaultAccountCode as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.skypeUserName) {\n\t\t\t\t\t\t\tbody.SkypeUserName = additionalFields.skypeUserName as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.taxNumber) {\n\t\t\t\t\t\t\tbody.taxNumber = additionalFields.taxNumber as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (additionalFields.xeroNetworkKey) {\n\t\t\t\t\t\t\tbody.xeroNetworkKey = additionalFields.xeroNetworkKey as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (phonesUi) {\n\t\t\t\t\t\t\tconst phoneValues = phonesUi?.phonesValues as IDataObject[];\n\t\t\t\t\t\t\tif (phoneValues) {\n\t\t\t\t\t\t\t\tconst phones: IPhone[] = [];\n\t\t\t\t\t\t\t\tfor (const phoneValue of phoneValues) {\n\t\t\t\t\t\t\t\t\tconst phone: IPhone = {};\n\t\t\t\t\t\t\t\t\tphone.PhoneType = phoneValue.phoneType as string;\n\t\t\t\t\t\t\t\t\tphone.PhoneNumber = phoneValue.phoneNumber as string;\n\t\t\t\t\t\t\t\t\tphone.PhoneAreaCode = phoneValue.phoneAreaCode as string;\n\t\t\t\t\t\t\t\t\tphone.PhoneCountryCode = phoneValue.phoneCountryCode as string;\n\t\t\t\t\t\t\t\t\tphones.push(phone);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbody.Phones = phones;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (addressesUi) {\n\t\t\t\t\t\t\tconst addressValues = addressesUi?.addressesValues as IDataObject[];\n\t\t\t\t\t\t\tif (addressValues) {\n\t\t\t\t\t\t\t\tconst addresses: IAddress[] = [];\n\t\t\t\t\t\t\t\tfor (const addressValue of addressValues) {\n\t\t\t\t\t\t\t\t\tconst address: IAddress = {};\n\t\t\t\t\t\t\t\t\taddress.AddressType = addressValue.type as string;\n\t\t\t\t\t\t\t\t\taddress.AddressLine1 = addressValue.line1 as string;\n\t\t\t\t\t\t\t\t\taddress.AddressLine2 = addressValue.line2 as string;\n\t\t\t\t\t\t\t\t\taddress.City = addressValue.city as string;\n\t\t\t\t\t\t\t\t\taddress.Region = addressValue.region as string;\n\t\t\t\t\t\t\t\t\taddress.PostalCode = addressValue.postalCode as string;\n\t\t\t\t\t\t\t\t\taddress.Country = addressValue.country as string;\n\t\t\t\t\t\t\t\t\taddress.AttentionTo = addressValue.attentionTo as string;\n\t\t\t\t\t\t\t\t\taddresses.push(address);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbody.Addresses = addresses;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await xeroApiRequest.call(this, 'POST', '/Contacts', {\n\t\t\t\t\t\t\torganizationId,\n\t\t\t\t\t\t\tContacts: [body],\n\t\t\t\t\t\t});\n\t\t\t\t\t\tresponseData = responseData.Contacts;\n\t\t\t\t\t}\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tconst organizationId = this.getNodeParameter('organizationId', i) as string;\n\t\t\t\t\t\tconst contactId = this.getNodeParameter('contactId', i) as string;\n\t\t\t\t\t\tresponseData = await xeroApiRequest.call(this, 'GET', `/Contacts/${contactId}`, {\n\t\t\t\t\t\t\torganizationId,\n\t\t\t\t\t\t});\n\t\t\t\t\t\tresponseData = responseData.Contacts;\n\t\t\t\t\t}\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst organizationId = this.getNodeParameter('organizationId', i) as string;\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tif (options.includeArchived) {\n\t\t\t\t\t\t\tqs.includeArchived = options.includeArchived as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.orderBy) {\n\t\t\t\t\t\t\tqs.order = `${options.orderBy} ${\n\t\t\t\t\t\t\t\toptions.sortOrder === undefined ? 'DESC' : options.sortOrder\n\t\t\t\t\t\t\t}`;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.where) {\n\t\t\t\t\t\t\tqs.where = options.where;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await xeroApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'Contacts',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/Contacts',\n\t\t\t\t\t\t\t\t{ organizationId },\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = await xeroApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/Contacts',\n\t\t\t\t\t\t\t\t{ organizationId },\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tresponseData = responseData.Contacts;\n\t\t\t\t\t\t\tresponseData = responseData.splice(0, limit);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst organizationId = this.getNodeParameter('organizationId', i) as string;\n\t\t\t\t\t\tconst contactId = this.getNodeParameter('contactId', i) as string;\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\t\tconst addressesUi = updateFields.addressesUi as IDataObject;\n\t\t\t\t\t\tconst phonesUi = updateFields.phonesUi as IDataObject;\n\n\t\t\t\t\t\tconst body: IContact = {};\n\n\t\t\t\t\t\tif (updateFields.accountNumber) {\n\t\t\t\t\t\t\tbody.AccountNumber = updateFields.accountNumber as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.name) {\n\t\t\t\t\t\t\tbody.Name = updateFields.name as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.bankAccountDetails) {\n\t\t\t\t\t\t\tbody.BankAccountDetails = updateFields.bankAccountDetails as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.contactNumber) {\n\t\t\t\t\t\t\tbody.ContactNumber = updateFields.contactNumber as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.contactStatus) {\n\t\t\t\t\t\t\tbody.ContactStatus = updateFields.contactStatus as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.defaultCurrency) {\n\t\t\t\t\t\t\tbody.DefaultCurrency = updateFields.defaultCurrency as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.emailAddress) {\n\t\t\t\t\t\t\tbody.EmailAddress = updateFields.emailAddress as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.firstName) {\n\t\t\t\t\t\t\tbody.FirstName = updateFields.firstName as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.lastName) {\n\t\t\t\t\t\t\tbody.LastName = updateFields.lastName as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.purchasesDefaultAccountCode) {\n\t\t\t\t\t\t\tbody.PurchasesDefaultAccountCode = updateFields.purchasesDefaultAccountCode as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.salesDefaultAccountCode) {\n\t\t\t\t\t\t\tbody.SalesDefaultAccountCode = updateFields.salesDefaultAccountCode as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.skypeUserName) {\n\t\t\t\t\t\t\tbody.SkypeUserName = updateFields.skypeUserName as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.taxNumber) {\n\t\t\t\t\t\t\tbody.taxNumber = updateFields.taxNumber as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.xeroNetworkKey) {\n\t\t\t\t\t\t\tbody.xeroNetworkKey = updateFields.xeroNetworkKey as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (phonesUi) {\n\t\t\t\t\t\t\tconst phoneValues = phonesUi?.phonesValues as IDataObject[];\n\t\t\t\t\t\t\tif (phoneValues) {\n\t\t\t\t\t\t\t\tconst phones: IPhone[] = [];\n\t\t\t\t\t\t\t\tfor (const phoneValue of phoneValues) {\n\t\t\t\t\t\t\t\t\tconst phone: IPhone = {};\n\t\t\t\t\t\t\t\t\tphone.PhoneType = phoneValue.phoneType as string;\n\t\t\t\t\t\t\t\t\tphone.PhoneNumber = phoneValue.phoneNumber as string;\n\t\t\t\t\t\t\t\t\tphone.PhoneAreaCode = phoneValue.phoneAreaCode as string;\n\t\t\t\t\t\t\t\t\tphone.PhoneCountryCode = phoneValue.phoneCountryCode as string;\n\t\t\t\t\t\t\t\t\tphones.push(phone);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbody.Phones = phones;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (addressesUi) {\n\t\t\t\t\t\t\tconst addressValues = addressesUi?.addressesValues as IDataObject[];\n\t\t\t\t\t\t\tif (addressValues) {\n\t\t\t\t\t\t\t\tconst addresses: IAddress[] = [];\n\t\t\t\t\t\t\t\tfor (const addressValue of addressValues) {\n\t\t\t\t\t\t\t\t\tconst address: IAddress = {};\n\t\t\t\t\t\t\t\t\taddress.AddressType = addressValue.type as string;\n\t\t\t\t\t\t\t\t\taddress.AddressLine1 = addressValue.line1 as string;\n\t\t\t\t\t\t\t\t\taddress.AddressLine2 = addressValue.line2 as string;\n\t\t\t\t\t\t\t\t\taddress.City = addressValue.city as string;\n\t\t\t\t\t\t\t\t\taddress.Region = addressValue.region as string;\n\t\t\t\t\t\t\t\t\taddress.PostalCode = addressValue.postalCode as string;\n\t\t\t\t\t\t\t\t\taddress.Country = addressValue.country as string;\n\t\t\t\t\t\t\t\t\taddress.AttentionTo = addressValue.attentionTo as string;\n\t\t\t\t\t\t\t\t\taddresses.push(address);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbody.Addresses = addresses;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await xeroApiRequest.call(this, 'POST', `/Contacts/${contactId}`, {\n\t\t\t\t\t\t\torganizationId,\n\t\t\t\t\t\t\tContacts: [body],\n\t\t\t\t\t\t});\n\t\t\t\t\t\tresponseData = responseData.Contacts;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\tthis.helpers.returnJsonArray(responseData as IDataObject[]),\n\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t);\n\t\t\t\treturnData.push(...executionData);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ json: { error: (error as JsonObject).message } });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAUO;AAEP,gCAAiD;AACjD,8BAAuD;AAEvD,gCAAiD;AAG1C,MAAM,KAA0B;AAAA,EAAhC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,QAAQ;AAAA,MAChB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA;AAAA,QAEA,GAAG;AAAA,QACH,GAAG;AAAA;AAAA,QAEH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA;AAAA;AAAA,QAGZ,MAAM,eAA2E;AAChF,gBAAM,iBAAiB,KAAK,wBAAwB,gBAAgB;AACpE,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,EAAE,OAAO,MAAM,IAAI,MAAM,uCAAe,KAAK,MAAM,OAAO,UAAU;AAAA,YACzE;AAAA,UACD,CAAC;AACD,qBAAW,QAAQ,OAAO;AACzB,kBAAM,WAAW,KAAK;AACtB,kBAAM,SAAS,KAAK;AACpB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,kBAA8E;AACnF,gBAAM,iBAAiB,KAAK,wBAAwB,gBAAgB;AACpE,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,EAAE,UAAU,SAAS,IAAI,MAAM,uCAAe,KAAK,MAAM,OAAO,aAAa;AAAA,YAClF;AAAA,UACD,CAAC;AACD,qBAAW,WAAW,UAAU;AAC/B,kBAAM,cAAc,QAAQ;AAC5B,kBAAM,YAAY,QAAQ;AAC1B,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,aAAyE;AAC9E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,UAAU,MAAM,uCAAe;AAAA,YACpC;AAAA,YACA;AAAA,YACA;AAAA,YACA,CAAC;AAAA,YACD,CAAC;AAAA,YACD;AAAA,UACD;AACA,qBAAW,UAAU,SAAS;AAC7B,kBAAM,aAAa,OAAO;AAC1B,kBAAM,WAAW,OAAO;AACxB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,oBAAgF;AACrF,gBAAM,iBAAiB,KAAK,wBAAwB,gBAAgB;AACpE,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,EAAE,gBAAgB,OAAO,IAAI,MAAM,uCAAe;AAAA,YACvD;AAAA,YACA;AAAA,YACA;AAAA,YACA,EAAE,eAAe;AAAA,UAClB;AACA,qBAAW,SAAS,QAAQ;AAC3B,kBAAM,YAAY,MAAM;AACxB,kBAAM,UAAU,MAAM;AACtB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,gBAA4E;AACjF,gBAAM,iBAAiB,KAAK,wBAAwB,gBAAgB;AACpE,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,EAAE,YAAY,WAAW,IAAI,MAAM,uCAAe,KAAK,MAAM,OAAO,eAAe;AAAA,YACxF;AAAA,UACD,CAAC;AACD,qBAAW,YAAY,YAAY;AAClC,kBAAM,eAAe,SAAS;AAC9B,kBAAM,aAAa,SAAS;AAC5B,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,uBAAmF;AACxF,gBAAM,iBAAiB,KAAK,wBAAwB,gBAAgB;AACpE,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,EAAE,oBAAoB,WAAW,IAAI,MAAM,uCAAe;AAAA,YAC/D;AAAA,YACA;AAAA,YACA;AAAA,YACA,EAAE,eAAe;AAAA,UAClB;AACA,qBAAW,YAAY,YAAY;AAClC,kBAAM,eAAe,SAAS;AAC9B,kBAAM,aAAa,SAAS;AAC5B,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAmBD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAAmC,CAAC;AAC1C,UAAM,SAAS,MAAM;AACrB,UAAM,KAAkB,CAAC;AACzB,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,cAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,cAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,YAAI,aAAa,WAAW;AAC3B,cAAI,cAAc,UAAU;AAC3B,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC5C,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,kBAAmB,KAAK,iBAAiB,eAAe,CAAC,EAC7D;AAEF,kBAAM,OAAiB;AAAA,cACtB;AAAA,cACA,MAAM;AAAA,cACN,SAAS,EAAE,WAAW,UAAU;AAAA,YACjC;AAEA,gBAAI,iBAAiB;AACpB,oBAAM,YAAyB,CAAC;AAChC,yBAAW,iBAAiB,iBAAiB;AAC5C,sBAAM,WAAsB;AAAA,kBAC3B,UAAU,CAAC;AAAA,gBACZ;AACA,yBAAS,cAAc,cAAc;AACrC,yBAAS,cAAc,cAAc;AACrC,yBAAS,eAAe,cAAc;AACtC,yBAAS,WAAW,cAAc;AAClC,yBAAS,aAAa,cAAc;AACpC,yBAAS,WAAY,cAAc,SAAoB,SAAS;AAChE,yBAAS,YAAY,cAAc;AACnC,yBAAS,UAAU,cAAc;AACjC,yBAAS,aAAa,cAAc;AAapC,0BAAU,KAAK,QAAQ;AAAA,cACxB;AACA,mBAAK,YAAY;AAAA,YAClB;AAEA,gBAAI,iBAAiB,iBAAiB;AACrC,mBAAK,kBAAkB,iBAAiB;AAAA,YACzC;AACA,gBAAI,iBAAiB,UAAU;AAC9B,mBAAK,eAAe,iBAAiB;AAAA,YACtC;AACA,gBAAI,iBAAiB,cAAc;AAClC,mBAAK,eAAe,iBAAiB;AAAA,YACtC;AACA,gBAAI,iBAAiB,MAAM;AAC1B,mBAAK,OAAO,iBAAiB;AAAA,YAC9B;AACA,gBAAI,iBAAiB,SAAS;AAC7B,mBAAK,UAAU,iBAAiB;AAAA,YACjC;AACA,gBAAI,iBAAiB,SAAS;AAC7B,mBAAK,UAAU,iBAAiB;AAAA,YACjC;AACA,gBAAI,iBAAiB,qBAAqB;AACzC,mBAAK,sBAAsB,iBAAiB;AAAA,YAC7C;AACA,gBAAI,iBAAiB,eAAe;AACnC,mBAAK,gBAAgB,iBAAiB;AAAA,YACvC;AACA,gBAAI,iBAAiB,gBAAgB;AACpC,mBAAK,kBAAkB,iBAAiB;AAAA,YACzC;AACA,gBAAI,iBAAiB,oBAAoB;AACxC,mBAAK,qBAAqB,iBAAiB;AAAA,YAC5C;AACA,gBAAI,iBAAiB,WAAW;AAC/B,mBAAK,YAAY,iBAAiB;AAAA,YACnC;AACA,gBAAI,iBAAiB,eAAe;AACnC,mBAAK,gBAAgB,iBAAiB;AAAA,YACvC;AACA,gBAAI,iBAAiB,QAAQ;AAC5B,mBAAK,SAAS,iBAAiB;AAAA,YAChC;AACA,gBAAI,iBAAiB,KAAK;AACzB,mBAAK,MAAM,iBAAiB;AAAA,YAC7B;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,aAAa,IAAI;AACxE,2BAAe,aAAa;AAAA,UAC7B;AACA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,kBAAM,OAAiB;AAAA,cACtB;AAAA,YACD;AAEA,gBAAI,aAAa,aAAa;AAC7B,oBAAM,kBAAmB,aAAa,YACpC;AACF,kBAAI,iBAAiB;AACpB,sBAAM,YAAyB,CAAC;AAChC,2BAAW,iBAAiB,iBAAiB;AAC5C,wBAAM,WAAsB;AAAA,oBAC3B,UAAU,CAAC;AAAA,kBACZ;AACA,2BAAS,cAAc,cAAc;AACrC,2BAAS,cAAc,cAAc;AACrC,2BAAS,eAAe,cAAc;AACtC,2BAAS,WAAW,cAAc;AAClC,2BAAS,aAAa,cAAc;AACpC,2BAAS,WAAY,cAAc,SAAoB,SAAS;AAChE,2BAAS,YAAY,cAAc;AACnC,2BAAS,UAAU,cAAc;AACjC,2BAAS,aAAa,cAAc;AAapC,4BAAU,KAAK,QAAQ;AAAA,gBACxB;AACA,qBAAK,YAAY;AAAA,cAClB;AAAA,YACD;AAEA,gBAAI,aAAa,MAAM;AACtB,mBAAK,OAAO,aAAa;AAAA,YAC1B;AACA,gBAAI,aAAa,SAAS;AACzB,mBAAK,UAAU,EAAE,WAAW,aAAa,UAAoB;AAAA,YAC9D;AACA,gBAAI,aAAa,iBAAiB;AACjC,mBAAK,kBAAkB,aAAa;AAAA,YACrC;AACA,gBAAI,aAAa,UAAU;AAC1B,mBAAK,eAAe,aAAa;AAAA,YAClC;AACA,gBAAI,aAAa,cAAc;AAC9B,mBAAK,eAAe,aAAa;AAAA,YAClC;AACA,gBAAI,aAAa,MAAM;AACtB,mBAAK,OAAO,aAAa;AAAA,YAC1B;AACA,gBAAI,aAAa,SAAS;AACzB,mBAAK,UAAU,aAAa;AAAA,YAC7B;AACA,gBAAI,aAAa,SAAS;AACzB,mBAAK,UAAU,aAAa;AAAA,YAC7B;AACA,gBAAI,aAAa,qBAAqB;AACrC,mBAAK,sBAAsB,aAAa;AAAA,YACzC;AACA,gBAAI,aAAa,eAAe;AAC/B,mBAAK,gBAAgB,aAAa;AAAA,YACnC;AACA,gBAAI,aAAa,gBAAgB;AAChC,mBAAK,kBAAkB,aAAa;AAAA,YACrC;AACA,gBAAI,aAAa,oBAAoB;AACpC,mBAAK,qBAAqB,aAAa;AAAA,YACxC;AACA,gBAAI,aAAa,WAAW;AAC3B,mBAAK,YAAY,aAAa;AAAA,YAC/B;AACA,gBAAI,aAAa,eAAe;AAC/B,mBAAK,gBAAgB,aAAa;AAAA,YACnC;AACA,gBAAI,aAAa,QAAQ;AACxB,mBAAK,SAAS,aAAa;AAAA,YAC5B;AACA,gBAAI,aAAa,KAAK;AACrB,mBAAK,MAAM,aAAa;AAAA,YACzB;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,aAAa,SAAS,IAAI,IAAI;AACrF,2BAAe,aAAa;AAAA,UAC7B;AACA,cAAI,cAAc,OAAO;AACxB,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,aAAa,SAAS,IAAI;AAAA,cAC/E;AAAA,YACD,CAAC;AACD,2BAAe,aAAa;AAAA,UAC7B;AACA,cAAI,cAAc,UAAU;AAC3B,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,gBAAI,QAAQ,UAAU;AACrB,iBAAG,WAAY,QAAQ,SAAsB,KAAK,GAAG;AAAA,YACtD;AACA,gBAAI,QAAQ,SAAS;AACpB,iBAAG,QAAQ,GAAG,QAAQ,OAAO,IAC5B,QAAQ,cAAc,SAAY,SAAS,QAAQ,SACpD;AAAA,YACD;AACA,gBAAI,QAAQ,OAAO;AAClB,iBAAG,QAAQ,QAAQ;AAAA,YACpB;AACA,gBAAI,QAAQ,gBAAgB;AAC3B,iBAAG,iBAAiB,QAAQ;AAAA,YAC7B;AACA,gBAAI,WAAW;AACd,6BAAe,MAAM,+CAAuB;AAAA,gBAC3C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,EAAE,eAAe;AAAA,gBACjB;AAAA,cACD;AAAA,YACD,OAAO;AACN,oBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,6BAAe,MAAM,uCAAe;AAAA,gBACnC;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,EAAE,eAAe;AAAA,gBACjB;AAAA,cACD;AACA,6BAAe,aAAa;AAC5B,6BAAe,aAAa,OAAO,GAAG,KAAK;AAAA,YAC5C;AAAA,UACD;AAAA,QACD;AACA,YAAI,aAAa,WAAW;AAC3B,cAAI,cAAc,UAAU;AAC3B,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC5C,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,kBAAM,cAAc,iBAAiB;AACrC,kBAAM,WAAW,iBAAiB;AAElC,kBAAM,OAAiB;AAAA,cACtB,MAAM;AAAA,YACP;AAEA,gBAAI,iBAAiB,eAAe;AACnC,mBAAK,gBAAgB,iBAAiB;AAAA,YACvC;AAEA,gBAAI,iBAAiB,oBAAoB;AACxC,mBAAK,qBAAqB,iBAAiB;AAAA,YAC5C;AAEA,gBAAI,iBAAiB,eAAe;AACnC,mBAAK,gBAAgB,iBAAiB;AAAA,YACvC;AAEA,gBAAI,iBAAiB,eAAe;AACnC,mBAAK,gBAAgB,iBAAiB;AAAA,YACvC;AAEA,gBAAI,iBAAiB,iBAAiB;AACrC,mBAAK,kBAAkB,iBAAiB;AAAA,YACzC;AAEA,gBAAI,iBAAiB,cAAc;AAClC,mBAAK,eAAe,iBAAiB;AAAA,YACtC;AAEA,gBAAI,iBAAiB,WAAW;AAC/B,mBAAK,YAAY,iBAAiB;AAAA,YACnC;AAEA,gBAAI,iBAAiB,UAAU;AAC9B,mBAAK,WAAW,iBAAiB;AAAA,YAClC;AAEA,gBAAI,iBAAiB,6BAA6B;AACjD,mBAAK,8BACJ,iBAAiB;AAAA,YACnB;AAEA,gBAAI,iBAAiB,yBAAyB;AAC7C,mBAAK,0BAA0B,iBAAiB;AAAA,YACjD;AAEA,gBAAI,iBAAiB,eAAe;AACnC,mBAAK,gBAAgB,iBAAiB;AAAA,YACvC;AAEA,gBAAI,iBAAiB,WAAW;AAC/B,mBAAK,YAAY,iBAAiB;AAAA,YACnC;AAEA,gBAAI,iBAAiB,gBAAgB;AACpC,mBAAK,iBAAiB,iBAAiB;AAAA,YACxC;AAEA,gBAAI,UAAU;AACb,oBAAM,cAAc,UAAU;AAC9B,kBAAI,aAAa;AAChB,sBAAM,SAAmB,CAAC;AAC1B,2BAAW,cAAc,aAAa;AACrC,wBAAM,QAAgB,CAAC;AACvB,wBAAM,YAAY,WAAW;AAC7B,wBAAM,cAAc,WAAW;AAC/B,wBAAM,gBAAgB,WAAW;AACjC,wBAAM,mBAAmB,WAAW;AACpC,yBAAO,KAAK,KAAK;AAAA,gBAClB;AACA,qBAAK,SAAS;AAAA,cACf;AAAA,YACD;AAEA,gBAAI,aAAa;AAChB,oBAAM,gBAAgB,aAAa;AACnC,kBAAI,eAAe;AAClB,sBAAM,YAAwB,CAAC;AAC/B,2BAAW,gBAAgB,eAAe;AACzC,wBAAM,UAAoB,CAAC;AAC3B,0BAAQ,cAAc,aAAa;AACnC,0BAAQ,eAAe,aAAa;AACpC,0BAAQ,eAAe,aAAa;AACpC,0BAAQ,OAAO,aAAa;AAC5B,0BAAQ,SAAS,aAAa;AAC9B,0BAAQ,aAAa,aAAa;AAClC,0BAAQ,UAAU,aAAa;AAC/B,0BAAQ,cAAc,aAAa;AACnC,4BAAU,KAAK,OAAO;AAAA,gBACvB;AACA,qBAAK,YAAY;AAAA,cAClB;AAAA,YACD;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,aAAa;AAAA,cACnE;AAAA,cACA,UAAU,CAAC,IAAI;AAAA,YAChB,CAAC;AACD,2BAAe,aAAa;AAAA,UAC7B;AACA,cAAI,cAAc,OAAO;AACxB,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,2BAAe,MAAM,uCAAe,KAAK,MAAM,OAAO,aAAa,SAAS,IAAI;AAAA,cAC/E;AAAA,YACD,CAAC;AACD,2BAAe,aAAa;AAAA,UAC7B;AACA,cAAI,cAAc,UAAU;AAC3B,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,gBAAI,QAAQ,iBAAiB;AAC5B,iBAAG,kBAAkB,QAAQ;AAAA,YAC9B;AACA,gBAAI,QAAQ,SAAS;AACpB,iBAAG,QAAQ,GAAG,QAAQ,OAAO,IAC5B,QAAQ,cAAc,SAAY,SAAS,QAAQ,SACpD;AAAA,YACD;AACA,gBAAI,QAAQ,OAAO;AAClB,iBAAG,QAAQ,QAAQ;AAAA,YACpB;AACA,gBAAI,WAAW;AACd,6BAAe,MAAM,+CAAuB;AAAA,gBAC3C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,EAAE,eAAe;AAAA,gBACjB;AAAA,cACD;AAAA,YACD,OAAO;AACN,oBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,6BAAe,MAAM,uCAAe;AAAA,gBACnC;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,EAAE,eAAe;AAAA,gBACjB;AAAA,cACD;AACA,6BAAe,aAAa;AAC5B,6BAAe,aAAa,OAAO,GAAG,KAAK;AAAA,YAC5C;AAAA,UACD;AACA,cAAI,cAAc,UAAU;AAC3B,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,kBAAM,cAAc,aAAa;AACjC,kBAAM,WAAW,aAAa;AAE9B,kBAAM,OAAiB,CAAC;AAExB,gBAAI,aAAa,eAAe;AAC/B,mBAAK,gBAAgB,aAAa;AAAA,YACnC;AAEA,gBAAI,aAAa,MAAM;AACtB,mBAAK,OAAO,aAAa;AAAA,YAC1B;AAEA,gBAAI,aAAa,oBAAoB;AACpC,mBAAK,qBAAqB,aAAa;AAAA,YACxC;AAEA,gBAAI,aAAa,eAAe;AAC/B,mBAAK,gBAAgB,aAAa;AAAA,YACnC;AAEA,gBAAI,aAAa,eAAe;AAC/B,mBAAK,gBAAgB,aAAa;AAAA,YACnC;AAEA,gBAAI,aAAa,iBAAiB;AACjC,mBAAK,kBAAkB,aAAa;AAAA,YACrC;AAEA,gBAAI,aAAa,cAAc;AAC9B,mBAAK,eAAe,aAAa;AAAA,YAClC;AAEA,gBAAI,aAAa,WAAW;AAC3B,mBAAK,YAAY,aAAa;AAAA,YAC/B;AAEA,gBAAI,aAAa,UAAU;AAC1B,mBAAK,WAAW,aAAa;AAAA,YAC9B;AAEA,gBAAI,aAAa,6BAA6B;AAC7C,mBAAK,8BAA8B,aAAa;AAAA,YACjD;AAEA,gBAAI,aAAa,yBAAyB;AACzC,mBAAK,0BAA0B,aAAa;AAAA,YAC7C;AAEA,gBAAI,aAAa,eAAe;AAC/B,mBAAK,gBAAgB,aAAa;AAAA,YACnC;AAEA,gBAAI,aAAa,WAAW;AAC3B,mBAAK,YAAY,aAAa;AAAA,YAC/B;AAEA,gBAAI,aAAa,gBAAgB;AAChC,mBAAK,iBAAiB,aAAa;AAAA,YACpC;AAEA,gBAAI,UAAU;AACb,oBAAM,cAAc,UAAU;AAC9B,kBAAI,aAAa;AAChB,sBAAM,SAAmB,CAAC;AAC1B,2BAAW,cAAc,aAAa;AACrC,wBAAM,QAAgB,CAAC;AACvB,wBAAM,YAAY,WAAW;AAC7B,wBAAM,cAAc,WAAW;AAC/B,wBAAM,gBAAgB,WAAW;AACjC,wBAAM,mBAAmB,WAAW;AACpC,yBAAO,KAAK,KAAK;AAAA,gBAClB;AACA,qBAAK,SAAS;AAAA,cACf;AAAA,YACD;AAEA,gBAAI,aAAa;AAChB,oBAAM,gBAAgB,aAAa;AACnC,kBAAI,eAAe;AAClB,sBAAM,YAAwB,CAAC;AAC/B,2BAAW,gBAAgB,eAAe;AACzC,wBAAM,UAAoB,CAAC;AAC3B,0BAAQ,cAAc,aAAa;AACnC,0BAAQ,eAAe,aAAa;AACpC,0BAAQ,eAAe,aAAa;AACpC,0BAAQ,OAAO,aAAa;AAC5B,0BAAQ,SAAS,aAAa;AAC9B,0BAAQ,aAAa,aAAa;AAClC,0BAAQ,UAAU,aAAa;AAC/B,0BAAQ,cAAc,aAAa;AACnC,4BAAU,KAAK,OAAO;AAAA,gBACvB;AACA,qBAAK,YAAY;AAAA,cAClB;AAAA,YACD;AAEA,2BAAe,MAAM,uCAAe,KAAK,MAAM,QAAQ,aAAa,SAAS,IAAI;AAAA,cAChF;AAAA,cACA,UAAU,CAAC,IAAI;AAAA,YAChB,CAAC;AACD,2BAAe,aAAa;AAAA,UAC7B;AAAA,QACD;AACA,cAAM,gBAAgB,KAAK,QAAQ;AAAA,UAClC,KAAK,QAAQ,gBAAgB,YAA6B;AAAA,UAC1D,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,QACzB;AACA,mBAAW,KAAK,GAAG,aAAa;AAAA,MACjC,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,MAAM,EAAE,OAAQ,MAAqB,QAAQ,EAAE,CAAC;AAClE;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AACA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": []}