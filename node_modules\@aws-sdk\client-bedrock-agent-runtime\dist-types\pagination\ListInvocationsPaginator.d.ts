import { Paginator } from "@smithy/types";
import { ListInvocationsCommandInput, ListInvocationsCommandOutput } from "../commands/ListInvocationsCommand";
import { BedrockAgentRuntimePaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListInvocations: (config: BedrockAgentRuntimePaginationConfiguration, input: ListInvocationsCommandInput, ...rest: any[]) => Paginator<ListInvocationsCommandOutput>;
