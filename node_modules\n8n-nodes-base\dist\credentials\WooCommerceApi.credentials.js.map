{"version": 3, "sources": ["../../credentials/WooCommerceApi.credentials.ts"], "sourcesContent": ["import type {\n\tICredentialDataDecryptedObject,\n\tICredentialTestRequest,\n\tICredentialType,\n\tIHttpRequestOptions,\n\tINodeProperties,\n} from 'n8n-workflow';\n\nexport class WooCommerceApi implements ICredentialType {\n\tname = 'wooCommerceApi';\n\n\tdisplayName = 'WooCommerce API';\n\n\tdocumentationUrl = 'wooCommerce';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Consumer Key',\n\t\t\tname: 'consumerKey',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Consumer Secret',\n\t\t\tname: 'consumerSecret',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'WooCommerce URL',\n\t\t\tname: 'url',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\tplaceholder: 'https://example.com',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Include Credentials in Query',\n\t\t\tname: 'includeCredentialsInQuery',\n\t\t\ttype: 'boolean',\n\t\t\tdefault: false,\n\t\t\tdescription:\n\t\t\t\t'Whether credentials should be included in the query. Occasionally, some servers may not parse the Authorization header correctly (if you see a “Consumer key is missing” error when authenticating over SSL, you have a server issue). In this case, you may provide the consumer key/secret as query string parameters instead.',\n\t\t},\n\t];\n\n\tasync authenticate(\n\t\tcredentials: ICredentialDataDecryptedObject,\n\t\trequestOptions: IHttpRequestOptions,\n\t): Promise<IHttpRequestOptions> {\n\t\trequestOptions.auth = {\n\t\t\t// @ts-ignore\n\t\t\tuser: credentials.consumerKey as string,\n\t\t\tpassword: credentials.consumerSecret as string,\n\t\t};\n\t\tif (credentials.includeCredentialsInQuery === true && requestOptions.qs) {\n\t\t\tdelete requestOptions.auth;\n\t\t\tObject.assign(requestOptions.qs, {\n\t\t\t\tconsumer_key: credentials.consumerKey,\n\t\t\t\tconsumer_secret: credentials.consumerSecret,\n\t\t\t});\n\t\t}\n\t\treturn requestOptions;\n\t}\n\n\ttest: ICredentialTestRequest = {\n\t\trequest: {\n\t\t\tbaseURL: '={{$credentials.url}}/wp-json/wc/v3',\n\t\t\turl: '/products/categories',\n\t\t},\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAQO,MAAM,eAA0C;AAAA,EAAhD;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,MACF;AAAA,IACD;AAqBA,gBAA+B;AAAA,MAC9B,SAAS;AAAA,QACR,SAAS;AAAA,QACT,KAAK;AAAA,MACN;AAAA,IACD;AAAA;AAAA,EAxBA,MAAM,aACL,aACA,gBAC+B;AAC/B,mBAAe,OAAO;AAAA;AAAA,MAErB,MAAM,YAAY;AAAA,MAClB,UAAU,YAAY;AAAA,IACvB;AACA,QAAI,YAAY,8BAA8B,QAAQ,eAAe,IAAI;AACxE,aAAO,eAAe;AACtB,aAAO,OAAO,eAAe,IAAI;AAAA,QAChC,cAAc,YAAY;AAAA,QAC1B,iBAAiB,YAAY;AAAA,MAC9B,CAAC;AAAA,IACF;AACA,WAAO;AAAA,EACR;AAQD;", "names": []}