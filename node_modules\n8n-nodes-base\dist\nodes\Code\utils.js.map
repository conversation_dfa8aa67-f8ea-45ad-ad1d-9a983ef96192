{"version": 3, "sources": ["../../../nodes/Code/utils.ts"], "sourcesContent": ["import type { INodeExecutionData, IDataObject, IExecuteFunctions } from 'n8n-workflow';\n\nexport function isObject(maybe: unknown): maybe is { [key: string]: unknown } {\n\treturn (\n\t\ttypeof maybe === 'object' && maybe !== null && !Array.isArray(maybe) && !(maybe instanceof Date)\n\t);\n}\n\nfunction isTraversable(maybe: unknown): maybe is IDataObject {\n\treturn isObject(maybe) && typeof maybe.toJSON !== 'function' && Object.keys(maybe).length > 0;\n}\n\n/**\n * Stringify any non-standard JS objects (e.g. `Date`, `RegExp`) inside output items at any depth.\n */\nexport function standardizeOutput(output: IDataObject) {\n\tfunction standardizeOutputRecursive(obj: IDataObject, knownObjects = new WeakSet()): IDataObject {\n\t\tfor (const [key, value] of Object.entries(obj)) {\n\t\t\tif (!isTraversable(value)) continue;\n\n\t\t\tif (typeof value === 'object' && value !== null) {\n\t\t\t\tif (knownObjects.has(value)) {\n\t\t\t\t\t// Found circular reference\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tknownObjects.add(value);\n\t\t\t}\n\n\t\t\tobj[key] =\n\t\t\t\tvalue.constructor.name !== 'Object'\n\t\t\t\t\t? JSON.stringify(value) // Date, RegExp, etc.\n\t\t\t\t\t: standardizeOutputRecursive(value, knownObjects);\n\t\t}\n\t\treturn obj;\n\t}\n\tstandardizeOutputRecursive(output);\n\treturn output;\n}\n\nexport const addPostExecutionWarning = (\n\tcontext: IExecuteFunctions,\n\treturnData: INodeExecutionData[],\n\tinputItemsLength: number,\n): void => {\n\tif (\n\t\treturnData.length !== inputItemsLength ||\n\t\treturnData.some((item) => item.pairedItem === undefined)\n\t) {\n\t\tcontext.addExecutionHints({\n\t\t\tmessage:\n\t\t\t\t'To make sure expressions after this node work, return the input items that produced each output item. <a target=\"_blank\" href=\"https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-code-node/\">More info</a>',\n\t\t\tlocation: 'outputPane',\n\t\t});\n\t}\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,SAAS,SAAS,OAAqD;AAC7E,SACC,OAAO,UAAU,YAAY,UAAU,QAAQ,CAAC,MAAM,QAAQ,KAAK,KAAK,EAAE,iBAAiB;AAE7F;AAEA,SAAS,cAAc,OAAsC;AAC5D,SAAO,SAAS,KAAK,KAAK,OAAO,MAAM,WAAW,cAAc,OAAO,KAAK,KAAK,EAAE,SAAS;AAC7F;AAKO,SAAS,kBAAkB,QAAqB;AACtD,WAAS,2BAA2B,KAAkB,eAAe,oBAAI,QAAQ,GAAgB;AAChG,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,GAAG,GAAG;AAC/C,UAAI,CAAC,cAAc,KAAK,EAAG;AAE3B,UAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAChD,YAAI,aAAa,IAAI,KAAK,GAAG;AAE5B;AAAA,QACD;AACA,qBAAa,IAAI,KAAK;AAAA,MACvB;AAEA,UAAI,GAAG,IACN,MAAM,YAAY,SAAS,WACxB,KAAK,UAAU,KAAK,IACpB,2BAA2B,OAAO,YAAY;AAAA,IACnD;AACA,WAAO;AAAA,EACR;AACA,6BAA2B,MAAM;AACjC,SAAO;AACR;AAEO,MAAM,0BAA0B,CACtC,SACA,YACA,qBACU;AACV,MACC,WAAW,WAAW,oBACtB,WAAW,KAAK,CAAC,SAAS,KAAK,eAAe,MAAS,GACtD;AACD,YAAQ,kBAAkB;AAAA,MACzB,SACC;AAAA,MACD,UAAU;AAAA,IACX,CAAC;AAAA,EACF;AACD;", "names": []}