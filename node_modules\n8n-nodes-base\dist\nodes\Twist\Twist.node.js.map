{"version": 3, "sources": ["../../../nodes/Twist/Twist.node.ts"], "sourcesContent": ["import moment from 'moment-timezone';\nimport type {\n\tIDataObject,\n\tIExecuteFunctions,\n\tILoadOptionsFunctions,\n\tINodeExecutionData,\n\tINodePropertyOptions,\n\tINodeType,\n\tINodeTypeDescription,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes } from 'n8n-workflow';\nimport { v4 as uuid } from 'uuid';\n\nimport { channelFields, channelOperations } from './ChannelDescription';\nimport { commentFields, commentOperations } from './CommentDescription';\nimport { twistApiRequest } from './GenericFunctions';\nimport {\n\tmessageConversationFields,\n\tmessageConversationOperations,\n} from './MessageConversationDescription';\nimport { threadFields, threadOperations } from './ThreadDescription';\n\nexport class Twist implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Twist',\n\t\tname: 'twist',\n\t\t// eslint-disable-next-line n8n-nodes-base/node-class-description-icon-not-svg\n\t\ticon: 'file:twist.png',\n\t\tgroup: ['input'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume Twist API',\n\t\tdefaults: {\n\t\t\tname: 'Twist',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'twistOAuth2Api',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Channel',\n\t\t\t\t\t\tvalue: 'channel',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Comment',\n\t\t\t\t\t\tvalue: 'comment',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Message Conversation',\n\t\t\t\t\t\tvalue: 'messageConversation',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Thread',\n\t\t\t\t\t\tvalue: 'thread',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'messageConversation',\n\t\t\t},\n\t\t\t...channelOperations,\n\t\t\t...channelFields,\n\t\t\t...commentOperations,\n\t\t\t...commentFields,\n\t\t\t...messageConversationOperations,\n\t\t\t...messageConversationFields,\n\t\t\t...threadOperations,\n\t\t\t...threadFields,\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\t// Get all the available workspaces to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getWorkspaces(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst workspaces = await twistApiRequest.call(this, 'GET', '/workspaces/get');\n\t\t\t\tfor (const workspace of workspaces) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: workspace.name,\n\t\t\t\t\t\tvalue: workspace.id,\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the available conversations to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getConversations(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst qs: IDataObject = {\n\t\t\t\t\tworkspace_id: this.getCurrentNodeParameter('workspaceId') as string,\n\t\t\t\t};\n\t\t\t\tconst conversations = await twistApiRequest.call(this, 'GET', '/conversations/get', {}, qs);\n\t\t\t\tfor (const conversation of conversations) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: conversation.title || conversation.id,\n\t\t\t\t\t\tvalue: conversation.id,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\n\t\t\t// Get all the available users to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getUsers(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst qs: IDataObject = {\n\t\t\t\t\tid: this.getCurrentNodeParameter('workspaceId') as string,\n\t\t\t\t};\n\t\t\t\tconst users = await twistApiRequest.call(this, 'GET', '/workspaces/get_users', {}, qs);\n\t\t\t\tfor (const user of users) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: user.name,\n\t\t\t\t\t\tvalue: user.id,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\n\t\t\t// Get all the available groups to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getGroups(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst qs: IDataObject = {\n\t\t\t\t\tworkspace_id: this.getCurrentNodeParameter('workspaceId') as string,\n\t\t\t\t};\n\t\t\t\tconst groups = await twistApiRequest.call(this, 'GET', '/groups/get', {}, qs);\n\t\t\t\tfor (const group of groups) {\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: group.name,\n\t\t\t\t\t\tvalue: group.id,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: IDataObject[] = [];\n\t\tconst length = items.length;\n\t\tconst qs: IDataObject = {};\n\t\tlet responseData;\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'channel') {\n\t\t\t\t\t//https://developer.twist.com/v3/#add-channel\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst workspaceId = this.getNodeParameter('workspaceId', i) as string;\n\t\t\t\t\t\tconst name = this.getNodeParameter('name', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tworkspace_id: workspaceId,\n\t\t\t\t\t\t\tname,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'POST', '/channels/add', body);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#remove-channel\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tqs.id = this.getNodeParameter('channelId', i) as string;\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'POST', '/channels/remove', {}, qs);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#get-channel\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tqs.id = this.getNodeParameter('channelId', i) as string;\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'GET', '/channels/getone', {}, qs);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#get-all-channels\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst workspaceId = this.getNodeParameter('workspaceId', i) as string;\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tconst filters = this.getNodeParameter('filters', i);\n\t\t\t\t\t\tqs.workspace_id = workspaceId;\n\t\t\t\t\t\tObject.assign(qs, filters);\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'GET', '/channels/get', {}, qs);\n\n\t\t\t\t\t\tif (!returnAll) {\n\t\t\t\t\t\t\tconst limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = responseData.splice(0, limit);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#update-channel\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst channelId = this.getNodeParameter('channelId', i) as string;\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tid: channelId,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tObject.assign(body, updateFields);\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'POST', '/channels/update', body);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#archive-channel\n\t\t\t\t\tif (operation === 'archive') {\n\t\t\t\t\t\tqs.id = this.getNodeParameter('channelId', i) as string;\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'POST', '/channels/archive', {}, qs);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#unarchive-channel\n\t\t\t\t\tif (operation === 'unarchive') {\n\t\t\t\t\t\tqs.id = this.getNodeParameter('channelId', i) as string;\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'POST', '/channels/unarchive', {}, qs);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (resource === 'comment') {\n\t\t\t\t\t//https://developer.twist.com/v3/#add-comment\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst threadId = this.getNodeParameter('threadId', i) as string;\n\t\t\t\t\t\tconst content = this.getNodeParameter('content', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tthread_id: threadId,\n\t\t\t\t\t\t\tcontent,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\n\t\t\t\t\t\tif (body.actionsUi) {\n\t\t\t\t\t\t\tconst actions = (body.actionsUi as IDataObject).actionValues as IDataObject[];\n\n\t\t\t\t\t\t\tif (actions) {\n\t\t\t\t\t\t\t\tbody.actions = actions;\n\t\t\t\t\t\t\t\tdelete body.actionsUi;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (body.binaryProperties) {\n\t\t\t\t\t\t\tconst binaryProperties = (body.binaryProperties as string).split(',');\n\n\t\t\t\t\t\t\tconst attachments: IDataObject[] = [];\n\n\t\t\t\t\t\t\tfor (const binaryProperty of binaryProperties) {\n\t\t\t\t\t\t\t\tconst binaryData = this.helpers.assertBinaryData(i, binaryProperty);\n\t\t\t\t\t\t\t\tconst dataBuffer = await this.helpers.getBinaryDataBuffer(i, binaryProperty);\n\n\t\t\t\t\t\t\t\tattachments.push(\n\t\t\t\t\t\t\t\t\t(await twistApiRequest.call(\n\t\t\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t\t\t\t'/attachments/upload',\n\t\t\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\tformData: {\n\t\t\t\t\t\t\t\t\t\t\t\tfile_name: {\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue: dataBuffer,\n\t\t\t\t\t\t\t\t\t\t\t\t\toptions: {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfilename: binaryData.fileName,\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t\tattachment_id: uuid(),\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t)) as IDataObject,\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tbody.attachments = attachments;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (body.direct_mentions) {\n\t\t\t\t\t\t\tconst directMentions: string[] = [];\n\t\t\t\t\t\t\tfor (const directMention of body.direct_mentions as number[]) {\n\t\t\t\t\t\t\t\tdirectMentions.push(`[name](twist-mention://${directMention})`);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbody.content = `${directMentions.join(' ')} ${body.content}`;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'POST', '/comments/add', body);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#remove-comment\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tqs.id = this.getNodeParameter('commentId', i) as string;\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'POST', '/comments/remove', {}, qs);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#get-comment\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tqs.id = this.getNodeParameter('commentId', i) as string;\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'GET', '/comments/getone', {}, qs);\n\t\t\t\t\t\tresponseData = responseData?.comment;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#get-all-comments\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst threadId = this.getNodeParameter('threadId', i) as string;\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tconst filters = this.getNodeParameter('filters', i);\n\t\t\t\t\t\tqs.thread_id = threadId;\n\n\t\t\t\t\t\tObject.assign(qs, filters);\n\t\t\t\t\t\tif (!returnAll) {\n\t\t\t\t\t\t\tqs.limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (qs.older_than_ts) {\n\t\t\t\t\t\t\tqs.older_than_ts = moment(qs.older_than_ts as string).unix();\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (qs.newer_than_ts) {\n\t\t\t\t\t\t\tqs.newer_than_ts = moment(qs.newer_than_ts as string).unix();\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'GET', '/comments/get', {}, qs);\n\t\t\t\t\t\tif (qs.as_ids) {\n\t\t\t\t\t\t\tresponseData = (responseData as number[]).map((id) => ({ ID: id }));\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#update-comment\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst commentId = this.getNodeParameter('commentId', i) as string;\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tid: commentId,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tObject.assign(body, updateFields);\n\n\t\t\t\t\t\tif (body.actionsUi) {\n\t\t\t\t\t\t\tconst actions = (body.actionsUi as IDataObject).actionValues as IDataObject[];\n\n\t\t\t\t\t\t\tif (actions) {\n\t\t\t\t\t\t\t\tbody.actions = actions;\n\t\t\t\t\t\t\t\tdelete body.actionsUi;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (body.binaryProperties) {\n\t\t\t\t\t\t\tconst binaryProperties = (body.binaryProperties as string).split(',');\n\n\t\t\t\t\t\t\tconst attachments: IDataObject[] = [];\n\n\t\t\t\t\t\t\tfor (const binaryProperty of binaryProperties) {\n\t\t\t\t\t\t\t\tconst binaryData = this.helpers.assertBinaryData(i, binaryProperty);\n\t\t\t\t\t\t\t\tconst dataBuffer = await this.helpers.getBinaryDataBuffer(i, binaryProperty);\n\n\t\t\t\t\t\t\t\tattachments.push(\n\t\t\t\t\t\t\t\t\t(await twistApiRequest.call(\n\t\t\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t\t\t\t'/attachments/upload',\n\t\t\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\tformData: {\n\t\t\t\t\t\t\t\t\t\t\t\tfile_name: {\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue: dataBuffer,\n\t\t\t\t\t\t\t\t\t\t\t\t\toptions: {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfilename: binaryData.fileName,\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t\tattachment_id: uuid(),\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t)) as IDataObject,\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tbody.attachments = attachments;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (body.direct_mentions) {\n\t\t\t\t\t\t\tconst directMentions: string[] = [];\n\t\t\t\t\t\t\tfor (const directMention of body.direct_mentions as number[]) {\n\t\t\t\t\t\t\t\tdirectMentions.push(`[name](twist-mention://${directMention})`);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbody.content = `${directMentions.join(' ')} ${body.content}`;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'POST', '/comments/update', body);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (resource === 'messageConversation') {\n\t\t\t\t\t//https://developer.twist.com/v3/#add-message-to-conversation\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst workspaceId = this.getNodeParameter('workspaceId', i) as string;\n\t\t\t\t\t\tconst conversationId = this.getNodeParameter('conversationId', i) as string;\n\t\t\t\t\t\tconst content = this.getNodeParameter('content', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tconversation_id: conversationId,\n\t\t\t\t\t\t\tworkspace_id: workspaceId,\n\t\t\t\t\t\t\tcontent,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\n\t\t\t\t\t\tif (body.actionsUi) {\n\t\t\t\t\t\t\tconst actions = (body.actionsUi as IDataObject).actionValues as IDataObject[];\n\n\t\t\t\t\t\t\tif (actions) {\n\t\t\t\t\t\t\t\tbody.actions = actions;\n\t\t\t\t\t\t\t\tdelete body.actionsUi;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (body.binaryProperties) {\n\t\t\t\t\t\t\tconst binaryProperties = (body.binaryProperties as string).split(',');\n\n\t\t\t\t\t\t\tconst attachments: IDataObject[] = [];\n\n\t\t\t\t\t\t\tfor (const binaryProperty of binaryProperties) {\n\t\t\t\t\t\t\t\tconst binaryData = this.helpers.assertBinaryData(i, binaryProperty);\n\t\t\t\t\t\t\t\tconst dataBuffer = await this.helpers.getBinaryDataBuffer(i, binaryProperty);\n\n\t\t\t\t\t\t\t\tattachments.push(\n\t\t\t\t\t\t\t\t\t(await twistApiRequest.call(\n\t\t\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t\t\t\t'/attachments/upload',\n\t\t\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\tformData: {\n\t\t\t\t\t\t\t\t\t\t\t\tfile_name: {\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue: dataBuffer,\n\t\t\t\t\t\t\t\t\t\t\t\t\toptions: {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfilename: binaryData.fileName,\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t\tattachment_id: uuid(),\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t)) as IDataObject,\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tbody.attachments = attachments;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (body.direct_mentions) {\n\t\t\t\t\t\t\tconst directMentions: string[] = [];\n\t\t\t\t\t\t\tfor (const directMention of body.direct_mentions as number[]) {\n\t\t\t\t\t\t\t\tdirectMentions.push(`[name](twist-mention://${directMention})`);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbody.content = `${directMentions.join(' ')} ${body.content}`;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// if (body.direct_group_mentions) {\n\t\t\t\t\t\t// \tconst directGroupMentions: string[] = [];\n\t\t\t\t\t\t// \tfor (const directGroupMention of body.direct_group_mentions as number[]) {\n\t\t\t\t\t\t// \t\tdirectGroupMentions.push(`[Group name](twist-group-mention://${directGroupMention})`);\n\t\t\t\t\t\t// \t}\n\t\t\t\t\t\t// \tbody.content = `${directGroupMentions.join(' ')} ${body.content}`;\n\t\t\t\t\t\t// }\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/conversation_messages/add',\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#get-message\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tqs.id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t'/conversation_messages/getone',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#get-all-messages\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst conversationId = this.getNodeParameter('conversationId', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tqs.conversation_id = conversationId;\n\t\t\t\t\t\tObject.assign(qs, additionalFields);\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t'/conversation_messages/get',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#remove-message-from-conversation\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tqs.id = this.getNodeParameter('id', i) as string;\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/conversation_messages/remove',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#update-message-in-conversation\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst id = this.getNodeParameter('id', i) as string;\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tid,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tObject.assign(body, updateFields);\n\n\t\t\t\t\t\tif (body.actionsUi) {\n\t\t\t\t\t\t\tconst actions = (body.actionsUi as IDataObject).actionValues as IDataObject[];\n\n\t\t\t\t\t\t\tif (actions) {\n\t\t\t\t\t\t\t\tbody.actions = actions;\n\t\t\t\t\t\t\t\tdelete body.actionsUi;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (body.binaryProperties) {\n\t\t\t\t\t\t\tconst binaryProperties = (body.binaryProperties as string).split(',');\n\n\t\t\t\t\t\t\tconst attachments: IDataObject[] = [];\n\n\t\t\t\t\t\t\tfor (const binaryProperty of binaryProperties) {\n\t\t\t\t\t\t\t\tconst binaryData = this.helpers.assertBinaryData(i, binaryProperty);\n\t\t\t\t\t\t\t\tconst dataBuffer = await this.helpers.getBinaryDataBuffer(i, binaryProperty);\n\n\t\t\t\t\t\t\t\tattachments.push(\n\t\t\t\t\t\t\t\t\t(await twistApiRequest.call(\n\t\t\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t\t\t\t'/attachments/upload',\n\t\t\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\tformData: {\n\t\t\t\t\t\t\t\t\t\t\t\tfile_name: {\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue: dataBuffer,\n\t\t\t\t\t\t\t\t\t\t\t\t\toptions: {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfilename: binaryData.fileName,\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t\tattachment_id: uuid(),\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t)) as IDataObject,\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tbody.attachments = attachments;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (body.direct_mentions) {\n\t\t\t\t\t\t\tconst directMentions: string[] = [];\n\t\t\t\t\t\t\tfor (const directMention of body.direct_mentions as number[]) {\n\t\t\t\t\t\t\t\tdirectMentions.push(`[name](twist-mention://${directMention})`);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbody.content = `${directMentions.join(' ')} ${body.content}`;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/conversation_messages/update',\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (resource === 'thread') {\n\t\t\t\t\t//https://developer.twist.com/v3/#add-thread\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst channelId = this.getNodeParameter('channelId', i) as string;\n\t\t\t\t\t\tconst title = this.getNodeParameter('title', i) as string;\n\t\t\t\t\t\tconst content = this.getNodeParameter('content', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tchannel_id: channelId,\n\t\t\t\t\t\t\tcontent,\n\t\t\t\t\t\t\ttitle,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tObject.assign(body, additionalFields);\n\n\t\t\t\t\t\tif (body.actionsUi) {\n\t\t\t\t\t\t\tconst actions = (body.actionsUi as IDataObject).actionValues as IDataObject[];\n\n\t\t\t\t\t\t\tif (actions) {\n\t\t\t\t\t\t\t\tbody.actions = actions;\n\t\t\t\t\t\t\t\tdelete body.actionsUi;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (body.binaryProperties) {\n\t\t\t\t\t\t\tconst binaryProperties = (body.binaryProperties as string).split(',');\n\n\t\t\t\t\t\t\tconst attachments: IDataObject[] = [];\n\n\t\t\t\t\t\t\tfor (const binaryProperty of binaryProperties) {\n\t\t\t\t\t\t\t\tconst binaryData = this.helpers.assertBinaryData(i, binaryProperty);\n\t\t\t\t\t\t\t\tconst dataBuffer = await this.helpers.getBinaryDataBuffer(i, binaryProperty);\n\n\t\t\t\t\t\t\t\tattachments.push(\n\t\t\t\t\t\t\t\t\t(await twistApiRequest.call(\n\t\t\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t\t\t\t'/attachments/upload',\n\t\t\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\tformData: {\n\t\t\t\t\t\t\t\t\t\t\t\tfile_name: {\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue: dataBuffer,\n\t\t\t\t\t\t\t\t\t\t\t\t\toptions: {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfilename: binaryData.fileName,\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t\tattachment_id: uuid(),\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t)) as IDataObject,\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tbody.attachments = attachments;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (body.direct_mentions) {\n\t\t\t\t\t\t\tconst directMentions: string[] = [];\n\t\t\t\t\t\t\tfor (const directMention of body.direct_mentions as number[]) {\n\t\t\t\t\t\t\t\tdirectMentions.push(`[name](twist-mention://${directMention})`);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbody.content = `${directMentions.join(' ')} ${body.content}`;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'POST', '/threads/add', body);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#remove-thread\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tqs.id = this.getNodeParameter('threadId', i) as string;\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'POST', '/threads/remove', {}, qs);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#get-thread\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tqs.id = this.getNodeParameter('threadId', i) as string;\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'GET', '/threads/getone', {}, qs);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#get-all-threads\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst channelId = this.getNodeParameter('channelId', i) as string;\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tconst filters = this.getNodeParameter('filters', i);\n\t\t\t\t\t\tqs.channel_id = channelId;\n\n\t\t\t\t\t\tObject.assign(qs, filters);\n\t\t\t\t\t\tif (!returnAll) {\n\t\t\t\t\t\t\tqs.limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (qs.older_than_ts) {\n\t\t\t\t\t\t\tqs.older_than_ts = moment(qs.older_than_ts as string).unix();\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (qs.newer_than_ts) {\n\t\t\t\t\t\t\tqs.newer_than_ts = moment(qs.newer_than_ts as string).unix();\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'GET', '/threads/get', {}, qs);\n\t\t\t\t\t\tif (qs.as_ids) {\n\t\t\t\t\t\t\tresponseData = (responseData as number[]).map((id) => ({ ID: id }));\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.twist.com/v3/#update-thread\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst threadId = this.getNodeParameter('threadId', i) as string;\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tid: threadId,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tObject.assign(body, updateFields);\n\n\t\t\t\t\t\tif (body.actionsUi) {\n\t\t\t\t\t\t\tconst actions = (body.actionsUi as IDataObject).actionValues as IDataObject[];\n\n\t\t\t\t\t\t\tif (actions) {\n\t\t\t\t\t\t\t\tbody.actions = actions;\n\t\t\t\t\t\t\t\tdelete body.actionsUi;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (body.binaryProperties) {\n\t\t\t\t\t\t\tconst binaryProperties = (body.binaryProperties as string).split(',');\n\n\t\t\t\t\t\t\tconst attachments: IDataObject[] = [];\n\n\t\t\t\t\t\t\tfor (const binaryProperty of binaryProperties) {\n\t\t\t\t\t\t\t\tconst binaryData = this.helpers.assertBinaryData(i, binaryProperty);\n\t\t\t\t\t\t\t\tconst dataBuffer = await this.helpers.getBinaryDataBuffer(i, binaryProperty);\n\n\t\t\t\t\t\t\t\tattachments.push(\n\t\t\t\t\t\t\t\t\t(await twistApiRequest.call(\n\t\t\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t\t\t\t'/attachments/upload',\n\t\t\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\tformData: {\n\t\t\t\t\t\t\t\t\t\t\t\tfile_name: {\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue: dataBuffer,\n\t\t\t\t\t\t\t\t\t\t\t\t\toptions: {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfilename: binaryData.fileName,\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t\tattachment_id: uuid(),\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t)) as IDataObject,\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tbody.attachments = attachments;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (body.direct_mentions) {\n\t\t\t\t\t\t\tconst directMentions: string[] = [];\n\t\t\t\t\t\t\tfor (const directMention of body.direct_mentions as number[]) {\n\t\t\t\t\t\t\t\tdirectMentions.push(`[name](twist-mention://${directMention})`);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbody.content = `${directMentions.join(' ')} ${body.content}`;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await twistApiRequest.call(this, 'POST', '/threads/update', body);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (Array.isArray(responseData)) {\n\t\t\t\t\treturnData.push.apply(returnData, responseData as IDataObject[]);\n\t\t\t\t} else {\n\t\t\t\t\treturnData.push(responseData as IDataObject);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ error: error.message });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\t\treturn [this.helpers.returnJsonArray(returnData)];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6BAAmB;AAUnB,0BAAoC;AACpC,kBAA2B;AAE3B,gCAAiD;AACjD,gCAAiD;AACjD,8BAAgC;AAChC,4CAGO;AACP,+BAA+C;AAExC,MAAM,MAA2B;AAAA,EAAjC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA;AAAA,MAEN,MAAM;AAAA,MACN,OAAO,CAAC,OAAO;AAAA,MACf,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA;AAAA;AAAA,QAGZ,MAAM,gBAA4E;AACjF,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,aAAa,MAAM,wCAAgB,KAAK,MAAM,OAAO,iBAAiB;AAC5E,qBAAW,aAAa,YAAY;AACnC,uBAAW,KAAK;AAAA,cACf,MAAM,UAAU;AAAA,cAChB,OAAO,UAAU;AAAA,YAClB,CAAC;AAAA,UACF;AAEA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,mBAA+E;AACpF,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,KAAkB;AAAA,YACvB,cAAc,KAAK,wBAAwB,aAAa;AAAA,UACzD;AACA,gBAAM,gBAAgB,MAAM,wCAAgB,KAAK,MAAM,OAAO,sBAAsB,CAAC,GAAG,EAAE;AAC1F,qBAAW,gBAAgB,eAAe;AACzC,uBAAW,KAAK;AAAA,cACf,MAAM,aAAa,SAAS,aAAa;AAAA,cACzC,OAAO,aAAa;AAAA,YACrB,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAIA,MAAM,WAAuE;AAC5E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,KAAkB;AAAA,YACvB,IAAI,KAAK,wBAAwB,aAAa;AAAA,UAC/C;AACA,gBAAM,QAAQ,MAAM,wCAAgB,KAAK,MAAM,OAAO,yBAAyB,CAAC,GAAG,EAAE;AACrF,qBAAW,QAAQ,OAAO;AACzB,uBAAW,KAAK;AAAA,cACf,MAAM,KAAK;AAAA,cACX,OAAO,KAAK;AAAA,YACb,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAIA,MAAM,YAAwE;AAC7E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,KAAkB;AAAA,YACvB,cAAc,KAAK,wBAAwB,aAAa;AAAA,UACzD;AACA,gBAAM,SAAS,MAAM,wCAAgB,KAAK,MAAM,OAAO,eAAe,CAAC,GAAG,EAAE;AAC5E,qBAAW,SAAS,QAAQ;AAC3B,uBAAW,KAAK;AAAA,cACf,MAAM,MAAM;AAAA,cACZ,OAAO,MAAM;AAAA,YACd,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAA4B,CAAC;AACnC,UAAM,SAAS,MAAM;AACrB,UAAM,KAAkB,CAAC;AACzB,QAAI;AACJ,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,YAAI,aAAa,WAAW;AAE3B,cAAI,cAAc,UAAU;AAC3B,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAC1D,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC5C,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,kBAAM,OAAoB;AAAA,cACzB,cAAc;AAAA,cACd;AAAA,YACD;AACA,mBAAO,OAAO,MAAM,gBAAgB;AAEpC,2BAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,iBAAiB,IAAI;AAAA,UAC9E;AAEA,cAAI,cAAc,UAAU;AAC3B,eAAG,KAAK,KAAK,iBAAiB,aAAa,CAAC;AAE5C,2BAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,oBAAoB,CAAC,GAAG,EAAE;AAAA,UACnF;AAEA,cAAI,cAAc,OAAO;AACxB,eAAG,KAAK,KAAK,iBAAiB,aAAa,CAAC;AAE5C,2BAAe,MAAM,wCAAgB,KAAK,MAAM,OAAO,oBAAoB,CAAC,GAAG,EAAE;AAAA,UAClF;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAC1D,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,eAAG,eAAe;AAClB,mBAAO,OAAO,IAAI,OAAO;AAEzB,2BAAe,MAAM,wCAAgB,KAAK,MAAM,OAAO,iBAAiB,CAAC,GAAG,EAAE;AAE9E,gBAAI,CAAC,WAAW;AACf,oBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,6BAAe,aAAa,OAAO,GAAG,KAAK;AAAA,YAC5C;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,kBAAM,OAAoB;AAAA,cACzB,IAAI;AAAA,YACL;AACA,mBAAO,OAAO,MAAM,YAAY;AAEhC,2BAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,oBAAoB,IAAI;AAAA,UACjF;AAEA,cAAI,cAAc,WAAW;AAC5B,eAAG,KAAK,KAAK,iBAAiB,aAAa,CAAC;AAE5C,2BAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,GAAG,EAAE;AAAA,UACpF;AAEA,cAAI,cAAc,aAAa;AAC9B,eAAG,KAAK,KAAK,iBAAiB,aAAa,CAAC;AAE5C,2BAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,uBAAuB,CAAC,GAAG,EAAE;AAAA,UACtF;AAAA,QACD;AACA,YAAI,aAAa,WAAW;AAE3B,cAAI,cAAc,UAAU;AAC3B,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,kBAAM,OAAoB;AAAA,cACzB,WAAW;AAAA,cACX;AAAA,YACD;AACA,mBAAO,OAAO,MAAM,gBAAgB;AAEpC,gBAAI,KAAK,WAAW;AACnB,oBAAM,UAAW,KAAK,UAA0B;AAEhD,kBAAI,SAAS;AACZ,qBAAK,UAAU;AACf,uBAAO,KAAK;AAAA,cACb;AAAA,YACD;AAEA,gBAAI,KAAK,kBAAkB;AAC1B,oBAAM,mBAAoB,KAAK,iBAA4B,MAAM,GAAG;AAEpE,oBAAM,cAA6B,CAAC;AAEpC,yBAAW,kBAAkB,kBAAkB;AAC9C,sBAAM,aAAa,KAAK,QAAQ,iBAAiB,GAAG,cAAc;AAClE,sBAAM,aAAa,MAAM,KAAK,QAAQ,oBAAoB,GAAG,cAAc;AAE3E,4BAAY;AAAA,kBACV,MAAM,wCAAgB;AAAA,oBACtB;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA,CAAC;AAAA,oBACD,CAAC;AAAA,oBACD;AAAA,sBACC,UAAU;AAAA,wBACT,WAAW;AAAA,0BACV,OAAO;AAAA,0BACP,SAAS;AAAA,4BACR,UAAU,WAAW;AAAA,0BACtB;AAAA,wBACD;AAAA,wBACA,mBAAe,YAAAA,IAAK;AAAA,sBACrB;AAAA,oBACD;AAAA,kBACD;AAAA,gBACD;AAAA,cACD;AAEA,mBAAK,cAAc;AAAA,YACpB;AAEA,gBAAI,KAAK,iBAAiB;AACzB,oBAAM,iBAA2B,CAAC;AAClC,yBAAW,iBAAiB,KAAK,iBAA6B;AAC7D,+BAAe,KAAK,0BAA0B,aAAa,GAAG;AAAA,cAC/D;AACA,mBAAK,UAAU,GAAG,eAAe,KAAK,GAAG,CAAC,IAAI,KAAK,OAAO;AAAA,YAC3D;AAEA,2BAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,iBAAiB,IAAI;AAAA,UAC9E;AAEA,cAAI,cAAc,UAAU;AAC3B,eAAG,KAAK,KAAK,iBAAiB,aAAa,CAAC;AAE5C,2BAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,oBAAoB,CAAC,GAAG,EAAE;AAAA,UACnF;AAEA,cAAI,cAAc,OAAO;AACxB,eAAG,KAAK,KAAK,iBAAiB,aAAa,CAAC;AAE5C,2BAAe,MAAM,wCAAgB,KAAK,MAAM,OAAO,oBAAoB,CAAC,GAAG,EAAE;AACjF,2BAAe,cAAc;AAAA,UAC9B;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,eAAG,YAAY;AAEf,mBAAO,OAAO,IAAI,OAAO;AACzB,gBAAI,CAAC,WAAW;AACf,iBAAG,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAAA,YAC5C;AACA,gBAAI,GAAG,eAAe;AACrB,iBAAG,oBAAgB,uBAAAC,SAAO,GAAG,aAAuB,EAAE,KAAK;AAAA,YAC5D;AACA,gBAAI,GAAG,eAAe;AACrB,iBAAG,oBAAgB,uBAAAA,SAAO,GAAG,aAAuB,EAAE,KAAK;AAAA,YAC5D;AAEA,2BAAe,MAAM,wCAAgB,KAAK,MAAM,OAAO,iBAAiB,CAAC,GAAG,EAAE;AAC9E,gBAAI,GAAG,QAAQ;AACd,6BAAgB,aAA0B,IAAI,CAAC,QAAQ,EAAE,IAAI,GAAG,EAAE;AAAA,YACnE;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,kBAAM,OAAoB;AAAA,cACzB,IAAI;AAAA,YACL;AACA,mBAAO,OAAO,MAAM,YAAY;AAEhC,gBAAI,KAAK,WAAW;AACnB,oBAAM,UAAW,KAAK,UAA0B;AAEhD,kBAAI,SAAS;AACZ,qBAAK,UAAU;AACf,uBAAO,KAAK;AAAA,cACb;AAAA,YACD;AAEA,gBAAI,KAAK,kBAAkB;AAC1B,oBAAM,mBAAoB,KAAK,iBAA4B,MAAM,GAAG;AAEpE,oBAAM,cAA6B,CAAC;AAEpC,yBAAW,kBAAkB,kBAAkB;AAC9C,sBAAM,aAAa,KAAK,QAAQ,iBAAiB,GAAG,cAAc;AAClE,sBAAM,aAAa,MAAM,KAAK,QAAQ,oBAAoB,GAAG,cAAc;AAE3E,4BAAY;AAAA,kBACV,MAAM,wCAAgB;AAAA,oBACtB;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA,CAAC;AAAA,oBACD,CAAC;AAAA,oBACD;AAAA,sBACC,UAAU;AAAA,wBACT,WAAW;AAAA,0BACV,OAAO;AAAA,0BACP,SAAS;AAAA,4BACR,UAAU,WAAW;AAAA,0BACtB;AAAA,wBACD;AAAA,wBACA,mBAAe,YAAAD,IAAK;AAAA,sBACrB;AAAA,oBACD;AAAA,kBACD;AAAA,gBACD;AAAA,cACD;AAEA,mBAAK,cAAc;AAAA,YACpB;AAEA,gBAAI,KAAK,iBAAiB;AACzB,oBAAM,iBAA2B,CAAC;AAClC,yBAAW,iBAAiB,KAAK,iBAA6B;AAC7D,+BAAe,KAAK,0BAA0B,aAAa,GAAG;AAAA,cAC/D;AACA,mBAAK,UAAU,GAAG,eAAe,KAAK,GAAG,CAAC,IAAI,KAAK,OAAO;AAAA,YAC3D;AAEA,2BAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,oBAAoB,IAAI;AAAA,UACjF;AAAA,QACD;AACA,YAAI,aAAa,uBAAuB;AAEvC,cAAI,cAAc,UAAU;AAC3B,kBAAM,cAAc,KAAK,iBAAiB,eAAe,CAAC;AAC1D,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,kBAAM,OAAoB;AAAA,cACzB,iBAAiB;AAAA,cACjB,cAAc;AAAA,cACd;AAAA,YACD;AACA,mBAAO,OAAO,MAAM,gBAAgB;AAEpC,gBAAI,KAAK,WAAW;AACnB,oBAAM,UAAW,KAAK,UAA0B;AAEhD,kBAAI,SAAS;AACZ,qBAAK,UAAU;AACf,uBAAO,KAAK;AAAA,cACb;AAAA,YACD;AAEA,gBAAI,KAAK,kBAAkB;AAC1B,oBAAM,mBAAoB,KAAK,iBAA4B,MAAM,GAAG;AAEpE,oBAAM,cAA6B,CAAC;AAEpC,yBAAW,kBAAkB,kBAAkB;AAC9C,sBAAM,aAAa,KAAK,QAAQ,iBAAiB,GAAG,cAAc;AAClE,sBAAM,aAAa,MAAM,KAAK,QAAQ,oBAAoB,GAAG,cAAc;AAE3E,4BAAY;AAAA,kBACV,MAAM,wCAAgB;AAAA,oBACtB;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA,CAAC;AAAA,oBACD,CAAC;AAAA,oBACD;AAAA,sBACC,UAAU;AAAA,wBACT,WAAW;AAAA,0BACV,OAAO;AAAA,0BACP,SAAS;AAAA,4BACR,UAAU,WAAW;AAAA,0BACtB;AAAA,wBACD;AAAA,wBACA,mBAAe,YAAAA,IAAK;AAAA,sBACrB;AAAA,oBACD;AAAA,kBACD;AAAA,gBACD;AAAA,cACD;AAEA,mBAAK,cAAc;AAAA,YACpB;AAEA,gBAAI,KAAK,iBAAiB;AACzB,oBAAM,iBAA2B,CAAC;AAClC,yBAAW,iBAAiB,KAAK,iBAA6B;AAC7D,+BAAe,KAAK,0BAA0B,aAAa,GAAG;AAAA,cAC/D;AACA,mBAAK,UAAU,GAAG,eAAe,KAAK,GAAG,CAAC,IAAI,KAAK,OAAO;AAAA,YAC3D;AAUA,2BAAe,MAAM,wCAAgB;AAAA,cACpC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,OAAO;AACxB,eAAG,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAErC,2BAAe,MAAM,wCAAgB;AAAA,cACpC;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,eAAG,kBAAkB;AACrB,mBAAO,OAAO,IAAI,gBAAgB;AAElC,2BAAe,MAAM,wCAAgB;AAAA,cACpC;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,eAAG,KAAK,KAAK,iBAAiB,MAAM,CAAC;AAErC,2BAAe,MAAM,wCAAgB;AAAA,cACpC;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,KAAK,KAAK,iBAAiB,MAAM,CAAC;AACxC,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,kBAAM,OAAoB;AAAA,cACzB;AAAA,YACD;AACA,mBAAO,OAAO,MAAM,YAAY;AAEhC,gBAAI,KAAK,WAAW;AACnB,oBAAM,UAAW,KAAK,UAA0B;AAEhD,kBAAI,SAAS;AACZ,qBAAK,UAAU;AACf,uBAAO,KAAK;AAAA,cACb;AAAA,YACD;AAEA,gBAAI,KAAK,kBAAkB;AAC1B,oBAAM,mBAAoB,KAAK,iBAA4B,MAAM,GAAG;AAEpE,oBAAM,cAA6B,CAAC;AAEpC,yBAAW,kBAAkB,kBAAkB;AAC9C,sBAAM,aAAa,KAAK,QAAQ,iBAAiB,GAAG,cAAc;AAClE,sBAAM,aAAa,MAAM,KAAK,QAAQ,oBAAoB,GAAG,cAAc;AAE3E,4BAAY;AAAA,kBACV,MAAM,wCAAgB;AAAA,oBACtB;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA,CAAC;AAAA,oBACD,CAAC;AAAA,oBACD;AAAA,sBACC,UAAU;AAAA,wBACT,WAAW;AAAA,0BACV,OAAO;AAAA,0BACP,SAAS;AAAA,4BACR,UAAU,WAAW;AAAA,0BACtB;AAAA,wBACD;AAAA,wBACA,mBAAe,YAAAA,IAAK;AAAA,sBACrB;AAAA,oBACD;AAAA,kBACD;AAAA,gBACD;AAAA,cACD;AAEA,mBAAK,cAAc;AAAA,YACpB;AAEA,gBAAI,KAAK,iBAAiB;AACzB,oBAAM,iBAA2B,CAAC;AAClC,yBAAW,iBAAiB,KAAK,iBAA6B;AAC7D,+BAAe,KAAK,0BAA0B,aAAa,GAAG;AAAA,cAC/D;AACA,mBAAK,UAAU,GAAG,eAAe,KAAK,GAAG,CAAC,IAAI,KAAK,OAAO;AAAA,YAC3D;AAEA,2BAAe,MAAM,wCAAgB;AAAA,cACpC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAAA,QACD;AACA,YAAI,aAAa,UAAU;AAE1B,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,kBAAM,OAAoB;AAAA,cACzB,YAAY;AAAA,cACZ;AAAA,cACA;AAAA,YACD;AACA,mBAAO,OAAO,MAAM,gBAAgB;AAEpC,gBAAI,KAAK,WAAW;AACnB,oBAAM,UAAW,KAAK,UAA0B;AAEhD,kBAAI,SAAS;AACZ,qBAAK,UAAU;AACf,uBAAO,KAAK;AAAA,cACb;AAAA,YACD;AAEA,gBAAI,KAAK,kBAAkB;AAC1B,oBAAM,mBAAoB,KAAK,iBAA4B,MAAM,GAAG;AAEpE,oBAAM,cAA6B,CAAC;AAEpC,yBAAW,kBAAkB,kBAAkB;AAC9C,sBAAM,aAAa,KAAK,QAAQ,iBAAiB,GAAG,cAAc;AAClE,sBAAM,aAAa,MAAM,KAAK,QAAQ,oBAAoB,GAAG,cAAc;AAE3E,4BAAY;AAAA,kBACV,MAAM,wCAAgB;AAAA,oBACtB;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA,CAAC;AAAA,oBACD,CAAC;AAAA,oBACD;AAAA,sBACC,UAAU;AAAA,wBACT,WAAW;AAAA,0BACV,OAAO;AAAA,0BACP,SAAS;AAAA,4BACR,UAAU,WAAW;AAAA,0BACtB;AAAA,wBACD;AAAA,wBACA,mBAAe,YAAAA,IAAK;AAAA,sBACrB;AAAA,oBACD;AAAA,kBACD;AAAA,gBACD;AAAA,cACD;AAEA,mBAAK,cAAc;AAAA,YACpB;AAEA,gBAAI,KAAK,iBAAiB;AACzB,oBAAM,iBAA2B,CAAC;AAClC,yBAAW,iBAAiB,KAAK,iBAA6B;AAC7D,+BAAe,KAAK,0BAA0B,aAAa,GAAG;AAAA,cAC/D;AACA,mBAAK,UAAU,GAAG,eAAe,KAAK,GAAG,CAAC,IAAI,KAAK,OAAO;AAAA,YAC3D;AAEA,2BAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,gBAAgB,IAAI;AAAA,UAC7E;AAEA,cAAI,cAAc,UAAU;AAC3B,eAAG,KAAK,KAAK,iBAAiB,YAAY,CAAC;AAE3C,2BAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,mBAAmB,CAAC,GAAG,EAAE;AAAA,UAClF;AAEA,cAAI,cAAc,OAAO;AACxB,eAAG,KAAK,KAAK,iBAAiB,YAAY,CAAC;AAE3C,2BAAe,MAAM,wCAAgB,KAAK,MAAM,OAAO,mBAAmB,CAAC,GAAG,EAAE;AAAA,UACjF;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,eAAG,aAAa;AAEhB,mBAAO,OAAO,IAAI,OAAO;AACzB,gBAAI,CAAC,WAAW;AACf,iBAAG,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAAA,YAC5C;AACA,gBAAI,GAAG,eAAe;AACrB,iBAAG,oBAAgB,uBAAAC,SAAO,GAAG,aAAuB,EAAE,KAAK;AAAA,YAC5D;AACA,gBAAI,GAAG,eAAe;AACrB,iBAAG,oBAAgB,uBAAAA,SAAO,GAAG,aAAuB,EAAE,KAAK;AAAA,YAC5D;AAEA,2BAAe,MAAM,wCAAgB,KAAK,MAAM,OAAO,gBAAgB,CAAC,GAAG,EAAE;AAC7E,gBAAI,GAAG,QAAQ;AACd,6BAAgB,aAA0B,IAAI,CAAC,QAAQ,EAAE,IAAI,GAAG,EAAE;AAAA,YACnE;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,kBAAM,OAAoB;AAAA,cACzB,IAAI;AAAA,YACL;AACA,mBAAO,OAAO,MAAM,YAAY;AAEhC,gBAAI,KAAK,WAAW;AACnB,oBAAM,UAAW,KAAK,UAA0B;AAEhD,kBAAI,SAAS;AACZ,qBAAK,UAAU;AACf,uBAAO,KAAK;AAAA,cACb;AAAA,YACD;AAEA,gBAAI,KAAK,kBAAkB;AAC1B,oBAAM,mBAAoB,KAAK,iBAA4B,MAAM,GAAG;AAEpE,oBAAM,cAA6B,CAAC;AAEpC,yBAAW,kBAAkB,kBAAkB;AAC9C,sBAAM,aAAa,KAAK,QAAQ,iBAAiB,GAAG,cAAc;AAClE,sBAAM,aAAa,MAAM,KAAK,QAAQ,oBAAoB,GAAG,cAAc;AAE3E,4BAAY;AAAA,kBACV,MAAM,wCAAgB;AAAA,oBACtB;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA,CAAC;AAAA,oBACD,CAAC;AAAA,oBACD;AAAA,sBACC,UAAU;AAAA,wBACT,WAAW;AAAA,0BACV,OAAO;AAAA,0BACP,SAAS;AAAA,4BACR,UAAU,WAAW;AAAA,0BACtB;AAAA,wBACD;AAAA,wBACA,mBAAe,YAAAD,IAAK;AAAA,sBACrB;AAAA,oBACD;AAAA,kBACD;AAAA,gBACD;AAAA,cACD;AAEA,mBAAK,cAAc;AAAA,YACpB;AAEA,gBAAI,KAAK,iBAAiB;AACzB,oBAAM,iBAA2B,CAAC;AAClC,yBAAW,iBAAiB,KAAK,iBAA6B;AAC7D,+BAAe,KAAK,0BAA0B,aAAa,GAAG;AAAA,cAC/D;AACA,mBAAK,UAAU,GAAG,eAAe,KAAK,GAAG,CAAC,IAAI,KAAK,OAAO;AAAA,YAC3D;AAEA,2BAAe,MAAM,wCAAgB,KAAK,MAAM,QAAQ,mBAAmB,IAAI;AAAA,UAChF;AAAA,QACD;AACA,YAAI,MAAM,QAAQ,YAAY,GAAG;AAChC,qBAAW,KAAK,MAAM,YAAY,YAA6B;AAAA,QAChE,OAAO;AACN,qBAAW,KAAK,YAA2B;AAAA,QAC5C;AAAA,MACD,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,OAAO,MAAM,QAAQ,CAAC;AACxC;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AACA,WAAO,CAAC,KAAK,QAAQ,gBAAgB,UAAU,CAAC;AAAA,EACjD;AACD;", "names": ["uuid", "moment"]}