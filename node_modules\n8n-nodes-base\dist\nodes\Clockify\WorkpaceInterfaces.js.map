{"version": 3, "sources": ["../../../nodes/Clockify/WorkpaceInterfaces.ts"], "sourcesContent": ["import type { IHourlyRateDto, IMembershipDto } from './CommonDtos';\n\nexport const AdminOnlyPages = {\n\tPROJECT: 'PROJECT',\n\tTEAM: 'TEAM',\n\tREPORTS: 'REPORTS',\n} as const;\n\nexport type AdminOnlyPagesEnum = (typeof AdminOnlyPages)[keyof typeof AdminOnlyPages];\n\nexport const DaysOfWeek = {\n\tMONDAY: 'MONDAY',\n\tTUESDAY: 'TUESDAY',\n\tWEDNESDAY: 'WEDNESDAY',\n\tTHURSDAY: 'THURSDAY',\n\tFRIDAY: 'FRIDAY',\n\tSATURDAY: 'SATURDAY',\n\tSUNDAY: 'SUNDAY',\n} as const;\n\nexport type DaysOfWeekEnum = (typeof DaysOfWeek)[keyof typeof DaysOfWeek];\n\nexport const DatePeriods = {\n\tDAYS: 'DAYS',\n\tWEEKS: 'WEEKS',\n\tMONTHS: 'MONTHS',\n} as const;\n\nexport type DatePeriodEnum = (typeof DatePeriods)[keyof typeof DatePeriods];\n\nexport const AutomaticLockTypes = {\n\tWEEKLY: 'WEEKLY',\n\tMONTHLY: 'MONTHLY',\n\tOLDER_THAN: 'OLDER_THAN',\n} as const;\n\nexport type AutomaticLockTypeEnum = (typeof AutomaticLockTypes)[keyof typeof AutomaticLockTypes];\n\ninterface IAutomaticLockDto {\n\tchangeDay: DaysOfWeekEnum;\n\tdayOfMonth: number;\n\tfirstDay: DaysOfWeekEnum;\n\tolderThanPeriod: DatePeriodEnum;\n\tolderThanValue: number;\n\ttype: AutomaticLockTypeEnum;\n}\n\ninterface IRound {\n\tminutes: string;\n\tround: string;\n}\n\ninterface IWorkspaceSettingsDto {\n\tadminOnlyPages: AdminOnlyPagesEnum[];\n\tautomaticLock: IAutomaticLockDto;\n\tcanSeeTimeSheet: boolean;\n\tdefaultBillableProjects: boolean;\n\tforceDescription: boolean;\n\tforceProjects: boolean;\n\tforceTags: boolean;\n\tforceTasks: boolean;\n\tlockTimeEntries: string;\n\tonlyAdminsCreateProject: boolean;\n\tonlyAdminsCreateTag: boolean;\n\tonlyAdminsSeeAllTimeEntries: boolean;\n\tonlyAdminsSeeBillableRates: boolean;\n\tonlyAdminsSeeDashboard: boolean;\n\tonlyAdminsSeePublicProjectsEntries: boolean;\n\tprojectFavorites: boolean;\n\tprojectGroupingLabel: string;\n\tprojectPickerSpecialFilter: boolean;\n\tround: IRound;\n\ttimeRoundingInReports: boolean;\n\ttrackTimeDownToSecond: boolean;\n}\n\nexport interface IWorkspaceDto {\n\thourlyRate: IHourlyRateDto;\n\tid: string;\n\timageUrl: string;\n\tmemberships: IMembershipDto[];\n\tname: string;\n\tworkspaceSettings: IWorkspaceSettingsDto;\n}\n\nexport interface IClientDto {\n\tid: string;\n\tname: string;\n\tworkspaceId: string;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,iBAAiB;AAAA,EAC7B,SAAS;AAAA,EACT,MAAM;AAAA,EACN,SAAS;AACV;AAIO,MAAM,aAAa;AAAA,EACzB,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,QAAQ;AACT;AAIO,MAAM,cAAc;AAAA,EAC1B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AACT;AAIO,MAAM,qBAAqB;AAAA,EACjC,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AACb;", "names": []}