{"version": 3, "sources": ["../../../../nodes/CrowdDev/descriptions/utils.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nexport const showFor =\n\t(resources: string[]) =>\n\t(operations?: string[]): Partial<INodeProperties> => {\n\t\treturn operations !== undefined\n\t\t\t? {\n\t\t\t\t\tdisplayOptions: {\n\t\t\t\t\t\tshow: {\n\t\t\t\t\t\t\tresource: resources,\n\t\t\t\t\t\t\toperation: operations,\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t}\n\t\t\t: {\n\t\t\t\t\tdisplayOptions: {\n\t\t\t\t\t\tshow: {\n\t\t\t\t\t\t\tresource: resources,\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t};\n\t};\n\nexport const mapWith =\n\t<T>(...objects: Array<Partial<T>>) =>\n\t(item: Partial<T>) =>\n\t\tObject.assign({}, item, ...objects);\n\nexport const getId = (): INodeProperties => ({\n\tdisplayName: 'ID',\n\tname: 'id',\n\ttype: 'string',\n\trequired: true,\n\tdefault: '',\n\trouting: {\n\t\tsend: {\n\t\t\ttype: 'query',\n\t\t\tproperty: 'ids[]',\n\t\t},\n\t},\n});\n\nexport const getAdditionalOptions = (fields: INodeProperties[]): INodeProperties => {\n\treturn {\n\t\tdisplayName: 'Additional Options',\n\t\tname: 'additionalOptions',\n\t\ttype: 'collection',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\toperation: ['getAll'],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\tplaceholder: 'Add option',\n\t\toptions: fields,\n\t};\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,UACZ,CAAC,cACD,CAAC,eAAoD;AACpD,SAAO,eAAe,SACnB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU;AAAA,QACV,WAAW;AAAA,MACZ;AAAA,IACD;AAAA,EACD,IACC;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU;AAAA,MACX;AAAA,IACD;AAAA,EACD;AACH;AAEM,MAAM,UACZ,IAAO,YACP,CAAC,SACA,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,OAAO;AAE7B,MAAM,QAAQ,OAAwB;AAAA,EAC5C,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,IACR,MAAM;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA,EACD;AACD;AAEO,MAAM,uBAAuB,CAAC,WAA+C;AACnF,SAAO;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,aAAa;AAAA,IACb,SAAS;AAAA,EACV;AACD;", "names": []}