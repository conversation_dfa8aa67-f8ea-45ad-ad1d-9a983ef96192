{"version": 3, "sources": ["../../../nodes/Wordpress/Wordpress.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tIDataObject,\n\tILoadOptionsFunctions,\n\tINodeExecutionData,\n\tINodePropertyOptions,\n\tINodeType,\n\tINodeTypeDescription,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes } from 'n8n-workflow';\n\nimport { wordpressApiRequest, wordpressApiRequestAllItems } from './GenericFunctions';\nimport { pageFields, pageOperations } from './PageDescription';\nimport type { IPage } from './PageInterface';\nimport { postFields, postOperations } from './PostDescription';\nimport type { IPost } from './PostInterface';\nimport { userFields, userOperations } from './UserDescription';\nimport type { IUser } from './UserInterface';\n\nexport class Wordpress implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Wordpress',\n\t\tname: 'wordpress',\n\t\ticon: 'file:wordpress.svg',\n\t\tgroup: ['output'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume Wordpress API',\n\t\tdefaults: {\n\t\t\tname: 'Wordpress',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'wordpressApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Post',\n\t\t\t\t\t\tvalue: 'post',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Page',\n\t\t\t\t\t\tvalue: 'page',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'User',\n\t\t\t\t\t\tvalue: 'user',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'post',\n\t\t\t},\n\t\t\t...postOperations,\n\t\t\t...postFields,\n\t\t\t...pageOperations,\n\t\t\t...pageFields,\n\t\t\t...userOperations,\n\t\t\t...userFields,\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\t// Get all the available categories to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getCategories(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst categories = await wordpressApiRequestAllItems.call(this, 'GET', '/categories', {});\n\t\t\t\tfor (const category of categories) {\n\t\t\t\t\tconst categoryName = category.name;\n\t\t\t\t\tconst categoryId = category.id;\n\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: categoryName,\n\t\t\t\t\t\tvalue: categoryId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the available tags to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getTags(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst tags = await wordpressApiRequestAllItems.call(this, 'GET', '/tags', {});\n\t\t\t\tfor (const tag of tags) {\n\t\t\t\t\tconst tagName = tag.name;\n\t\t\t\t\tconst tagId = tag.id;\n\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: tagName,\n\t\t\t\t\t\tvalue: tagId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the available authors to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getAuthors(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst authors = await wordpressApiRequestAllItems.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/users',\n\t\t\t\t\t{},\n\t\t\t\t\t{ who: 'authors' },\n\t\t\t\t);\n\t\t\t\tfor (const author of authors) {\n\t\t\t\t\tconst authorName = author.name;\n\t\t\t\t\tconst authorId = author.id;\n\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: authorName,\n\t\t\t\t\t\tvalue: authorId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: INodeExecutionData[] = [];\n\t\tconst length = items.length;\n\t\tlet responseData;\n\t\tconst qs: IDataObject = {};\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'post') {\n\t\t\t\t\t//https://developer.wordpress.org/rest-api/reference/posts/#create-a-post\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst title = this.getNodeParameter('title', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tconst body: IPost = {\n\t\t\t\t\t\t\ttitle,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tif (additionalFields.authorId) {\n\t\t\t\t\t\t\tbody.author = additionalFields.authorId as number;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.content) {\n\t\t\t\t\t\t\tbody.content = additionalFields.content as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.slug) {\n\t\t\t\t\t\t\tbody.slug = additionalFields.slug as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.password) {\n\t\t\t\t\t\t\tbody.password = additionalFields.password as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.status) {\n\t\t\t\t\t\t\tbody.status = additionalFields.status as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.commentStatus) {\n\t\t\t\t\t\t\tbody.comment_status = additionalFields.commentStatus as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.pingStatus) {\n\t\t\t\t\t\t\tbody.ping_status = additionalFields.pingStatus as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.sticky) {\n\t\t\t\t\t\t\tbody.sticky = additionalFields.sticky as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.postTemplate) {\n\t\t\t\t\t\t\tbody.template = this.getNodeParameter(\n\t\t\t\t\t\t\t\t'additionalFields.postTemplate.values.template',\n\t\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t\t'',\n\t\t\t\t\t\t\t) as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.categories) {\n\t\t\t\t\t\t\tbody.categories = additionalFields.categories as number[];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.tags) {\n\t\t\t\t\t\t\tbody.tags = additionalFields.tags as number[];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.format) {\n\t\t\t\t\t\t\tbody.format = additionalFields.format as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await wordpressApiRequest.call(this, 'POST', '/posts', body);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.wordpress.org/rest-api/reference/posts/#update-a-post\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst postId = this.getNodeParameter('postId', i) as string;\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\t\tconst body: IPost = {\n\t\t\t\t\t\t\tid: parseInt(postId, 10),\n\t\t\t\t\t\t};\n\t\t\t\t\t\tif (updateFields.authorId) {\n\t\t\t\t\t\t\tbody.author = updateFields.authorId as number;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.title) {\n\t\t\t\t\t\t\tbody.title = updateFields.title as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.content) {\n\t\t\t\t\t\t\tbody.content = updateFields.content as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.slug) {\n\t\t\t\t\t\t\tbody.slug = updateFields.slug as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.password) {\n\t\t\t\t\t\t\tbody.password = updateFields.password as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.status) {\n\t\t\t\t\t\t\tbody.status = updateFields.status as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.commentStatus) {\n\t\t\t\t\t\t\tbody.comment_status = updateFields.commentStatus as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.pingStatus) {\n\t\t\t\t\t\t\tbody.ping_status = updateFields.pingStatus as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.sticky) {\n\t\t\t\t\t\t\tbody.sticky = updateFields.sticky as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.postTemplate) {\n\t\t\t\t\t\t\tbody.template = this.getNodeParameter(\n\t\t\t\t\t\t\t\t'updateFields.postTemplate.values.template',\n\t\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t\t'',\n\t\t\t\t\t\t\t) as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.categories) {\n\t\t\t\t\t\t\tbody.categories = updateFields.categories as number[];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.tags) {\n\t\t\t\t\t\t\tbody.tags = updateFields.tags as number[];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.format) {\n\t\t\t\t\t\t\tbody.format = updateFields.format as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await wordpressApiRequest.call(this, 'POST', `/posts/${postId}`, body);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.wordpress.org/rest-api/reference/posts/#retrieve-a-post\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tconst postId = this.getNodeParameter('postId', i) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tif (options.password) {\n\t\t\t\t\t\t\tqs.password = options.password as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.context) {\n\t\t\t\t\t\t\tqs.context = options.context as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await wordpressApiRequest.call(this, 'GET', `/posts/${postId}`, {}, qs);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.wordpress.org/rest-api/reference/posts/#list-posts\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tif (options.context) {\n\t\t\t\t\t\t\tqs.context = options.context as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.orderBy) {\n\t\t\t\t\t\t\tqs.orderby = options.orderBy as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.order) {\n\t\t\t\t\t\t\tqs.order = options.order as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.search) {\n\t\t\t\t\t\t\tqs.search = options.search as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.after) {\n\t\t\t\t\t\t\tqs.after = options.after as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.author) {\n\t\t\t\t\t\t\tqs.author = options.author as number[];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.categories) {\n\t\t\t\t\t\t\tqs.categories = options.categories as number[];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.excludedCategories) {\n\t\t\t\t\t\t\tqs.categories_exclude = options.excludedCategories as number[];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.tags) {\n\t\t\t\t\t\t\tqs.tags = options.tags as number[];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.excludedTags) {\n\t\t\t\t\t\t\tqs.tags_exclude = options.excludedTags as number[];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.sticky) {\n\t\t\t\t\t\t\tqs.sticky = options.sticky as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.status) {\n\t\t\t\t\t\t\tqs.status = options.status as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await wordpressApiRequestAllItems.call(this, 'GET', '/posts', {}, qs);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tqs.per_page = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = await wordpressApiRequest.call(this, 'GET', '/posts', {}, qs);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.wordpress.org/rest-api/reference/posts/#delete-a-post\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tconst postId = this.getNodeParameter('postId', i) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tif (options.force) {\n\t\t\t\t\t\t\tqs.force = options.force as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await wordpressApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'DELETE',\n\t\t\t\t\t\t\t`/posts/${postId}`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (resource === 'page') {\n\t\t\t\t\t//https://developer.wordpress.org/rest-api/reference/pages/#create-a-page\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst title = this.getNodeParameter('title', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tconst body: IPage = {\n\t\t\t\t\t\t\ttitle,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tif (additionalFields.authorId) {\n\t\t\t\t\t\t\tbody.author = additionalFields.authorId as number;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.content) {\n\t\t\t\t\t\t\tbody.content = additionalFields.content as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.slug) {\n\t\t\t\t\t\t\tbody.slug = additionalFields.slug as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.password) {\n\t\t\t\t\t\t\tbody.password = additionalFields.password as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.status) {\n\t\t\t\t\t\t\tbody.status = additionalFields.status as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.commentStatus) {\n\t\t\t\t\t\t\tbody.comment_status = additionalFields.commentStatus as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.pingStatus) {\n\t\t\t\t\t\t\tbody.ping_status = additionalFields.pingStatus as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.pageTemplate) {\n\t\t\t\t\t\t\tbody.template = this.getNodeParameter(\n\t\t\t\t\t\t\t\t'additionalFields.pageTemplate.values.template',\n\t\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t\t'',\n\t\t\t\t\t\t\t) as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.menuOrder) {\n\t\t\t\t\t\t\tbody.menu_order = additionalFields.menuOrder as number;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.parent) {\n\t\t\t\t\t\t\tbody.parent = additionalFields.parent as number;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.featuredMediaId) {\n\t\t\t\t\t\t\tbody.featured_media = additionalFields.featuredMediaId as number;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await wordpressApiRequest.call(this, 'POST', '/pages', body);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.wordpress.org/rest-api/reference/pages/#update-a-page\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst pageId = this.getNodeParameter('pageId', i) as string;\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\t\tconst body: IPage = {\n\t\t\t\t\t\t\tid: parseInt(pageId, 10),\n\t\t\t\t\t\t};\n\t\t\t\t\t\tif (updateFields.authorId) {\n\t\t\t\t\t\t\tbody.author = updateFields.authorId as number;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.title) {\n\t\t\t\t\t\t\tbody.title = updateFields.title as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.content) {\n\t\t\t\t\t\t\tbody.content = updateFields.content as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.slug) {\n\t\t\t\t\t\t\tbody.slug = updateFields.slug as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.password) {\n\t\t\t\t\t\t\tbody.password = updateFields.password as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.status) {\n\t\t\t\t\t\t\tbody.status = updateFields.status as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.commentStatus) {\n\t\t\t\t\t\t\tbody.comment_status = updateFields.commentStatus as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.pingStatus) {\n\t\t\t\t\t\t\tbody.ping_status = updateFields.pingStatus as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.pageTemplate) {\n\t\t\t\t\t\t\tbody.template = this.getNodeParameter(\n\t\t\t\t\t\t\t\t'updateFields.pageTemplate.values.template',\n\t\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\t\t'',\n\t\t\t\t\t\t\t) as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.menuOrder) {\n\t\t\t\t\t\t\tbody.menu_order = updateFields.menuOrder as number;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.parent) {\n\t\t\t\t\t\t\tbody.parent = updateFields.parent as number;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.featuredMediaId) {\n\t\t\t\t\t\t\tbody.featured_media = updateFields.featuredMediaId as number;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await wordpressApiRequest.call(this, 'POST', `/pages/${pageId}`, body);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.wordpress.org/rest-api/reference/pages/#retrieve-a-page\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tconst pageId = this.getNodeParameter('pageId', i) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tif (options.password) {\n\t\t\t\t\t\t\tqs.password = options.password as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.context) {\n\t\t\t\t\t\t\tqs.context = options.context as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await wordpressApiRequest.call(this, 'GET', `/pages/${pageId}`, {}, qs);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.wordpress.org/rest-api/reference/pages/#list-pages\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tif (options.context) {\n\t\t\t\t\t\t\tqs.context = options.context as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.orderBy) {\n\t\t\t\t\t\t\tqs.orderby = options.orderBy as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.order) {\n\t\t\t\t\t\t\tqs.order = options.order as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.search) {\n\t\t\t\t\t\t\tqs.search = options.search as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.after) {\n\t\t\t\t\t\t\tqs.after = options.after as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.author) {\n\t\t\t\t\t\t\tqs.author = options.author as number[];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.parent) {\n\t\t\t\t\t\t\tqs.parent = options.parent as number;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.menuOrder) {\n\t\t\t\t\t\t\tqs.menu_order = options.menuOrder as number;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.status) {\n\t\t\t\t\t\t\tqs.status = options.status as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.page) {\n\t\t\t\t\t\t\tqs.page = options.page as number;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await wordpressApiRequestAllItems.call(this, 'GET', '/pages', {}, qs);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tqs.per_page = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = await wordpressApiRequest.call(this, 'GET', '/pages', {}, qs);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.wordpress.org/rest-api/reference/pages/#delete-a-page\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tconst pageId = this.getNodeParameter('pageId', i) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tif (options.force) {\n\t\t\t\t\t\t\tqs.force = options.force as boolean;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await wordpressApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'DELETE',\n\t\t\t\t\t\t\t`/pages/${pageId}`,\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (resource === 'user') {\n\t\t\t\t\t//https://developer.wordpress.org/rest-api/reference/users/#create-a-user\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst name = this.getNodeParameter('name', i) as string;\n\t\t\t\t\t\tconst username = this.getNodeParameter('username', i) as string;\n\t\t\t\t\t\tconst firstName = this.getNodeParameter('firstName', i) as string;\n\t\t\t\t\t\tconst lastName = this.getNodeParameter('lastName', i) as string;\n\t\t\t\t\t\tconst email = this.getNodeParameter('email', i) as string;\n\t\t\t\t\t\tconst password = this.getNodeParameter('password', i) as string;\n\t\t\t\t\t\tconst additionalFields = this.getNodeParameter('additionalFields', i);\n\t\t\t\t\t\tconst body: IUser = {\n\t\t\t\t\t\t\tname,\n\t\t\t\t\t\t\tusername,\n\t\t\t\t\t\t\tfirst_name: firstName,\n\t\t\t\t\t\t\tlast_name: lastName,\n\t\t\t\t\t\t\temail,\n\t\t\t\t\t\t\tpassword,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tif (additionalFields.url) {\n\t\t\t\t\t\t\tbody.url = additionalFields.url as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.description) {\n\t\t\t\t\t\t\tbody.description = additionalFields.description as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.nickname) {\n\t\t\t\t\t\t\tbody.nickname = additionalFields.nickname as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (additionalFields.slug) {\n\t\t\t\t\t\t\tbody.slug = additionalFields.slug as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await wordpressApiRequest.call(this, 'POST', '/users', body);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.wordpress.org/rest-api/reference/users/#update-a-user\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst userId = this.getNodeParameter('userId', i) as number;\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\t\t\t\t\t\tconst body: IUser = {\n\t\t\t\t\t\t\tid: userId,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tif (updateFields.name) {\n\t\t\t\t\t\t\tbody.name = updateFields.name as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.firstName) {\n\t\t\t\t\t\t\tbody.first_name = updateFields.firstName as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.lastName) {\n\t\t\t\t\t\t\tbody.last_name = updateFields.lastName as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.email) {\n\t\t\t\t\t\t\tbody.email = updateFields.email as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.password) {\n\t\t\t\t\t\t\tbody.password = updateFields.password as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.username) {\n\t\t\t\t\t\t\tbody.username = updateFields.username as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.url) {\n\t\t\t\t\t\t\tbody.url = updateFields.url as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.description) {\n\t\t\t\t\t\t\tbody.description = updateFields.description as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.nickname) {\n\t\t\t\t\t\t\tbody.nickname = updateFields.nickname as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (updateFields.slug) {\n\t\t\t\t\t\t\tbody.slug = updateFields.slug as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await wordpressApiRequest.call(this, 'POST', `/users/${userId}`, body);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.wordpress.org/rest-api/reference/users/#retrieve-a-user\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tconst userId = this.getNodeParameter('userId', i) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tif (options.context) {\n\t\t\t\t\t\t\tqs.context = options.context as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresponseData = await wordpressApiRequest.call(this, 'GET', `/users/${userId}`, {}, qs);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.wordpress.org/rest-api/reference/users/#list-users\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tif (options.context) {\n\t\t\t\t\t\t\tqs.context = options.context as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.orderBy) {\n\t\t\t\t\t\t\tqs.orderby = options.orderBy as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.order) {\n\t\t\t\t\t\t\tqs.order = options.order as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.search) {\n\t\t\t\t\t\t\tqs.search = options.search as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (options.who) {\n\t\t\t\t\t\t\tqs.who = options.who as string;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await wordpressApiRequestAllItems.call(this, 'GET', '/users', {}, qs);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tqs.per_page = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = await wordpressApiRequest.call(this, 'GET', '/users', {}, qs);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developer.wordpress.org/rest-api/reference/users/#delete-a-user\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tconst reassign = this.getNodeParameter('reassign', i) as string;\n\t\t\t\t\t\tqs.reassign = reassign;\n\t\t\t\t\t\tqs.force = true;\n\t\t\t\t\t\tresponseData = await wordpressApiRequest.call(this, 'DELETE', '/users/me', {}, qs);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\tthis.helpers.returnJsonArray(responseData as IDataObject[]),\n\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t);\n\t\t\t\treturnData.push(...executionData);\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\treturnData.push({ json: { error: error.message } });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,0BAAoC;AAEpC,8BAAiE;AACjE,6BAA2C;AAE3C,6BAA2C;AAE3C,6BAA2C;AAGpC,MAAM,UAA+B;AAAA,EAArC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,QAAQ;AAAA,MAChB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA;AAAA;AAAA,QAGZ,MAAM,gBAA4E;AACjF,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,aAAa,MAAM,oDAA4B,KAAK,MAAM,OAAO,eAAe,CAAC,CAAC;AACxF,qBAAW,YAAY,YAAY;AAClC,kBAAM,eAAe,SAAS;AAC9B,kBAAM,aAAa,SAAS;AAE5B,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,UAAsE;AAC3E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,OAAO,MAAM,oDAA4B,KAAK,MAAM,OAAO,SAAS,CAAC,CAAC;AAC5E,qBAAW,OAAO,MAAM;AACvB,kBAAM,UAAU,IAAI;AACpB,kBAAM,QAAQ,IAAI;AAElB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,aAAyE;AAC9E,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,UAAU,MAAM,oDAA4B;AAAA,YACjD;AAAA,YACA;AAAA,YACA;AAAA,YACA,CAAC;AAAA,YACD,EAAE,KAAK,UAAU;AAAA,UAClB;AACA,qBAAW,UAAU,SAAS;AAC7B,kBAAM,aAAa,OAAO;AAC1B,kBAAM,WAAW,OAAO;AAExB,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAAmC,CAAC;AAC1C,UAAM,SAAS,MAAM;AACrB,QAAI;AACJ,UAAM,KAAkB,CAAC;AACzB,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,YAAI,aAAa,QAAQ;AAExB,cAAI,cAAc,UAAU;AAC3B,kBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,kBAAM,OAAc;AAAA,cACnB;AAAA,YACD;AACA,gBAAI,iBAAiB,UAAU;AAC9B,mBAAK,SAAS,iBAAiB;AAAA,YAChC;AACA,gBAAI,iBAAiB,SAAS;AAC7B,mBAAK,UAAU,iBAAiB;AAAA,YACjC;AACA,gBAAI,iBAAiB,MAAM;AAC1B,mBAAK,OAAO,iBAAiB;AAAA,YAC9B;AACA,gBAAI,iBAAiB,UAAU;AAC9B,mBAAK,WAAW,iBAAiB;AAAA,YAClC;AACA,gBAAI,iBAAiB,QAAQ;AAC5B,mBAAK,SAAS,iBAAiB;AAAA,YAChC;AACA,gBAAI,iBAAiB,eAAe;AACnC,mBAAK,iBAAiB,iBAAiB;AAAA,YACxC;AACA,gBAAI,iBAAiB,YAAY;AAChC,mBAAK,cAAc,iBAAiB;AAAA,YACrC;AACA,gBAAI,iBAAiB,QAAQ;AAC5B,mBAAK,SAAS,iBAAiB;AAAA,YAChC;AACA,gBAAI,iBAAiB,cAAc;AAClC,mBAAK,WAAW,KAAK;AAAA,gBACpB;AAAA,gBACA;AAAA,gBACA;AAAA,cACD;AAAA,YACD;AACA,gBAAI,iBAAiB,YAAY;AAChC,mBAAK,aAAa,iBAAiB;AAAA,YACpC;AACA,gBAAI,iBAAiB,MAAM;AAC1B,mBAAK,OAAO,iBAAiB;AAAA,YAC9B;AACA,gBAAI,iBAAiB,QAAQ;AAC5B,mBAAK,SAAS,iBAAiB;AAAA,YAChC;AACA,2BAAe,MAAM,4CAAoB,KAAK,MAAM,QAAQ,UAAU,IAAI;AAAA,UAC3E;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,kBAAM,OAAc;AAAA,cACnB,IAAI,SAAS,QAAQ,EAAE;AAAA,YACxB;AACA,gBAAI,aAAa,UAAU;AAC1B,mBAAK,SAAS,aAAa;AAAA,YAC5B;AACA,gBAAI,aAAa,OAAO;AACvB,mBAAK,QAAQ,aAAa;AAAA,YAC3B;AACA,gBAAI,aAAa,SAAS;AACzB,mBAAK,UAAU,aAAa;AAAA,YAC7B;AACA,gBAAI,aAAa,MAAM;AACtB,mBAAK,OAAO,aAAa;AAAA,YAC1B;AACA,gBAAI,aAAa,UAAU;AAC1B,mBAAK,WAAW,aAAa;AAAA,YAC9B;AACA,gBAAI,aAAa,QAAQ;AACxB,mBAAK,SAAS,aAAa;AAAA,YAC5B;AACA,gBAAI,aAAa,eAAe;AAC/B,mBAAK,iBAAiB,aAAa;AAAA,YACpC;AACA,gBAAI,aAAa,YAAY;AAC5B,mBAAK,cAAc,aAAa;AAAA,YACjC;AACA,gBAAI,aAAa,QAAQ;AACxB,mBAAK,SAAS,aAAa;AAAA,YAC5B;AACA,gBAAI,aAAa,cAAc;AAC9B,mBAAK,WAAW,KAAK;AAAA,gBACpB;AAAA,gBACA;AAAA,gBACA;AAAA,cACD;AAAA,YACD;AACA,gBAAI,aAAa,YAAY;AAC5B,mBAAK,aAAa,aAAa;AAAA,YAChC;AACA,gBAAI,aAAa,MAAM;AACtB,mBAAK,OAAO,aAAa;AAAA,YAC1B;AACA,gBAAI,aAAa,QAAQ;AACxB,mBAAK,SAAS,aAAa;AAAA,YAC5B;AACA,2BAAe,MAAM,4CAAoB,KAAK,MAAM,QAAQ,UAAU,MAAM,IAAI,IAAI;AAAA,UACrF;AAEA,cAAI,cAAc,OAAO;AACxB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,gBAAI,QAAQ,UAAU;AACrB,iBAAG,WAAW,QAAQ;AAAA,YACvB;AACA,gBAAI,QAAQ,SAAS;AACpB,iBAAG,UAAU,QAAQ;AAAA,YACtB;AACA,2BAAe,MAAM,4CAAoB,KAAK,MAAM,OAAO,UAAU,MAAM,IAAI,CAAC,GAAG,EAAE;AAAA,UACtF;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,gBAAI,QAAQ,SAAS;AACpB,iBAAG,UAAU,QAAQ;AAAA,YACtB;AACA,gBAAI,QAAQ,SAAS;AACpB,iBAAG,UAAU,QAAQ;AAAA,YACtB;AACA,gBAAI,QAAQ,OAAO;AAClB,iBAAG,QAAQ,QAAQ;AAAA,YACpB;AACA,gBAAI,QAAQ,QAAQ;AACnB,iBAAG,SAAS,QAAQ;AAAA,YACrB;AACA,gBAAI,QAAQ,OAAO;AAClB,iBAAG,QAAQ,QAAQ;AAAA,YACpB;AACA,gBAAI,QAAQ,QAAQ;AACnB,iBAAG,SAAS,QAAQ;AAAA,YACrB;AACA,gBAAI,QAAQ,YAAY;AACvB,iBAAG,aAAa,QAAQ;AAAA,YACzB;AACA,gBAAI,QAAQ,oBAAoB;AAC/B,iBAAG,qBAAqB,QAAQ;AAAA,YACjC;AACA,gBAAI,QAAQ,MAAM;AACjB,iBAAG,OAAO,QAAQ;AAAA,YACnB;AACA,gBAAI,QAAQ,cAAc;AACzB,iBAAG,eAAe,QAAQ;AAAA,YAC3B;AACA,gBAAI,QAAQ,QAAQ;AACnB,iBAAG,SAAS,QAAQ;AAAA,YACrB;AACA,gBAAI,QAAQ,QAAQ;AACnB,iBAAG,SAAS,QAAQ;AAAA,YACrB;AACA,gBAAI,WAAW;AACd,6BAAe,MAAM,oDAA4B,KAAK,MAAM,OAAO,UAAU,CAAC,GAAG,EAAE;AAAA,YACpF,OAAO;AACN,iBAAG,WAAW,KAAK,iBAAiB,SAAS,CAAC;AAC9C,6BAAe,MAAM,4CAAoB,KAAK,MAAM,OAAO,UAAU,CAAC,GAAG,EAAE;AAAA,YAC5E;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,gBAAI,QAAQ,OAAO;AAClB,iBAAG,QAAQ,QAAQ;AAAA,YACpB;AACA,2BAAe,MAAM,4CAAoB;AAAA,cACxC;AAAA,cACA;AAAA,cACA,UAAU,MAAM;AAAA,cAChB,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AACA,YAAI,aAAa,QAAQ;AAExB,cAAI,cAAc,UAAU;AAC3B,kBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,kBAAM,OAAc;AAAA,cACnB;AAAA,YACD;AACA,gBAAI,iBAAiB,UAAU;AAC9B,mBAAK,SAAS,iBAAiB;AAAA,YAChC;AACA,gBAAI,iBAAiB,SAAS;AAC7B,mBAAK,UAAU,iBAAiB;AAAA,YACjC;AACA,gBAAI,iBAAiB,MAAM;AAC1B,mBAAK,OAAO,iBAAiB;AAAA,YAC9B;AACA,gBAAI,iBAAiB,UAAU;AAC9B,mBAAK,WAAW,iBAAiB;AAAA,YAClC;AACA,gBAAI,iBAAiB,QAAQ;AAC5B,mBAAK,SAAS,iBAAiB;AAAA,YAChC;AACA,gBAAI,iBAAiB,eAAe;AACnC,mBAAK,iBAAiB,iBAAiB;AAAA,YACxC;AACA,gBAAI,iBAAiB,YAAY;AAChC,mBAAK,cAAc,iBAAiB;AAAA,YACrC;AACA,gBAAI,iBAAiB,cAAc;AAClC,mBAAK,WAAW,KAAK;AAAA,gBACpB;AAAA,gBACA;AAAA,gBACA;AAAA,cACD;AAAA,YACD;AACA,gBAAI,iBAAiB,WAAW;AAC/B,mBAAK,aAAa,iBAAiB;AAAA,YACpC;AACA,gBAAI,iBAAiB,QAAQ;AAC5B,mBAAK,SAAS,iBAAiB;AAAA,YAChC;AACA,gBAAI,iBAAiB,iBAAiB;AACrC,mBAAK,iBAAiB,iBAAiB;AAAA,YACxC;AACA,2BAAe,MAAM,4CAAoB,KAAK,MAAM,QAAQ,UAAU,IAAI;AAAA,UAC3E;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,kBAAM,OAAc;AAAA,cACnB,IAAI,SAAS,QAAQ,EAAE;AAAA,YACxB;AACA,gBAAI,aAAa,UAAU;AAC1B,mBAAK,SAAS,aAAa;AAAA,YAC5B;AACA,gBAAI,aAAa,OAAO;AACvB,mBAAK,QAAQ,aAAa;AAAA,YAC3B;AACA,gBAAI,aAAa,SAAS;AACzB,mBAAK,UAAU,aAAa;AAAA,YAC7B;AACA,gBAAI,aAAa,MAAM;AACtB,mBAAK,OAAO,aAAa;AAAA,YAC1B;AACA,gBAAI,aAAa,UAAU;AAC1B,mBAAK,WAAW,aAAa;AAAA,YAC9B;AACA,gBAAI,aAAa,QAAQ;AACxB,mBAAK,SAAS,aAAa;AAAA,YAC5B;AACA,gBAAI,aAAa,eAAe;AAC/B,mBAAK,iBAAiB,aAAa;AAAA,YACpC;AACA,gBAAI,aAAa,YAAY;AAC5B,mBAAK,cAAc,aAAa;AAAA,YACjC;AACA,gBAAI,aAAa,cAAc;AAC9B,mBAAK,WAAW,KAAK;AAAA,gBACpB;AAAA,gBACA;AAAA,gBACA;AAAA,cACD;AAAA,YACD;AACA,gBAAI,aAAa,WAAW;AAC3B,mBAAK,aAAa,aAAa;AAAA,YAChC;AACA,gBAAI,aAAa,QAAQ;AACxB,mBAAK,SAAS,aAAa;AAAA,YAC5B;AACA,gBAAI,aAAa,iBAAiB;AACjC,mBAAK,iBAAiB,aAAa;AAAA,YACpC;AACA,2BAAe,MAAM,4CAAoB,KAAK,MAAM,QAAQ,UAAU,MAAM,IAAI,IAAI;AAAA,UACrF;AAEA,cAAI,cAAc,OAAO;AACxB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,gBAAI,QAAQ,UAAU;AACrB,iBAAG,WAAW,QAAQ;AAAA,YACvB;AACA,gBAAI,QAAQ,SAAS;AACpB,iBAAG,UAAU,QAAQ;AAAA,YACtB;AACA,2BAAe,MAAM,4CAAoB,KAAK,MAAM,OAAO,UAAU,MAAM,IAAI,CAAC,GAAG,EAAE;AAAA,UACtF;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,gBAAI,QAAQ,SAAS;AACpB,iBAAG,UAAU,QAAQ;AAAA,YACtB;AACA,gBAAI,QAAQ,SAAS;AACpB,iBAAG,UAAU,QAAQ;AAAA,YACtB;AACA,gBAAI,QAAQ,OAAO;AAClB,iBAAG,QAAQ,QAAQ;AAAA,YACpB;AACA,gBAAI,QAAQ,QAAQ;AACnB,iBAAG,SAAS,QAAQ;AAAA,YACrB;AACA,gBAAI,QAAQ,OAAO;AAClB,iBAAG,QAAQ,QAAQ;AAAA,YACpB;AACA,gBAAI,QAAQ,QAAQ;AACnB,iBAAG,SAAS,QAAQ;AAAA,YACrB;AACA,gBAAI,QAAQ,QAAQ;AACnB,iBAAG,SAAS,QAAQ;AAAA,YACrB;AACA,gBAAI,QAAQ,WAAW;AACtB,iBAAG,aAAa,QAAQ;AAAA,YACzB;AACA,gBAAI,QAAQ,QAAQ;AACnB,iBAAG,SAAS,QAAQ;AAAA,YACrB;AACA,gBAAI,QAAQ,MAAM;AACjB,iBAAG,OAAO,QAAQ;AAAA,YACnB;AACA,gBAAI,WAAW;AACd,6BAAe,MAAM,oDAA4B,KAAK,MAAM,OAAO,UAAU,CAAC,GAAG,EAAE;AAAA,YACpF,OAAO;AACN,iBAAG,WAAW,KAAK,iBAAiB,SAAS,CAAC;AAC9C,6BAAe,MAAM,4CAAoB,KAAK,MAAM,OAAO,UAAU,CAAC,GAAG,EAAE;AAAA,YAC5E;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,gBAAI,QAAQ,OAAO;AAClB,iBAAG,QAAQ,QAAQ;AAAA,YACpB;AACA,2BAAe,MAAM,4CAAoB;AAAA,cACxC;AAAA,cACA;AAAA,cACA,UAAU,MAAM;AAAA,cAChB,CAAC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AACA,YAAI,aAAa,QAAQ;AAExB,cAAI,cAAc,UAAU;AAC3B,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC5C,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,kBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,kBAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,kBAAM,OAAc;AAAA,cACnB;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,WAAW;AAAA,cACX;AAAA,cACA;AAAA,YACD;AACA,gBAAI,iBAAiB,KAAK;AACzB,mBAAK,MAAM,iBAAiB;AAAA,YAC7B;AACA,gBAAI,iBAAiB,aAAa;AACjC,mBAAK,cAAc,iBAAiB;AAAA,YACrC;AACA,gBAAI,iBAAiB,UAAU;AAC9B,mBAAK,WAAW,iBAAiB;AAAA,YAClC;AACA,gBAAI,iBAAiB,MAAM;AAC1B,mBAAK,OAAO,iBAAiB;AAAA,YAC9B;AACA,2BAAe,MAAM,4CAAoB,KAAK,MAAM,QAAQ,UAAU,IAAI;AAAA,UAC3E;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAC5D,kBAAM,OAAc;AAAA,cACnB,IAAI;AAAA,YACL;AACA,gBAAI,aAAa,MAAM;AACtB,mBAAK,OAAO,aAAa;AAAA,YAC1B;AACA,gBAAI,aAAa,WAAW;AAC3B,mBAAK,aAAa,aAAa;AAAA,YAChC;AACA,gBAAI,aAAa,UAAU;AAC1B,mBAAK,YAAY,aAAa;AAAA,YAC/B;AACA,gBAAI,aAAa,OAAO;AACvB,mBAAK,QAAQ,aAAa;AAAA,YAC3B;AACA,gBAAI,aAAa,UAAU;AAC1B,mBAAK,WAAW,aAAa;AAAA,YAC9B;AACA,gBAAI,aAAa,UAAU;AAC1B,mBAAK,WAAW,aAAa;AAAA,YAC9B;AACA,gBAAI,aAAa,KAAK;AACrB,mBAAK,MAAM,aAAa;AAAA,YACzB;AACA,gBAAI,aAAa,aAAa;AAC7B,mBAAK,cAAc,aAAa;AAAA,YACjC;AACA,gBAAI,aAAa,UAAU;AAC1B,mBAAK,WAAW,aAAa;AAAA,YAC9B;AACA,gBAAI,aAAa,MAAM;AACtB,mBAAK,OAAO,aAAa;AAAA,YAC1B;AACA,2BAAe,MAAM,4CAAoB,KAAK,MAAM,QAAQ,UAAU,MAAM,IAAI,IAAI;AAAA,UACrF;AAEA,cAAI,cAAc,OAAO;AACxB,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAChD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,gBAAI,QAAQ,SAAS;AACpB,iBAAG,UAAU,QAAQ;AAAA,YACtB;AACA,2BAAe,MAAM,4CAAoB,KAAK,MAAM,OAAO,UAAU,MAAM,IAAI,CAAC,GAAG,EAAE;AAAA,UACtF;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,gBAAI,QAAQ,SAAS;AACpB,iBAAG,UAAU,QAAQ;AAAA,YACtB;AACA,gBAAI,QAAQ,SAAS;AACpB,iBAAG,UAAU,QAAQ;AAAA,YACtB;AACA,gBAAI,QAAQ,OAAO;AAClB,iBAAG,QAAQ,QAAQ;AAAA,YACpB;AACA,gBAAI,QAAQ,QAAQ;AACnB,iBAAG,SAAS,QAAQ;AAAA,YACrB;AACA,gBAAI,QAAQ,KAAK;AAChB,iBAAG,MAAM,QAAQ;AAAA,YAClB;AACA,gBAAI,WAAW;AACd,6BAAe,MAAM,oDAA4B,KAAK,MAAM,OAAO,UAAU,CAAC,GAAG,EAAE;AAAA,YACpF,OAAO;AACN,iBAAG,WAAW,KAAK,iBAAiB,SAAS,CAAC;AAC9C,6BAAe,MAAM,4CAAoB,KAAK,MAAM,OAAO,UAAU,CAAC,GAAG,EAAE;AAAA,YAC5E;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,eAAG,WAAW;AACd,eAAG,QAAQ;AACX,2BAAe,MAAM,4CAAoB,KAAK,MAAM,UAAU,aAAa,CAAC,GAAG,EAAE;AAAA,UAClF;AAAA,QACD;AACA,cAAM,gBAAgB,KAAK,QAAQ;AAAA,UAClC,KAAK,QAAQ,gBAAgB,YAA6B;AAAA,UAC1D,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,QACzB;AACA,mBAAW,KAAK,GAAG,aAAa;AAAA,MACjC,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,qBAAW,KAAK,EAAE,MAAM,EAAE,OAAO,MAAM,QAAQ,EAAE,CAAC;AAClD;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AACA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": []}