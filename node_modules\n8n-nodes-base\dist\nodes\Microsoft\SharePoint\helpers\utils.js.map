{"version": 3, "sources": ["../../../../../nodes/Microsoft/SharePoint/helpers/utils.ts"], "sourcesContent": ["import type {\n\tIDataObject,\n\tIExecuteSingleFunctions,\n\tIHttpRequestOptions,\n\tIN8nHttpFullResponse,\n\tINodeExecutionData,\n\tJsonObject,\n\tResourceMapperValue,\n} from 'n8n-workflow';\nimport { jsonParse, NodeApiError, NodeOperationError } from 'n8n-workflow';\n\nimport type { IErrorResponse } from './interfaces';\nimport { microsoftSharePointApiRequest } from '../transport';\n\nexport async function simplifyItemPostReceive(\n\tthis: IExecuteSingleFunctions,\n\titems: INodeExecutionData[],\n\t_response: IN8nHttpFullResponse,\n): Promise<INodeExecutionData[]> {\n\tif (items.length === 0) {\n\t\treturn items;\n\t}\n\n\tconst simplify = this.getNodeParameter('simplify') as boolean;\n\tif (simplify) {\n\t\tfor (const item of items) {\n\t\t\tdelete item.json['@odata.context'];\n\t\t\tdelete item.json['@odata.etag'];\n\t\t\tdelete item.json['<EMAIL>'];\n\t\t\tdelete (item.json.fields as IDataObject)?.['@odata.etag'];\n\t\t}\n\t}\n\n\treturn items;\n}\n\nexport async function simplifyListPostReceive(\n\tthis: IExecuteSingleFunctions,\n\titems: INodeExecutionData[],\n\t_response: IN8nHttpFullResponse,\n): Promise<INodeExecutionData[]> {\n\tif (items.length === 0) {\n\t\treturn items;\n\t}\n\n\tconst simplify = this.getNodeParameter('simplify') as boolean;\n\tif (simplify) {\n\t\tfor (const item of items) {\n\t\t\tdelete item.json['@odata.context'];\n\t\t\tdelete item.json['@odata.etag'];\n\t\t}\n\t}\n\n\treturn items;\n}\n\nexport async function downloadFilePostReceive(\n\tthis: IExecuteSingleFunctions,\n\t_items: INodeExecutionData[],\n\tresponse: IN8nHttpFullResponse,\n): Promise<INodeExecutionData[]> {\n\tlet fileName: string | undefined;\n\tif (response.headers['content-disposition']) {\n\t\tlet fileNameMatch = /filename\\*=(?:(\\\\?['\"])(.*?)\\1|(?:[^\\s]+'.*?')?([^;\\n]*))/g.exec(\n\t\t\tresponse.headers['content-disposition'] as string,\n\t\t);\n\t\tfileName =\n\t\t\tfileNameMatch && fileNameMatch.length > 1 ? fileNameMatch[3] || fileNameMatch[2] : undefined;\n\t\tif (fileName) {\n\t\t\tfileName = decodeURIComponent(fileName);\n\t\t} else {\n\t\t\tfileNameMatch = /filename=\"?([^\"]*?)\"?(;|$)/g.exec(\n\t\t\t\tresponse.headers['content-disposition'] as string,\n\t\t\t);\n\t\t\tfileName = fileNameMatch && fileNameMatch.length > 1 ? fileNameMatch[1] : undefined;\n\t\t}\n\t}\n\n\tconst newItem: INodeExecutionData = {\n\t\tjson: {},\n\t\tbinary: {\n\t\t\tdata: await this.helpers.prepareBinaryData(\n\t\t\t\tresponse.body as Buffer,\n\t\t\t\tfileName,\n\t\t\t\tresponse.headers['content-type'] as string,\n\t\t\t),\n\t\t},\n\t};\n\n\treturn [newItem];\n}\n\nexport async function uploadFilePreSend(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst binaryProperty = this.getNodeParameter('fileContents') as string;\n\tthis.helpers.assertBinaryData(binaryProperty);\n\tconst binaryDataBuffer = await this.helpers.getBinaryDataBuffer(binaryProperty);\n\trequestOptions.body = binaryDataBuffer;\n\treturn requestOptions;\n}\n\nexport async function itemGetAllFieldsPreSend(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst fields = this.getNodeParameter('options.fields') as string[];\n\trequestOptions.qs ??= {};\n\tif (fields.some((x) => x === 'fields')) {\n\t\trequestOptions.qs.$expand = 'fields';\n\t}\n\trequestOptions.qs.$select = fields.map((x) => x);\n\treturn requestOptions;\n}\n\nexport async function itemColumnsPreSend(\n\tthis: IExecuteSingleFunctions,\n\trequestOptions: IHttpRequestOptions,\n): Promise<IHttpRequestOptions> {\n\tconst mapperValue = this.getNodeParameter('columns') as ResourceMapperValue;\n\tconst operation = this.getNodeParameter('operation') as string;\n\n\tif (['upsert', 'update'].includes(operation) && mapperValue.matchingColumns?.length > 0) {\n\t\tif (!mapperValue.matchingColumns.includes('id')) {\n\t\t\tconst site = this.getNodeParameter('site', undefined, { extractValue: true }) as string;\n\t\t\tconst list = this.getNodeParameter('list', undefined, { extractValue: true }) as string;\n\n\t\t\tconst response = await microsoftSharePointApiRequest.call(\n\t\t\t\tthis,\n\t\t\t\t'GET',\n\t\t\t\t`/sites/${site}/lists/${list}/items`,\n\t\t\t\t{},\n\t\t\t\t{\n\t\t\t\t\t$filter: mapperValue.matchingColumns\n\t\t\t\t\t\t.map((x) => `fields/${x} eq '${mapperValue.value![x]}'`)\n\t\t\t\t\t\t.join(' and'),\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tPrefer: 'HonorNonIndexedQueriesWarningMayFailRandomly',\n\t\t\t\t},\n\t\t\t);\n\t\t\tif (response.value?.length === 1) {\n\t\t\t\tmapperValue.matchingColumns.push('id');\n\t\t\t\tmapperValue.value ??= {};\n\t\t\t\tmapperValue.value.id = response.value[0].id;\n\t\t\t}\n\t\t}\n\n\t\tif (operation === 'upsert') {\n\t\t\tif (mapperValue.matchingColumns.includes('id')) {\n\t\t\t\tif (!mapperValue.value?.id) {\n\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\"The column(s) don't match any existing item\",\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tdescription: 'Double-check the value(s) for the columns to match and try again',\n\t\t\t\t\t\t},\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\trequestOptions.url += '/' + mapperValue.value.id;\n\t\t\t\tdelete mapperValue.value.id;\n\t\t\t\trequestOptions.method = 'PATCH';\n\t\t\t}\n\t\t} else if (operation === 'update') {\n\t\t\tif (mapperValue.matchingColumns.includes('id') && mapperValue.value?.id) {\n\t\t\t\trequestOptions.url += '/' + mapperValue.value.id;\n\t\t\t\tdelete mapperValue.value.id;\n\t\t\t} else {\n\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\"The column(s) don't match any existing item\",\n\t\t\t\t\t{\n\t\t\t\t\t\tdescription: 'Double-check the value(s) for the columns to match and try again',\n\t\t\t\t\t},\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\tconst fields = {} as IDataObject;\n\tfor (const [key, value] of Object.entries(mapperValue.value ?? {})) {\n\t\tif (mapperValue.schema.find((x) => x.id === key)?.type === 'url') {\n\t\t\tfields[key] = {\n\t\t\t\tDescription: value,\n\t\t\t\tUrl: value,\n\t\t\t};\n\t\t} else {\n\t\t\tfields[key] = value;\n\t\t}\n\t}\n\trequestOptions.body ??= {};\n\t(requestOptions.body as IDataObject).fields = fields;\n\n\treturn requestOptions;\n}\n\nexport async function handleErrorPostReceive(\n\tthis: IExecuteSingleFunctions,\n\tdata: INodeExecutionData[],\n\tresponse: IN8nHttpFullResponse,\n): Promise<INodeExecutionData[]> {\n\tif (String(response.statusCode).startsWith('4') || String(response.statusCode).startsWith('5')) {\n\t\tconst resource = this.getNodeParameter('resource') as string;\n\t\tconst operation = this.getNodeParameter('operation') as string;\n\n\t\tif (resource === 'file' && operation === 'download' && Buffer.isBuffer(response.body)) {\n\t\t\tresponse.body = jsonParse((response.body as Buffer).toString());\n\t\t}\n\t\tconst error = (response.body as IErrorResponse)?.error ?? {};\n\n\t\tif (resource === 'file') {\n\t\t\tif (operation === 'download') {\n\t\t\t} else if (operation === 'update') {\n\t\t\t} else if (operation === 'upload') {\n\t\t\t}\n\t\t} else if (resource === 'item') {\n\t\t\tif (operation === 'create') {\n\t\t\t\tif (error.code === 'invalidRequest') {\n\t\t\t\t\tif (\n\t\t\t\t\t\terror.message ===\n\t\t\t\t\t\t'One or more fields with unique constraints already has the provided value.'\n\t\t\t\t\t) {\n\t\t\t\t\t\tthrow new NodeApiError(this.getNode(), response as unknown as JsonObject, {\n\t\t\t\t\t\t\tmessage: 'One or more fields with unique constraints already has the provided value',\n\t\t\t\t\t\t\tdescription: \"Double-check the value(s) in 'Values to Send' and try again\",\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new NodeApiError(this.getNode(), response as unknown as JsonObject, {\n\t\t\t\t\t\t\tmessage: error.message,\n\t\t\t\t\t\t\tdescription: \"Double-check the value(s) in 'Values to Send' and try again\",\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (operation === 'delete') {\n\t\t\t} else if (operation === 'get') {\n\t\t\t} else if (operation === 'getAll') {\n\t\t\t} else if (operation === 'update') {\n\t\t\t\tif (error.code === 'invalidRequest') {\n\t\t\t\t\tthrow new NodeApiError(this.getNode(), response as unknown as JsonObject, {\n\t\t\t\t\t\tmessage: error.message,\n\t\t\t\t\t\tdescription: \"Double-check the value(s) in 'Values to Update' and try again\",\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} else if (operation === 'upsert') {\n\t\t\t\tif (error.code === 'invalidRequest') {\n\t\t\t\t\tthrow new NodeApiError(this.getNode(), response as unknown as JsonObject, {\n\t\t\t\t\t\tmessage: error.message,\n\t\t\t\t\t\tdescription: \"Double-check the value(s) in 'Values to Send' and try again\",\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t} else if (resource === 'list') {\n\t\t\tif (operation === 'get') {\n\t\t\t} else if (operation === 'getAll') {\n\t\t\t}\n\t\t}\n\n\t\tif (error.code === 'itemNotFound') {\n\t\t\tif (error.message.includes('list item')) {\n\t\t\t\tthrow new NodeApiError(this.getNode(), response as unknown as JsonObject, {\n\t\t\t\t\tmessage: \"The required item doesn't match any existing one\",\n\t\t\t\t\tdescription: \"Double-check the value in the parameter 'Item' and try again\",\n\t\t\t\t});\n\t\t\t} else if (error.message.includes('list')) {\n\t\t\t\tthrow new NodeApiError(this.getNode(), response as unknown as JsonObject, {\n\t\t\t\t\tmessage: \"The required list doesn't match any existing one\",\n\t\t\t\t\tdescription: \"Double-check the value in the parameter 'List' and try again\",\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tthrow new NodeApiError(this.getNode(), response as unknown as JsonObject);\n\t}\n\n\treturn data;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,0BAA4D;AAG5D,uBAA8C;AAE9C,eAAsB,wBAErB,OACA,WACgC;AAChC,MAAI,MAAM,WAAW,GAAG;AACvB,WAAO;AAAA,EACR;AAEA,QAAM,WAAW,KAAK,iBAAiB,UAAU;AACjD,MAAI,UAAU;AACb,eAAW,QAAQ,OAAO;AACzB,aAAO,KAAK,KAAK,gBAAgB;AACjC,aAAO,KAAK,KAAK,aAAa;AAC9B,aAAO,KAAK,KAAK,6BAA6B;AAC9C,aAAQ,KAAK,KAAK,SAAyB,aAAa;AAAA,IACzD;AAAA,EACD;AAEA,SAAO;AACR;AAEA,eAAsB,wBAErB,OACA,WACgC;AAChC,MAAI,MAAM,WAAW,GAAG;AACvB,WAAO;AAAA,EACR;AAEA,QAAM,WAAW,KAAK,iBAAiB,UAAU;AACjD,MAAI,UAAU;AACb,eAAW,QAAQ,OAAO;AACzB,aAAO,KAAK,KAAK,gBAAgB;AACjC,aAAO,KAAK,KAAK,aAAa;AAAA,IAC/B;AAAA,EACD;AAEA,SAAO;AACR;AAEA,eAAsB,wBAErB,QACA,UACgC;AAChC,MAAI;AACJ,MAAI,SAAS,QAAQ,qBAAqB,GAAG;AAC5C,QAAI,gBAAgB,6DAA6D;AAAA,MAChF,SAAS,QAAQ,qBAAqB;AAAA,IACvC;AACA,eACC,iBAAiB,cAAc,SAAS,IAAI,cAAc,CAAC,KAAK,cAAc,CAAC,IAAI;AACpF,QAAI,UAAU;AACb,iBAAW,mBAAmB,QAAQ;AAAA,IACvC,OAAO;AACN,sBAAgB,8BAA8B;AAAA,QAC7C,SAAS,QAAQ,qBAAqB;AAAA,MACvC;AACA,iBAAW,iBAAiB,cAAc,SAAS,IAAI,cAAc,CAAC,IAAI;AAAA,IAC3E;AAAA,EACD;AAEA,QAAM,UAA8B;AAAA,IACnC,MAAM,CAAC;AAAA,IACP,QAAQ;AAAA,MACP,MAAM,MAAM,KAAK,QAAQ;AAAA,QACxB,SAAS;AAAA,QACT;AAAA,QACA,SAAS,QAAQ,cAAc;AAAA,MAChC;AAAA,IACD;AAAA,EACD;AAEA,SAAO,CAAC,OAAO;AAChB;AAEA,eAAsB,kBAErB,gBAC+B;AAC/B,QAAM,iBAAiB,KAAK,iBAAiB,cAAc;AAC3D,OAAK,QAAQ,iBAAiB,cAAc;AAC5C,QAAM,mBAAmB,MAAM,KAAK,QAAQ,oBAAoB,cAAc;AAC9E,iBAAe,OAAO;AACtB,SAAO;AACR;AAEA,eAAsB,wBAErB,gBAC+B;AAC/B,QAAM,SAAS,KAAK,iBAAiB,gBAAgB;AACrD,iBAAe,OAAO,CAAC;AACvB,MAAI,OAAO,KAAK,CAAC,MAAM,MAAM,QAAQ,GAAG;AACvC,mBAAe,GAAG,UAAU;AAAA,EAC7B;AACA,iBAAe,GAAG,UAAU,OAAO,IAAI,CAAC,MAAM,CAAC;AAC/C,SAAO;AACR;AAEA,eAAsB,mBAErB,gBAC+B;AAC/B,QAAM,cAAc,KAAK,iBAAiB,SAAS;AACnD,QAAM,YAAY,KAAK,iBAAiB,WAAW;AAEnD,MAAI,CAAC,UAAU,QAAQ,EAAE,SAAS,SAAS,KAAK,YAAY,iBAAiB,SAAS,GAAG;AACxF,QAAI,CAAC,YAAY,gBAAgB,SAAS,IAAI,GAAG;AAChD,YAAM,OAAO,KAAK,iBAAiB,QAAQ,QAAW,EAAE,cAAc,KAAK,CAAC;AAC5E,YAAM,OAAO,KAAK,iBAAiB,QAAQ,QAAW,EAAE,cAAc,KAAK,CAAC;AAE5E,YAAM,WAAW,MAAM,+CAA8B;AAAA,QACpD;AAAA,QACA;AAAA,QACA,UAAU,IAAI,UAAU,IAAI;AAAA,QAC5B,CAAC;AAAA,QACD;AAAA,UACC,SAAS,YAAY,gBACnB,IAAI,CAAC,MAAM,UAAU,CAAC,QAAQ,YAAY,MAAO,CAAC,CAAC,GAAG,EACtD,KAAK,MAAM;AAAA,QACd;AAAA,QACA;AAAA,UACC,QAAQ;AAAA,QACT;AAAA,MACD;AACA,UAAI,SAAS,OAAO,WAAW,GAAG;AACjC,oBAAY,gBAAgB,KAAK,IAAI;AACrC,oBAAY,UAAU,CAAC;AACvB,oBAAY,MAAM,KAAK,SAAS,MAAM,CAAC,EAAE;AAAA,MAC1C;AAAA,IACD;AAEA,QAAI,cAAc,UAAU;AAC3B,UAAI,YAAY,gBAAgB,SAAS,IAAI,GAAG;AAC/C,YAAI,CAAC,YAAY,OAAO,IAAI;AAC3B,gBAAM,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb;AAAA,YACA;AAAA,cACC,aAAa;AAAA,YACd;AAAA,UACD;AAAA,QACD;AACA,uBAAe,OAAO,MAAM,YAAY,MAAM;AAC9C,eAAO,YAAY,MAAM;AACzB,uBAAe,SAAS;AAAA,MACzB;AAAA,IACD,WAAW,cAAc,UAAU;AAClC,UAAI,YAAY,gBAAgB,SAAS,IAAI,KAAK,YAAY,OAAO,IAAI;AACxE,uBAAe,OAAO,MAAM,YAAY,MAAM;AAC9C,eAAO,YAAY,MAAM;AAAA,MAC1B,OAAO;AACN,cAAM,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb;AAAA,UACA;AAAA,YACC,aAAa;AAAA,UACd;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,QAAM,SAAS,CAAC;AAChB,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,YAAY,SAAS,CAAC,CAAC,GAAG;AACnE,QAAI,YAAY,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,GAAG,GAAG,SAAS,OAAO;AACjE,aAAO,GAAG,IAAI;AAAA,QACb,aAAa;AAAA,QACb,KAAK;AAAA,MACN;AAAA,IACD,OAAO;AACN,aAAO,GAAG,IAAI;AAAA,IACf;AAAA,EACD;AACA,iBAAe,SAAS,CAAC;AACzB,EAAC,eAAe,KAAqB,SAAS;AAE9C,SAAO;AACR;AAEA,eAAsB,uBAErB,MACA,UACgC;AAChC,MAAI,OAAO,SAAS,UAAU,EAAE,WAAW,GAAG,KAAK,OAAO,SAAS,UAAU,EAAE,WAAW,GAAG,GAAG;AAC/F,UAAM,WAAW,KAAK,iBAAiB,UAAU;AACjD,UAAM,YAAY,KAAK,iBAAiB,WAAW;AAEnD,QAAI,aAAa,UAAU,cAAc,cAAc,OAAO,SAAS,SAAS,IAAI,GAAG;AACtF,eAAS,WAAO,+BAAW,SAAS,KAAgB,SAAS,CAAC;AAAA,IAC/D;AACA,UAAM,QAAS,SAAS,MAAyB,SAAS,CAAC;AAE3D,QAAI,aAAa,QAAQ;AACxB,UAAI,cAAc,YAAY;AAAA,MAC9B,WAAW,cAAc,UAAU;AAAA,MACnC,WAAW,cAAc,UAAU;AAAA,MACnC;AAAA,IACD,WAAW,aAAa,QAAQ;AAC/B,UAAI,cAAc,UAAU;AAC3B,YAAI,MAAM,SAAS,kBAAkB;AACpC,cACC,MAAM,YACN,8EACC;AACD,kBAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,UAAmC;AAAA,cACzE,SAAS;AAAA,cACT,aAAa;AAAA,YACd,CAAC;AAAA,UACF,OAAO;AACN,kBAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,UAAmC;AAAA,cACzE,SAAS,MAAM;AAAA,cACf,aAAa;AAAA,YACd,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACD,WAAW,cAAc,UAAU;AAAA,MACnC,WAAW,cAAc,OAAO;AAAA,MAChC,WAAW,cAAc,UAAU;AAAA,MACnC,WAAW,cAAc,UAAU;AAClC,YAAI,MAAM,SAAS,kBAAkB;AACpC,gBAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,UAAmC;AAAA,YACzE,SAAS,MAAM;AAAA,YACf,aAAa;AAAA,UACd,CAAC;AAAA,QACF;AAAA,MACD,WAAW,cAAc,UAAU;AAClC,YAAI,MAAM,SAAS,kBAAkB;AACpC,gBAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,UAAmC;AAAA,YACzE,SAAS,MAAM;AAAA,YACf,aAAa;AAAA,UACd,CAAC;AAAA,QACF;AAAA,MACD;AAAA,IACD,WAAW,aAAa,QAAQ;AAC/B,UAAI,cAAc,OAAO;AAAA,MACzB,WAAW,cAAc,UAAU;AAAA,MACnC;AAAA,IACD;AAEA,QAAI,MAAM,SAAS,gBAAgB;AAClC,UAAI,MAAM,QAAQ,SAAS,WAAW,GAAG;AACxC,cAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,UAAmC;AAAA,UACzE,SAAS;AAAA,UACT,aAAa;AAAA,QACd,CAAC;AAAA,MACF,WAAW,MAAM,QAAQ,SAAS,MAAM,GAAG;AAC1C,cAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,UAAmC;AAAA,UACzE,SAAS;AAAA,UACT,aAAa;AAAA,QACd,CAAC;AAAA,MACF;AAAA,IACD;AAEA,UAAM,IAAI,iCAAa,KAAK,QAAQ,GAAG,QAAiC;AAAA,EACzE;AAEA,SAAO;AACR;", "names": []}