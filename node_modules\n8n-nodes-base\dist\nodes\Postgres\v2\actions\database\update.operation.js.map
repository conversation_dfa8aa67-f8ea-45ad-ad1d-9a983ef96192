{"version": 3, "sources": ["../../../../../../nodes/Postgres/v2/actions/database/update.operation.ts"], "sourcesContent": ["import type {\n\tIDataObject,\n\tIExecuteFunctions,\n\tINodeExecutionData,\n\tINodeProperties,\n} from 'n8n-workflow';\nimport { NodeOperationError } from 'n8n-workflow';\n\nimport { updateDisplayOptions } from '@utils/utilities';\n\nimport type {\n\tPgpDatabase,\n\tPostgresNodeOptions,\n\tQueriesRunner,\n\tQueryValues,\n\tQueryWithValues,\n} from '../../helpers/interfaces';\nimport {\n\taddReturning,\n\tcheckItemAgainstSchema,\n\tconfigureTableSchemaUpdater,\n\tdoesRowExist,\n\tgetTableSchema,\n\tprepareItem,\n\tconvertArraysToPostgresFormat,\n\treplaceEmptyStringsByNulls,\n} from '../../helpers/utils';\nimport { optionsCollection } from '../common.descriptions';\n\nconst properties: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Data Mode',\n\t\tname: 'dataMode',\n\t\ttype: 'options',\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Auto-Map Input Data to Columns',\n\t\t\t\tvalue: 'autoMapInputData',\n\t\t\t\tdescription: 'Use when node input properties names exactly match the table column names',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Map Each Column Manually',\n\t\t\t\tvalue: 'defineBelow',\n\t\t\t\tdescription: 'Set the value for each destination column manually',\n\t\t\t},\n\t\t],\n\t\tdefault: 'autoMapInputData',\n\t\tdescription:\n\t\t\t'Whether to map node input properties and the table data automatically or manually',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\t'@version': [2, 2.1],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: `\n\t\tIn this mode, make sure incoming data fields are named the same as the columns in your table. If needed, use an 'Edit Fields' node before this node to change the field names.\n\t\t`,\n\t\tname: 'notice',\n\t\ttype: 'notice',\n\t\tdefault: '',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tdataMode: ['autoMapInputData'],\n\t\t\t\t'@version': [2],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-wrong-for-dynamic-options\n\t\tdisplayName: 'Column to Match On',\n\t\tname: 'columnToMatchOn',\n\t\ttype: 'options',\n\t\trequired: true,\n\t\t// eslint-disable-next-line n8n-nodes-base/node-param-description-wrong-for-dynamic-options\n\t\tdescription:\n\t\t\t'The column to compare when finding the rows to update. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\" target=\"_blank\">expression</a>.',\n\t\ttypeOptions: {\n\t\t\tloadOptionsMethod: 'getColumns',\n\t\t\tloadOptionsDependsOn: ['schema.value', 'table.value'],\n\t\t},\n\t\tdefault: '',\n\t\thint: 'The column to use when matching rows in Postgres to the input items of this node. Usually an ID.',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\t'@version': [2, 2.1],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Value of Column to Match On',\n\t\tname: 'valueToMatchOn',\n\t\ttype: 'string',\n\t\tdefault: '',\n\t\tdescription:\n\t\t\t'Rows with a value in the specified \"Column to Match On\" that corresponds to the value in this field will be updated',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tdataMode: ['defineBelow'],\n\t\t\t\t'@version': [2, 2.1],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Values to Send',\n\t\tname: 'valuesToSend',\n\t\tplaceholder: 'Add Value',\n\t\ttype: 'fixedCollection',\n\t\ttypeOptions: {\n\t\t\tmultipleValueButtonText: 'Add Value',\n\t\t\tmultipleValues: true,\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tdataMode: ['defineBelow'],\n\t\t\t\t'@version': [2, 2.1],\n\t\t\t},\n\t\t},\n\t\tdefault: {},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Values',\n\t\t\t\tname: 'values',\n\t\t\t\tvalues: [\n\t\t\t\t\t{\n\t\t\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-wrong-for-dynamic-options\n\t\t\t\t\t\tdisplayName: 'Column',\n\t\t\t\t\t\tname: 'column',\n\t\t\t\t\t\ttype: 'options',\n\t\t\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-description-wrong-for-dynamic-options\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\" target=\"_blank\">expression</a>',\n\t\t\t\t\t\ttypeOptions: {\n\t\t\t\t\t\t\tloadOptionsMethod: 'getColumnsWithoutColumnToMatchOn',\n\t\t\t\t\t\t\tloadOptionsDependsOn: ['schema.value', 'table.value'],\n\t\t\t\t\t\t},\n\t\t\t\t\t\tdefault: [],\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Value',\n\t\t\t\t\t\tname: 'value',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Columns',\n\t\tname: 'columns',\n\t\ttype: 'resourceMapper',\n\t\tnoDataExpression: true,\n\t\tdefault: {\n\t\t\tmappingMode: 'defineBelow',\n\t\t\tvalue: null,\n\t\t},\n\t\trequired: true,\n\t\ttypeOptions: {\n\t\t\tloadOptionsDependsOn: ['table.value', 'operation'],\n\t\t\tresourceMapper: {\n\t\t\t\tresourceMapperMethod: 'getMappingColumns',\n\t\t\t\tmode: 'update',\n\t\t\t\tfieldWords: {\n\t\t\t\t\tsingular: 'column',\n\t\t\t\t\tplural: 'columns',\n\t\t\t\t},\n\t\t\t\taddAllFields: true,\n\t\t\t\tmultiKeyMatch: true,\n\t\t\t},\n\t\t},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\t'@version': [{ _cnd: { gte: 2.2 } }],\n\t\t\t},\n\t\t},\n\t},\n\toptionsCollection,\n];\n\nconst displayOptions = {\n\tshow: {\n\t\tresource: ['database'],\n\t\toperation: ['update'],\n\t},\n\thide: {\n\t\ttable: [''],\n\t},\n};\n\nexport const description = updateDisplayOptions(displayOptions, properties);\n\nexport async function execute(\n\tthis: IExecuteFunctions,\n\trunQueries: QueriesRunner,\n\titems: INodeExecutionData[],\n\tnodeOptions: PostgresNodeOptions,\n\tdb: PgpDatabase,\n): Promise<INodeExecutionData[]> {\n\titems = replaceEmptyStringsByNulls(items, nodeOptions.replaceEmptyStrings as boolean);\n\tconst nodeVersion = nodeOptions.nodeVersion as number;\n\n\tlet schema = this.getNodeParameter('schema', 0, undefined, {\n\t\textractValue: true,\n\t}) as string;\n\n\tlet table = this.getNodeParameter('table', 0, undefined, {\n\t\textractValue: true,\n\t}) as string;\n\n\tconst updateTableSchema = configureTableSchemaUpdater(schema, table);\n\n\tlet tableSchema = await getTableSchema(db, schema, table);\n\n\tconst queries: QueryWithValues[] = [];\n\n\tfor (let i = 0; i < items.length; i++) {\n\t\tschema = this.getNodeParameter('schema', i, undefined, {\n\t\t\textractValue: true,\n\t\t}) as string;\n\n\t\ttable = this.getNodeParameter('table', i, undefined, {\n\t\t\textractValue: true,\n\t\t}) as string;\n\n\t\tconst columnsToMatchOn: string[] =\n\t\t\tnodeVersion < 2.2\n\t\t\t\t? [this.getNodeParameter('columnToMatchOn', i) as string]\n\t\t\t\t: (this.getNodeParameter('columns.matchingColumns', i) as string[]);\n\n\t\tconst dataMode =\n\t\t\tnodeVersion < 2.2\n\t\t\t\t? (this.getNodeParameter('dataMode', i) as string)\n\t\t\t\t: (this.getNodeParameter('columns.mappingMode', i) as string);\n\n\t\tlet item: IDataObject = {};\n\t\tlet valueToMatchOn: string | IDataObject = '';\n\t\tif (nodeVersion < 2.2) {\n\t\t\tvalueToMatchOn = this.getNodeParameter('valueToMatchOn', i) as string;\n\t\t}\n\n\t\tif (dataMode === 'autoMapInputData') {\n\t\t\titem = items[i].json;\n\t\t\tif (nodeVersion < 2.2) {\n\t\t\t\tvalueToMatchOn = item[columnsToMatchOn[0]] as string;\n\t\t\t}\n\t\t}\n\n\t\tif (dataMode === 'defineBelow') {\n\t\t\tconst valuesToSend =\n\t\t\t\tnodeVersion < 2.2\n\t\t\t\t\t? ((this.getNodeParameter('valuesToSend', i, []) as IDataObject).values as IDataObject[])\n\t\t\t\t\t: ((this.getNodeParameter('columns.values', i, []) as IDataObject)\n\t\t\t\t\t\t\t.values as IDataObject[]);\n\n\t\t\tif (nodeVersion < 2.2) {\n\t\t\t\titem = prepareItem(valuesToSend);\n\t\t\t\titem[columnsToMatchOn[0]] = this.getNodeParameter('valueToMatchOn', i) as string;\n\t\t\t} else {\n\t\t\t\titem = this.getNodeParameter('columns.value', i) as IDataObject;\n\t\t\t}\n\t\t}\n\n\t\tconst matchValues: string[] = [];\n\t\tif (nodeVersion < 2.2) {\n\t\t\tif (!item[columnsToMatchOn[0]] && dataMode === 'autoMapInputData') {\n\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\"Column to match on not found in input item. Add a column to match on or set the 'Data Mode' to 'Define Below' to define the value to match on.\",\n\t\t\t\t);\n\t\t\t}\n\t\t\tmatchValues.push(valueToMatchOn);\n\t\t\tmatchValues.push(columnsToMatchOn[0]);\n\t\t} else {\n\t\t\tcolumnsToMatchOn.forEach((column) => {\n\t\t\t\tmatchValues.push(column);\n\t\t\t\tmatchValues.push(item[column] as string);\n\t\t\t});\n\t\t\tconst rowExists = await doesRowExist(db, schema, table, matchValues);\n\t\t\tif (!rowExists) {\n\t\t\t\tconst descriptionValues: string[] = [];\n\t\t\t\tmatchValues.forEach((_, index) => {\n\t\t\t\t\tif (index % 2 === 0) {\n\t\t\t\t\t\tdescriptionValues.push(`${matchValues[index]}=${matchValues[index + 1]}`);\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\"The row you are trying to update doesn't exist\",\n\t\t\t\t\t{\n\t\t\t\t\t\tdescription: `No rows matching the provided values (${descriptionValues.join(\n\t\t\t\t\t\t\t', ',\n\t\t\t\t\t\t)}) were found in the table \"${table}\".`,\n\t\t\t\t\t\titemIndex: i,\n\t\t\t\t\t},\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\ttableSchema = await updateTableSchema(db, tableSchema, schema, table);\n\n\t\tif (nodeVersion >= 2.4) {\n\t\t\tconvertArraysToPostgresFormat(item, tableSchema, this.getNode(), i);\n\t\t}\n\n\t\titem = checkItemAgainstSchema(this.getNode(), item, tableSchema, i);\n\n\t\tlet values: QueryValues = [schema, table];\n\n\t\tlet valuesLength = values.length + 1;\n\n\t\tlet condition = '';\n\t\tif (nodeVersion < 2.2) {\n\t\t\tcondition = `$${valuesLength}:name = $${valuesLength + 1}`;\n\t\t\tvaluesLength = valuesLength + 2;\n\t\t\tvalues.push(columnsToMatchOn[0], valueToMatchOn);\n\t\t} else {\n\t\t\tconst conditions: string[] = [];\n\t\t\tfor (const column of columnsToMatchOn) {\n\t\t\t\tconditions.push(`$${valuesLength}:name = $${valuesLength + 1}`);\n\t\t\t\tvaluesLength = valuesLength + 2;\n\t\t\t\tvalues.push(column, item[column] as string);\n\t\t\t}\n\t\t\tcondition = conditions.join(' AND ');\n\t\t}\n\n\t\tconst updateColumns = Object.keys(item).filter((column) => !columnsToMatchOn.includes(column));\n\n\t\tif (!Object.keys(updateColumns).length) {\n\t\t\tthrow new NodeOperationError(\n\t\t\t\tthis.getNode(),\n\t\t\t\t\"Add values to update to the input item or set the 'Data Mode' to 'Define Below' to define the values to update.\",\n\t\t\t);\n\t\t}\n\n\t\tconst updates: string[] = [];\n\n\t\tfor (const column of updateColumns) {\n\t\t\tupdates.push(`$${valuesLength}:name = $${valuesLength + 1}`);\n\t\t\tvaluesLength = valuesLength + 2;\n\t\t\tvalues.push(column, item[column] as string);\n\t\t}\n\n\t\tlet query = `UPDATE $1:name.$2:name SET ${updates.join(', ')} WHERE ${condition}`;\n\n\t\tconst outputColumns = this.getNodeParameter('options.outputColumns', i, ['*']) as string[];\n\n\t\t[query, values] = addReturning(query, outputColumns, values);\n\n\t\tqueries.push({ query, values });\n\t}\n\n\tconst results = await runQueries(queries, items, nodeOptions);\n\treturn results;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,0BAAmC;AAEnC,uBAAqC;AASrC,mBASO;AACP,oBAAkC;AAElC,MAAM,aAAgC;AAAA,EACrC;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aACC;AAAA,IACD,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,YAAY,CAAC,GAAG,GAAG;AAAA,MACpB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA;AAAA;AAAA,IAGb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,kBAAkB;AAAA,QAC7B,YAAY,CAAC,CAAC;AAAA,MACf;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA;AAAA,IAEC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA;AAAA,IAEV,aACC;AAAA,IACD,aAAa;AAAA,MACZ,mBAAmB;AAAA,MACnB,sBAAsB,CAAC,gBAAgB,aAAa;AAAA,IACrD;AAAA,IACA,SAAS;AAAA,IACT,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,YAAY,CAAC,GAAG,GAAG;AAAA,MACpB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aACC;AAAA,IACD,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,aAAa;AAAA,QACxB,YAAY,CAAC,GAAG,GAAG;AAAA,MACpB;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,MACZ,yBAAyB;AAAA,MACzB,gBAAgB;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,aAAa;AAAA,QACxB,YAAY,CAAC,GAAG,GAAG;AAAA,MACpB;AAAA,IACD;AAAA,IACA,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,MACR;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,QAAQ;AAAA,UACP;AAAA;AAAA,YAEC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA;AAAA,YAEN,aACC;AAAA,YACD,aAAa;AAAA,cACZ,mBAAmB;AAAA,cACnB,sBAAsB,CAAC,gBAAgB,aAAa;AAAA,YACrD;AAAA,YACA,SAAS,CAAC;AAAA,UACX;AAAA,UACA;AAAA,YACC,aAAa;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,SAAS;AAAA,MACR,aAAa;AAAA,MACb,OAAO;AAAA,IACR;AAAA,IACA,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,sBAAsB,CAAC,eAAe,WAAW;AAAA,MACjD,gBAAgB;AAAA,QACf,sBAAsB;AAAA,QACtB,MAAM;AAAA,QACN,YAAY;AAAA,UACX,UAAU;AAAA,UACV,QAAQ;AAAA,QACT;AAAA,QACA,cAAc;AAAA,QACd,eAAe;AAAA,MAChB;AAAA,IACD;AAAA,IACA,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,YAAY,CAAC,EAAE,MAAM,EAAE,KAAK,IAAI,EAAE,CAAC;AAAA,MACpC;AAAA,IACD;AAAA,EACD;AAAA,EACA;AACD;AAEA,MAAM,iBAAiB;AAAA,EACtB,MAAM;AAAA,IACL,UAAU,CAAC,UAAU;AAAA,IACrB,WAAW,CAAC,QAAQ;AAAA,EACrB;AAAA,EACA,MAAM;AAAA,IACL,OAAO,CAAC,EAAE;AAAA,EACX;AACD;AAEO,MAAM,kBAAc,uCAAqB,gBAAgB,UAAU;AAE1E,eAAsB,QAErB,YACA,OACA,aACA,IACgC;AAChC,cAAQ,yCAA2B,OAAO,YAAY,mBAA8B;AACpF,QAAM,cAAc,YAAY;AAEhC,MAAI,SAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,IAC1D,cAAc;AAAA,EACf,CAAC;AAED,MAAI,QAAQ,KAAK,iBAAiB,SAAS,GAAG,QAAW;AAAA,IACxD,cAAc;AAAA,EACf,CAAC;AAED,QAAM,wBAAoB,0CAA4B,QAAQ,KAAK;AAEnE,MAAI,cAAc,UAAM,6BAAe,IAAI,QAAQ,KAAK;AAExD,QAAM,UAA6B,CAAC;AAEpC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,aAAS,KAAK,iBAAiB,UAAU,GAAG,QAAW;AAAA,MACtD,cAAc;AAAA,IACf,CAAC;AAED,YAAQ,KAAK,iBAAiB,SAAS,GAAG,QAAW;AAAA,MACpD,cAAc;AAAA,IACf,CAAC;AAED,UAAM,mBACL,cAAc,MACX,CAAC,KAAK,iBAAiB,mBAAmB,CAAC,CAAW,IACrD,KAAK,iBAAiB,2BAA2B,CAAC;AAEvD,UAAM,WACL,cAAc,MACV,KAAK,iBAAiB,YAAY,CAAC,IACnC,KAAK,iBAAiB,uBAAuB,CAAC;AAEnD,QAAI,OAAoB,CAAC;AACzB,QAAI,iBAAuC;AAC3C,QAAI,cAAc,KAAK;AACtB,uBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAAA,IAC3D;AAEA,QAAI,aAAa,oBAAoB;AACpC,aAAO,MAAM,CAAC,EAAE;AAChB,UAAI,cAAc,KAAK;AACtB,yBAAiB,KAAK,iBAAiB,CAAC,CAAC;AAAA,MAC1C;AAAA,IACD;AAEA,QAAI,aAAa,eAAe;AAC/B,YAAM,eACL,cAAc,MACT,KAAK,iBAAiB,gBAAgB,GAAG,CAAC,CAAC,EAAkB,SAC7D,KAAK,iBAAiB,kBAAkB,GAAG,CAAC,CAAC,EAC9C;AAEL,UAAI,cAAc,KAAK;AACtB,mBAAO,0BAAY,YAAY;AAC/B,aAAK,iBAAiB,CAAC,CAAC,IAAI,KAAK,iBAAiB,kBAAkB,CAAC;AAAA,MACtE,OAAO;AACN,eAAO,KAAK,iBAAiB,iBAAiB,CAAC;AAAA,MAChD;AAAA,IACD;AAEA,UAAM,cAAwB,CAAC;AAC/B,QAAI,cAAc,KAAK;AACtB,UAAI,CAAC,KAAK,iBAAiB,CAAC,CAAC,KAAK,aAAa,oBAAoB;AAClE,cAAM,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb;AAAA,QACD;AAAA,MACD;AACA,kBAAY,KAAK,cAAc;AAC/B,kBAAY,KAAK,iBAAiB,CAAC,CAAC;AAAA,IACrC,OAAO;AACN,uBAAiB,QAAQ,CAAC,WAAW;AACpC,oBAAY,KAAK,MAAM;AACvB,oBAAY,KAAK,KAAK,MAAM,CAAW;AAAA,MACxC,CAAC;AACD,YAAM,YAAY,UAAM,2BAAa,IAAI,QAAQ,OAAO,WAAW;AACnE,UAAI,CAAC,WAAW;AACf,cAAM,oBAA8B,CAAC;AACrC,oBAAY,QAAQ,CAAC,GAAG,UAAU;AACjC,cAAI,QAAQ,MAAM,GAAG;AACpB,8BAAkB,KAAK,GAAG,YAAY,KAAK,CAAC,IAAI,YAAY,QAAQ,CAAC,CAAC,EAAE;AAAA,UACzE;AAAA,QACD,CAAC;AAED,cAAM,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb;AAAA,UACA;AAAA,YACC,aAAa,yCAAyC,kBAAkB;AAAA,cACvE;AAAA,YACD,CAAC,8BAA8B,KAAK;AAAA,YACpC,WAAW;AAAA,UACZ;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,kBAAc,MAAM,kBAAkB,IAAI,aAAa,QAAQ,KAAK;AAEpE,QAAI,eAAe,KAAK;AACvB,sDAA8B,MAAM,aAAa,KAAK,QAAQ,GAAG,CAAC;AAAA,IACnE;AAEA,eAAO,qCAAuB,KAAK,QAAQ,GAAG,MAAM,aAAa,CAAC;AAElE,QAAI,SAAsB,CAAC,QAAQ,KAAK;AAExC,QAAI,eAAe,OAAO,SAAS;AAEnC,QAAI,YAAY;AAChB,QAAI,cAAc,KAAK;AACtB,kBAAY,IAAI,YAAY,YAAY,eAAe,CAAC;AACxD,qBAAe,eAAe;AAC9B,aAAO,KAAK,iBAAiB,CAAC,GAAG,cAAc;AAAA,IAChD,OAAO;AACN,YAAM,aAAuB,CAAC;AAC9B,iBAAW,UAAU,kBAAkB;AACtC,mBAAW,KAAK,IAAI,YAAY,YAAY,eAAe,CAAC,EAAE;AAC9D,uBAAe,eAAe;AAC9B,eAAO,KAAK,QAAQ,KAAK,MAAM,CAAW;AAAA,MAC3C;AACA,kBAAY,WAAW,KAAK,OAAO;AAAA,IACpC;AAEA,UAAM,gBAAgB,OAAO,KAAK,IAAI,EAAE,OAAO,CAAC,WAAW,CAAC,iBAAiB,SAAS,MAAM,CAAC;AAE7F,QAAI,CAAC,OAAO,KAAK,aAAa,EAAE,QAAQ;AACvC,YAAM,IAAI;AAAA,QACT,KAAK,QAAQ;AAAA,QACb;AAAA,MACD;AAAA,IACD;AAEA,UAAM,UAAoB,CAAC;AAE3B,eAAW,UAAU,eAAe;AACnC,cAAQ,KAAK,IAAI,YAAY,YAAY,eAAe,CAAC,EAAE;AAC3D,qBAAe,eAAe;AAC9B,aAAO,KAAK,QAAQ,KAAK,MAAM,CAAW;AAAA,IAC3C;AAEA,QAAI,QAAQ,8BAA8B,QAAQ,KAAK,IAAI,CAAC,UAAU,SAAS;AAE/E,UAAM,gBAAgB,KAAK,iBAAiB,yBAAyB,GAAG,CAAC,GAAG,CAAC;AAE7E,KAAC,OAAO,MAAM,QAAI,2BAAa,OAAO,eAAe,MAAM;AAE3D,YAAQ,KAAK,EAAE,OAAO,OAAO,CAAC;AAAA,EAC/B;AAEA,QAAM,UAAU,MAAM,WAAW,SAAS,OAAO,WAAW;AAC5D,SAAO;AACR;", "names": []}