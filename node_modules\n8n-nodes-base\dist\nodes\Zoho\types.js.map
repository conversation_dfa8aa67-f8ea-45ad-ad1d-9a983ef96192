{"version": 3, "sources": ["../../../nodes/Zoho/types.ts"], "sourcesContent": ["import type { IDataObject } from 'n8n-workflow';\n\n// ----------------------------------------\n//          for generic functions\n// ----------------------------------------\n\ntype Resource =\n\t| 'account'\n\t| 'contact'\n\t| 'deal'\n\t| 'invoice'\n\t| 'lead'\n\t| 'product'\n\t| 'quote'\n\t| 'vendor';\n\nexport type CamelCaseResource = Resource | 'purchaseOrder' | 'salesOrder';\n\nexport type SnakeCaseResource = Resource | 'purchase_order' | 'sales_order';\n\nexport type GetAllFilterOptions = {\n\tfields: string[];\n\t[otherOptions: string]: unknown;\n};\n\n// ----------------------------------------\n//               for auth\n// ----------------------------------------\n\nexport type ZohoOAuth2ApiCredentials = {\n\toauthTokenData: {\n\t\tapi_domain: string;\n\t};\n};\n\n// ----------------------------------------\n//         for field adjusters\n// ----------------------------------------\n\nexport type IdType = 'accountId' | 'contactId' | 'dealId' | 'purchaseOrderId';\n\nexport type NameType = 'Account_Name' | 'Full_Name' | 'Deal_Name' | 'Product_Name' | 'Vendor_Name';\n\nexport type LocationType =\n\t| 'Address'\n\t| 'Billing_Address'\n\t| 'Mailing_Address'\n\t| 'Shipping_Address'\n\t| 'Other_Address';\n\nexport type DateType =\n\t| 'Date_of_Birth'\n\t| 'Closing_Date'\n\t| 'Due_Date'\n\t| 'Invoice_Date'\n\t| 'PO_Date'\n\t| 'Valid_Till';\n\nexport type AllFields = { [Date in DateType]?: string } & {\n\t[Location in LocationType]?: { address_fields: { [key: string]: string } };\n} & { Account?: { subfields: { id: string; name: string } } } & {\n\t[key in 'accountId' | 'contactId' | 'dealId']?: string;\n} & { customFields?: { customFields: Array<{ fieldId: string; value: string }> } } & {\n\tProduct_Details?: ProductDetails;\n} & IDataObject;\n\nexport type ProductDetails = Array<{ id: string; quantity: number }>;\n\nexport type ResourceItems = Array<{ [key: string]: string }>;\n\n// ----------------------------------------\n//         for resource loaders\n// ----------------------------------------\n\nexport type LoadedAccounts = Array<{\n\tAccount_Name: string;\n\tid: string;\n}>;\n\nexport type LoadedContacts = Array<{\n\tFull_Name: string;\n\tid: string;\n}>;\n\nexport type LoadedDeals = Array<{\n\tDeal_Name: string;\n\tid: string;\n}>;\n\nexport type LoadedFields = {\n\tfields: Array<{\n\t\tfield_label: string;\n\t\tapi_name: string;\n\t\tcustom_field: boolean;\n\t}>;\n};\n\nexport type LoadedVendors = Array<{\n\tVendor_Name: string;\n\tid: string;\n}>;\n\nexport type LoadedProducts = Array<{\n\tProduct_Name: string;\n\tid: string;\n}>;\n\nexport type LoadedLayouts = {\n\tlayouts: Array<{\n\t\tsections: Array<{\n\t\t\tapi_name: string;\n\t\t\tfields: Array<{\n\t\t\t\tapi_name: string;\n\t\t\t\tpick_list_values: Array<{\n\t\t\t\t\tdisplay_value: string;\n\t\t\t\t\tactual_value: string;\n\t\t\t\t}>;\n\t\t\t}>;\n\t\t}>;\n\t}>;\n};\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}