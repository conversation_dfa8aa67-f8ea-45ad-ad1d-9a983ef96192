{"version": 3, "sources": ["../../../../nodes/Transform/Summarize/utils.ts"], "sourcesContent": ["import get from 'lodash/get';\nimport {\n\ttype GenericValue,\n\ttype IDataObject,\n\ttype IExecuteFunctions,\n\tNodeOperationError,\n} from 'n8n-workflow';\n\ntype AggregationType =\n\t| 'append'\n\t| 'average'\n\t| 'concatenate'\n\t| 'count'\n\t| 'countUnique'\n\t| 'max'\n\t| 'min'\n\t| 'sum';\n\nexport type Aggregation = {\n\taggregation: AggregationType;\n\tfield: string;\n\tincludeEmpty?: boolean;\n\tseparateBy?: string;\n\tcustomSeparator?: string;\n};\n\nexport type Aggregations = Aggregation[];\n\nconst AggregationDisplayNames = {\n\tappend: 'appended_',\n\taverage: 'average_',\n\tconcatenate: 'concatenated_',\n\tcount: 'count_',\n\tcountUnique: 'unique_count_',\n\tmax: 'max_',\n\tmin: 'min_',\n\tsum: 'sum_',\n};\n\nexport const NUMERICAL_AGGREGATIONS = ['average', 'sum'];\n\nexport type SummarizeOptions = {\n\tcontinueIfFieldNotFound: boolean;\n\tdisableDotNotation?: boolean;\n\toutputFormat?: 'separateItems' | 'singleItem';\n\tskipEmptySplitFields?: boolean;\n};\n\nexport type ValueGetterFn = (\n\titem: IDataObject,\n\tfield: string,\n) => IDataObject | IDataObject[] | GenericValue | GenericValue[];\n\nfunction isEmpty<T>(value: T) {\n\treturn value === undefined || value === null || value === '';\n}\n\nfunction normalizeFieldName(fieldName: string) {\n\treturn fieldName.replace(/[\\]\\[\"]/g, '').replace(/[ .]/g, '_');\n}\n\nexport const fieldValueGetter = (disableDotNotation?: boolean) => {\n\treturn (item: IDataObject, field: string) =>\n\t\tdisableDotNotation ? item[field] : get(item, field);\n};\n\nexport function checkIfFieldExists(\n\tthis: IExecuteFunctions,\n\titems: IDataObject[],\n\taggregations: Aggregations,\n\tgetValue: ValueGetterFn,\n) {\n\tfor (const aggregation of aggregations) {\n\t\tif (aggregation.field === '') {\n\t\t\tcontinue;\n\t\t}\n\t\tconst exist = items.some((item) => getValue(item, aggregation.field) !== undefined);\n\t\tif (!exist) {\n\t\t\tthrow new NodeOperationError(\n\t\t\t\tthis.getNode(),\n\t\t\t\t`The field '${aggregation.field}' does not exist in any items`,\n\t\t\t);\n\t\t}\n\t}\n}\n\nfunction aggregate(items: IDataObject[], entry: Aggregation, getValue: ValueGetterFn) {\n\tconst { aggregation, field } = entry;\n\tlet data = [...items];\n\n\tif (NUMERICAL_AGGREGATIONS.includes(aggregation)) {\n\t\tdata = data.filter(\n\t\t\t(item) => typeof getValue(item, field) === 'number' && !isEmpty(getValue(item, field)),\n\t\t);\n\t}\n\n\tswitch (aggregation) {\n\t\t//combine operations\n\t\tcase 'append':\n\t\t\tif (!entry.includeEmpty) {\n\t\t\t\tdata = data.filter((item) => !isEmpty(getValue(item, field)));\n\t\t\t}\n\t\t\treturn data.map((item) => getValue(item, field));\n\t\tcase 'concatenate':\n\t\t\tconst separateBy = entry.separateBy === 'other' ? entry.customSeparator : entry.separateBy;\n\t\t\tif (!entry.includeEmpty) {\n\t\t\t\tdata = data.filter((item) => !isEmpty(getValue(item, field)));\n\t\t\t}\n\t\t\treturn data\n\t\t\t\t.map((item) => {\n\t\t\t\t\tlet value = getValue(item, field);\n\t\t\t\t\tif (typeof value === 'object') {\n\t\t\t\t\t\tvalue = JSON.stringify(value);\n\t\t\t\t\t}\n\t\t\t\t\tif (typeof value === 'undefined') {\n\t\t\t\t\t\tvalue = 'undefined';\n\t\t\t\t\t}\n\n\t\t\t\t\treturn value;\n\t\t\t\t})\n\t\t\t\t.join(separateBy);\n\n\t\t//numerical operations\n\t\tcase 'average':\n\t\t\treturn (\n\t\t\t\tdata.reduce((acc, item) => {\n\t\t\t\t\treturn acc + (getValue(item, field) as number);\n\t\t\t\t}, 0) / data.length\n\t\t\t);\n\t\tcase 'sum':\n\t\t\treturn data.reduce((acc, item) => {\n\t\t\t\treturn acc + (getValue(item, field) as number);\n\t\t\t}, 0);\n\t\t//comparison operations\n\t\tcase 'min':\n\t\t\tlet min;\n\t\t\tfor (const item of data) {\n\t\t\t\tconst value = getValue(item, field);\n\t\t\t\tif (value !== undefined && value !== null && value !== '') {\n\t\t\t\t\tif (min === undefined || value < min) {\n\t\t\t\t\t\tmin = value;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn min ?? null;\n\t\tcase 'max':\n\t\t\tlet max;\n\t\t\tfor (const item of data) {\n\t\t\t\tconst value = getValue(item, field);\n\t\t\t\tif (value !== undefined && value !== null && value !== '') {\n\t\t\t\t\tif (max === undefined || value > max) {\n\t\t\t\t\t\tmax = value;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn max ?? null;\n\n\t\t//count operations\n\t\tcase 'countUnique':\n\t\t\tif (!entry.includeEmpty) {\n\t\t\t\treturn new Set(data.map((item) => getValue(item, field)).filter((item) => !isEmpty(item)))\n\t\t\t\t\t.size;\n\t\t\t}\n\t\t\treturn new Set(data.map((item) => getValue(item, field))).size;\n\n\t\tdefault:\n\t\t\t//count by default\n\t\t\tif (!entry.includeEmpty) {\n\t\t\t\treturn data.filter((item) => !isEmpty(getValue(item, field))).length;\n\t\t\t}\n\t\t\treturn data.length;\n\t}\n}\n\nfunction aggregateData(\n\tdata: IDataObject[],\n\tfieldsToSummarize: Aggregations,\n\toptions: SummarizeOptions,\n\tgetValue: ValueGetterFn,\n): { returnData: IDataObject; pairedItems?: number[] } {\n\tconst returnData = Object.fromEntries(\n\t\tfieldsToSummarize.map((aggregation) => {\n\t\t\tconst key = normalizeFieldName(\n\t\t\t\t`${AggregationDisplayNames[aggregation.aggregation]}${aggregation.field}`,\n\t\t\t);\n\t\t\tconst result = aggregate(data, aggregation, getValue);\n\t\t\treturn [key, result];\n\t\t}),\n\t);\n\n\tif (options.outputFormat === 'singleItem') {\n\t\treturn { returnData };\n\t}\n\n\treturn { returnData, pairedItems: data.map((item) => item._itemIndex as number) };\n}\n\ntype AggregationResult = { returnData: IDataObject; pairedItems?: number[] };\ntype NestedAggregationResult =\n\t| AggregationResult\n\t| { fieldName: string; splits: Map<unknown, NestedAggregationResult> };\n\n// Using Map to preserve types\n// With a plain JS object, keys are converted to string\nexport function aggregateAndSplitData({\n\tsplitKeys,\n\tinputItems,\n\tfieldsToSummarize,\n\toptions,\n\tgetValue,\n\tconvertKeysToString = false,\n}: {\n\tsplitKeys: string[] | undefined;\n\tinputItems: IDataObject[];\n\tfieldsToSummarize: Aggregations;\n\toptions: SummarizeOptions;\n\tgetValue: ValueGetterFn;\n\tconvertKeysToString?: boolean; // Legacy option for v1\n}): NestedAggregationResult {\n\tif (!splitKeys?.length) {\n\t\treturn aggregateData(inputItems, fieldsToSummarize, options, getValue);\n\t}\n\n\tconst [firstSplitKey, ...restSplitKeys] = splitKeys;\n\n\tconst groupedItems = new Map<unknown, IDataObject[]>();\n\tfor (const item of inputItems) {\n\t\tlet splitValue = getValue(item, firstSplitKey);\n\n\t\tif (splitValue && typeof splitValue === 'object') {\n\t\t\tsplitValue = JSON.stringify(splitValue);\n\t\t}\n\n\t\tif (convertKeysToString) {\n\t\t\tsplitValue = String(splitValue);\n\t\t}\n\n\t\tif (options.skipEmptySplitFields && typeof splitValue !== 'number' && !splitValue) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tconst group = groupedItems.get(splitValue) ?? [];\n\t\tgroupedItems.set(splitValue, group.concat([item]));\n\t}\n\n\tconst splits = new Map(\n\t\tArray.from(groupedItems.entries()).map(([groupKey, items]) => [\n\t\t\tgroupKey,\n\t\t\taggregateAndSplitData({\n\t\t\t\tsplitKeys: restSplitKeys,\n\t\t\t\tinputItems: items,\n\t\t\t\tfieldsToSummarize,\n\t\t\t\toptions,\n\t\t\t\tgetValue,\n\t\t\t}),\n\t\t]),\n\t);\n\n\treturn { fieldName: firstSplitKey, splits };\n}\n\nexport function flattenAggregationResultToObject(result: NestedAggregationResult): IDataObject {\n\tif ('splits' in result) {\n\t\treturn Object.fromEntries(\n\t\t\tArray.from(result.splits.entries()).map(([key, value]) => [\n\t\t\t\tkey,\n\t\t\t\tflattenAggregationResultToObject(value),\n\t\t\t]),\n\t\t);\n\t}\n\n\treturn result.returnData;\n}\n\nexport function flattenAggregationResultToArray(\n\tresult: NestedAggregationResult,\n): AggregationResult[] {\n\tif ('splits' in result) {\n\t\treturn Array.from(result.splits.entries()).flatMap(([value, innerResult]) =>\n\t\t\tflattenAggregationResultToArray(innerResult).map((v) => {\n\t\t\t\tv.returnData[normalizeFieldName(result.fieldName)] = value as IDataObject;\n\t\t\t\treturn v;\n\t\t\t}),\n\t\t);\n\t}\n\treturn [result];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAgB;AAChB,0BAKO;AAsBP,MAAM,0BAA0B;AAAA,EAC/B,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,aAAa;AAAA,EACb,OAAO;AAAA,EACP,aAAa;AAAA,EACb,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACN;AAEO,MAAM,yBAAyB,CAAC,WAAW,KAAK;AAcvD,SAAS,QAAW,OAAU;AAC7B,SAAO,UAAU,UAAa,UAAU,QAAQ,UAAU;AAC3D;AAEA,SAAS,mBAAmB,WAAmB;AAC9C,SAAO,UAAU,QAAQ,YAAY,EAAE,EAAE,QAAQ,SAAS,GAAG;AAC9D;AAEO,MAAM,mBAAmB,CAAC,uBAAiC;AACjE,SAAO,CAAC,MAAmB,UAC1B,qBAAqB,KAAK,KAAK,QAAI,WAAAA,SAAI,MAAM,KAAK;AACpD;AAEO,SAAS,mBAEf,OACA,cACA,UACC;AACD,aAAW,eAAe,cAAc;AACvC,QAAI,YAAY,UAAU,IAAI;AAC7B;AAAA,IACD;AACA,UAAM,QAAQ,MAAM,KAAK,CAAC,SAAS,SAAS,MAAM,YAAY,KAAK,MAAM,MAAS;AAClF,QAAI,CAAC,OAAO;AACX,YAAM,IAAI;AAAA,QACT,KAAK,QAAQ;AAAA,QACb,cAAc,YAAY,KAAK;AAAA,MAChC;AAAA,IACD;AAAA,EACD;AACD;AAEA,SAAS,UAAU,OAAsB,OAAoB,UAAyB;AACrF,QAAM,EAAE,aAAa,MAAM,IAAI;AAC/B,MAAI,OAAO,CAAC,GAAG,KAAK;AAEpB,MAAI,uBAAuB,SAAS,WAAW,GAAG;AACjD,WAAO,KAAK;AAAA,MACX,CAAC,SAAS,OAAO,SAAS,MAAM,KAAK,MAAM,YAAY,CAAC,QAAQ,SAAS,MAAM,KAAK,CAAC;AAAA,IACtF;AAAA,EACD;AAEA,UAAQ,aAAa;AAAA;AAAA,IAEpB,KAAK;AACJ,UAAI,CAAC,MAAM,cAAc;AACxB,eAAO,KAAK,OAAO,CAAC,SAAS,CAAC,QAAQ,SAAS,MAAM,KAAK,CAAC,CAAC;AAAA,MAC7D;AACA,aAAO,KAAK,IAAI,CAAC,SAAS,SAAS,MAAM,KAAK,CAAC;AAAA,IAChD,KAAK;AACJ,YAAM,aAAa,MAAM,eAAe,UAAU,MAAM,kBAAkB,MAAM;AAChF,UAAI,CAAC,MAAM,cAAc;AACxB,eAAO,KAAK,OAAO,CAAC,SAAS,CAAC,QAAQ,SAAS,MAAM,KAAK,CAAC,CAAC;AAAA,MAC7D;AACA,aAAO,KACL,IAAI,CAAC,SAAS;AACd,YAAI,QAAQ,SAAS,MAAM,KAAK;AAChC,YAAI,OAAO,UAAU,UAAU;AAC9B,kBAAQ,KAAK,UAAU,KAAK;AAAA,QAC7B;AACA,YAAI,OAAO,UAAU,aAAa;AACjC,kBAAQ;AAAA,QACT;AAEA,eAAO;AAAA,MACR,CAAC,EACA,KAAK,UAAU;AAAA;AAAA,IAGlB,KAAK;AACJ,aACC,KAAK,OAAO,CAAC,KAAK,SAAS;AAC1B,eAAO,MAAO,SAAS,MAAM,KAAK;AAAA,MACnC,GAAG,CAAC,IAAI,KAAK;AAAA,IAEf,KAAK;AACJ,aAAO,KAAK,OAAO,CAAC,KAAK,SAAS;AACjC,eAAO,MAAO,SAAS,MAAM,KAAK;AAAA,MACnC,GAAG,CAAC;AAAA;AAAA,IAEL,KAAK;AACJ,UAAI;AACJ,iBAAW,QAAQ,MAAM;AACxB,cAAM,QAAQ,SAAS,MAAM,KAAK;AAClC,YAAI,UAAU,UAAa,UAAU,QAAQ,UAAU,IAAI;AAC1D,cAAI,QAAQ,UAAa,QAAQ,KAAK;AACrC,kBAAM;AAAA,UACP;AAAA,QACD;AAAA,MACD;AACA,aAAO,OAAO;AAAA,IACf,KAAK;AACJ,UAAI;AACJ,iBAAW,QAAQ,MAAM;AACxB,cAAM,QAAQ,SAAS,MAAM,KAAK;AAClC,YAAI,UAAU,UAAa,UAAU,QAAQ,UAAU,IAAI;AAC1D,cAAI,QAAQ,UAAa,QAAQ,KAAK;AACrC,kBAAM;AAAA,UACP;AAAA,QACD;AAAA,MACD;AACA,aAAO,OAAO;AAAA;AAAA,IAGf,KAAK;AACJ,UAAI,CAAC,MAAM,cAAc;AACxB,eAAO,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,SAAS,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,QAAQ,IAAI,CAAC,CAAC,EACvF;AAAA,MACH;AACA,aAAO,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,SAAS,MAAM,KAAK,CAAC,CAAC,EAAE;AAAA,IAE3D;AAEC,UAAI,CAAC,MAAM,cAAc;AACxB,eAAO,KAAK,OAAO,CAAC,SAAS,CAAC,QAAQ,SAAS,MAAM,KAAK,CAAC,CAAC,EAAE;AAAA,MAC/D;AACA,aAAO,KAAK;AAAA,EACd;AACD;AAEA,SAAS,cACR,MACA,mBACA,SACA,UACsD;AACtD,QAAM,aAAa,OAAO;AAAA,IACzB,kBAAkB,IAAI,CAAC,gBAAgB;AACtC,YAAM,MAAM;AAAA,QACX,GAAG,wBAAwB,YAAY,WAAW,CAAC,GAAG,YAAY,KAAK;AAAA,MACxE;AACA,YAAM,SAAS,UAAU,MAAM,aAAa,QAAQ;AACpD,aAAO,CAAC,KAAK,MAAM;AAAA,IACpB,CAAC;AAAA,EACF;AAEA,MAAI,QAAQ,iBAAiB,cAAc;AAC1C,WAAO,EAAE,WAAW;AAAA,EACrB;AAEA,SAAO,EAAE,YAAY,aAAa,KAAK,IAAI,CAAC,SAAS,KAAK,UAAoB,EAAE;AACjF;AASO,SAAS,sBAAsB;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,sBAAsB;AACvB,GAO4B;AAC3B,MAAI,CAAC,WAAW,QAAQ;AACvB,WAAO,cAAc,YAAY,mBAAmB,SAAS,QAAQ;AAAA,EACtE;AAEA,QAAM,CAAC,eAAe,GAAG,aAAa,IAAI;AAE1C,QAAM,eAAe,oBAAI,IAA4B;AACrD,aAAW,QAAQ,YAAY;AAC9B,QAAI,aAAa,SAAS,MAAM,aAAa;AAE7C,QAAI,cAAc,OAAO,eAAe,UAAU;AACjD,mBAAa,KAAK,UAAU,UAAU;AAAA,IACvC;AAEA,QAAI,qBAAqB;AACxB,mBAAa,OAAO,UAAU;AAAA,IAC/B;AAEA,QAAI,QAAQ,wBAAwB,OAAO,eAAe,YAAY,CAAC,YAAY;AAClF;AAAA,IACD;AAEA,UAAM,QAAQ,aAAa,IAAI,UAAU,KAAK,CAAC;AAC/C,iBAAa,IAAI,YAAY,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;AAAA,EAClD;AAEA,QAAM,SAAS,IAAI;AAAA,IAClB,MAAM,KAAK,aAAa,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC,UAAU,KAAK,MAAM;AAAA,MAC7D;AAAA,MACA,sBAAsB;AAAA,QACrB,WAAW;AAAA,QACX,YAAY;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACD,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AAEA,SAAO,EAAE,WAAW,eAAe,OAAO;AAC3C;AAEO,SAAS,iCAAiC,QAA8C;AAC9F,MAAI,YAAY,QAAQ;AACvB,WAAO,OAAO;AAAA,MACb,MAAM,KAAK,OAAO,OAAO,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AAAA,QACzD;AAAA,QACA,iCAAiC,KAAK;AAAA,MACvC,CAAC;AAAA,IACF;AAAA,EACD;AAEA,SAAO,OAAO;AACf;AAEO,SAAS,gCACf,QACsB;AACtB,MAAI,YAAY,QAAQ;AACvB,WAAO,MAAM,KAAK,OAAO,OAAO,QAAQ,CAAC,EAAE;AAAA,MAAQ,CAAC,CAAC,OAAO,WAAW,MACtE,gCAAgC,WAAW,EAAE,IAAI,CAAC,MAAM;AACvD,UAAE,WAAW,mBAAmB,OAAO,SAAS,CAAC,IAAI;AACrD,eAAO;AAAA,MACR,CAAC;AAAA,IACF;AAAA,EACD;AACA,SAAO,CAAC,MAAM;AACf;", "names": ["get"]}