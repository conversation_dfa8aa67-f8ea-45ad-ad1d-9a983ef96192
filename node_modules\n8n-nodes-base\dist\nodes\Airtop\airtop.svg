<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><g filter="url(#a)"><g clip-path="url(#b)"><circle cx="16" cy="16" r="16" fill="#102626"/><g filter="url(#c)"><path stroke="#58D1EC" stroke-opacity=".2" stroke-width="21.821" d="m-24.172-33.325 41.946 51.414"/></g><path fill="url(#d)" fill-rule="evenodd" d="M12.598 9.258c1.607-2.545 5.318-2.545 6.925 0l2.33 3.69c.705 1.115-.097 2.57-1.416 2.57-2.207 0-3.088-.841-3.66-4.182l-1.423.005c-.496 3.346-1.507 4.177-4.17 4.177l-.005 1.437c2.696 0 4.175 1.495 4.175 3.454a2.94 2.94 0 0 1-2.94 2.94h-1.285c-3.225 0-5.185-3.555-3.463-6.282l4.932-7.809zm7.125 14.092a2.945 2.945 0 0 1-2.945-2.946c0-1.954 1.305-3.449 3.659-3.449h1.443c1.479 0 3.093.924 3.093 2.746 0 2.209-2.511 3.649-3.921 3.649h-1.329z" clip-rule="evenodd"/></g></g><defs><filter id="a" width="32" height="35" x="0" y="-2" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="1"/><feGaussianBlur stdDeviation="1"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0.345098 0 0 0 0 0.819608 0 0 0 0 0.92549 0 0 0 0.16 0"/><feBlend in2="shape" result="effect1_innerShadow_820_10455"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="-2"/><feGaussianBlur stdDeviation="1"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0.0148985 0 0 0 0 0.0926901 0 0 0 0 0.0926901 0 0 0 0.4 0"/><feBlend in2="effect1_innerShadow_820_10455" result="effect2_innerShadow_820_10455"/></filter><filter id="c" width="74.854" height="81.209" x="-40.627" y="-48.223" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feGaussianBlur result="effect1_foregroundBlur_820_10455" stdDeviation="4"/></filter><radialGradient id="d" cx="0" cy="0" r="1" gradientTransform="matrix(-11.68435 11.70476 -89.05215 -88.89686 19.28 9.557)" gradientUnits="userSpaceOnUse"><stop offset=".613" stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity=".8"/></radialGradient><clipPath id="b"><rect width="32" height="32" fill="#fff" rx="16"/></clipPath></defs></svg>