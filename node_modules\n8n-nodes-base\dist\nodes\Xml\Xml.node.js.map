{"version": 3, "sources": ["../../../nodes/Xml/Xml.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tINodeExecutionData,\n\tINodeType,\n\tINodeTypeDescription,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes, NodeOperationError, deepCopy } from 'n8n-workflow';\nimport { <PERSON><PERSON><PERSON>, Parse<PERSON> } from 'xml2js';\n\nexport class Xml implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'XML',\n\t\tname: 'xml',\n\t\ticon: 'fa:file-code',\n\t\ticonColor: 'purple',\n\t\tgroup: ['transform'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"mode\"]===\"jsonToxml\" ? \"JSON to XML\" : \"XML to JSON\"}}',\n\t\tdescription: 'Convert data from and to XML',\n\t\tdefaults: {\n\t\t\tname: 'XML',\n\t\t\tcolor: '#333377',\n\t\t},\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Mode',\n\t\t\t\tname: 'mode',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'JSON to XML',\n\t\t\t\t\t\tvalue: 'jsonToxml',\n\t\t\t\t\t\tdescription: 'Converts data from JSON to XML',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'XML to JSON',\n\t\t\t\t\t\tvalue: 'xmlToJson',\n\t\t\t\t\t\tdescription: 'Converts data from XML to JSON',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'xmlToJson',\n\t\t\t\tdescription: 'From and to what format the data should be converted',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName:\n\t\t\t\t\t\"If your XML is inside a binary file, use the 'Extract from File' node to convert it to text first\",\n\t\t\t\tname: 'xmlNotice',\n\t\t\t\ttype: 'notice',\n\t\t\t\tdefault: '',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tmode: ['xmlToJson'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\n\t\t\t// ----------------------------------\n\t\t\t//         option:jsonToxml\n\t\t\t// ----------------------------------\n\t\t\t{\n\t\t\t\tdisplayName: 'Property Name',\n\t\t\t\tname: 'dataPropertyName',\n\t\t\t\ttype: 'string',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tmode: ['jsonToxml'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdefault: 'data',\n\t\t\t\trequired: true,\n\t\t\t\tdescription: 'Name of the property to which to contains the converted XML data',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Options',\n\t\t\t\tname: 'options',\n\t\t\t\ttype: 'collection',\n\t\t\t\tplaceholder: 'Add option',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tmode: ['jsonToxml'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Allow Surrogate Chars',\n\t\t\t\t\t\tname: 'allowSurrogateChars',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\tdescription: 'Whether to allow using characters from the Unicode surrogate blocks',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Attribute Key',\n\t\t\t\t\t\tname: 'attrkey',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '$',\n\t\t\t\t\t\tdescription: 'Prefix that is used to access the attributes',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Cdata',\n\t\t\t\t\t\tname: 'cdata',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Whether to wrap text nodes in &lt;![CDATA[ ... ]]&gt; instead of escaping when necessary. Does not add &lt;![CDATA[ ... ]]&gt; if it is not required.',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Character Key',\n\t\t\t\t\t\tname: 'charkey',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '_',\n\t\t\t\t\t\tdescription: 'Prefix that is used to access the character content',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Headless',\n\t\t\t\t\t\tname: 'headless',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\tdescription: 'Whether to omit the XML header',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Root Name',\n\t\t\t\t\t\tname: 'rootName',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: 'root',\n\t\t\t\t\t\tdescription: 'Root element name to be used',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\n\t\t\t// ----------------------------------\n\t\t\t//         option:xmlToJson\n\t\t\t// ----------------------------------\n\t\t\t{\n\t\t\t\tdisplayName: 'Property Name',\n\t\t\t\tname: 'dataPropertyName',\n\t\t\t\ttype: 'string',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tmode: ['xmlToJson'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdefault: 'data',\n\t\t\t\trequired: true,\n\t\t\t\tdescription: 'Name of the property which contains the XML data to convert',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Options',\n\t\t\t\tname: 'options',\n\t\t\t\ttype: 'collection',\n\t\t\t\tplaceholder: 'Add option',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tmode: ['xmlToJson'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdefault: {},\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Attribute Key',\n\t\t\t\t\t\tname: 'attrkey',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '$',\n\t\t\t\t\t\tdescription: 'Prefix that is used to access the attributes',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Character Key',\n\t\t\t\t\t\tname: 'charkey',\n\t\t\t\t\t\ttype: 'string',\n\t\t\t\t\t\tdefault: '_',\n\t\t\t\t\t\tdescription: 'Prefix that is used to access the character content',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Explicit Array',\n\t\t\t\t\t\tname: 'explicitArray',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Whether to always put child nodes in an array if true; otherwise an array is created only if there is more than one',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Explicit Root',\n\t\t\t\t\t\tname: 'explicitRoot',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: true,\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Whether to set this if you want to get the root node in the resulting object',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Ignore Attributes',\n\t\t\t\t\t\tname: 'ignoreAttrs',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\tdescription: 'Whether to ignore all XML attributes and only create text nodes',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Merge Attributes',\n\t\t\t\t\t\tname: 'mergeAttrs',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: true,\n\t\t\t\t\t\tdescription:\n\t\t\t\t\t\t\t'Whether to merge attributes and child elements as properties of the parent, instead of keying attributes off a child attribute object. This option is ignored if ignoreAttrs is true.',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Normalize',\n\t\t\t\t\t\tname: 'normalize',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\tdescription: 'Whether to trim whitespaces inside text nodes',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Normalize Tags',\n\t\t\t\t\t\tname: 'normalizeTags',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\tdescription: 'Whether to normalize all tag names to lowercase',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tdisplayName: 'Trim',\n\t\t\t\t\t\tname: 'trim',\n\t\t\t\t\t\ttype: 'boolean',\n\t\t\t\t\t\tdefault: false,\n\t\t\t\t\t\tdescription: 'Whether to trim the whitespace at the beginning and end of text nodes',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t},\n\t\t],\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\n\t\tconst mode = this.getNodeParameter('mode', 0) as string;\n\t\tconst dataPropertyName = this.getNodeParameter('dataPropertyName', 0);\n\t\tconst options = this.getNodeParameter('options', 0, {});\n\n\t\tlet item: INodeExecutionData;\n\t\tconst returnData: INodeExecutionData[] = [];\n\t\tfor (let itemIndex = 0; itemIndex < items.length; itemIndex++) {\n\t\t\ttry {\n\t\t\t\titem = items[itemIndex];\n\n\t\t\t\tif (mode === 'xmlToJson') {\n\t\t\t\t\tconst parserOptions = Object.assign(\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tmergeAttrs: true,\n\t\t\t\t\t\t\texplicitArray: false,\n\t\t\t\t\t\t},\n\t\t\t\t\t\toptions,\n\t\t\t\t\t);\n\n\t\t\t\t\tconst parser = new Parser(parserOptions);\n\n\t\t\t\t\tif (item.json[dataPropertyName] === undefined) {\n\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t`Item has no JSON property called \"${dataPropertyName}\"`,\n\t\t\t\t\t\t\t{ itemIndex },\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\tconst json = await parser.parseStringPromise(item.json[dataPropertyName] as string);\n\t\t\t\t\treturnData.push({ json: deepCopy(json) });\n\t\t\t\t} else if (mode === 'jsonToxml') {\n\t\t\t\t\tconst builder = new Builder(options);\n\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tjson: {\n\t\t\t\t\t\t\t[dataPropertyName]: builder.buildObject(items[itemIndex].json),\n\t\t\t\t\t\t},\n\t\t\t\t\t\tpairedItem: {\n\t\t\t\t\t\t\titem: itemIndex,\n\t\t\t\t\t\t},\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tthrow new NodeOperationError(this.getNode(), `The operation \"${mode}\" is not known!`, {\n\t\t\t\t\t\titemIndex,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\titems[itemIndex] = {\n\t\t\t\t\t\tjson: {\n\t\t\t\t\t\t\terror: error.message,\n\t\t\t\t\t\t},\n\t\t\t\t\t\tpairedItem: {\n\t\t\t\t\t\t\titem: itemIndex,\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,0BAAkE;AAClE,oBAAgC;AAEzB,MAAM,IAAyB;AAAA,EAA/B;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,MACX,OAAO,CAAC,WAAW;AAAA,MACnB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,UACD;AAAA,UACA,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aACC;AAAA,UACD,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,MAAM,CAAC,WAAW;AAAA,YACnB;AAAA,UACD;AAAA,QACD;AAAA;AAAA;AAAA;AAAA,QAKA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,MAAM,CAAC,WAAW;AAAA,YACnB;AAAA,UACD;AAAA,UACA,SAAS;AAAA,UACT,UAAU;AAAA,UACV,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,MAAM,CAAC,WAAW;AAAA,YACnB;AAAA,UACD;AAAA,UACA,SAAS,CAAC;AAAA,UACV,SAAS;AAAA,YACR;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aACC;AAAA,YACF;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,UACD;AAAA,QACD;AAAA;AAAA;AAAA;AAAA,QAKA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,MAAM,CAAC,WAAW;AAAA,YACnB;AAAA,UACD;AAAA,UACA,SAAS;AAAA,UACT,UAAU;AAAA,UACV,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,MAAM,CAAC,WAAW;AAAA,YACnB;AAAA,UACD;AAAA,UACA,SAAS,CAAC;AAAA,UACV,SAAS;AAAA,YACR;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aACC;AAAA,YACF;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aACC;AAAA,YACF;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aACC;AAAA,YACF;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,aAAa;AAAA,cACb,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,YACd;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAEhC,UAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC5C,UAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,CAAC;AACpE,UAAM,UAAU,KAAK,iBAAiB,WAAW,GAAG,CAAC,CAAC;AAEtD,QAAI;AACJ,UAAM,aAAmC,CAAC;AAC1C,aAAS,YAAY,GAAG,YAAY,MAAM,QAAQ,aAAa;AAC9D,UAAI;AACH,eAAO,MAAM,SAAS;AAEtB,YAAI,SAAS,aAAa;AACzB,gBAAM,gBAAgB,OAAO;AAAA,YAC5B;AAAA,cACC,YAAY;AAAA,cACZ,eAAe;AAAA,YAChB;AAAA,YACA;AAAA,UACD;AAEA,gBAAM,SAAS,IAAI,qBAAO,aAAa;AAEvC,cAAI,KAAK,KAAK,gBAAgB,MAAM,QAAW;AAC9C,kBAAM,IAAI;AAAA,cACT,KAAK,QAAQ;AAAA,cACb,qCAAqC,gBAAgB;AAAA,cACrD,EAAE,UAAU;AAAA,YACb;AAAA,UACD;AAEA,gBAAM,OAAO,MAAM,OAAO,mBAAmB,KAAK,KAAK,gBAAgB,CAAW;AAClF,qBAAW,KAAK,EAAE,UAAM,8BAAS,IAAI,EAAE,CAAC;AAAA,QACzC,WAAW,SAAS,aAAa;AAChC,gBAAM,UAAU,IAAI,sBAAQ,OAAO;AAEnC,qBAAW,KAAK;AAAA,YACf,MAAM;AAAA,cACL,CAAC,gBAAgB,GAAG,QAAQ,YAAY,MAAM,SAAS,EAAE,IAAI;AAAA,YAC9D;AAAA,YACA,YAAY;AAAA,cACX,MAAM;AAAA,YACP;AAAA,UACD,CAAC;AAAA,QACF,OAAO;AACN,gBAAM,IAAI,uCAAmB,KAAK,QAAQ,GAAG,kBAAkB,IAAI,mBAAmB;AAAA,YACrF;AAAA,UACD,CAAC;AAAA,QACF;AAAA,MACD,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,gBAAM,SAAS,IAAI;AAAA,YAClB,MAAM;AAAA,cACL,OAAO,MAAM;AAAA,YACd;AAAA,YACA,YAAY;AAAA,cACX,MAAM;AAAA,YACP;AAAA,UACD;AACA;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAAA,IACD;AAEA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": []}