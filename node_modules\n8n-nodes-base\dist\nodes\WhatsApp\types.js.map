{"version": 3, "sources": ["../../../nodes/WhatsApp/types.ts"], "sourcesContent": ["import type { GenericValue } from 'n8n-workflow';\n\nexport type BaseFacebookResponse<TData> = { data: TData };\nexport type BasePaginatedFacebookResponse<TData> = BaseFacebookResponse<TData> & {\n\tpaging: { cursors: { before?: string; after?: string } };\n};\n\nexport type WhatsAppAppWebhookSubscriptionsResponse = BaseFacebookResponse<\n\tWhatsAppAppWebhookSubscription[]\n>;\n\nexport interface WhatsAppAppWebhookSubscription {\n\tobject: string;\n\tcallback_url: string;\n\tactive: boolean;\n\tfields: WhatsAppAppWebhookSubscriptionField[];\n}\n\nexport interface WhatsAppAppWebhookSubscriptionField {\n\tname: string;\n\tversion: string;\n}\n\nexport interface CreateFacebookAppWebhookSubscription {\n\tobject: string;\n\tcallback_url: string;\n\tfields: string[];\n\tinclude_values: boolean;\n\tverify_token: string;\n}\n\nexport type FacebookPageListResponse = BasePaginatedFacebookResponse<FacebookPage[]>;\nexport type FacebookFormListResponse = BasePaginatedFacebookResponse<FacebookForm[]>;\n\nexport interface FacebookPage {\n\tid: string;\n\tname: string;\n\taccess_token: string;\n\tcategory: string;\n\tcategory_list: FacebookPageCategory[];\n\ttasks: string[];\n}\n\nexport interface FacebookPageCategory {\n\tid: string;\n\tname: string;\n}\n\nexport interface FacebookFormQuestion {\n\tid: string;\n\tkey: string;\n\tlabel: string;\n\ttype: string;\n}\n\nexport interface FacebookForm {\n\tid: string;\n\tname: string;\n\tlocale: string;\n\tstatus: string;\n\tpage: {\n\t\tid: string;\n\t\tname: string;\n\t};\n\tquestions: FacebookFormQuestion[];\n}\n\nexport interface WhatsAppPageEvent {\n\tobject: 'whatsapp_business_account';\n\tentry: WhatsAppEventEntry[];\n}\n\nexport type WhatsAppEventChanges = Array<{\n\tfield: string;\n\tvalue: { statuses?: Array<{ status: string }> };\n}>;\n\nexport interface WhatsAppEventEntry {\n\tid: string;\n\ttime: number;\n\tchanges: WhatsAppEventChanges;\n}\n\nexport interface FacebookFormLeadData {\n\tid: string;\n\tcreated_time: string;\n\tad_id: string;\n\tad_name: string;\n\tadset_id: string;\n\tadset_name: string;\n\tform_id: string;\n\tfield_data: [\n\t\t{\n\t\t\tname: string;\n\t\t\tvalues: GenericValue[];\n\t\t},\n\t];\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}