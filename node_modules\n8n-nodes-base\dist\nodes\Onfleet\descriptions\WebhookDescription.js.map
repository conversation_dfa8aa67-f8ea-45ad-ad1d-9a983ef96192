{"version": 3, "sources": ["../../../../nodes/Onfleet/descriptions/WebhookDescription.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nimport { webhookMapping } from '../WebhookMapping';\n\nexport const webhookOperations: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['webhook'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Create',\n\t\t\t\tvalue: 'create',\n\t\t\t\tdescription: 'Create a new Onfleet webhook',\n\t\t\t\taction: 'Create a webhook',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'delete',\n\t\t\t\tdescription: 'Delete an Onfleet webhook',\n\t\t\t\taction: 'Delete a webhook',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Get many Onfleet webhooks',\n\t\t\t\taction: 'Get many webhooks',\n\t\t\t},\n\t\t],\n\t\tdefault: 'getAll',\n\t},\n];\n\nconst urlField = {\n\tdisplayName: 'URL',\n\tname: 'url',\n\ttype: 'string',\n\tdefault: '',\n\tdescription:\n\t\t'The URL that Onfleet should issue a request against as soon as the trigger condition is met. It must be HTTPS and have a valid certificate.',\n} as INodeProperties;\n\nconst nameField = {\n\tdisplayName: 'Name',\n\tname: 'name',\n\ttype: 'string',\n\tdefault: '',\n\tdescription: 'A name for the webhook for identification',\n} as INodeProperties;\n\nconst triggerField = {\n\tdisplayName: 'Trigger',\n\tname: 'trigger',\n\ttype: 'options',\n\toptions: Object.entries(webhookMapping).map(([_key, value]) => {\n\t\treturn {\n\t\t\tname: value.name,\n\t\t\tvalue: value.key,\n\t\t};\n\t}),\n\tdefault: '',\n\tdescription: 'The number corresponding to the trigger condition on which the webhook should fire',\n} as INodeProperties;\n\nconst thresholdField = {\n\tdisplayName: 'Threshold',\n\tname: 'threshold',\n\ttype: 'number',\n\tdefault: 0,\n\tdescription:\n\t\t'For trigger Task Eta, the time threshold in seconds; for trigger Task Arrival, the distance threshold in meters',\n} as INodeProperties;\n\nexport const webhookFields: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Webhook ID',\n\t\tname: 'id',\n\t\ttype: 'string',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['webhook'],\n\t\t\t\toperation: ['delete'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\trequired: true,\n\t\tdescription: 'The ID of the webhook object for lookup',\n\t},\n\t{\n\t\t...urlField,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['webhook'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\trequired: true,\n\t},\n\t{\n\t\t...nameField,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['webhook'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\trequired: true,\n\t},\n\t{\n\t\t...triggerField,\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['webhook'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\trequired: true,\n\t},\n\t{\n\t\tdisplayName: 'Additional Fields',\n\t\tname: 'additionalFields',\n\t\ttype: 'collection',\n\t\tplaceholder: 'Add Field',\n\t\tdefault: {},\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['webhook'],\n\t\t\t\toperation: ['create'],\n\t\t\t},\n\t\t},\n\t\toptions: [thresholdField],\n\t},\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,4BAA+B;AAExB,MAAM,oBAAuC;AAAA,EACnD;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,SAAS;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,SAAS;AAAA,EACV;AACD;AAEA,MAAM,WAAW;AAAA,EAChB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aACC;AACF;AAEA,MAAM,YAAY;AAAA,EACjB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aAAa;AACd;AAEA,MAAM,eAAe;AAAA,EACpB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS,OAAO,QAAQ,oCAAc,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM;AAC9D,WAAO;AAAA,MACN,MAAM,MAAM;AAAA,MACZ,OAAO,MAAM;AAAA,IACd;AAAA,EACD,CAAC;AAAA,EACD,SAAS;AAAA,EACT,aAAa;AACd;AAEA,MAAM,iBAAiB;AAAA,EACtB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aACC;AACF;AAEO,MAAM,gBAAmC;AAAA,EAC/C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,SAAS;AAAA,QACpB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,GAAG;AAAA,IACH,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,SAAS;AAAA,QACpB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,UAAU;AAAA,EACX;AAAA,EACA;AAAA,IACC,GAAG;AAAA,IACH,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,SAAS;AAAA,QACpB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,UAAU;AAAA,EACX;AAAA,EACA;AAAA,IACC,GAAG;AAAA,IACH,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,SAAS;AAAA,QACpB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,UAAU;AAAA,EACX;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,SAAS;AAAA,QACpB,WAAW,CAAC,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,IACA,SAAS,CAAC,cAAc;AAAA,EACzB;AACD;", "names": []}