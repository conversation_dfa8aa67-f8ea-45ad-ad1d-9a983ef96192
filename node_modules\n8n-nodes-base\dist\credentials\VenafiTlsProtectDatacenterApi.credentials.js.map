{"version": 3, "sources": ["../../credentials/VenafiTlsProtectDatacenterApi.credentials.ts"], "sourcesContent": ["import type {\n\tIAuthenticateGeneric,\n\tICredentialDataDecryptedObject,\n\tICredentialType,\n\tIHttpRequestHelper,\n\tIHttpRequestOptions,\n\tINodeProperties,\n} from 'n8n-workflow';\n\nexport class VenafiTlsProtectDatacenterApi implements ICredentialType {\n\tname = 'venafiTlsProtectDatacenterApi';\n\n\tdisplayName = 'Venafi TLS Protect Datacenter API';\n\n\tdocumentationUrl = 'venafitlsprotectdatacenter';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Domain',\n\t\t\tname: 'domain',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\tplaceholder: 'https://example.com',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Client ID',\n\t\t\tname: 'clientId',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Username',\n\t\t\tname: 'username',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Password',\n\t\t\tname: 'password',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: {\n\t\t\t\tpassword: true,\n\t\t\t},\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Allow Self-Signed Certificates',\n\t\t\tname: 'allowUnauthorizedCerts',\n\t\t\ttype: 'boolean',\n\t\t\tdefault: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Access Token',\n\t\t\tname: 'token',\n\t\t\ttype: 'hidden',\n\n\t\t\ttypeOptions: {\n\t\t\t\texpirable: true,\n\t\t\t},\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Scope',\n\t\t\tname: 'scope',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: 'certificate:manage',\n\t\t},\n\t];\n\n\tasync preAuthentication(this: IHttpRequestHelper, credentials: ICredentialDataDecryptedObject) {\n\t\tconst url = `${credentials.domain}/vedauth/authorize/oauth`;\n\n\t\tconst requestOptions: IHttpRequestOptions = {\n\t\t\turl,\n\t\t\tmethod: 'POST',\n\t\t\tjson: true,\n\t\t\tskipSslCertificateValidation: credentials.allowUnauthorizedCerts as boolean,\n\t\t\tbody: {\n\t\t\t\tclient_id: credentials.clientId,\n\t\t\t\tusername: credentials.username,\n\t\t\t\tpassword: credentials.password,\n\t\t\t\tscope: credentials.scope,\n\t\t\t},\n\t\t};\n\n\t\tconst { access_token } = (await this.helpers.httpRequest(requestOptions)) as {\n\t\t\taccess_token: string;\n\t\t};\n\n\t\treturn { token: access_token };\n\t}\n\n\tauthenticate: IAuthenticateGeneric = {\n\t\ttype: 'generic',\n\t\tproperties: {\n\t\t\theaders: {\n\t\t\t\tAuthorization: '=Bearer {{$credentials.token}}',\n\t\t\t},\n\t\t},\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AASO,MAAM,8BAAyD;AAAA,EAA/D;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QAEN,aAAa;AAAA,UACZ,WAAW;AAAA,QACZ;AAAA,QACA,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,IACD;AAyBA,wBAAqC;AAAA,MACpC,MAAM;AAAA,MACN,YAAY;AAAA,QACX,SAAS;AAAA,UACR,eAAe;AAAA,QAChB;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EA9BA,MAAM,kBAA4C,aAA6C;AAC9F,UAAM,MAAM,GAAG,YAAY,MAAM;AAEjC,UAAM,iBAAsC;AAAA,MAC3C;AAAA,MACA,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,8BAA8B,YAAY;AAAA,MAC1C,MAAM;AAAA,QACL,WAAW,YAAY;AAAA,QACvB,UAAU,YAAY;AAAA,QACtB,UAAU,YAAY;AAAA,QACtB,OAAO,YAAY;AAAA,MACpB;AAAA,IACD;AAEA,UAAM,EAAE,aAAa,IAAK,MAAM,KAAK,QAAQ,YAAY,cAAc;AAIvE,WAAO,EAAE,OAAO,aAAa;AAAA,EAC9B;AAUD;", "names": []}