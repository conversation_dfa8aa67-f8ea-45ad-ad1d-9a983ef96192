{"version": 3, "sources": ["../../../nodes/Zammad/types.ts"], "sourcesContent": ["import type { IDataObject } from 'n8n-workflow';\n\nexport declare namespace Zammad {\n\texport type Resource = 'group' | 'organization' | 'ticket' | 'user';\n\n\texport type AuthMethod = 'basicAuth' | 'tokenAuth';\n\n\texport type Credentials = BasicAuthCredentials | TokenAuthCredentials;\n\n\ttype CredentialsBase = {\n\t\tbaseUrl: string;\n\t\tallowUnauthorizedCerts: boolean;\n\t};\n\n\texport type BasicAuthCredentials = CredentialsBase & {\n\t\tauthType: 'basicAuth';\n\t\tusername: string;\n\t\tpassword: string;\n\t};\n\n\texport type TokenAuthCredentials = CredentialsBase & {\n\t\tauthType: 'tokenAuth';\n\t\taccessToken: string;\n\t};\n\n\texport type UserAdditionalFields = IDataObject & CustomFieldsUi & AddressUi;\n\texport type UserUpdateFields = UserAdditionalFields;\n\texport type UserFilterFields = IDataObject & SortUi;\n\n\texport type Organization = {\n\t\tactive: boolean;\n\t\tid: number;\n\t\tname: string;\n\t};\n\n\texport type Group = Organization;\n\n\texport type GroupUpdateFields = UserUpdateFields;\n\n\texport type User = {\n\t\tid: number;\n\t\tlogin: string;\n\t\tlastname: string;\n\t\temail: string;\n\t\trole_ids: number[];\n\t};\n\n\texport type Field = {\n\t\tid: number;\n\t\tdisplay: string;\n\t\tname: string;\n\t\tobject: string;\n\t\tcreated_by_id: number;\n\t};\n\n\texport type UserField = {\n\t\tdisplay: string;\n\t\tname: string;\n\t};\n\n\texport type CustomFieldsUi = {\n\t\tcustomFieldsUi?: {\n\t\t\tcustomFieldPairs: Array<{ name: string; value: string }>;\n\t\t};\n\t};\n\n\texport type SortUi = {\n\t\tsortUi?: {\n\t\t\tsortDetails: {\n\t\t\t\tsort_by: string;\n\t\t\t\torder_by: string;\n\t\t\t};\n\t\t};\n\t};\n\n\texport type AddressUi = {\n\t\taddressUi?: {\n\t\t\taddressDetails: {\n\t\t\t\tcity: string;\n\t\t\t\tcountry: string;\n\t\t\t\tstreet: string;\n\t\t\t\tzip: string;\n\t\t\t};\n\t\t};\n\t};\n\n\texport type Article = {\n\t\tarticleDetails: {\n\t\t\tvisibility: 'external' | 'internal';\n\t\t\tsubject: string;\n\t\t\tbody: string;\n\t\t\tsender: 'Agent' | 'Customer' | 'System';\n\t\t\ttype: 'chat' | 'email' | 'fax' | 'note' | 'phone' | 'sms';\n\t\t\treply_to: string;\n\t\t};\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}