{"version": 3, "sources": ["../../../../nodes/Telegram/util/triggerUtils.ts"], "sourcesContent": ["import {\n\ttype ICredentialDataDecryptedObject,\n\ttype IDataObject,\n\ttype IWebhookFunctions,\n\ttype IWebhookResponseData,\n} from 'n8n-workflow';\n\nimport { apiRequest, getImageBySize } from '../GenericFunctions';\nimport { type IEvent } from '../IEvent';\n\nexport const downloadFile = async (\n\twebhookFunctions: IWebhookFunctions,\n\tcredentials: ICredentialDataDecryptedObject,\n\tbodyData: IEvent,\n\tadditionalFields: IDataObject,\n): Promise<IWebhookResponseData> => {\n\tlet imageSize = 'large';\n\n\tlet key: 'message' | 'channel_post' = 'message';\n\n\tif (bodyData.channel_post) {\n\t\tkey = 'channel_post';\n\t}\n\n\tif (\n\t\t(bodyData[key]?.photo && Array.isArray(bodyData[key]?.photo)) ||\n\t\tbodyData[key]?.document ||\n\t\tbodyData[key]?.video\n\t) {\n\t\tif (additionalFields.imageSize) {\n\t\t\timageSize = additionalFields.imageSize as string;\n\t\t}\n\n\t\tlet fileId;\n\n\t\tif (bodyData[key]?.photo) {\n\t\t\tlet image = getImageBySize(bodyData[key]?.photo as IDataObject[], imageSize) as IDataObject;\n\n\t\t\t// When the image is sent from the desktop app telegram does not resize the image\n\t\t\t// So return the only image available\n\t\t\t// Basically the Image Size parameter would work just when the images comes from the mobile app\n\t\t\tif (image === undefined) {\n\t\t\t\timage = bodyData[key]!.photo![0];\n\t\t\t}\n\n\t\t\tfileId = image.file_id;\n\t\t} else if (bodyData[key]?.video) {\n\t\t\tfileId = bodyData[key]?.video?.file_id;\n\t\t} else {\n\t\t\tfileId = bodyData[key]?.document?.file_id;\n\t\t}\n\n\t\tconst {\n\t\t\tresult: { file_path },\n\t\t} = await apiRequest.call(webhookFunctions, 'GET', `getFile?file_id=${fileId}`, {});\n\n\t\tconst file = await apiRequest.call(\n\t\t\twebhookFunctions,\n\t\t\t'GET',\n\t\t\t'',\n\t\t\t{},\n\t\t\t{},\n\t\t\t{\n\t\t\t\tjson: false,\n\t\t\t\tencoding: null,\n\t\t\t\turi: `${credentials.baseUrl}/file/bot${credentials.accessToken}/${file_path}`,\n\t\t\t\tresolveWithFullResponse: true,\n\t\t\t},\n\t\t);\n\n\t\tconst data = Buffer.from(file.body as string);\n\n\t\tconst fileName = file_path.split('/').pop();\n\n\t\tconst binaryData = await webhookFunctions.helpers.prepareBinaryData(\n\t\t\tdata as unknown as Buffer,\n\t\t\tfileName as string,\n\t\t);\n\n\t\treturn {\n\t\t\tworkflowData: [\n\t\t\t\t[\n\t\t\t\t\t{\n\t\t\t\t\t\tjson: bodyData as unknown as IDataObject,\n\t\t\t\t\t\tbinary: {\n\t\t\t\t\t\t\tdata: binaryData,\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t],\n\t\t};\n\t}\n\n\treturn {};\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,8BAA2C;AAGpC,MAAM,eAAe,OAC3B,kBACA,aACA,UACA,qBACmC;AACnC,MAAI,YAAY;AAEhB,MAAI,MAAkC;AAEtC,MAAI,SAAS,cAAc;AAC1B,UAAM;AAAA,EACP;AAEA,MACE,SAAS,GAAG,GAAG,SAAS,MAAM,QAAQ,SAAS,GAAG,GAAG,KAAK,KAC3D,SAAS,GAAG,GAAG,YACf,SAAS,GAAG,GAAG,OACd;AACD,QAAI,iBAAiB,WAAW;AAC/B,kBAAY,iBAAiB;AAAA,IAC9B;AAEA,QAAI;AAEJ,QAAI,SAAS,GAAG,GAAG,OAAO;AACzB,UAAI,YAAQ,wCAAe,SAAS,GAAG,GAAG,OAAwB,SAAS;AAK3E,UAAI,UAAU,QAAW;AACxB,gBAAQ,SAAS,GAAG,EAAG,MAAO,CAAC;AAAA,MAChC;AAEA,eAAS,MAAM;AAAA,IAChB,WAAW,SAAS,GAAG,GAAG,OAAO;AAChC,eAAS,SAAS,GAAG,GAAG,OAAO;AAAA,IAChC,OAAO;AACN,eAAS,SAAS,GAAG,GAAG,UAAU;AAAA,IACnC;AAEA,UAAM;AAAA,MACL,QAAQ,EAAE,UAAU;AAAA,IACrB,IAAI,MAAM,mCAAW,KAAK,kBAAkB,OAAO,mBAAmB,MAAM,IAAI,CAAC,CAAC;AAElF,UAAM,OAAO,MAAM,mCAAW;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC;AAAA,MACD,CAAC;AAAA,MACD;AAAA,QACC,MAAM;AAAA,QACN,UAAU;AAAA,QACV,KAAK,GAAG,YAAY,OAAO,YAAY,YAAY,WAAW,IAAI,SAAS;AAAA,QAC3E,yBAAyB;AAAA,MAC1B;AAAA,IACD;AAEA,UAAM,OAAO,OAAO,KAAK,KAAK,IAAc;AAE5C,UAAM,WAAW,UAAU,MAAM,GAAG,EAAE,IAAI;AAE1C,UAAM,aAAa,MAAM,iBAAiB,QAAQ;AAAA,MACjD;AAAA,MACA;AAAA,IACD;AAEA,WAAO;AAAA,MACN,cAAc;AAAA,QACb;AAAA,UACC;AAAA,YACC,MAAM;AAAA,YACN,QAAQ;AAAA,cACP,MAAM;AAAA,YACP;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,SAAO,CAAC;AACT;", "names": []}