{"version": 3, "sources": ["../../../nodes/Wait/Wait.node.ts"], "sourcesContent": ["import type {\n\tIExecuteFunctions,\n\tINodeExecutionData,\n\tINodeTypeDescription,\n\tINodeProperties,\n\tIDisplayOptions,\n\tIWebhookFunctions,\n} from 'n8n-workflow';\nimport {\n\tNodeConnectionTypes,\n\tWAIT_INDEFINITELY,\n\tFORM_TRIGGER_NODE_TYPE,\n\ttryToParseDateTime,\n\tNodeOperationError,\n} from 'n8n-workflow';\n\nimport { updateDisplayOptions } from '../../utils/utilities';\nimport {\n\tformDescription,\n\tformFields,\n\trespondWithOptions,\n\tformRespondMode,\n\tformTitle,\n\tappendAttributionToForm,\n} from '../Form/common.descriptions';\nimport { formWebhook } from '../Form/utils';\nimport {\n\tauthenticationProperty,\n\tcredentialsProperty,\n\tdefaultWebhookDescription,\n\thttpMethodsProperty,\n\toptionsProperty,\n\tresponseBinaryPropertyNameProperty,\n\tresponseCodeProperty,\n\tresponseDataProperty,\n\tresponseModeProperty,\n} from '../Webhook/description';\nimport { Webhook } from '../Webhook/Webhook.node';\n\nconst toWaitAmount: INodeProperties = {\n\tdisplayName: 'Wait Amount',\n\tname: 'amount',\n\ttype: 'number',\n\ttypeOptions: {\n\t\tminValue: 0,\n\t\tnumberPrecision: 2,\n\t},\n\tdefault: 1,\n\tdescription: 'The time to wait',\n};\n\nconst unitSelector: INodeProperties = {\n\tdisplayName: 'Wait Unit',\n\tname: 'unit',\n\ttype: 'options',\n\toptions: [\n\t\t{\n\t\t\tname: 'Seconds',\n\t\t\tvalue: 'seconds',\n\t\t},\n\t\t{\n\t\t\tname: 'Minutes',\n\t\t\tvalue: 'minutes',\n\t\t},\n\t\t{\n\t\t\tname: 'Hours',\n\t\t\tvalue: 'hours',\n\t\t},\n\t\t{\n\t\t\tname: 'Days',\n\t\t\tvalue: 'days',\n\t\t},\n\t],\n\tdefault: 'hours',\n\tdescription: 'The time unit of the Wait Amount value',\n};\n\nconst waitTimeProperties: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Limit Wait Time',\n\t\tname: 'limitWaitTime',\n\t\ttype: 'boolean',\n\t\tdefault: false,\n\t\tdescription:\n\t\t\t'Whether to limit the time this node should wait for a user response before execution resumes',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresume: ['webhook', 'form'],\n\t\t\t},\n\t\t},\n\t},\n\t{\n\t\tdisplayName: 'Limit Type',\n\t\tname: 'limitType',\n\t\ttype: 'options',\n\t\tdefault: 'afterTimeInterval',\n\t\tdescription:\n\t\t\t'Sets the condition for the execution to resume. Can be a specified date or after some time.',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tlimitWaitTime: [true],\n\t\t\t\tresume: ['webhook', 'form'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'After Time Interval',\n\t\t\t\tdescription: 'Waits for a certain amount of time',\n\t\t\t\tvalue: 'afterTimeInterval',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'At Specified Time',\n\t\t\t\tdescription: 'Waits until the set date and time to continue',\n\t\t\t\tvalue: 'atSpecifiedTime',\n\t\t\t},\n\t\t],\n\t},\n\t{\n\t\tdisplayName: 'Amount',\n\t\tname: 'resumeAmount',\n\t\ttype: 'number',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tlimitType: ['afterTimeInterval'],\n\t\t\t\tlimitWaitTime: [true],\n\t\t\t\tresume: ['webhook', 'form'],\n\t\t\t},\n\t\t},\n\t\ttypeOptions: {\n\t\t\tminValue: 0,\n\t\t\tnumberPrecision: 2,\n\t\t},\n\t\tdefault: 1,\n\t\tdescription: 'The time to wait',\n\t},\n\t{\n\t\tdisplayName: 'Unit',\n\t\tname: 'resumeUnit',\n\t\ttype: 'options',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tlimitType: ['afterTimeInterval'],\n\t\t\t\tlimitWaitTime: [true],\n\t\t\t\tresume: ['webhook', 'form'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Seconds',\n\t\t\t\tvalue: 'seconds',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Minutes',\n\t\t\t\tvalue: 'minutes',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Hours',\n\t\t\t\tvalue: 'hours',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Days',\n\t\t\t\tvalue: 'days',\n\t\t\t},\n\t\t],\n\t\tdefault: 'hours',\n\t\tdescription: 'Unit of the interval value',\n\t},\n\t{\n\t\tdisplayName: 'Max Date and Time',\n\t\tname: 'maxDateAndTime',\n\t\ttype: 'dateTime',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tlimitType: ['atSpecifiedTime'],\n\t\t\t\tlimitWaitTime: [true],\n\t\t\t\tresume: ['webhook', 'form'],\n\t\t\t},\n\t\t},\n\t\tdefault: '',\n\t\tdescription: 'Continue execution after the specified date and time',\n\t},\n];\n\nconst webhookSuffix: INodeProperties = {\n\tdisplayName: 'Webhook Suffix',\n\tname: 'webhookSuffix',\n\ttype: 'string',\n\tdefault: '',\n\tplaceholder: 'webhook',\n\tnoDataExpression: true,\n\tdescription:\n\t\t'This suffix path will be appended to the restart URL. Helpful when using multiple wait nodes.',\n};\n\nconst displayOnWebhook: IDisplayOptions = {\n\tshow: {\n\t\tresume: ['webhook'],\n\t},\n};\n\nconst displayOnFormSubmission = {\n\tshow: {\n\t\tresume: ['form'],\n\t},\n};\n\nconst onFormSubmitProperties = updateDisplayOptions(displayOnFormSubmission, [\n\tformTitle,\n\tformDescription,\n\tformFields,\n\tformRespondMode,\n]);\n\nconst onWebhookCallProperties = updateDisplayOptions(displayOnWebhook, [\n\t{\n\t\t...httpMethodsProperty,\n\t\tdescription: 'The HTTP method of the Webhook call',\n\t},\n\tresponseCodeProperty,\n\tresponseModeProperty,\n\tresponseDataProperty,\n\tresponseBinaryPropertyNameProperty,\n]);\n\nconst webhookPath = '={{$parameter[\"options\"][\"webhookSuffix\"] || \"\"}}';\n\nexport class Wait extends Webhook {\n\tauthPropertyName = 'incomingAuthentication';\n\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Wait',\n\t\tname: 'wait',\n\t\ticon: 'fa:pause-circle',\n\t\ticonColor: 'crimson',\n\t\tgroup: ['organization'],\n\t\tversion: [1, 1.1],\n\t\tdescription: 'Wait before continue with execution',\n\t\tdefaults: {\n\t\t\tname: 'Wait',\n\t\t\tcolor: '#804050',\n\t\t},\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: credentialsProperty(this.authPropertyName),\n\t\twebhooks: [\n\t\t\t{\n\t\t\t\t...defaultWebhookDescription,\n\t\t\t\tresponseData: '={{$parameter[\"responseData\"]}}',\n\t\t\t\tpath: webhookPath,\n\t\t\t\trestartWebhook: true,\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'default',\n\t\t\t\thttpMethod: 'GET',\n\t\t\t\tresponseMode: 'onReceived',\n\t\t\t\tpath: webhookPath,\n\t\t\t\trestartWebhook: true,\n\t\t\t\tisFullPath: true,\n\t\t\t\tnodeType: 'form',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'default',\n\t\t\t\thttpMethod: 'POST',\n\t\t\t\tresponseMode: '={{$parameter[\"responseMode\"]}}',\n\t\t\t\tresponseData: '={{$parameter[\"responseMode\"] === \"lastNode\" ? \"noData\" : undefined}}',\n\t\t\t\tpath: webhookPath,\n\t\t\t\trestartWebhook: true,\n\t\t\t\tisFullPath: true,\n\t\t\t\tnodeType: 'form',\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resume',\n\t\t\t\tname: 'resume',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'After Time Interval',\n\t\t\t\t\t\tvalue: 'timeInterval',\n\t\t\t\t\t\tdescription: 'Waits for a certain amount of time',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'At Specified Time',\n\t\t\t\t\t\tvalue: 'specificTime',\n\t\t\t\t\t\tdescription: 'Waits until a specific date and time to continue',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'On Webhook Call',\n\t\t\t\t\t\tvalue: 'webhook',\n\t\t\t\t\t\tdescription: 'Waits for a webhook call before continuing',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'On Form Submitted',\n\t\t\t\t\t\tvalue: 'form',\n\t\t\t\t\t\tdescription: 'Waits for a form submission before continuing',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'timeInterval',\n\t\t\t\tdescription: 'Determines the waiting mode to use before the workflow continues',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Authentication',\n\t\t\t\tname: 'incomingAuthentication',\n\t\t\t\ttype: 'options',\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Basic Auth',\n\t\t\t\t\t\tvalue: 'basicAuth',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'None',\n\t\t\t\t\t\tvalue: 'none',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'none',\n\t\t\t\tdescription:\n\t\t\t\t\t'If and how incoming resume-webhook-requests to $execution.resumeFormUrl should be authenticated for additional security',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresume: ['form'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\t...authenticationProperty(this.authPropertyName),\n\t\t\t\tdescription:\n\t\t\t\t\t'If and how incoming resume-webhook-requests to $execution.resumeUrl should be authenticated for additional security',\n\t\t\t\tdisplayOptions: displayOnWebhook,\n\t\t\t},\n\n\t\t\t// ----------------------------------\n\t\t\t//         resume:specificTime\n\t\t\t// ----------------------------------\n\t\t\t{\n\t\t\t\tdisplayName: 'Date and Time',\n\t\t\t\tname: 'dateTime',\n\t\t\t\ttype: 'dateTime',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresume: ['specificTime'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tdefault: '',\n\t\t\t\tdescription: 'The date and time to wait for before continuing',\n\t\t\t\trequired: true,\n\t\t\t},\n\n\t\t\t// ----------------------------------\n\t\t\t//         resume:timeInterval\n\t\t\t// ----------------------------------\n\t\t\t{\n\t\t\t\t...toWaitAmount,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresume: ['timeInterval'],\n\t\t\t\t\t\t'@version': [1],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\t...toWaitAmount,\n\t\t\t\tdefault: 5,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresume: ['timeInterval'],\n\t\t\t\t\t},\n\t\t\t\t\thide: {\n\t\t\t\t\t\t'@version': [1],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\t...unitSelector,\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresume: ['timeInterval'],\n\t\t\t\t\t\t'@version': [1],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\t...unitSelector,\n\t\t\t\tdefault: 'seconds',\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresume: ['timeInterval'],\n\t\t\t\t\t},\n\t\t\t\t\thide: {\n\t\t\t\t\t\t'@version': [1],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\n\t\t\t// ----------------------------------\n\t\t\t//         resume:webhook & form\n\t\t\t// ----------------------------------\n\t\t\t{\n\t\t\t\tdisplayName:\n\t\t\t\t\t'The webhook URL will be generated at run time. It can be referenced with the <strong>$execution.resumeUrl</strong> variable. Send it somewhere before getting to this node. <a href=\"https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.wait/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.wait\" target=\"_blank\">More info</a>',\n\t\t\t\tname: 'webhookNotice',\n\t\t\t\ttype: 'notice',\n\t\t\t\tdisplayOptions: displayOnWebhook,\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName:\n\t\t\t\t\t'The form url will be generated at run time. It can be referenced with the <strong>$execution.resumeFormUrl</strong> variable. Send it somewhere before getting to this node. <a href=\"https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.wait/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.wait\" target=\"_blank\">More info</a>',\n\t\t\t\tname: 'formNotice',\n\t\t\t\ttype: 'notice',\n\t\t\t\tdisplayOptions: displayOnFormSubmission,\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\t...onFormSubmitProperties,\n\t\t\t...onWebhookCallProperties,\n\t\t\t...waitTimeProperties,\n\t\t\t{\n\t\t\t\t...optionsProperty,\n\t\t\t\tdisplayOptions: displayOnWebhook,\n\t\t\t\toptions: [...(optionsProperty.options as INodeProperties[]), webhookSuffix],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Options',\n\t\t\t\tname: 'options',\n\t\t\t\ttype: 'collection',\n\t\t\t\tplaceholder: 'Add option',\n\t\t\t\tdefault: {},\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresume: ['form'],\n\t\t\t\t\t},\n\t\t\t\t\thide: {\n\t\t\t\t\t\tresponseMode: ['responseNode'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\toptions: [appendAttributionToForm, respondWithOptions, webhookSuffix],\n\t\t\t},\n\t\t\t{\n\t\t\t\tdisplayName: 'Options',\n\t\t\t\tname: 'options',\n\t\t\t\ttype: 'collection',\n\t\t\t\tplaceholder: 'Add option',\n\t\t\t\tdefault: {},\n\t\t\t\tdisplayOptions: {\n\t\t\t\t\tshow: {\n\t\t\t\t\t\tresume: ['form'],\n\t\t\t\t\t},\n\t\t\t\t\thide: {\n\t\t\t\t\t\tresponseMode: ['onReceived', 'lastNode'],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\toptions: [appendAttributionToForm, webhookSuffix],\n\t\t\t},\n\t\t],\n\t};\n\n\tasync webhook(context: IWebhookFunctions) {\n\t\tconst resume = context.getNodeParameter('resume', 0) as string;\n\t\tif (resume === 'form') return await formWebhook(context, this.authPropertyName);\n\t\treturn await super.webhook(context);\n\t}\n\n\tasync execute(context: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst resume = context.getNodeParameter('resume', 0) as string;\n\n\t\tif (['webhook', 'form'].includes(resume)) {\n\t\t\tlet hasFormTrigger = false;\n\n\t\t\tif (resume === 'form') {\n\t\t\t\tconst parentNodes = context.getParentNodes(context.getNode().name);\n\t\t\t\thasFormTrigger = parentNodes.some((node) => node.type === FORM_TRIGGER_NODE_TYPE);\n\t\t\t}\n\n\t\t\tconst returnData = await this.configureAndPutToWait(context);\n\n\t\t\tif (resume === 'form' && hasFormTrigger) {\n\t\t\t\tcontext.sendResponse({\n\t\t\t\t\theaders: {\n\t\t\t\t\t\tlocation: context.evaluateExpression('{{ $execution.resumeFormUrl }}', 0),\n\t\t\t\t\t},\n\t\t\t\t\tstatusCode: 307,\n\t\t\t\t});\n\t\t\t}\n\n\t\t\treturn returnData;\n\t\t}\n\n\t\tlet waitTill: Date;\n\t\tif (resume === 'timeInterval') {\n\t\t\tconst unit = context.getNodeParameter('unit', 0) as string;\n\n\t\t\tlet waitAmount = context.getNodeParameter('amount', 0) as number;\n\t\t\tif (unit === 'minutes') {\n\t\t\t\twaitAmount *= 60;\n\t\t\t}\n\t\t\tif (unit === 'hours') {\n\t\t\t\twaitAmount *= 60 * 60;\n\t\t\t}\n\t\t\tif (unit === 'days') {\n\t\t\t\twaitAmount *= 60 * 60 * 24;\n\t\t\t}\n\n\t\t\twaitAmount *= 1000;\n\n\t\t\t// Timezone does not change relative dates, since they are just\n\t\t\t// a number of seconds added to the current timestamp\n\t\t\twaitTill = new Date(new Date().getTime() + waitAmount);\n\t\t} else {\n\t\t\ttry {\n\t\t\t\tconst dateTimeStrRaw = context.getNodeParameter('dateTime', 0);\n\t\t\t\tconst parsedDateTime = tryToParseDateTime(dateTimeStrRaw, context.getTimezone());\n\n\t\t\t\twaitTill = parsedDateTime.toUTC().toJSDate();\n\t\t\t} catch (e) {\n\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\tcontext.getNode(),\n\t\t\t\t\t'[Wait node] Cannot put execution to wait because `dateTime` parameter is not a valid date. Please pick a specific date and time to wait until.',\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tconst waitValue = Math.max(waitTill.getTime() - new Date().getTime(), 0);\n\n\t\tif (waitValue < 65000) {\n\t\t\t// If wait time is shorter than 65 seconds leave execution active because\n\t\t\t// we just check the database every 60 seconds.\n\t\t\treturn await new Promise((resolve) => {\n\t\t\t\tconst timer = setTimeout(() => resolve([context.getInputData()]), waitValue);\n\t\t\t\tcontext.onExecutionCancellation(() => clearTimeout(timer));\n\t\t\t});\n\t\t}\n\n\t\t// If longer than 65 seconds put execution to wait\n\t\treturn await this.putToWait(context, waitTill);\n\t}\n\n\tprivate async configureAndPutToWait(context: IExecuteFunctions) {\n\t\tlet waitTill = WAIT_INDEFINITELY;\n\t\tconst limitWaitTime = context.getNodeParameter('limitWaitTime', 0);\n\n\t\tif (limitWaitTime === true) {\n\t\t\tconst limitType = context.getNodeParameter('limitType', 0);\n\n\t\t\tif (limitType === 'afterTimeInterval') {\n\t\t\t\tlet waitAmount = context.getNodeParameter('resumeAmount', 0) as number;\n\t\t\t\tconst resumeUnit = context.getNodeParameter('resumeUnit', 0);\n\n\t\t\t\tif (resumeUnit === 'minutes') {\n\t\t\t\t\twaitAmount *= 60;\n\t\t\t\t}\n\t\t\t\tif (resumeUnit === 'hours') {\n\t\t\t\t\twaitAmount *= 60 * 60;\n\t\t\t\t}\n\t\t\t\tif (resumeUnit === 'days') {\n\t\t\t\t\twaitAmount *= 60 * 60 * 24;\n\t\t\t\t}\n\n\t\t\t\twaitAmount *= 1000;\n\t\t\t\twaitTill = new Date(new Date().getTime() + waitAmount);\n\t\t\t} else {\n\t\t\t\twaitTill = new Date(context.getNodeParameter('maxDateAndTime', 0) as string);\n\t\t\t}\n\t\t}\n\n\t\treturn await this.putToWait(context, waitTill);\n\t}\n\n\tprivate async putToWait(context: IExecuteFunctions, waitTill: Date) {\n\t\tawait context.putExecutionToWait(waitTill);\n\t\treturn [context.getInputData()];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,0BAMO;AAEP,uBAAqC;AACrC,oBAOO;AACP,mBAA4B;AAC5B,yBAUO;AACP,qBAAwB;AAExB,MAAM,eAAgC;AAAA,EACrC,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,aAAa;AAAA,IACZ,UAAU;AAAA,IACV,iBAAiB;AAAA,EAClB;AAAA,EACA,SAAS;AAAA,EACT,aAAa;AACd;AAEA,MAAM,eAAgC;AAAA,EACrC,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,IACR;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,EACD;AAAA,EACA,SAAS;AAAA,EACT,aAAa;AACd;AAEA,MAAM,qBAAwC;AAAA,EAC7C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aACC;AAAA,IACD,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,QAAQ,CAAC,WAAW,MAAM;AAAA,MAC3B;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aACC;AAAA,IACD,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,eAAe,CAAC,IAAI;AAAA,QACpB,QAAQ,CAAC,WAAW,MAAM;AAAA,MAC3B;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,OAAO;AAAA,MACR;AAAA,IACD;AAAA,EACD;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,mBAAmB;AAAA,QAC/B,eAAe,CAAC,IAAI;AAAA,QACpB,QAAQ,CAAC,WAAW,MAAM;AAAA,MAC3B;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,UAAU;AAAA,MACV,iBAAiB;AAAA,IAClB;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,mBAAmB;AAAA,QAC/B,eAAe,CAAC,IAAI;AAAA,QACpB,QAAQ,CAAC,WAAW,MAAM;AAAA,MAC3B;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,WAAW,CAAC,iBAAiB;AAAA,QAC7B,eAAe,CAAC,IAAI;AAAA,QACpB,QAAQ,CAAC,WAAW,MAAM;AAAA,MAC3B;AAAA,IACD;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AACD;AAEA,MAAM,gBAAiC;AAAA,EACtC,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,aACC;AACF;AAEA,MAAM,mBAAoC;AAAA,EACzC,MAAM;AAAA,IACL,QAAQ,CAAC,SAAS;AAAA,EACnB;AACD;AAEA,MAAM,0BAA0B;AAAA,EAC/B,MAAM;AAAA,IACL,QAAQ,CAAC,MAAM;AAAA,EAChB;AACD;AAEA,MAAM,6BAAyB,uCAAqB,yBAAyB;AAAA,EAC5E;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAED,MAAM,8BAA0B,uCAAqB,kBAAkB;AAAA,EACtE;AAAA,IACC,GAAG;AAAA,IACH,aAAa;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAED,MAAM,cAAc;AAEb,MAAM,aAAa,uBAAQ;AAAA,EAA3B;AAAA;AACN,4BAAmB;AAEnB,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,MACX,OAAO,CAAC,cAAc;AAAA,MACtB,SAAS,CAAC,GAAG,GAAG;AAAA,MAChB,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACA,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,iBAAa,wCAAoB,KAAK,gBAAgB;AAAA,MACtD,UAAU;AAAA,QACT;AAAA,UACC,GAAG;AAAA,UACH,cAAc;AAAA,UACd,MAAM;AAAA,UACN,gBAAgB;AAAA,QACjB;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,MAAM;AAAA,UACN,gBAAgB;AAAA,UAChB,YAAY;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,QACA;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,cAAc;AAAA,UACd,MAAM;AAAA,UACN,gBAAgB;AAAA,UAChB,YAAY;AAAA,UACZ,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACd;AAAA,UACD;AAAA,UACA,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,UACT,aACC;AAAA,UACD,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,QAAQ,CAAC,MAAM;AAAA,YAChB;AAAA,UACD;AAAA,QACD;AAAA,QACA;AAAA,UACC,OAAG,2CAAuB,KAAK,gBAAgB;AAAA,UAC/C,aACC;AAAA,UACD,gBAAgB;AAAA,QACjB;AAAA;AAAA;AAAA;AAAA,QAKA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,QAAQ,CAAC,cAAc;AAAA,YACxB;AAAA,UACD;AAAA,UACA,SAAS;AAAA,UACT,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA;AAAA;AAAA;AAAA,QAKA;AAAA,UACC,GAAG;AAAA,UACH,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,QAAQ,CAAC,cAAc;AAAA,cACvB,YAAY,CAAC,CAAC;AAAA,YACf;AAAA,UACD;AAAA,QACD;AAAA,QACA;AAAA,UACC,GAAG;AAAA,UACH,SAAS;AAAA,UACT,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,QAAQ,CAAC,cAAc;AAAA,YACxB;AAAA,YACA,MAAM;AAAA,cACL,YAAY,CAAC,CAAC;AAAA,YACf;AAAA,UACD;AAAA,QACD;AAAA,QACA;AAAA,UACC,GAAG;AAAA,UACH,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,QAAQ,CAAC,cAAc;AAAA,cACvB,YAAY,CAAC,CAAC;AAAA,YACf;AAAA,UACD;AAAA,QACD;AAAA,QACA;AAAA,UACC,GAAG;AAAA,UACH,SAAS;AAAA,UACT,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,QAAQ,CAAC,cAAc;AAAA,YACxB;AAAA,YACA,MAAM;AAAA,cACL,YAAY,CAAC,CAAC;AAAA,YACf;AAAA,UACD;AAAA,QACD;AAAA;AAAA;AAAA;AAAA,QAKA;AAAA,UACC,aACC;AAAA,UACD,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB;AAAA,UAChB,SAAS;AAAA,QACV;AAAA,QACA;AAAA,UACC,aACC;AAAA,UACD,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB;AAAA,UAChB,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH;AAAA,UACC,GAAG;AAAA,UACH,gBAAgB;AAAA,UAChB,SAAS,CAAC,GAAI,mCAAgB,SAA+B,aAAa;AAAA,QAC3E;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,SAAS,CAAC;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,QAAQ,CAAC,MAAM;AAAA,YAChB;AAAA,YACA,MAAM;AAAA,cACL,cAAc,CAAC,cAAc;AAAA,YAC9B;AAAA,UACD;AAAA,UACA,SAAS,CAAC,uCAAyB,kCAAoB,aAAa;AAAA,QACrE;AAAA,QACA;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,SAAS,CAAC;AAAA,UACV,gBAAgB;AAAA,YACf,MAAM;AAAA,cACL,QAAQ,CAAC,MAAM;AAAA,YAChB;AAAA,YACA,MAAM;AAAA,cACL,cAAc,CAAC,cAAc,UAAU;AAAA,YACxC;AAAA,UACD;AAAA,UACA,SAAS,CAAC,uCAAyB,aAAa;AAAA,QACjD;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,QAAQ,SAA4B;AACzC,UAAM,SAAS,QAAQ,iBAAiB,UAAU,CAAC;AACnD,QAAI,WAAW,OAAQ,QAAO,UAAM,0BAAY,SAAS,KAAK,gBAAgB;AAC9E,WAAO,MAAM,MAAM,QAAQ,OAAO;AAAA,EACnC;AAAA,EAEA,MAAM,QAAQ,SAA6D;AAC1E,UAAM,SAAS,QAAQ,iBAAiB,UAAU,CAAC;AAEnD,QAAI,CAAC,WAAW,MAAM,EAAE,SAAS,MAAM,GAAG;AACzC,UAAI,iBAAiB;AAErB,UAAI,WAAW,QAAQ;AACtB,cAAM,cAAc,QAAQ,eAAe,QAAQ,QAAQ,EAAE,IAAI;AACjE,yBAAiB,YAAY,KAAK,CAAC,SAAS,KAAK,SAAS,0CAAsB;AAAA,MACjF;AAEA,YAAM,aAAa,MAAM,KAAK,sBAAsB,OAAO;AAE3D,UAAI,WAAW,UAAU,gBAAgB;AACxC,gBAAQ,aAAa;AAAA,UACpB,SAAS;AAAA,YACR,UAAU,QAAQ,mBAAmB,kCAAkC,CAAC;AAAA,UACzE;AAAA,UACA,YAAY;AAAA,QACb,CAAC;AAAA,MACF;AAEA,aAAO;AAAA,IACR;AAEA,QAAI;AACJ,QAAI,WAAW,gBAAgB;AAC9B,YAAM,OAAO,QAAQ,iBAAiB,QAAQ,CAAC;AAE/C,UAAI,aAAa,QAAQ,iBAAiB,UAAU,CAAC;AACrD,UAAI,SAAS,WAAW;AACvB,sBAAc;AAAA,MACf;AACA,UAAI,SAAS,SAAS;AACrB,sBAAc,KAAK;AAAA,MACpB;AACA,UAAI,SAAS,QAAQ;AACpB,sBAAc,KAAK,KAAK;AAAA,MACzB;AAEA,oBAAc;AAId,iBAAW,IAAI,MAAK,oBAAI,KAAK,GAAE,QAAQ,IAAI,UAAU;AAAA,IACtD,OAAO;AACN,UAAI;AACH,cAAM,iBAAiB,QAAQ,iBAAiB,YAAY,CAAC;AAC7D,cAAM,qBAAiB,wCAAmB,gBAAgB,QAAQ,YAAY,CAAC;AAE/E,mBAAW,eAAe,MAAM,EAAE,SAAS;AAAA,MAC5C,SAAS,GAAG;AACX,cAAM,IAAI;AAAA,UACT,QAAQ,QAAQ;AAAA,UAChB;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,UAAM,YAAY,KAAK,IAAI,SAAS,QAAQ,KAAI,oBAAI,KAAK,GAAE,QAAQ,GAAG,CAAC;AAEvE,QAAI,YAAY,MAAO;AAGtB,aAAO,MAAM,IAAI,QAAQ,CAAC,YAAY;AACrC,cAAM,QAAQ,WAAW,MAAM,QAAQ,CAAC,QAAQ,aAAa,CAAC,CAAC,GAAG,SAAS;AAC3E,gBAAQ,wBAAwB,MAAM,aAAa,KAAK,CAAC;AAAA,MAC1D,CAAC;AAAA,IACF;AAGA,WAAO,MAAM,KAAK,UAAU,SAAS,QAAQ;AAAA,EAC9C;AAAA,EAEA,MAAc,sBAAsB,SAA4B;AAC/D,QAAI,WAAW;AACf,UAAM,gBAAgB,QAAQ,iBAAiB,iBAAiB,CAAC;AAEjE,QAAI,kBAAkB,MAAM;AAC3B,YAAM,YAAY,QAAQ,iBAAiB,aAAa,CAAC;AAEzD,UAAI,cAAc,qBAAqB;AACtC,YAAI,aAAa,QAAQ,iBAAiB,gBAAgB,CAAC;AAC3D,cAAM,aAAa,QAAQ,iBAAiB,cAAc,CAAC;AAE3D,YAAI,eAAe,WAAW;AAC7B,wBAAc;AAAA,QACf;AACA,YAAI,eAAe,SAAS;AAC3B,wBAAc,KAAK;AAAA,QACpB;AACA,YAAI,eAAe,QAAQ;AAC1B,wBAAc,KAAK,KAAK;AAAA,QACzB;AAEA,sBAAc;AACd,mBAAW,IAAI,MAAK,oBAAI,KAAK,GAAE,QAAQ,IAAI,UAAU;AAAA,MACtD,OAAO;AACN,mBAAW,IAAI,KAAK,QAAQ,iBAAiB,kBAAkB,CAAC,CAAW;AAAA,MAC5E;AAAA,IACD;AAEA,WAAO,MAAM,KAAK,UAAU,SAAS,QAAQ;AAAA,EAC9C;AAAA,EAEA,MAAc,UAAU,SAA4B,UAAgB;AACnE,UAAM,QAAQ,mBAAmB,QAAQ;AACzC,WAAO,CAAC,QAAQ,aAAa,CAAC;AAAA,EAC/B;AACD;", "names": []}