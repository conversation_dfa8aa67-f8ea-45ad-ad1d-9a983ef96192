{"version": 3, "sources": ["../../../../nodes/Google/YouTube/YouTube.node.ts"], "sourcesContent": ["import type {\n\tIDataObject,\n\tIExecuteFunctions,\n\tILoadOptionsFunctions,\n\tINodeExecutionData,\n\tINodePropertyOptions,\n\tINodeType,\n\tINodeTypeDescription,\n} from 'n8n-workflow';\nimport { NodeConnectionTypes, BINARY_ENCODING, NodeOperationError } from 'n8n-workflow';\nimport { Readable } from 'stream';\n\nimport { isoCountryCodes } from '@utils/ISOCountryCodes';\n\nimport { channelFields, channelOperations } from './ChannelDescription';\nimport { googleApiRequest, googleApiRequestAllItems } from './GenericFunctions';\nimport { playlistFields, playlistOperations } from './PlaylistDescription';\nimport { playlistItemFields, playlistItemOperations } from './PlaylistItemDescription';\nimport { videoCategoryFields, videoCategoryOperations } from './VideoCategoryDescription';\nimport { videoFields, videoOperations } from './VideoDescription';\nimport { validateAndSetDate } from '../GenericFunctions';\n\nconst UPLOAD_CHUNK_SIZE = 1024 * 1024;\n\nexport class YouTube implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'YouTube',\n\t\tname: 'youTube',\n\t\t// eslint-disable-next-line n8n-nodes-base/node-class-description-icon-not-svg\n\t\ticon: 'file:youTube.png',\n\t\tgroup: ['input'],\n\t\tversion: 1,\n\t\tsubtitle: '={{$parameter[\"operation\"] + \": \" + $parameter[\"resource\"]}}',\n\t\tdescription: 'Consume YouTube API',\n\t\tdefaults: {\n\t\t\tname: 'YouTube',\n\t\t},\n\t\tusableAsTool: true,\n\t\tinputs: [NodeConnectionTypes.Main],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'youTubeOAuth2Api',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\toptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Channel',\n\t\t\t\t\t\tvalue: 'channel',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Playlist',\n\t\t\t\t\t\tvalue: 'playlist',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Playlist Item',\n\t\t\t\t\t\tvalue: 'playlistItem',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Video',\n\t\t\t\t\t\tvalue: 'video',\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: 'Video Category',\n\t\t\t\t\t\tvalue: 'videoCategory',\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tdefault: 'channel',\n\t\t\t},\n\t\t\t...channelOperations,\n\t\t\t...channelFields,\n\n\t\t\t...playlistOperations,\n\t\t\t...playlistFields,\n\n\t\t\t...playlistItemOperations,\n\t\t\t...playlistItemFields,\n\n\t\t\t...videoOperations,\n\t\t\t...videoFields,\n\n\t\t\t...videoCategoryOperations,\n\t\t\t...videoCategoryFields,\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\t// Get all the languages to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getLanguages(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst languages = await googleApiRequestAllItems.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'items',\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/youtube/v3/i18nLanguages',\n\t\t\t\t);\n\t\t\t\tfor (const language of languages) {\n\t\t\t\t\tconst languageName = language.id.toUpperCase();\n\t\t\t\t\tconst languageId = language.id;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: languageName,\n\t\t\t\t\t\tvalue: languageId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the countries codes to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getCountriesCodes(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tfor (const countryCode of isoCountryCodes) {\n\t\t\t\t\tconst countryCodeName = `${countryCode.name} - ${countryCode.alpha2}`;\n\t\t\t\t\tconst countryCodeId = countryCode.alpha2;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: countryCodeName,\n\t\t\t\t\t\tvalue: countryCodeId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the video categories to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getVideoCategories(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst countryCode = this.getCurrentNodeParameter('regionCode') as string;\n\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst qs: IDataObject = {};\n\t\t\t\tqs.regionCode = countryCode;\n\t\t\t\tqs.part = 'snippet';\n\t\t\t\tconst categories = await googleApiRequestAllItems.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'items',\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/youtube/v3/videoCategories',\n\t\t\t\t\t{},\n\t\t\t\t\tqs,\n\t\t\t\t);\n\t\t\t\tfor (const category of categories) {\n\t\t\t\t\tconst categoryName = category.snippet.title;\n\t\t\t\t\tconst categoryId = category.id;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: categoryName,\n\t\t\t\t\t\tvalue: categoryId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t\t// Get all the playlists to display them to user so that they can\n\t\t\t// select them easily\n\t\t\tasync getPlaylists(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst returnData: INodePropertyOptions[] = [];\n\t\t\t\tconst qs: IDataObject = {};\n\t\t\t\tqs.part = 'snippet';\n\t\t\t\tqs.mine = true;\n\t\t\t\tconst playlists = await googleApiRequestAllItems.call(\n\t\t\t\t\tthis,\n\t\t\t\t\t'items',\n\t\t\t\t\t'GET',\n\t\t\t\t\t'/youtube/v3/playlists',\n\t\t\t\t\t{},\n\t\t\t\t\tqs,\n\t\t\t\t);\n\t\t\t\tfor (const playlist of playlists) {\n\t\t\t\t\tconst playlistName = playlist.snippet.title;\n\t\t\t\t\tconst playlistId = playlist.id;\n\t\t\t\t\treturnData.push({\n\t\t\t\t\t\tname: playlistName,\n\t\t\t\t\t\tvalue: playlistId,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn returnData;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {\n\t\tconst items = this.getInputData();\n\t\tconst returnData: INodeExecutionData[] = [];\n\t\tconst length = items.length;\n\t\tconst qs: IDataObject = {};\n\t\tlet responseData;\n\t\tconst resource = this.getNodeParameter('resource', 0);\n\t\tconst operation = this.getNodeParameter('operation', 0);\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\ttry {\n\t\t\t\tif (resource === 'channel') {\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/channels/list\n\t\t\t\t\t\tlet part = this.getNodeParameter('part', i) as string[];\n\t\t\t\t\t\tconst channelId = this.getNodeParameter('channelId', i) as string;\n\n\t\t\t\t\t\tif (part.includes('*')) {\n\t\t\t\t\t\t\tpart = [\n\t\t\t\t\t\t\t\t'brandingSettings',\n\t\t\t\t\t\t\t\t'contentDetails',\n\t\t\t\t\t\t\t\t'contentOwnerDetails',\n\t\t\t\t\t\t\t\t'id',\n\t\t\t\t\t\t\t\t'localizations',\n\t\t\t\t\t\t\t\t'snippet',\n\t\t\t\t\t\t\t\t'statistics',\n\t\t\t\t\t\t\t\t'status',\n\t\t\t\t\t\t\t\t'topicDetails',\n\t\t\t\t\t\t\t];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tqs.part = part.join(',');\n\n\t\t\t\t\t\tqs.id = channelId;\n\n\t\t\t\t\t\tresponseData = await googleApiRequest.call(this, 'GET', '/youtube/v3/channels', {}, qs);\n\n\t\t\t\t\t\tresponseData = responseData.items;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/channels/list\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tlet part = this.getNodeParameter('part', i) as string[];\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tconst filters = this.getNodeParameter('filters', i);\n\n\t\t\t\t\t\tif (part.includes('*')) {\n\t\t\t\t\t\t\tpart = [\n\t\t\t\t\t\t\t\t'brandingSettings',\n\t\t\t\t\t\t\t\t'contentDetails',\n\t\t\t\t\t\t\t\t'contentOwnerDetails',\n\t\t\t\t\t\t\t\t'id',\n\t\t\t\t\t\t\t\t'localizations',\n\t\t\t\t\t\t\t\t'snippet',\n\t\t\t\t\t\t\t\t'statistics',\n\t\t\t\t\t\t\t\t'status',\n\t\t\t\t\t\t\t\t'topicDetails',\n\t\t\t\t\t\t\t];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tqs.part = part.join(',');\n\n\t\t\t\t\t\tObject.assign(qs, options, filters);\n\n\t\t\t\t\t\tqs.mine = true;\n\n\t\t\t\t\t\tif (qs.categoryId || qs.forUsername || qs.id || qs.managedByMe) {\n\t\t\t\t\t\t\tdelete qs.mine;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await googleApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'items',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/youtube/v3/channels',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tqs.maxResults = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = await googleApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/youtube/v3/channels',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tresponseData = responseData.items;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/channels/update\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst channelId = this.getNodeParameter('channelId', i) as string;\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tid: channelId,\n\t\t\t\t\t\t\tbrandingSettings: {\n\t\t\t\t\t\t\t\tchannel: {},\n\t\t\t\t\t\t\t\timage: {},\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tqs.part = 'brandingSettings';\n\n\t\t\t\t\t\tif (updateFields.onBehalfOfContentOwner) {\n\t\t\t\t\t\t\tqs.onBehalfOfContentOwner = updateFields.onBehalfOfContentOwner as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.brandingSettingsUi) {\n\t\t\t\t\t\t\tconst channelSettingsValues = (updateFields.brandingSettingsUi as IDataObject)\n\t\t\t\t\t\t\t\t.channelSettingsValues as IDataObject | undefined;\n\t\t\t\t\t\t\tconst channelSettings: IDataObject = {};\n\t\t\t\t\t\t\tif (channelSettingsValues?.channel) {\n\t\t\t\t\t\t\t\tconst channelSettingsOptions = channelSettingsValues.channel as IDataObject;\n\t\t\t\t\t\t\t\tif (channelSettingsOptions.country) {\n\t\t\t\t\t\t\t\t\tchannelSettings.country = channelSettingsOptions.country;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (channelSettingsOptions.description) {\n\t\t\t\t\t\t\t\t\tchannelSettings.description = channelSettingsOptions.description;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (channelSettingsOptions.defaultLanguage) {\n\t\t\t\t\t\t\t\t\tchannelSettings.defaultLanguage = channelSettingsOptions.defaultLanguage;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (channelSettingsOptions.defaultTab) {\n\t\t\t\t\t\t\t\t\tchannelSettings.defaultTab = channelSettingsOptions.defaultTab;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (channelSettingsOptions.featuredChannelsTitle) {\n\t\t\t\t\t\t\t\t\tchannelSettings.featuredChannelsTitle =\n\t\t\t\t\t\t\t\t\t\tchannelSettingsOptions.featuredChannelsTitle;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (channelSettingsOptions.featuredChannelsUrls) {\n\t\t\t\t\t\t\t\t\tchannelSettings.featuredChannelsUrls =\n\t\t\t\t\t\t\t\t\t\tchannelSettingsOptions.featuredChannelsUrls;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (channelSettingsOptions.keywords) {\n\t\t\t\t\t\t\t\t\tchannelSettings.keywords = channelSettingsOptions.keywords;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (channelSettingsOptions.moderateComments) {\n\t\t\t\t\t\t\t\t\tchannelSettings.moderateComments =\n\t\t\t\t\t\t\t\t\t\tchannelSettingsOptions.moderateComments as boolean;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (channelSettingsOptions.profileColor) {\n\t\t\t\t\t\t\t\t\tchannelSettings.profileColor = channelSettingsOptions.profileColor as string;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (channelSettingsOptions.profileColor) {\n\t\t\t\t\t\t\t\t\tchannelSettings.profileColor = channelSettingsOptions.profileColor as string;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (channelSettingsOptions.showRelatedChannels) {\n\t\t\t\t\t\t\t\t\tchannelSettings.showRelatedChannels =\n\t\t\t\t\t\t\t\t\t\tchannelSettingsOptions.showRelatedChannels as boolean;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (channelSettingsOptions.showBrowseView) {\n\t\t\t\t\t\t\t\t\tchannelSettings.showBrowseView = channelSettingsOptions.showBrowseView as boolean;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (channelSettingsOptions.trackingAnalyticsAccountId) {\n\t\t\t\t\t\t\t\t\tchannelSettings.trackingAnalyticsAccountId =\n\t\t\t\t\t\t\t\t\t\tchannelSettingsOptions.trackingAnalyticsAccountId as string;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (channelSettingsOptions.unsubscribedTrailer) {\n\t\t\t\t\t\t\t\t\tchannelSettings.unsubscribedTrailer =\n\t\t\t\t\t\t\t\t\t\tchannelSettingsOptions.unsubscribedTrailer as string;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tconst imageSettingsValues = (updateFields.brandingSettingsUi as IDataObject)\n\t\t\t\t\t\t\t\t.imageSettingsValues as IDataObject | undefined;\n\t\t\t\t\t\t\tconst imageSettings: IDataObject = {};\n\t\t\t\t\t\t\tif (imageSettingsValues?.image) {\n\t\t\t\t\t\t\t\tconst imageSettingsOptions = imageSettings.image as IDataObject;\n\t\t\t\t\t\t\t\tif (imageSettingsOptions.bannerExternalUrl) {\n\t\t\t\t\t\t\t\t\timageSettings.bannerExternalUrl =\n\t\t\t\t\t\t\t\t\t\timageSettingsOptions.bannerExternalUrl as string;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (imageSettingsOptions.trackingImageUrl) {\n\t\t\t\t\t\t\t\t\timageSettings.trackingImageUrl = imageSettingsOptions.trackingImageUrl as string;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (imageSettingsOptions.watchIconImageUrl) {\n\t\t\t\t\t\t\t\t\timageSettings.watchIconImageUrl =\n\t\t\t\t\t\t\t\t\t\timageSettingsOptions.watchIconImageUrl as string;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.brandingSettings.channel = channelSettings;\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.brandingSettings.image = imageSettings;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await googleApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'PUT',\n\t\t\t\t\t\t\t'/youtube/v3/channels',\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/channelBanners/insert\n\t\t\t\t\tif (operation === 'uploadBanner') {\n\t\t\t\t\t\tconst channelId = this.getNodeParameter('channelId', i) as string;\n\t\t\t\t\t\tconst binaryProperty = this.getNodeParameter('binaryProperty', i);\n\t\t\t\t\t\tconst binaryData = this.helpers.assertBinaryData(i, binaryProperty);\n\t\t\t\t\t\tconst body = await this.helpers.getBinaryDataBuffer(i, binaryProperty);\n\n\t\t\t\t\t\tconst requestOptions = {\n\t\t\t\t\t\t\theaders: {\n\t\t\t\t\t\t\t\t...(binaryData.mimeType ? { 'Content-Type': binaryData.mimeType } : {}),\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tjson: false,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst response = await googleApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/upload/youtube/v3/channelBanners/insert',\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\tundefined,\n\t\t\t\t\t\t\trequestOptions,\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tconst { url } = JSON.parse(response as string);\n\n\t\t\t\t\t\tqs.part = 'brandingSettings';\n\n\t\t\t\t\t\tresponseData = await googleApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'PUT',\n\t\t\t\t\t\t\t'/youtube/v3/channels',\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tid: channelId,\n\t\t\t\t\t\t\t\tbrandingSettings: {\n\t\t\t\t\t\t\t\t\timage: {\n\t\t\t\t\t\t\t\t\t\tbannerExternalUrl: url,\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (resource === 'playlist') {\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/playlists/list\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tlet part = this.getNodeParameter('part', i) as string[];\n\t\t\t\t\t\tconst playlistId = this.getNodeParameter('playlistId', i) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\n\t\t\t\t\t\tif (part.includes('*')) {\n\t\t\t\t\t\t\tpart = ['contentDetails', 'id', 'localizations', 'player', 'snippet', 'status'];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tqs.part = part.join(',');\n\n\t\t\t\t\t\tqs.id = playlistId;\n\n\t\t\t\t\t\tObject.assign(qs, options);\n\n\t\t\t\t\t\tresponseData = await googleApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t'/youtube/v3/playlists',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tresponseData = responseData.items;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/playlists/list\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tlet part = this.getNodeParameter('part', i) as string[];\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tconst filters = this.getNodeParameter('filters', i);\n\n\t\t\t\t\t\tif (part.includes('*')) {\n\t\t\t\t\t\t\tpart = ['contentDetails', 'id', 'localizations', 'player', 'snippet', 'status'];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tqs.part = part.join(',');\n\n\t\t\t\t\t\tObject.assign(qs, options, filters);\n\n\t\t\t\t\t\tqs.mine = true;\n\n\t\t\t\t\t\tif (qs.channelId || qs.id) {\n\t\t\t\t\t\t\tdelete qs.mine;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await googleApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'items',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/youtube/v3/playlists',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tqs.maxResults = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = await googleApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/youtube/v3/playlists',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tresponseData = responseData.items;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/playlists/insert\n\t\t\t\t\tif (operation === 'create') {\n\t\t\t\t\t\tconst title = this.getNodeParameter('title', i) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\n\t\t\t\t\t\tqs.part = 'snippet';\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tsnippet: {\n\t\t\t\t\t\t\t\ttitle,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (options.tags) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.snippet.tags = (options.tags as string).split(',');\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (options.description) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.snippet.privacyStatus = options.privacyStatus as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (options.defaultLanguage) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.snippet.defaultLanguage = options.defaultLanguage as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (options.onBehalfOfContentOwner) {\n\t\t\t\t\t\t\tqs.onBehalfOfContentOwner = options.onBehalfOfContentOwner as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (options.onBehalfOfContentOwnerChannel) {\n\t\t\t\t\t\t\tqs.onBehalfOfContentOwnerChannel = options.onBehalfOfContentOwnerChannel as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await googleApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/youtube/v3/playlists',\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/playlists/update\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst playlistId = this.getNodeParameter('playlistId', i) as string;\n\t\t\t\t\t\tconst title = this.getNodeParameter('title', i) as string;\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\tqs.part = 'snippet, status';\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tid: playlistId,\n\t\t\t\t\t\t\tsnippet: {\n\t\t\t\t\t\t\t\ttitle,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tstatus: {},\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (updateFields.tags) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.snippet.tags = (updateFields.tags as string).split(',');\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.privacyStatus) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.status.privacyStatus = updateFields.privacyStatus as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.description) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.snippet.description = updateFields.description as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.defaultLanguage) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.snippet.defaultLanguage = updateFields.defaultLanguage as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.onBehalfOfContentOwner) {\n\t\t\t\t\t\t\tqs.onBehalfOfContentOwner = updateFields.onBehalfOfContentOwner as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await googleApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'PUT',\n\t\t\t\t\t\t\t'/youtube/v3/playlists',\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/playlists/delete\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tconst playlistId = this.getNodeParameter('playlistId', i) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tid: playlistId,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (options.onBehalfOfContentOwner) {\n\t\t\t\t\t\t\tqs.onBehalfOfContentOwner = options.onBehalfOfContentOwner as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await googleApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'DELETE',\n\t\t\t\t\t\t\t'/youtube/v3/playlists',\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tresponseData = { success: true };\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (resource === 'playlistItem') {\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/playlistItems/list\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tlet part = this.getNodeParameter('part', i) as string[];\n\t\t\t\t\t\tconst playlistItemId = this.getNodeParameter('playlistItemId', i) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\n\t\t\t\t\t\tif (part.includes('*')) {\n\t\t\t\t\t\t\tpart = ['contentDetails', 'id', 'snippet', 'status'];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tqs.part = part.join(',');\n\n\t\t\t\t\t\tqs.id = playlistItemId;\n\n\t\t\t\t\t\tObject.assign(qs, options);\n\n\t\t\t\t\t\tresponseData = await googleApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t'/youtube/v3/playlistItems',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tresponseData = responseData.items;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/playlistItems/list\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tlet part = this.getNodeParameter('part', i) as string[];\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tconst playlistId = this.getNodeParameter('playlistId', i) as string;\n\t\t\t\t\t\t//const filters = this.getNodeParameter('filters', i);\n\n\t\t\t\t\t\tif (part.includes('*')) {\n\t\t\t\t\t\t\tpart = ['contentDetails', 'id', 'snippet', 'status'];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tqs.playlistId = playlistId;\n\n\t\t\t\t\t\tqs.part = part.join(',');\n\n\t\t\t\t\t\tObject.assign(qs, options);\n\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await googleApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'items',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/youtube/v3/playlistItems',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tqs.maxResults = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = await googleApiRequest.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/youtube/v3/playlistItems',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tresponseData = responseData.items;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/playlistItems/insert\n\t\t\t\t\tif (operation === 'add') {\n\t\t\t\t\t\tconst playlistId = this.getNodeParameter('playlistId', i) as string;\n\t\t\t\t\t\tconst videoId = this.getNodeParameter('videoId', i) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\n\t\t\t\t\t\tqs.part = 'snippet, contentDetails';\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tsnippet: {\n\t\t\t\t\t\t\t\tplaylistId,\n\t\t\t\t\t\t\t\tresourceId: {\n\t\t\t\t\t\t\t\t\tkind: 'youtube#video',\n\t\t\t\t\t\t\t\t\tvideoId,\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tcontentDetails: {},\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (options.position) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.snippet.position = options.position as number;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (options.note) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.contentDetails.note = options.note as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (options.startAt) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.contentDetails.startAt = options.startAt as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (options.endAt) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.contentDetails.endAt = options.endAt as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (options.onBehalfOfContentOwner) {\n\t\t\t\t\t\t\tqs.onBehalfOfContentOwner = options.onBehalfOfContentOwner as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await googleApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/youtube/v3/playlistItems',\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/playlistItems/delete\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tconst playlistItemId = this.getNodeParameter('playlistItemId', i) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tid: playlistItemId,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (options.onBehalfOfContentOwner) {\n\t\t\t\t\t\t\tqs.onBehalfOfContentOwner = options.onBehalfOfContentOwner as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await googleApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'DELETE',\n\t\t\t\t\t\t\t'/youtube/v3/playlistItems',\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tresponseData = { success: true };\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (resource === 'video') {\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/search/list\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tconst filters = this.getNodeParameter('filters', i);\n\n\t\t\t\t\t\tqs.part = 'snippet';\n\n\t\t\t\t\t\tqs.type = 'video';\n\n\t\t\t\t\t\tqs.forMine = true;\n\n\t\t\t\t\t\tif (filters.publishedAfter) {\n\t\t\t\t\t\t\tvalidateAndSetDate(filters, 'publishedAfter', this.getTimezone(), this);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (filters.publishedBefore) {\n\t\t\t\t\t\t\tvalidateAndSetDate(filters, 'publishedBefore', this.getTimezone(), this);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tObject.assign(qs, options, filters);\n\n\t\t\t\t\t\tif (Object.keys(filters).length > 0) {\n\t\t\t\t\t\t\tdelete qs.forMine;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (qs.relatedToVideoId && qs.forDeveloper !== undefined) {\n\t\t\t\t\t\t\tthrow new NodeOperationError(\n\t\t\t\t\t\t\t\tthis.getNode(),\n\t\t\t\t\t\t\t\t\"When using the parameter 'related to video' the parameter 'for developer' cannot be set\",\n\t\t\t\t\t\t\t\t{ itemIndex: i },\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (returnAll) {\n\t\t\t\t\t\t\tresponseData = await googleApiRequestAllItems.call(\n\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t'items',\n\t\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t\t'/youtube/v3/search',\n\t\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tqs.maxResults = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = await googleApiRequest.call(this, 'GET', '/youtube/v3/search', {}, qs);\n\t\t\t\t\t\t\tresponseData = responseData.items;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/videos/list?hl=en\n\t\t\t\t\tif (operation === 'get') {\n\t\t\t\t\t\tlet part = this.getNodeParameter('part', i) as string[];\n\t\t\t\t\t\tconst videoId = this.getNodeParameter('videoId', i) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\n\t\t\t\t\t\tif (part.includes('*')) {\n\t\t\t\t\t\t\tpart = [\n\t\t\t\t\t\t\t\t'contentDetails',\n\t\t\t\t\t\t\t\t'id',\n\t\t\t\t\t\t\t\t'liveStreamingDetails',\n\t\t\t\t\t\t\t\t'localizations',\n\t\t\t\t\t\t\t\t'player',\n\t\t\t\t\t\t\t\t'recordingDetails',\n\t\t\t\t\t\t\t\t'snippet',\n\t\t\t\t\t\t\t\t'statistics',\n\t\t\t\t\t\t\t\t'status',\n\t\t\t\t\t\t\t\t'topicDetails',\n\t\t\t\t\t\t\t];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tqs.part = part.join(',');\n\n\t\t\t\t\t\tqs.id = videoId;\n\n\t\t\t\t\t\tObject.assign(qs, options);\n\n\t\t\t\t\t\tresponseData = await googleApiRequest.call(this, 'GET', '/youtube/v3/videos', {}, qs);\n\n\t\t\t\t\t\tresponseData = responseData.items;\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.google.com/youtube/v3/guides/uploading_a_video?hl=en\n\t\t\t\t\tif (operation === 'upload') {\n\t\t\t\t\t\tconst title = this.getNodeParameter('title', i) as string;\n\t\t\t\t\t\tconst categoryId = this.getNodeParameter('categoryId', i) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\t\t\t\t\t\tconst binaryProperty = this.getNodeParameter('binaryProperty', i);\n\n\t\t\t\t\t\tconst binaryData = this.helpers.assertBinaryData(i, binaryProperty);\n\n\t\t\t\t\t\tlet mimeType: string;\n\t\t\t\t\t\tlet contentLength: number;\n\t\t\t\t\t\tlet fileContent: Readable;\n\n\t\t\t\t\t\tif (binaryData.id) {\n\t\t\t\t\t\t\t// Stream data in 256KB chunks, and upload the via the resumable upload api\n\t\t\t\t\t\t\tfileContent = await this.helpers.getBinaryStream(binaryData.id, UPLOAD_CHUNK_SIZE);\n\t\t\t\t\t\t\tconst metadata = await this.helpers.getBinaryMetadata(binaryData.id);\n\t\t\t\t\t\t\tcontentLength = metadata.fileSize;\n\t\t\t\t\t\t\tmimeType = metadata.mimeType ?? binaryData.mimeType;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst buffer = Buffer.from(binaryData.data, BINARY_ENCODING);\n\t\t\t\t\t\t\tfileContent = Readable.from(buffer);\n\t\t\t\t\t\t\tcontentLength = buffer.length;\n\t\t\t\t\t\t\tmimeType = binaryData.mimeType;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst payload = {\n\t\t\t\t\t\t\tsnippet: {\n\t\t\t\t\t\t\t\ttitle,\n\t\t\t\t\t\t\t\tcategoryId,\n\t\t\t\t\t\t\t\tdescription: options.description,\n\t\t\t\t\t\t\t\ttags: (options.tags as string)?.split(','),\n\t\t\t\t\t\t\t\tdefaultLanguage: options.defaultLanguage,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tstatus: {\n\t\t\t\t\t\t\t\tprivacyStatus: options.privacyStatus,\n\t\t\t\t\t\t\t\tembeddable: options.embeddable,\n\t\t\t\t\t\t\t\tpublicStatsViewable: options.publicStatsViewable,\n\t\t\t\t\t\t\t\tpublishAt: options.publishAt,\n\t\t\t\t\t\t\t\tselfDeclaredMadeForKids: options.selfDeclaredMadeForKids,\n\t\t\t\t\t\t\t\tlicense: options.license,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\trecordingDetails: {\n\t\t\t\t\t\t\t\trecordingDate: options.recordingDate,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tconst resumableUpload = await googleApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/upload/youtube/v3/videos',\n\t\t\t\t\t\t\tpayload,\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tuploadType: 'resumable',\n\t\t\t\t\t\t\t\tpart: 'snippet,status,recordingDetails',\n\t\t\t\t\t\t\t\tnotifySubscribers: options.notifySubscribers ?? false,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tundefined,\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\theaders: {\n\t\t\t\t\t\t\t\t\t'X-Upload-Content-Length': contentLength,\n\t\t\t\t\t\t\t\t\t'X-Upload-Content-Type': mimeType,\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tjson: true,\n\t\t\t\t\t\t\t\tresolveWithFullResponse: true,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tconst uploadUrl = resumableUpload.headers.location;\n\n\t\t\t\t\t\tlet uploadId;\n\t\t\t\t\t\tlet offset = 0;\n\t\t\t\t\t\tfor await (const chunk of fileContent) {\n\t\t\t\t\t\t\tconst nextOffset = offset + Number(chunk.length);\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tconst response = await this.helpers.httpRequest({\n\t\t\t\t\t\t\t\t\tmethod: 'PUT',\n\t\t\t\t\t\t\t\t\turl: uploadUrl,\n\t\t\t\t\t\t\t\t\theaders: {\n\t\t\t\t\t\t\t\t\t\t'Content-Length': chunk.length,\n\t\t\t\t\t\t\t\t\t\t'Content-Range': `bytes ${offset}-${nextOffset - 1}/${contentLength}`,\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\tbody: chunk,\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tuploadId = response.id;\n\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\tif (error.response?.status !== 308) throw error;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\toffset = nextOffset;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = { uploadId, ...resumableUpload.body };\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/playlists/update\n\t\t\t\t\tif (operation === 'update') {\n\t\t\t\t\t\tconst id = this.getNodeParameter('videoId', i) as string;\n\t\t\t\t\t\tconst title = this.getNodeParameter('title', i) as string;\n\t\t\t\t\t\tconst categoryId = this.getNodeParameter('categoryId', i) as string;\n\t\t\t\t\t\tconst updateFields = this.getNodeParameter('updateFields', i);\n\n\t\t\t\t\t\tqs.part = 'snippet, status, recordingDetails';\n\n\t\t\t\t\t\tconst body = {\n\t\t\t\t\t\t\tid,\n\t\t\t\t\t\t\tsnippet: {\n\t\t\t\t\t\t\t\ttitle,\n\t\t\t\t\t\t\t\tcategoryId,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tstatus: {},\n\t\t\t\t\t\t\trecordingDetails: {},\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (updateFields.description) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.snippet.description = updateFields.description as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.privacyStatus) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.status.privacyStatus = updateFields.privacyStatus as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.tags) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.snippet.tags = (updateFields.tags as string).split(',');\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.embeddable) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.status.embeddable = updateFields.embeddable as boolean;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.publicStatsViewable) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.status.publicStatsViewable = updateFields.publicStatsViewable as boolean;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.publishAt) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.status.publishAt = updateFields.publishAt as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.selfDeclaredMadeForKids) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.status.selfDeclaredMadeForKids = updateFields.selfDeclaredMadeForKids as boolean;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.recordingDate) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.recordingDetails.recordingDate = updateFields.recordingDate as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.license) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.status.license = updateFields.license as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (updateFields.defaultLanguage) {\n\t\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\t\tbody.snippet.defaultLanguage = updateFields.defaultLanguage as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await googleApiRequest.call(this, 'PUT', '/youtube/v3/videos', body, qs);\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/videos/delete?hl=en\n\t\t\t\t\tif (operation === 'delete') {\n\t\t\t\t\t\tconst videoId = this.getNodeParameter('videoId', i) as string;\n\t\t\t\t\t\tconst options = this.getNodeParameter('options', i);\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tid: videoId,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (options.onBehalfOfContentOwner) {\n\t\t\t\t\t\t\tqs.onBehalfOfContentOwner = options.onBehalfOfContentOwner as string;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresponseData = await googleApiRequest.call(this, 'DELETE', '/youtube/v3/videos', body);\n\n\t\t\t\t\t\tresponseData = { success: true };\n\t\t\t\t\t}\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/videos/rate?hl=en\n\t\t\t\t\tif (operation === 'rate') {\n\t\t\t\t\t\tconst videoId = this.getNodeParameter('videoId', i) as string;\n\t\t\t\t\t\tconst rating = this.getNodeParameter('rating', i) as string;\n\n\t\t\t\t\t\tconst body: IDataObject = {\n\t\t\t\t\t\t\tid: videoId,\n\t\t\t\t\t\t\trating,\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tresponseData = await googleApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'POST',\n\t\t\t\t\t\t\t'/youtube/v3/videos/rate',\n\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tresponseData = { success: true };\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (resource === 'videoCategory') {\n\t\t\t\t\t//https://developers.google.com/youtube/v3/docs/videoCategories/list\n\t\t\t\t\tif (operation === 'getAll') {\n\t\t\t\t\t\tconst returnAll = this.getNodeParameter('returnAll', i);\n\t\t\t\t\t\tconst regionCode = this.getNodeParameter('regionCode', i) as string;\n\n\t\t\t\t\t\tqs.regionCode = regionCode;\n\n\t\t\t\t\t\tqs.part = 'snippet';\n\n\t\t\t\t\t\tresponseData = await googleApiRequest.call(\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t'GET',\n\t\t\t\t\t\t\t'/youtube/v3/videoCategories',\n\t\t\t\t\t\t\t{},\n\t\t\t\t\t\t\tqs,\n\t\t\t\t\t\t);\n\t\t\t\t\t\tresponseData = responseData.items;\n\n\t\t\t\t\t\tif (!returnAll) {\n\t\t\t\t\t\t\tconst limit = this.getNodeParameter('limit', i);\n\t\t\t\t\t\t\tresponseData = responseData.splice(0, limit);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (this.continueOnFail()) {\n\t\t\t\t\tconst executionErrorData = this.helpers.constructExecutionMetaData(\n\t\t\t\t\t\tthis.helpers.returnJsonArray({ error: error.message }),\n\t\t\t\t\t\t{ itemData: { item: i } },\n\t\t\t\t\t);\n\t\t\t\t\treturnData.push(...executionErrorData);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\n\t\t\tconst executionData = this.helpers.constructExecutionMetaData(\n\t\t\t\tthis.helpers.returnJsonArray(responseData as IDataObject[]),\n\t\t\t\t{ itemData: { item: i } },\n\t\t\t);\n\n\t\t\treturnData.push(...executionData);\n\t\t}\n\n\t\treturn [returnData];\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,0BAAyE;AACzE,oBAAyB;AAEzB,6BAAgC;AAEhC,gCAAiD;AACjD,8BAA2D;AAC3D,iCAAmD;AACnD,qCAA2D;AAC3D,sCAA6D;AAC7D,8BAA6C;AAC7C,IAAAA,2BAAmC;AAEnC,MAAM,oBAAoB,OAAO;AAE1B,MAAM,QAA6B;AAAA,EAAnC;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA;AAAA,MAEN,MAAM;AAAA,MACN,OAAO,CAAC,OAAO;AAAA,MACf,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ,CAAC,wCAAoB,IAAI;AAAA,MACjC,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,YAAY;AAAA,QACX;AAAA,UACC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS;AAAA,QACV;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,QAEH,GAAG;AAAA,QACH,GAAG;AAAA,QAEH,GAAG;AAAA,QACH,GAAG;AAAA,QAEH,GAAG;AAAA,QACH,GAAG;AAAA,QAEH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA;AAAA;AAAA,QAGZ,MAAM,eAA2E;AAChF,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,YAAY,MAAM,iDAAyB;AAAA,YAChD;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AACA,qBAAW,YAAY,WAAW;AACjC,kBAAM,eAAe,SAAS,GAAG,YAAY;AAC7C,kBAAM,aAAa,SAAS;AAC5B,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,oBAAgF;AACrF,gBAAM,aAAqC,CAAC;AAC5C,qBAAW,eAAe,wCAAiB;AAC1C,kBAAM,kBAAkB,GAAG,YAAY,IAAI,MAAM,YAAY,MAAM;AACnE,kBAAM,gBAAgB,YAAY;AAClC,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,qBAAiF;AACtF,gBAAM,cAAc,KAAK,wBAAwB,YAAY;AAE7D,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,KAAkB,CAAC;AACzB,aAAG,aAAa;AAChB,aAAG,OAAO;AACV,gBAAM,aAAa,MAAM,iDAAyB;AAAA,YACjD;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,CAAC;AAAA,YACD;AAAA,UACD;AACA,qBAAW,YAAY,YAAY;AAClC,kBAAM,eAAe,SAAS,QAAQ;AACtC,kBAAM,aAAa,SAAS;AAC5B,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAGA,MAAM,eAA2E;AAChF,gBAAM,aAAqC,CAAC;AAC5C,gBAAM,KAAkB,CAAC;AACzB,aAAG,OAAO;AACV,aAAG,OAAO;AACV,gBAAM,YAAY,MAAM,iDAAyB;AAAA,YAChD;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,CAAC;AAAA,YACD;AAAA,UACD;AACA,qBAAW,YAAY,WAAW;AACjC,kBAAM,eAAe,SAAS,QAAQ;AACtC,kBAAM,aAAa,SAAS;AAC5B,uBAAW,KAAK;AAAA,cACf,MAAM;AAAA,cACN,OAAO;AAAA,YACR,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAkE;AACvE,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,aAAmC,CAAC;AAC1C,UAAM,SAAS,MAAM;AACrB,UAAM,KAAkB,CAAC;AACzB,QAAI;AACJ,UAAM,WAAW,KAAK,iBAAiB,YAAY,CAAC;AACpD,UAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAI;AACH,YAAI,aAAa,WAAW;AAC3B,cAAI,cAAc,OAAO;AAExB,gBAAI,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC1C,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AAEtD,gBAAI,KAAK,SAAS,GAAG,GAAG;AACvB,qBAAO;AAAA,gBACN;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACD;AAAA,YACD;AAEA,eAAG,OAAO,KAAK,KAAK,GAAG;AAEvB,eAAG,KAAK;AAER,2BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,wBAAwB,CAAC,GAAG,EAAE;AAEtF,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,gBAAI,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC1C,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gBAAI,KAAK,SAAS,GAAG,GAAG;AACvB,qBAAO;AAAA,gBACN;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACD;AAAA,YACD;AAEA,eAAG,OAAO,KAAK,KAAK,GAAG;AAEvB,mBAAO,OAAO,IAAI,SAAS,OAAO;AAElC,eAAG,OAAO;AAEV,gBAAI,GAAG,cAAc,GAAG,eAAe,GAAG,MAAM,GAAG,aAAa;AAC/D,qBAAO,GAAG;AAAA,YACX;AAEA,gBAAI,WAAW;AACd,6BAAe,MAAM,iDAAyB;AAAA,gBAC7C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,iBAAG,aAAa,KAAK,iBAAiB,SAAS,CAAC;AAChD,6BAAe,MAAM,yCAAiB;AAAA,gBACrC;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AACA,6BAAe,aAAa;AAAA,YAC7B;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,kBAAM,OAAoB;AAAA,cACzB,IAAI;AAAA,cACJ,kBAAkB;AAAA,gBACjB,SAAS,CAAC;AAAA,gBACV,OAAO,CAAC;AAAA,cACT;AAAA,YACD;AAEA,eAAG,OAAO;AAEV,gBAAI,aAAa,wBAAwB;AACxC,iBAAG,yBAAyB,aAAa;AAAA,YAC1C;AAEA,gBAAI,aAAa,oBAAoB;AACpC,oBAAM,wBAAyB,aAAa,mBAC1C;AACF,oBAAM,kBAA+B,CAAC;AACtC,kBAAI,uBAAuB,SAAS;AACnC,sBAAM,yBAAyB,sBAAsB;AACrD,oBAAI,uBAAuB,SAAS;AACnC,kCAAgB,UAAU,uBAAuB;AAAA,gBAClD;AACA,oBAAI,uBAAuB,aAAa;AACvC,kCAAgB,cAAc,uBAAuB;AAAA,gBACtD;AACA,oBAAI,uBAAuB,iBAAiB;AAC3C,kCAAgB,kBAAkB,uBAAuB;AAAA,gBAC1D;AACA,oBAAI,uBAAuB,YAAY;AACtC,kCAAgB,aAAa,uBAAuB;AAAA,gBACrD;AACA,oBAAI,uBAAuB,uBAAuB;AACjD,kCAAgB,wBACf,uBAAuB;AAAA,gBACzB;AACA,oBAAI,uBAAuB,sBAAsB;AAChD,kCAAgB,uBACf,uBAAuB;AAAA,gBACzB;AACA,oBAAI,uBAAuB,UAAU;AACpC,kCAAgB,WAAW,uBAAuB;AAAA,gBACnD;AACA,oBAAI,uBAAuB,kBAAkB;AAC5C,kCAAgB,mBACf,uBAAuB;AAAA,gBACzB;AACA,oBAAI,uBAAuB,cAAc;AACxC,kCAAgB,eAAe,uBAAuB;AAAA,gBACvD;AACA,oBAAI,uBAAuB,cAAc;AACxC,kCAAgB,eAAe,uBAAuB;AAAA,gBACvD;AACA,oBAAI,uBAAuB,qBAAqB;AAC/C,kCAAgB,sBACf,uBAAuB;AAAA,gBACzB;AACA,oBAAI,uBAAuB,gBAAgB;AAC1C,kCAAgB,iBAAiB,uBAAuB;AAAA,gBACzD;AACA,oBAAI,uBAAuB,4BAA4B;AACtD,kCAAgB,6BACf,uBAAuB;AAAA,gBACzB;AACA,oBAAI,uBAAuB,qBAAqB;AAC/C,kCAAgB,sBACf,uBAAuB;AAAA,gBACzB;AAAA,cACD;AAEA,oBAAM,sBAAuB,aAAa,mBACxC;AACF,oBAAM,gBAA6B,CAAC;AACpC,kBAAI,qBAAqB,OAAO;AAC/B,sBAAM,uBAAuB,cAAc;AAC3C,oBAAI,qBAAqB,mBAAmB;AAC3C,gCAAc,oBACb,qBAAqB;AAAA,gBACvB;AACA,oBAAI,qBAAqB,kBAAkB;AAC1C,gCAAc,mBAAmB,qBAAqB;AAAA,gBACvD;AACA,oBAAI,qBAAqB,mBAAmB;AAC3C,gCAAc,oBACb,qBAAqB;AAAA,gBACvB;AAAA,cACD;AAGA,mBAAK,iBAAiB,UAAU;AAEhC,mBAAK,iBAAiB,QAAQ;AAAA,YAC/B;AAEA,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,gBAAgB;AACjC,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,aAAa,KAAK,QAAQ,iBAAiB,GAAG,cAAc;AAClE,kBAAM,OAAO,MAAM,KAAK,QAAQ,oBAAoB,GAAG,cAAc;AAErE,kBAAM,iBAAiB;AAAA,cACtB,SAAS;AAAA,gBACR,GAAI,WAAW,WAAW,EAAE,gBAAgB,WAAW,SAAS,IAAI,CAAC;AAAA,cACtE;AAAA,cACA,MAAM;AAAA,YACP;AAEA,kBAAM,WAAW,MAAM,yCAAiB;AAAA,cACvC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAEA,kBAAM,EAAE,IAAI,IAAI,KAAK,MAAM,QAAkB;AAE7C,eAAG,OAAO;AAEV,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,gBACC,IAAI;AAAA,gBACJ,kBAAkB;AAAA,kBACjB,OAAO;AAAA,oBACN,mBAAmB;AAAA,kBACpB;AAAA,gBACD;AAAA,cACD;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAAA,QACD;AACA,YAAI,aAAa,YAAY;AAE5B,cAAI,cAAc,OAAO;AACxB,gBAAI,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC1C,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AACxD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gBAAI,KAAK,SAAS,GAAG,GAAG;AACvB,qBAAO,CAAC,kBAAkB,MAAM,iBAAiB,UAAU,WAAW,QAAQ;AAAA,YAC/E;AAEA,eAAG,OAAO,KAAK,KAAK,GAAG;AAEvB,eAAG,KAAK;AAER,mBAAO,OAAO,IAAI,OAAO;AAEzB,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD;AAAA,YACD;AAEA,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,gBAAI,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC1C,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gBAAI,KAAK,SAAS,GAAG,GAAG;AACvB,qBAAO,CAAC,kBAAkB,MAAM,iBAAiB,UAAU,WAAW,QAAQ;AAAA,YAC/E;AAEA,eAAG,OAAO,KAAK,KAAK,GAAG;AAEvB,mBAAO,OAAO,IAAI,SAAS,OAAO;AAElC,eAAG,OAAO;AAEV,gBAAI,GAAG,aAAa,GAAG,IAAI;AAC1B,qBAAO,GAAG;AAAA,YACX;AAEA,gBAAI,WAAW;AACd,6BAAe,MAAM,iDAAyB;AAAA,gBAC7C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,iBAAG,aAAa,KAAK,iBAAiB,SAAS,CAAC;AAChD,6BAAe,MAAM,yCAAiB;AAAA,gBACrC;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AACA,6BAAe,aAAa;AAAA,YAC7B;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,eAAG,OAAO;AAEV,kBAAM,OAAoB;AAAA,cACzB,SAAS;AAAA,gBACR;AAAA,cACD;AAAA,YACD;AAEA,gBAAI,QAAQ,MAAM;AAEjB,mBAAK,QAAQ,OAAQ,QAAQ,KAAgB,MAAM,GAAG;AAAA,YACvD;AAEA,gBAAI,QAAQ,aAAa;AAExB,mBAAK,QAAQ,gBAAgB,QAAQ;AAAA,YACtC;AAEA,gBAAI,QAAQ,iBAAiB;AAE5B,mBAAK,QAAQ,kBAAkB,QAAQ;AAAA,YACxC;AAEA,gBAAI,QAAQ,wBAAwB;AACnC,iBAAG,yBAAyB,QAAQ;AAAA,YACrC;AAEA,gBAAI,QAAQ,+BAA+B;AAC1C,iBAAG,gCAAgC,QAAQ;AAAA,YAC5C;AAEA,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AACxD,kBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,eAAG,OAAO;AAEV,kBAAM,OAAoB;AAAA,cACzB,IAAI;AAAA,cACJ,SAAS;AAAA,gBACR;AAAA,cACD;AAAA,cACA,QAAQ,CAAC;AAAA,YACV;AAEA,gBAAI,aAAa,MAAM;AAEtB,mBAAK,QAAQ,OAAQ,aAAa,KAAgB,MAAM,GAAG;AAAA,YAC5D;AAEA,gBAAI,aAAa,eAAe;AAE/B,mBAAK,OAAO,gBAAgB,aAAa;AAAA,YAC1C;AAEA,gBAAI,aAAa,aAAa;AAE7B,mBAAK,QAAQ,cAAc,aAAa;AAAA,YACzC;AAEA,gBAAI,aAAa,iBAAiB;AAEjC,mBAAK,QAAQ,kBAAkB,aAAa;AAAA,YAC7C;AAEA,gBAAI,aAAa,wBAAwB;AACxC,iBAAG,yBAAyB,aAAa;AAAA,YAC1C;AAEA,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AACxD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,kBAAM,OAAoB;AAAA,cACzB,IAAI;AAAA,YACL;AAEA,gBAAI,QAAQ,wBAAwB;AACnC,iBAAG,yBAAyB,QAAQ;AAAA,YACrC;AAEA,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAEA,2BAAe,EAAE,SAAS,KAAK;AAAA,UAChC;AAAA,QACD;AACA,YAAI,aAAa,gBAAgB;AAEhC,cAAI,cAAc,OAAO;AACxB,gBAAI,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC1C,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gBAAI,KAAK,SAAS,GAAG,GAAG;AACvB,qBAAO,CAAC,kBAAkB,MAAM,WAAW,QAAQ;AAAA,YACpD;AAEA,eAAG,OAAO,KAAK,KAAK,GAAG;AAEvB,eAAG,KAAK;AAER,mBAAO,OAAO,IAAI,OAAO;AAEzB,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD;AAAA,YACD;AAEA,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,gBAAI,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC1C,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AAGxD,gBAAI,KAAK,SAAS,GAAG,GAAG;AACvB,qBAAO,CAAC,kBAAkB,MAAM,WAAW,QAAQ;AAAA,YACpD;AAEA,eAAG,aAAa;AAEhB,eAAG,OAAO,KAAK,KAAK,GAAG;AAEvB,mBAAO,OAAO,IAAI,OAAO;AAEzB,gBAAI,WAAW;AACd,6BAAe,MAAM,iDAAyB;AAAA,gBAC7C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,iBAAG,aAAa,KAAK,iBAAiB,SAAS,CAAC;AAChD,6BAAe,MAAM,yCAAiB;AAAA,gBACrC;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AACA,6BAAe,aAAa;AAAA,YAC7B;AAAA,UACD;AAEA,cAAI,cAAc,OAAO;AACxB,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AACxD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,eAAG,OAAO;AAEV,kBAAM,OAAoB;AAAA,cACzB,SAAS;AAAA,gBACR;AAAA,gBACA,YAAY;AAAA,kBACX,MAAM;AAAA,kBACN;AAAA,gBACD;AAAA,cACD;AAAA,cACA,gBAAgB,CAAC;AAAA,YAClB;AAEA,gBAAI,QAAQ,UAAU;AAErB,mBAAK,QAAQ,WAAW,QAAQ;AAAA,YACjC;AAEA,gBAAI,QAAQ,MAAM;AAEjB,mBAAK,eAAe,OAAO,QAAQ;AAAA,YACpC;AAEA,gBAAI,QAAQ,SAAS;AAEpB,mBAAK,eAAe,UAAU,QAAQ;AAAA,YACvC;AAEA,gBAAI,QAAQ,OAAO;AAElB,mBAAK,eAAe,QAAQ,QAAQ;AAAA,YACrC;AAEA,gBAAI,QAAQ,wBAAwB;AACnC,iBAAG,yBAAyB,QAAQ;AAAA,YACrC;AAEA,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAChE,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,kBAAM,OAAoB;AAAA,cACzB,IAAI;AAAA,YACL;AAEA,gBAAI,QAAQ,wBAAwB;AACnC,iBAAG,yBAAyB,QAAQ;AAAA,YACrC;AAEA,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAEA,2BAAe,EAAE,SAAS,KAAK;AAAA,UAChC;AAAA,QACD;AACA,YAAI,aAAa,SAAS;AAEzB,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,eAAG,OAAO;AAEV,eAAG,OAAO;AAEV,eAAG,UAAU;AAEb,gBAAI,QAAQ,gBAAgB;AAC3B,+DAAmB,SAAS,kBAAkB,KAAK,YAAY,GAAG,IAAI;AAAA,YACvE;AAEA,gBAAI,QAAQ,iBAAiB;AAC5B,+DAAmB,SAAS,mBAAmB,KAAK,YAAY,GAAG,IAAI;AAAA,YACxE;AAEA,mBAAO,OAAO,IAAI,SAAS,OAAO;AAElC,gBAAI,OAAO,KAAK,OAAO,EAAE,SAAS,GAAG;AACpC,qBAAO,GAAG;AAAA,YACX;AAEA,gBAAI,GAAG,oBAAoB,GAAG,iBAAiB,QAAW;AACzD,oBAAM,IAAI;AAAA,gBACT,KAAK,QAAQ;AAAA,gBACb;AAAA,gBACA,EAAE,WAAW,EAAE;AAAA,cAChB;AAAA,YACD;AAEA,gBAAI,WAAW;AACd,6BAAe,MAAM,iDAAyB;AAAA,gBAC7C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,gBACD;AAAA,cACD;AAAA,YACD,OAAO;AACN,iBAAG,aAAa,KAAK,iBAAiB,SAAS,CAAC;AAChD,6BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,sBAAsB,CAAC,GAAG,EAAE;AACpF,6BAAe,aAAa;AAAA,YAC7B;AAAA,UACD;AAEA,cAAI,cAAc,OAAO;AACxB,gBAAI,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC1C,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,gBAAI,KAAK,SAAS,GAAG,GAAG;AACvB,qBAAO;AAAA,gBACN;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACD;AAAA,YACD;AAEA,eAAG,OAAO,KAAK,KAAK,GAAG;AAEvB,eAAG,KAAK;AAER,mBAAO,OAAO,IAAI,OAAO;AAEzB,2BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,sBAAsB,CAAC,GAAG,EAAE;AAEpF,2BAAe,aAAa;AAAA,UAC7B;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AACxD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,iBAAiB,KAAK,iBAAiB,kBAAkB,CAAC;AAEhE,kBAAM,aAAa,KAAK,QAAQ,iBAAiB,GAAG,cAAc;AAElE,gBAAI;AACJ,gBAAI;AACJ,gBAAI;AAEJ,gBAAI,WAAW,IAAI;AAElB,4BAAc,MAAM,KAAK,QAAQ,gBAAgB,WAAW,IAAI,iBAAiB;AACjF,oBAAM,WAAW,MAAM,KAAK,QAAQ,kBAAkB,WAAW,EAAE;AACnE,8BAAgB,SAAS;AACzB,yBAAW,SAAS,YAAY,WAAW;AAAA,YAC5C,OAAO;AACN,oBAAM,SAAS,OAAO,KAAK,WAAW,MAAM,mCAAe;AAC3D,4BAAc,uBAAS,KAAK,MAAM;AAClC,8BAAgB,OAAO;AACvB,yBAAW,WAAW;AAAA,YACvB;AAEA,kBAAM,UAAU;AAAA,cACf,SAAS;AAAA,gBACR;AAAA,gBACA;AAAA,gBACA,aAAa,QAAQ;AAAA,gBACrB,MAAO,QAAQ,MAAiB,MAAM,GAAG;AAAA,gBACzC,iBAAiB,QAAQ;AAAA,cAC1B;AAAA,cACA,QAAQ;AAAA,gBACP,eAAe,QAAQ;AAAA,gBACvB,YAAY,QAAQ;AAAA,gBACpB,qBAAqB,QAAQ;AAAA,gBAC7B,WAAW,QAAQ;AAAA,gBACnB,yBAAyB,QAAQ;AAAA,gBACjC,SAAS,QAAQ;AAAA,cAClB;AAAA,cACA,kBAAkB;AAAA,gBACjB,eAAe,QAAQ;AAAA,cACxB;AAAA,YACD;AAEA,kBAAM,kBAAkB,MAAM,yCAAiB;AAAA,cAC9C;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,gBACC,YAAY;AAAA,gBACZ,MAAM;AAAA,gBACN,mBAAmB,QAAQ,qBAAqB;AAAA,cACjD;AAAA,cACA;AAAA,cACA;AAAA,gBACC,SAAS;AAAA,kBACR,2BAA2B;AAAA,kBAC3B,yBAAyB;AAAA,gBAC1B;AAAA,gBACA,MAAM;AAAA,gBACN,yBAAyB;AAAA,cAC1B;AAAA,YACD;AAEA,kBAAM,YAAY,gBAAgB,QAAQ;AAE1C,gBAAI;AACJ,gBAAI,SAAS;AACb,6BAAiB,SAAS,aAAa;AACtC,oBAAM,aAAa,SAAS,OAAO,MAAM,MAAM;AAC/C,kBAAI;AACH,sBAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAAA,kBAC/C,QAAQ;AAAA,kBACR,KAAK;AAAA,kBACL,SAAS;AAAA,oBACR,kBAAkB,MAAM;AAAA,oBACxB,iBAAiB,SAAS,MAAM,IAAI,aAAa,CAAC,IAAI,aAAa;AAAA,kBACpE;AAAA,kBACA,MAAM;AAAA,gBACP,CAAC;AACD,2BAAW,SAAS;AAAA,cACrB,SAAS,OAAO;AACf,oBAAI,MAAM,UAAU,WAAW,IAAK,OAAM;AAAA,cAC3C;AACA,uBAAS;AAAA,YACV;AAEA,2BAAe,EAAE,UAAU,GAAG,gBAAgB,KAAK;AAAA,UACpD;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,KAAK,KAAK,iBAAiB,WAAW,CAAC;AAC7C,kBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AACxD,kBAAM,eAAe,KAAK,iBAAiB,gBAAgB,CAAC;AAE5D,eAAG,OAAO;AAEV,kBAAM,OAAO;AAAA,cACZ;AAAA,cACA,SAAS;AAAA,gBACR;AAAA,gBACA;AAAA,cACD;AAAA,cACA,QAAQ,CAAC;AAAA,cACT,kBAAkB,CAAC;AAAA,YACpB;AAEA,gBAAI,aAAa,aAAa;AAE7B,mBAAK,QAAQ,cAAc,aAAa;AAAA,YACzC;AAEA,gBAAI,aAAa,eAAe;AAE/B,mBAAK,OAAO,gBAAgB,aAAa;AAAA,YAC1C;AAEA,gBAAI,aAAa,MAAM;AAEtB,mBAAK,QAAQ,OAAQ,aAAa,KAAgB,MAAM,GAAG;AAAA,YAC5D;AAEA,gBAAI,aAAa,YAAY;AAE5B,mBAAK,OAAO,aAAa,aAAa;AAAA,YACvC;AAEA,gBAAI,aAAa,qBAAqB;AAErC,mBAAK,OAAO,sBAAsB,aAAa;AAAA,YAChD;AAEA,gBAAI,aAAa,WAAW;AAE3B,mBAAK,OAAO,YAAY,aAAa;AAAA,YACtC;AAEA,gBAAI,aAAa,yBAAyB;AAEzC,mBAAK,OAAO,0BAA0B,aAAa;AAAA,YACpD;AAEA,gBAAI,aAAa,eAAe;AAE/B,mBAAK,iBAAiB,gBAAgB,aAAa;AAAA,YACpD;AAEA,gBAAI,aAAa,SAAS;AAEzB,mBAAK,OAAO,UAAU,aAAa;AAAA,YACpC;AAEA,gBAAI,aAAa,iBAAiB;AAEjC,mBAAK,QAAQ,kBAAkB,aAAa;AAAA,YAC7C;AAEA,2BAAe,MAAM,yCAAiB,KAAK,MAAM,OAAO,sBAAsB,MAAM,EAAE;AAAA,UACvF;AAEA,cAAI,cAAc,UAAU;AAC3B,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAElD,kBAAM,OAAoB;AAAA,cACzB,IAAI;AAAA,YACL;AAEA,gBAAI,QAAQ,wBAAwB;AACnC,iBAAG,yBAAyB,QAAQ;AAAA,YACrC;AAEA,2BAAe,MAAM,yCAAiB,KAAK,MAAM,UAAU,sBAAsB,IAAI;AAErF,2BAAe,EAAE,SAAS,KAAK;AAAA,UAChC;AAEA,cAAI,cAAc,QAAQ;AACzB,kBAAM,UAAU,KAAK,iBAAiB,WAAW,CAAC;AAClD,kBAAM,SAAS,KAAK,iBAAiB,UAAU,CAAC;AAEhD,kBAAM,OAAoB;AAAA,cACzB,IAAI;AAAA,cACJ;AAAA,YACD;AAEA,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAEA,2BAAe,EAAE,SAAS,KAAK;AAAA,UAChC;AAAA,QACD;AACA,YAAI,aAAa,iBAAiB;AAEjC,cAAI,cAAc,UAAU;AAC3B,kBAAM,YAAY,KAAK,iBAAiB,aAAa,CAAC;AACtD,kBAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AAExD,eAAG,aAAa;AAEhB,eAAG,OAAO;AAEV,2BAAe,MAAM,yCAAiB;AAAA,cACrC;AAAA,cACA;AAAA,cACA;AAAA,cACA,CAAC;AAAA,cACD;AAAA,YACD;AACA,2BAAe,aAAa;AAE5B,gBAAI,CAAC,WAAW;AACf,oBAAM,QAAQ,KAAK,iBAAiB,SAAS,CAAC;AAC9C,6BAAe,aAAa,OAAO,GAAG,KAAK;AAAA,YAC5C;AAAA,UACD;AAAA,QACD;AAAA,MACD,SAAS,OAAO;AACf,YAAI,KAAK,eAAe,GAAG;AAC1B,gBAAM,qBAAqB,KAAK,QAAQ;AAAA,YACvC,KAAK,QAAQ,gBAAgB,EAAE,OAAO,MAAM,QAAQ,CAAC;AAAA,YACrD,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,UACzB;AACA,qBAAW,KAAK,GAAG,kBAAkB;AACrC;AAAA,QACD;AACA,cAAM;AAAA,MACP;AAEA,YAAM,gBAAgB,KAAK,QAAQ;AAAA,QAClC,KAAK,QAAQ,gBAAgB,YAA6B;AAAA,QAC1D,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;AAAA,MACzB;AAEA,iBAAW,KAAK,GAAG,aAAa;AAAA,IACjC;AAEA,WAAO,CAAC,UAAU;AAAA,EACnB;AACD;", "names": ["import_GenericFunctions"]}