import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { BedrockAgentRuntimeClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../BedrockAgentRuntimeClient";
import { ListInvocationStepsRequest, ListInvocationStepsResponse } from "../models/models_1";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link ListInvocationStepsCommand}.
 */
export interface ListInvocationStepsCommandInput extends ListInvocationStepsRequest {
}
/**
 * @public
 *
 * The output of {@link ListInvocationStepsCommand}.
 */
export interface ListInvocationStepsCommandOutput extends ListInvocationStepsResponse, __MetadataBearer {
}
declare const ListInvocationStepsCommand_base: {
    new (input: ListInvocationStepsCommandInput): import("@smithy/smithy-client").CommandImpl<ListInvocationStepsCommandInput, ListInvocationStepsCommandOutput, BedrockAgentRuntimeClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (input: ListInvocationStepsCommandInput): import("@smithy/smithy-client").CommandImpl<ListInvocationStepsCommandInput, ListInvocationStepsCommandOutput, BedrockAgentRuntimeClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Lists all invocation steps associated with a session and optionally, an invocation within the session. For more information about sessions, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { BedrockAgentRuntimeClient, ListInvocationStepsCommand } from "@aws-sdk/client-bedrock-agent-runtime"; // ES Modules import
 * // const { BedrockAgentRuntimeClient, ListInvocationStepsCommand } = require("@aws-sdk/client-bedrock-agent-runtime"); // CommonJS import
 * const client = new BedrockAgentRuntimeClient(config);
 * const input = { // ListInvocationStepsRequest
 *   invocationIdentifier: "STRING_VALUE",
 *   nextToken: "STRING_VALUE",
 *   maxResults: Number("int"),
 *   sessionIdentifier: "STRING_VALUE", // required
 * };
 * const command = new ListInvocationStepsCommand(input);
 * const response = await client.send(command);
 * // { // ListInvocationStepsResponse
 * //   invocationStepSummaries: [ // InvocationStepSummaries // required
 * //     { // InvocationStepSummary
 * //       sessionId: "STRING_VALUE", // required
 * //       invocationId: "STRING_VALUE", // required
 * //       invocationStepId: "STRING_VALUE", // required
 * //       invocationStepTime: new Date("TIMESTAMP"), // required
 * //     },
 * //   ],
 * //   nextToken: "STRING_VALUE",
 * // };
 *
 * ```
 *
 * @param ListInvocationStepsCommandInput - {@link ListInvocationStepsCommandInput}
 * @returns {@link ListInvocationStepsCommandOutput}
 * @see {@link ListInvocationStepsCommandInput} for command's `input` shape.
 * @see {@link ListInvocationStepsCommandOutput} for command's `response` shape.
 * @see {@link BedrockAgentRuntimeClientResolvedConfig | config} for BedrockAgentRuntimeClient's `config` shape.
 *
 * @throws {@link AccessDeniedException} (client fault)
 *  <p>The request is denied because of missing access permissions. Check your permissions and retry your request.</p>
 *
 * @throws {@link InternalServerException} (server fault)
 *  <p>An internal server error occurred. Retry your request.</p>
 *
 * @throws {@link ResourceNotFoundException} (client fault)
 *  <p>The specified resource Amazon Resource Name (ARN) was not found. Check the Amazon Resource Name (ARN) and try your request again.</p>
 *
 * @throws {@link ThrottlingException} (client fault)
 *  <p>The number of requests exceeds the limit. Resubmit your request later.</p>
 *
 * @throws {@link ValidationException} (client fault)
 *  <p>Input validation failed. Check your request parameters and retry the request.</p>
 *
 * @throws {@link BedrockAgentRuntimeServiceException}
 * <p>Base exception class for all service exceptions from BedrockAgentRuntime service.</p>
 *
 *
 * @public
 */
export declare class ListInvocationStepsCommand extends ListInvocationStepsCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: ListInvocationStepsRequest;
            output: ListInvocationStepsResponse;
        };
        sdk: {
            input: ListInvocationStepsCommandInput;
            output: ListInvocationStepsCommandOutput;
        };
    };
}
