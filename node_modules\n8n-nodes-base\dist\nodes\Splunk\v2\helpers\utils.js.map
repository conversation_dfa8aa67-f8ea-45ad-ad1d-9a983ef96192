{"version": 3, "sources": ["../../../../../nodes/Splunk/v2/helpers/utils.ts"], "sourcesContent": ["import type { IExecuteFunctions, IDataObject } from 'n8n-workflow';\nimport { parseString } from 'xml2js';\n\nimport type { SplunkError, SplunkFeedResponse } from './interfaces';\nimport { SPLUNK } from '../../v1/types';\n\nfunction compactEntryContent(splunkObject: any): any {\n\tif (typeof splunkObject !== 'object') {\n\t\treturn {};\n\t}\n\n\tif (Array.isArray(splunkObject)) {\n\t\treturn splunkObject.reduce((acc, cur) => {\n\t\t\tacc = { ...acc, ...compactEntryContent(cur) };\n\t\t\treturn acc;\n\t\t}, {});\n\t}\n\n\tif (splunkObject[SPLUNK.DICT]) {\n\t\tconst obj = splunkObject[SPLUNK.DICT][SPLUNK.KEY];\n\t\treturn { [splunkObject.$.name]: compactEntryContent(obj) };\n\t}\n\n\tif (splunkObject[SPLUNK.LIST]) {\n\t\tconst items = splunkObject[SPLUNK.LIST][SPLUNK.ITEM];\n\t\treturn { [splunkObject.$.name]: items };\n\t}\n\n\tif (splunkObject._) {\n\t\treturn {\n\t\t\t[splunkObject.$.name]: splunkObject._,\n\t\t};\n\t}\n\n\treturn {\n\t\t[splunkObject.$.name]: '',\n\t};\n}\n\nfunction formatEntryContent(content: any): any {\n\treturn content[SPLUNK.DICT][SPLUNK.KEY].reduce((acc: any, cur: any) => {\n\t\tacc = { ...acc, ...compactEntryContent(cur) };\n\t\treturn acc;\n\t}, {});\n}\n\nexport function formatEntry(entry: any, doNotFormatContent = false): any {\n\tconst { content, link, ...rest } = entry;\n\tconst formattedContent = doNotFormatContent ? content : formatEntryContent(content);\n\tconst formattedEntry = { ...rest, ...formattedContent };\n\n\tif (formattedEntry.id) {\n\t\tformattedEntry.entryUrl = formattedEntry.id;\n\t\tformattedEntry.id = formattedEntry.id.split('/').pop();\n\t}\n\n\treturn formattedEntry;\n}\n\nexport async function parseXml(xml: string) {\n\treturn await new Promise((resolve, reject) => {\n\t\tparseString(xml, { explicitArray: false }, (error, result) => {\n\t\t\terror ? reject(error) : resolve(result);\n\t\t});\n\t});\n}\n\nexport function extractErrorDescription(rawError: SplunkError) {\n\tconst messages = rawError.response?.messages;\n\treturn messages ? { [messages.msg.$.type.toLowerCase()]: messages.msg._ } : rawError;\n}\n\nexport function toUnixEpoch(timestamp: string) {\n\treturn Date.parse(timestamp) / 1000;\n}\n\nexport function formatFeed(responseData: SplunkFeedResponse) {\n\tconst { entry: entries } = responseData.feed;\n\n\tif (!entries) return [];\n\n\treturn Array.isArray(entries)\n\t\t? entries.map((entry) => formatEntry(entry))\n\t\t: [formatEntry(entries)];\n}\n\nexport function setReturnAllOrLimit(this: IExecuteFunctions, qs: IDataObject) {\n\tqs.count = this.getNodeParameter('returnAll', 0) ? 0 : this.getNodeParameter('limit', 0);\n}\n\nexport function populate(source: IDataObject, destination: IDataObject) {\n\tif (Object.keys(source).length) {\n\t\tObject.assign(destination, source);\n\t}\n}\n\nexport function getId(\n\tthis: IExecuteFunctions,\n\ti: number,\n\tidType: 'userId' | 'searchJobId' | 'searchConfigurationId',\n\tendpoint: string,\n) {\n\tconst id = this.getNodeParameter(idType, i) as string;\n\n\treturn id.includes(endpoint) ? id.split(endpoint).pop() : id;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA4B;AAG5B,mBAAuB;AAEvB,SAAS,oBAAoB,cAAwB;AACpD,MAAI,OAAO,iBAAiB,UAAU;AACrC,WAAO,CAAC;AAAA,EACT;AAEA,MAAI,MAAM,QAAQ,YAAY,GAAG;AAChC,WAAO,aAAa,OAAO,CAAC,KAAK,QAAQ;AACxC,YAAM,EAAE,GAAG,KAAK,GAAG,oBAAoB,GAAG,EAAE;AAC5C,aAAO;AAAA,IACR,GAAG,CAAC,CAAC;AAAA,EACN;AAEA,MAAI,aAAa,oBAAO,IAAI,GAAG;AAC9B,UAAM,MAAM,aAAa,oBAAO,IAAI,EAAE,oBAAO,GAAG;AAChD,WAAO,EAAE,CAAC,aAAa,EAAE,IAAI,GAAG,oBAAoB,GAAG,EAAE;AAAA,EAC1D;AAEA,MAAI,aAAa,oBAAO,IAAI,GAAG;AAC9B,UAAM,QAAQ,aAAa,oBAAO,IAAI,EAAE,oBAAO,IAAI;AACnD,WAAO,EAAE,CAAC,aAAa,EAAE,IAAI,GAAG,MAAM;AAAA,EACvC;AAEA,MAAI,aAAa,GAAG;AACnB,WAAO;AAAA,MACN,CAAC,aAAa,EAAE,IAAI,GAAG,aAAa;AAAA,IACrC;AAAA,EACD;AAEA,SAAO;AAAA,IACN,CAAC,aAAa,EAAE,IAAI,GAAG;AAAA,EACxB;AACD;AAEA,SAAS,mBAAmB,SAAmB;AAC9C,SAAO,QAAQ,oBAAO,IAAI,EAAE,oBAAO,GAAG,EAAE,OAAO,CAAC,KAAU,QAAa;AACtE,UAAM,EAAE,GAAG,KAAK,GAAG,oBAAoB,GAAG,EAAE;AAC5C,WAAO;AAAA,EACR,GAAG,CAAC,CAAC;AACN;AAEO,SAAS,YAAY,OAAY,qBAAqB,OAAY;AACxE,QAAM,EAAE,SAAS,MAAM,GAAG,KAAK,IAAI;AACnC,QAAM,mBAAmB,qBAAqB,UAAU,mBAAmB,OAAO;AAClF,QAAM,iBAAiB,EAAE,GAAG,MAAM,GAAG,iBAAiB;AAEtD,MAAI,eAAe,IAAI;AACtB,mBAAe,WAAW,eAAe;AACzC,mBAAe,KAAK,eAAe,GAAG,MAAM,GAAG,EAAE,IAAI;AAAA,EACtD;AAEA,SAAO;AACR;AAEA,eAAsB,SAAS,KAAa;AAC3C,SAAO,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7C,mCAAY,KAAK,EAAE,eAAe,MAAM,GAAG,CAAC,OAAO,WAAW;AAC7D,cAAQ,OAAO,KAAK,IAAI,QAAQ,MAAM;AAAA,IACvC,CAAC;AAAA,EACF,CAAC;AACF;AAEO,SAAS,wBAAwB,UAAuB;AAC9D,QAAM,WAAW,SAAS,UAAU;AACpC,SAAO,WAAW,EAAE,CAAC,SAAS,IAAI,EAAE,KAAK,YAAY,CAAC,GAAG,SAAS,IAAI,EAAE,IAAI;AAC7E;AAEO,SAAS,YAAY,WAAmB;AAC9C,SAAO,KAAK,MAAM,SAAS,IAAI;AAChC;AAEO,SAAS,WAAW,cAAkC;AAC5D,QAAM,EAAE,OAAO,QAAQ,IAAI,aAAa;AAExC,MAAI,CAAC,QAAS,QAAO,CAAC;AAEtB,SAAO,MAAM,QAAQ,OAAO,IACzB,QAAQ,IAAI,CAAC,UAAU,YAAY,KAAK,CAAC,IACzC,CAAC,YAAY,OAAO,CAAC;AACzB;AAEO,SAAS,oBAA6C,IAAiB;AAC7E,KAAG,QAAQ,KAAK,iBAAiB,aAAa,CAAC,IAAI,IAAI,KAAK,iBAAiB,SAAS,CAAC;AACxF;AAEO,SAAS,SAAS,QAAqB,aAA0B;AACvE,MAAI,OAAO,KAAK,MAAM,EAAE,QAAQ;AAC/B,WAAO,OAAO,aAAa,MAAM;AAAA,EAClC;AACD;AAEO,SAAS,MAEf,GACA,QACA,UACC;AACD,QAAM,KAAK,KAAK,iBAAiB,QAAQ,CAAC;AAE1C,SAAO,GAAG,SAAS,QAAQ,IAAI,GAAG,MAAM,QAAQ,EAAE,IAAI,IAAI;AAC3D;", "names": []}