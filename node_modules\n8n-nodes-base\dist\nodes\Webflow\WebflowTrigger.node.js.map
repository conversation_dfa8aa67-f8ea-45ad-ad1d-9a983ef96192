{"version": 3, "sources": ["../../../nodes/Webflow/WebflowTrigger.node.ts"], "sourcesContent": ["import type { INodeTypeBaseDescription, IVersionedNodeType } from 'n8n-workflow';\nimport { VersionedNodeType } from 'n8n-workflow';\n\nimport { WebflowTriggerV1 } from './V1/WebflowTriggerV1.node';\nimport { WebflowTriggerV2 } from './V2/WebflowTriggerV2.node';\n\nexport class WebflowTrigger extends VersionedNodeType {\n\tconstructor() {\n\t\tconst baseDescription: INodeTypeBaseDescription = {\n\t\t\tdisplayName: 'Webflow Trigger',\n\t\t\tname: 'webflowTrigger',\n\t\t\ticon: 'file:webflow.svg',\n\t\t\tgroup: ['trigger'],\n\t\t\tdescription: 'Handle Webflow events via webhooks',\n\t\t\tdefaultVersion: 2,\n\t\t};\n\n\t\tconst nodeVersions: IVersionedNodeType['nodeVersions'] = {\n\t\t\t1: new WebflowTriggerV1(baseDescription),\n\t\t\t2: new WebflowTriggerV2(baseDescription),\n\t\t};\n\n\t\tsuper(nodeVersions, baseDescription);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,0BAAkC;AAElC,8BAAiC;AACjC,8BAAiC;AAE1B,MAAM,uBAAuB,sCAAkB;AAAA,EACrD,cAAc;AACb,UAAM,kBAA4C;AAAA,MACjD,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,SAAS;AAAA,MACjB,aAAa;AAAA,MACb,gBAAgB;AAAA,IACjB;AAEA,UAAM,eAAmD;AAAA,MACxD,GAAG,IAAI,yCAAiB,eAAe;AAAA,MACvC,GAAG,IAAI,yCAAiB,eAAe;AAAA,IACxC;AAEA,UAAM,cAAc,eAAe;AAAA,EACpC;AACD;", "names": []}