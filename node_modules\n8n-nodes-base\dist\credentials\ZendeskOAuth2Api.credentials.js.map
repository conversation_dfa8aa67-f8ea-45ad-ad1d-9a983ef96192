{"version": 3, "sources": ["../../credentials/ZendeskOAuth2Api.credentials.ts"], "sourcesContent": ["import type { ICredentialType, INodeProperties } from 'n8n-workflow';\n\nconst scopes = ['read', 'write'];\n\nexport class ZendeskOAuth2Api implements ICredentialType {\n\tname = 'zendeskOAuth2Api';\n\n\textends = ['oAuth2Api'];\n\n\tdisplayName = 'Zendesk OAuth2 API';\n\n\tdocumentationUrl = 'zendesk';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'Subdomain',\n\t\t\tname: 'subdomain',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\tplaceholder: 'n8n',\n\t\t\tdescription: 'The subdomain of your Zendesk work environment',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Grant Type',\n\t\t\tname: 'grantType',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: 'authorizationCode',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Authorization URL',\n\t\t\tname: 'authUrl',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: '=https://{{$self[\"subdomain\"]}}.zendesk.com/oauth/authorizations/new',\n\t\t\tdescription: 'URL to get authorization code. Replace {SUBDOMAIN_HERE} with your subdomain.',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Access Token URL',\n\t\t\tname: 'accessTokenUrl',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: '=https://{{$self[\"subdomain\"]}}.zendesk.com/oauth/tokens',\n\t\t\tdescription: 'URL to get access token. Replace {SUBDOMAIN_HERE} with your subdomain.',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Client ID',\n\t\t\tname: 'clientId',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Client Secret',\n\t\t\tname: 'clientSecret',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\tdefault: '',\n\t\t\trequired: true,\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Scope',\n\t\t\tname: 'scope',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: scopes.join(' '),\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Auth URI Query Parameters',\n\t\t\tname: 'authQueryParameters',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: '',\n\t\t\tdescription:\n\t\t\t\t'For some services additional query parameters have to be set which can be defined here',\n\t\t\tplaceholder: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Authentication',\n\t\t\tname: 'authentication',\n\t\t\ttype: 'hidden',\n\t\t\tdefault: 'body',\n\t\t},\n\t];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,MAAM,SAAS,CAAC,QAAQ,OAAO;AAExB,MAAM,iBAA4C;AAAA,EAAlD;AACN,gBAAO;AAEP,mBAAU,CAAC,WAAW;AAEtB,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,UAAU;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,UAAU;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,UAAU;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,QACT,UAAU;AAAA,MACX;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,OAAO,KAAK,GAAG;AAAA,MACzB;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aACC;AAAA,QACD,aAAa;AAAA,MACd;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,IACD;AAAA;AACD;", "names": []}