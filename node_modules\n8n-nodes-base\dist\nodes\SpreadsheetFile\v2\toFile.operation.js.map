{"version": 3, "sources": ["../../../../nodes/SpreadsheetFile/v2/toFile.operation.ts"], "sourcesContent": ["import type { IExecuteFunctions, INodeExecutionData, INodeProperties } from 'n8n-workflow';\n\nimport type { JsonToSpreadsheetBinaryFormat, JsonToSpreadsheetBinaryOptions } from '@utils/binary';\nimport { convertJsonToSpreadsheetBinary } from '@utils/binary';\nimport { generatePairedItemData } from '@utils/utilities';\n\nimport { toFileOptions, toFileProperties } from '../description';\n\nexport const description: INodeProperties[] = [...toFileProperties, toFileOptions];\n\nexport async function execute(this: IExecuteFunctions, items: INodeExecutionData[]) {\n\tconst returnData: INodeExecutionData[] = [];\n\n\tconst pairedItem = generatePairedItemData(items.length);\n\n\ttry {\n\t\tconst binaryPropertyName = this.getNodeParameter('binaryPropertyName', 0);\n\t\tconst fileFormat = this.getNodeParameter('fileFormat', 0) as JsonToSpreadsheetBinaryFormat;\n\t\tconst options = this.getNodeParameter('options', 0, {}) as JsonToSpreadsheetBinaryOptions;\n\n\t\tconst binaryData = await convertJsonToSpreadsheetBinary.call(this, items, fileFormat, options);\n\n\t\tconst newItem: INodeExecutionData = {\n\t\t\tjson: {},\n\t\t\tbinary: {\n\t\t\t\t[binaryPropertyName]: binaryData,\n\t\t\t},\n\t\t\tpairedItem,\n\t\t};\n\n\t\treturnData.push(newItem);\n\t} catch (error) {\n\t\tif (this.continueOnFail()) {\n\t\t\treturnData.push({\n\t\t\t\tjson: {\n\t\t\t\t\terror: error.message,\n\t\t\t\t},\n\t\t\t\tpairedItem,\n\t\t\t});\n\t\t} else {\n\t\t\tthrow error;\n\t\t}\n\t}\n\treturn returnData;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,oBAA+C;AAC/C,uBAAuC;AAEvC,yBAAgD;AAEzC,MAAM,cAAiC,CAAC,GAAG,qCAAkB,gCAAa;AAEjF,eAAsB,QAAiC,OAA6B;AACnF,QAAM,aAAmC,CAAC;AAE1C,QAAM,iBAAa,yCAAuB,MAAM,MAAM;AAEtD,MAAI;AACH,UAAM,qBAAqB,KAAK,iBAAiB,sBAAsB,CAAC;AACxE,UAAM,aAAa,KAAK,iBAAiB,cAAc,CAAC;AACxD,UAAM,UAAU,KAAK,iBAAiB,WAAW,GAAG,CAAC,CAAC;AAEtD,UAAM,aAAa,MAAM,6CAA+B,KAAK,MAAM,OAAO,YAAY,OAAO;AAE7F,UAAM,UAA8B;AAAA,MACnC,MAAM,CAAC;AAAA,MACP,QAAQ;AAAA,QACP,CAAC,kBAAkB,GAAG;AAAA,MACvB;AAAA,MACA;AAAA,IACD;AAEA,eAAW,KAAK,OAAO;AAAA,EACxB,SAAS,OAAO;AACf,QAAI,KAAK,eAAe,GAAG;AAC1B,iBAAW,KAAK;AAAA,QACf,MAAM;AAAA,UACL,OAAO,MAAM;AAAA,QACd;AAAA,QACA;AAAA,MACD,CAAC;AAAA,IACF,OAAO;AACN,YAAM;AAAA,IACP;AAAA,EACD;AACA,SAAO;AACR;", "names": []}