{"version": 3, "sources": ["../../../../../../nodes/Aws/IAM/descriptions/user/User.resource.ts"], "sourcesContent": ["import type { INodeProperties } from 'n8n-workflow';\n\nimport * as addToGroup from './addToGroup.operation';\nimport * as create from './create.operation';\nimport * as del from './delete.operation';\nimport * as get from './get.operation';\nimport * as getAll from './getAll.operation';\nimport * as removeFromGroup from './removeFromGroup.operation';\nimport * as update from './update.operation';\nimport { CURRENT_VERSION } from '../../helpers/constants';\nimport { handleError } from '../../helpers/errorHandler';\nimport { removeUserFromGroups, simplifyGetAllUsersResponse } from '../../helpers/utils';\n\nexport const description: INodeProperties[] = [\n\t{\n\t\tdisplayName: 'Operation',\n\t\tname: 'operation',\n\t\ttype: 'options',\n\t\tnoDataExpression: true,\n\t\tdefault: 'getAll',\n\t\tdisplayOptions: {\n\t\t\tshow: {\n\t\t\t\tresource: ['user'],\n\t\t\t},\n\t\t},\n\t\toptions: [\n\t\t\t{\n\t\t\t\tname: 'Add to Group',\n\t\t\t\tvalue: 'addToGroup',\n\t\t\t\tdescription: 'Add an existing user to a group',\n\t\t\t\taction: 'Add user to group',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\turl: '',\n\t\t\t\t\t\tbody: {\n\t\t\t\t\t\t\tAction: 'AddUserToGroup',\n\t\t\t\t\t\t\tVersion: CURRENT_VERSION,\n\t\t\t\t\t\t\tUserName: '={{ $parameter[\"user\"] }}',\n\t\t\t\t\t\t\tGroupName: '={{ $parameter[\"group\"] }}',\n\t\t\t\t\t\t},\n\t\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [handleError],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Create',\n\t\t\t\tvalue: 'create',\n\t\t\t\tdescription: 'Create a new user',\n\t\t\t\taction: 'Create user',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\turl: '',\n\t\t\t\t\t\tbody: {\n\t\t\t\t\t\t\tAction: 'CreateUser',\n\t\t\t\t\t\t\tVersion: CURRENT_VERSION,\n\t\t\t\t\t\t\tUserName: '={{ $parameter[\"userName\"] }}',\n\t\t\t\t\t\t},\n\t\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [handleError],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Delete',\n\t\t\t\tvalue: 'delete',\n\t\t\t\tdescription: 'Delete a user',\n\t\t\t\taction: 'Delete user',\n\t\t\t\trouting: {\n\t\t\t\t\tsend: {\n\t\t\t\t\t\tpreSend: [removeUserFromGroups],\n\t\t\t\t\t},\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\turl: '',\n\t\t\t\t\t\tbody: {\n\t\t\t\t\t\t\tAction: 'DeleteUser',\n\t\t\t\t\t\t\tVersion: CURRENT_VERSION,\n\t\t\t\t\t\t\tUserName: '={{ $parameter[\"user\"] }}',\n\t\t\t\t\t\t},\n\t\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [handleError],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get',\n\t\t\t\tvalue: 'get',\n\t\t\t\tdescription: 'Retrieve a user',\n\t\t\t\taction: 'Get user',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\turl: '',\n\t\t\t\t\t\tbody: {\n\t\t\t\t\t\t\tAction: 'GetUser',\n\t\t\t\t\t\t\tVersion: CURRENT_VERSION,\n\t\t\t\t\t\t\tUserName: '={{ $parameter[\"user\"] }}',\n\t\t\t\t\t\t},\n\t\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\ttype: 'rootProperty',\n\t\t\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\t\t\tproperty: 'GetUserResponse.GetUserResult.User',\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\thandleError,\n\t\t\t\t\t\t],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Get Many',\n\t\t\t\tvalue: 'getAll',\n\t\t\t\tdescription: 'Retrieve a list of users',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\turl: '',\n\t\t\t\t\t\tbody: {\n\t\t\t\t\t\t\tAction: 'ListUsers',\n\t\t\t\t\t\t\tVersion: CURRENT_VERSION,\n\t\t\t\t\t\t},\n\t\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [handleError, simplifyGetAllUsersResponse],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\taction: 'Get many users',\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Remove From Group',\n\t\t\t\tvalue: 'removeFromGroup',\n\t\t\t\tdescription: 'Remove a user from a group',\n\t\t\t\taction: 'Remove user from group',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\turl: '',\n\t\t\t\t\t\tbody: {\n\t\t\t\t\t\t\tAction: 'RemoveUserFromGroup',\n\t\t\t\t\t\t\tVersion: CURRENT_VERSION,\n\t\t\t\t\t\t\tUserName: '={{ $parameter[\"user\"] }}',\n\t\t\t\t\t\t\tGroupName: '={{ $parameter[\"group\"] }}',\n\t\t\t\t\t\t},\n\t\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [handleError],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'Update',\n\t\t\t\tvalue: 'update',\n\t\t\t\tdescription: 'Update a user',\n\t\t\t\taction: 'Update user',\n\t\t\t\trouting: {\n\t\t\t\t\trequest: {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\turl: '',\n\t\t\t\t\t\tbody: {\n\t\t\t\t\t\t\tAction: 'UpdateUser',\n\t\t\t\t\t\t\tVersion: CURRENT_VERSION,\n\t\t\t\t\t\t\tNewUserName: '={{ $parameter[\"userName\"] }}',\n\t\t\t\t\t\t\tUserName: '={{ $parameter[\"user\"] }}',\n\t\t\t\t\t\t},\n\t\t\t\t\t\tignoreHttpStatusErrors: true,\n\t\t\t\t\t},\n\t\t\t\t\toutput: {\n\t\t\t\t\t\tpostReceive: [handleError],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t],\n\t},\n\n\t...addToGroup.description,\n\t...create.description,\n\t...del.description,\n\t...get.description,\n\t...getAll.description,\n\t...update.description,\n\t...removeFromGroup.description,\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,iBAA4B;AAC5B,aAAwB;AACxB,UAAqB;AACrB,UAAqB;AACrB,aAAwB;AACxB,sBAAiC;AACjC,aAAwB;AACxB,uBAAgC;AAChC,0BAA4B;AAC5B,mBAAkE;AAE3D,MAAM,cAAiC;AAAA,EAC7C;AAAA,IACC,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,MAAM;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS;AAAA,MACR;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,MAAM;AAAA,cACL,QAAQ;AAAA,cACR,SAAS;AAAA,cACT,UAAU;AAAA,cACV,WAAW;AAAA,YACZ;AAAA,YACA,wBAAwB;AAAA,UACzB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa,CAAC,+BAAW;AAAA,UAC1B;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,MAAM;AAAA,cACL,QAAQ;AAAA,cACR,SAAS;AAAA,cACT,UAAU;AAAA,YACX;AAAA,YACA,wBAAwB;AAAA,UACzB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa,CAAC,+BAAW;AAAA,UAC1B;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,SAAS;AAAA,UACR,MAAM;AAAA,YACL,SAAS,CAAC,iCAAoB;AAAA,UAC/B;AAAA,UACA,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,MAAM;AAAA,cACL,QAAQ;AAAA,cACR,SAAS;AAAA,cACT,UAAU;AAAA,YACX;AAAA,YACA,wBAAwB;AAAA,UACzB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa,CAAC,+BAAW;AAAA,UAC1B;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,MAAM;AAAA,cACL,QAAQ;AAAA,cACR,SAAS;AAAA,cACT,UAAU;AAAA,YACX;AAAA,YACA,wBAAwB;AAAA,UACzB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa;AAAA,cACZ;AAAA,gBACC,MAAM;AAAA,gBACN,YAAY;AAAA,kBACX,UAAU;AAAA,gBACX;AAAA,cACD;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,MAAM;AAAA,cACL,QAAQ;AAAA,cACR,SAAS;AAAA,YACV;AAAA,YACA,wBAAwB;AAAA,UACzB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa,CAAC,iCAAa,wCAA2B;AAAA,UACvD;AAAA,QACD;AAAA,QACA,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,MAAM;AAAA,cACL,QAAQ;AAAA,cACR,SAAS;AAAA,cACT,UAAU;AAAA,cACV,WAAW;AAAA,YACZ;AAAA,YACA,wBAAwB;AAAA,UACzB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa,CAAC,+BAAW;AAAA,UAC1B;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,SAAS;AAAA,UACR,SAAS;AAAA,YACR,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,MAAM;AAAA,cACL,QAAQ;AAAA,cACR,SAAS;AAAA,cACT,aAAa;AAAA,cACb,UAAU;AAAA,YACX;AAAA,YACA,wBAAwB;AAAA,UACzB;AAAA,UACA,QAAQ;AAAA,YACP,aAAa,CAAC,+BAAW;AAAA,UAC1B;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EAEA,GAAG,WAAW;AAAA,EACd,GAAG,OAAO;AAAA,EACV,GAAG,IAAI;AAAA,EACP,GAAG,IAAI;AAAA,EACP,GAAG,OAAO;AAAA,EACV,GAAG,OAAO;AAAA,EACV,GAAG,gBAAgB;AACpB;", "names": []}