import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  BedrockAgentRuntimeClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../BedrockAgentRuntimeClient";
import { RetrieveAndGenerateResponse } from "../models/models_0";
import { RetrieveAndGenerateRequest } from "../models/models_1";
export { __MetadataBearer };
export { $Command };
export interface RetrieveAndGenerateCommandInput
  extends RetrieveAndGenerateRequest {}
export interface RetrieveAndGenerateCommandOutput
  extends RetrieveAndGenerateResponse,
    __MetadataBearer {}
declare const RetrieveAndGenerateCommand_base: {
  new (
    input: RetrieveAndGenerateCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    RetrieveAndGenerateCommandInput,
    RetrieveAndGenerateCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: RetrieveAndGenerateCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    RetrieveAndGenerateCommandInput,
    RetrieveAndGenerateCommandOutput,
    BedrockAgentRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class RetrieveAndGenerateCommand extends RetrieveAndGenerateCommand_base {
  protected static __types: {
    api: {
      input: RetrieveAndGenerateRequest;
      output: RetrieveAndGenerateResponse;
    };
    sdk: {
      input: RetrieveAndGenerateCommandInput;
      output: RetrieveAndGenerateCommandOutput;
    };
  };
}
