{"version": 3, "sources": ["../../../../nodes/Venafi/ProtectCloud/VenafiTlsProtectCloudTrigger.node.ts"], "sourcesContent": ["import {\n\tNodeConnectionTypes,\n\ttype IHookFunctions,\n\ttype ILoadOptionsFunctions,\n\ttype INodePropertyOptions,\n\ttype INodeType,\n\ttype INodeTypeDescription,\n\ttype IWebhookFunctions,\n\ttype IWebhookResponseData,\n} from 'n8n-workflow';\n\nimport { venafiApiRequest } from './GenericFunctions';\n\nexport class VenafiTlsProtectCloudTrigger implements INodeType {\n\tdescription: INodeTypeDescription = {\n\t\tdisplayName: 'Venafi TLS Protect Cloud Trigger',\n\t\tname: 'venafiTlsProtectCloudTrigger',\n\t\ticon: 'file:../venafi.svg',\n\t\tgroup: ['trigger'],\n\t\tversion: 1,\n\t\tdescription: 'Starts the workflow when Venafi events occur',\n\t\tdefaults: {\n\t\t\tname: 'Venafi TLS Protect Cloud Trigger',\n\t\t},\n\t\tcredentials: [\n\t\t\t{\n\t\t\t\tname: 'venafiTlsProtectCloudApi',\n\t\t\t\trequired: true,\n\t\t\t},\n\t\t],\n\t\twebhooks: [\n\t\t\t{\n\t\t\t\tname: 'default',\n\t\t\t\thttpMethod: 'POST',\n\t\t\t\tresponseMode: 'onReceived',\n\t\t\t\tpath: 'webhook',\n\t\t\t},\n\t\t],\n\t\tinputs: [],\n\t\toutputs: [NodeConnectionTypes.Main],\n\t\tproperties: [\n\t\t\t{\n\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-wrong-for-dynamic-options\n\t\t\t\tdisplayName: 'Resource',\n\t\t\t\tname: 'resource',\n\t\t\t\ttype: 'options',\n\t\t\t\tnoDataExpression: true,\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getActivityTypes',\n\t\t\t\t},\n\t\t\t\trequired: true,\n\t\t\t\tdefault: [],\n\t\t\t\tdescription:\n\t\t\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t\t{\n\t\t\t\t// eslint-disable-next-line n8n-nodes-base/node-param-display-name-wrong-for-dynamic-multi-options\n\t\t\t\tdisplayName: 'Trigger On',\n\t\t\t\tname: 'triggerOn',\n\t\t\t\ttype: 'multiOptions',\n\t\t\t\ttypeOptions: {\n\t\t\t\t\tloadOptionsMethod: 'getActivitySubTypes',\n\t\t\t\t\tloadOptionsDependsOn: ['resource'],\n\t\t\t\t},\n\t\t\t\trequired: true,\n\t\t\t\tdefault: [],\n\t\t\t\tdescription:\n\t\t\t\t\t'Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>. Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>. Choose from the list, or specify IDs using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>.',\n\t\t\t},\n\t\t],\n\t};\n\n\tmethods = {\n\t\tloadOptions: {\n\t\t\tasync getActivityTypes(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst activitytypes = await venafiApiRequest.call(this, 'GET', '/v1/activitytypes');\n\t\t\t\treturn activitytypes.map(\n\t\t\t\t\t({ key, readableName }: { key: string; readableName: string }) => ({\n\t\t\t\t\t\tname: readableName,\n\t\t\t\t\t\tvalue: key,\n\t\t\t\t\t}),\n\t\t\t\t);\n\t\t\t},\n\t\t\tasync getActivitySubTypes(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {\n\t\t\t\tconst resource = this.getCurrentNodeParameter('resource') as string;\n\t\t\t\tconst activitytypes = await venafiApiRequest.call(this, 'GET', '/v1/activitytypes');\n\t\t\t\tconst activity = activitytypes.find(({ key }: { key: string }) => key === resource) as {\n\t\t\t\t\tvalues: [{ key: string; readableName: string }];\n\t\t\t\t};\n\t\t\t\tconst subActivities = activity.values.map(({ key, readableName }) => ({\n\t\t\t\t\tname: readableName,\n\t\t\t\t\tvalue: key,\n\t\t\t\t}));\n\t\t\t\tsubActivities.unshift({ name: '[All]', value: '*' });\n\t\t\t\treturn subActivities;\n\t\t\t},\n\t\t},\n\t};\n\n\twebhookMethods = {\n\t\tdefault: {\n\t\t\tasync checkExists(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\n\t\t\t\tconst { connectors } = await venafiApiRequest.call(this, 'GET', '/v1/connectors');\n\n\t\t\t\tfor (const connector of connectors) {\n\t\t\t\t\tconst {\n\t\t\t\t\t\tid,\n\t\t\t\t\t\tstatus,\n\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\ttarget: {\n\t\t\t\t\t\t\t\tconnection: { url },\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t} = connector;\n\n\t\t\t\t\tif (url === webhookUrl && status === 'Active') {\n\t\t\t\t\t\tawait venafiApiRequest.call(this, 'DELETE', `/v1/connectors/${id}`);\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tasync create(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookUrl = this.getNodeWebhookUrl('default');\n\t\t\t\tconst resource = this.getNodeParameter('resource') as string;\n\t\t\t\tconst body = {\n\t\t\t\t\tname: `n8n-webhook (${webhookUrl})`,\n\t\t\t\t\tproperties: {\n\t\t\t\t\t\tconnectorKind: 'WEBHOOK',\n\t\t\t\t\t\ttarget: {\n\t\t\t\t\t\t\ttype: 'generic',\n\t\t\t\t\t\t\tconnection: {\n\t\t\t\t\t\t\t\turl: webhookUrl,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfilter: {\n\t\t\t\t\t\t\tactivityTypes: [resource],\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t};\n\n\t\t\t\tconst responseData = await venafiApiRequest.call(this, 'POST', '/v1/connectors', body);\n\n\t\t\t\tif (responseData.id === undefined) {\n\t\t\t\t\t// Required data is missing so was not successful\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\t\t\t\twebhookData.webhookId = responseData.id as string;\n\n\t\t\t\treturn true;\n\t\t\t},\n\t\t\tasync delete(this: IHookFunctions): Promise<boolean> {\n\t\t\t\tconst webhookData = this.getWorkflowStaticData('node');\n\n\t\t\t\tif (webhookData.webhookId !== undefined) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tawait venafiApiRequest.call(this, 'DELETE', `/v1/connectors/${webhookData.webhookId}`);\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\n\t\t\t\t\t// Remove from the static workflow data so that it is clear\n\t\t\t\t\t// that no webhooks are registered anymore\n\t\t\t\t\tdelete webhookData.webhookId;\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\t\t\t},\n\t\t},\n\t};\n\n\tasync webhook(this: IWebhookFunctions): Promise<IWebhookResponseData> {\n\t\tconst { events } = this.getBodyData() as { events: [{ message: string; eventName: string }] };\n\t\tconst triggerOn = this.getNodeParameter('triggerOn') as string;\n\n\t\tif (Array.isArray(events) && events[0]?.message?.includes('TESTING CONNECTION...')) {\n\t\t\t// Is a create webhook confirmation request\n\t\t\tconst res = this.getResponseObject();\n\t\t\tres.status(200).end();\n\t\t\treturn {\n\t\t\t\tnoWebhookResponse: true,\n\t\t\t};\n\t\t}\n\n\t\tif (!triggerOn.includes('*') && !triggerOn.includes(events[0]?.eventName)) return {};\n\n\t\treturn {\n\t\t\tworkflowData: [this.helpers.returnJsonArray(events)],\n\t\t};\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BASO;AAEP,8BAAiC;AAE1B,MAAM,6BAAkD;AAAA,EAAxD;AACN,uBAAoC;AAAA,MACnC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO,CAAC,SAAS;AAAA,MACjB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACT,MAAM;AAAA,MACP;AAAA,MACA,aAAa;AAAA,QACZ;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA,UAAU;AAAA,QACT;AAAA,UACC,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,MAAM;AAAA,QACP;AAAA,MACD;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC,wCAAoB,IAAI;AAAA,MAClC,YAAY;AAAA,QACX;AAAA;AAAA,UAEC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,aAAa;AAAA,YACZ,mBAAmB;AAAA,UACpB;AAAA,UACA,UAAU;AAAA,UACV,SAAS,CAAC;AAAA,UACV,aACC;AAAA,QACF;AAAA,QACA;AAAA;AAAA,UAEC,aAAa;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,YACZ,mBAAmB;AAAA,YACnB,sBAAsB,CAAC,UAAU;AAAA,UAClC;AAAA,UACA,UAAU;AAAA,UACV,SAAS,CAAC;AAAA,UACV,aACC;AAAA,QACF;AAAA,MACD;AAAA,IACD;AAEA,mBAAU;AAAA,MACT,aAAa;AAAA,QACZ,MAAM,mBAA+E;AACpF,gBAAM,gBAAgB,MAAM,yCAAiB,KAAK,MAAM,OAAO,mBAAmB;AAClF,iBAAO,cAAc;AAAA,YACpB,CAAC,EAAE,KAAK,aAAa,OAA8C;AAAA,cAClE,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AAAA,UACD;AAAA,QACD;AAAA,QACA,MAAM,sBAAkF;AACvF,gBAAM,WAAW,KAAK,wBAAwB,UAAU;AACxD,gBAAM,gBAAgB,MAAM,yCAAiB,KAAK,MAAM,OAAO,mBAAmB;AAClF,gBAAM,WAAW,cAAc,KAAK,CAAC,EAAE,IAAI,MAAuB,QAAQ,QAAQ;AAGlF,gBAAM,gBAAgB,SAAS,OAAO,IAAI,CAAC,EAAE,KAAK,aAAa,OAAO;AAAA,YACrE,MAAM;AAAA,YACN,OAAO;AAAA,UACR,EAAE;AACF,wBAAc,QAAQ,EAAE,MAAM,SAAS,OAAO,IAAI,CAAC;AACnD,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAEA,0BAAiB;AAAA,MAChB,SAAS;AAAA,QACR,MAAM,cAAoD;AACzD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AAEnD,gBAAM,EAAE,WAAW,IAAI,MAAM,yCAAiB,KAAK,MAAM,OAAO,gBAAgB;AAEhF,qBAAW,aAAa,YAAY;AACnC,kBAAM;AAAA,cACL;AAAA,cACA;AAAA,cACA,YAAY;AAAA,gBACX,QAAQ;AAAA,kBACP,YAAY,EAAE,IAAI;AAAA,gBACnB;AAAA,cACD;AAAA,YACD,IAAI;AAEJ,gBAAI,QAAQ,cAAc,WAAW,UAAU;AAC9C,oBAAM,yCAAiB,KAAK,MAAM,UAAU,kBAAkB,EAAE,EAAE;AAClE,qBAAO;AAAA,YACR;AAAA,UACD;AACA,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,aAAa,KAAK,kBAAkB,SAAS;AACnD,gBAAM,WAAW,KAAK,iBAAiB,UAAU;AACjD,gBAAM,OAAO;AAAA,YACZ,MAAM,gBAAgB,UAAU;AAAA,YAChC,YAAY;AAAA,cACX,eAAe;AAAA,cACf,QAAQ;AAAA,gBACP,MAAM;AAAA,gBACN,YAAY;AAAA,kBACX,KAAK;AAAA,gBACN;AAAA,cACD;AAAA,cACA,QAAQ;AAAA,gBACP,eAAe,CAAC,QAAQ;AAAA,cACzB;AAAA,YACD;AAAA,UACD;AAEA,gBAAM,eAAe,MAAM,yCAAiB,KAAK,MAAM,QAAQ,kBAAkB,IAAI;AAErF,cAAI,aAAa,OAAO,QAAW;AAElC,mBAAO;AAAA,UACR;AAEA,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,sBAAY,YAAY,aAAa;AAErC,iBAAO;AAAA,QACR;AAAA,QACA,MAAM,SAA+C;AACpD,gBAAM,cAAc,KAAK,sBAAsB,MAAM;AAErD,cAAI,YAAY,cAAc,QAAW;AACxC,gBAAI;AACH,oBAAM,yCAAiB,KAAK,MAAM,UAAU,kBAAkB,YAAY,SAAS,EAAE;AAAA,YACtF,SAAS,OAAO;AACf,qBAAO;AAAA,YACR;AAIA,mBAAO,YAAY;AAAA,UACpB;AAEA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA;AAAA,EAEA,MAAM,UAAgE;AACrE,UAAM,EAAE,OAAO,IAAI,KAAK,YAAY;AACpC,UAAM,YAAY,KAAK,iBAAiB,WAAW;AAEnD,QAAI,MAAM,QAAQ,MAAM,KAAK,OAAO,CAAC,GAAG,SAAS,SAAS,uBAAuB,GAAG;AAEnF,YAAM,MAAM,KAAK,kBAAkB;AACnC,UAAI,OAAO,GAAG,EAAE,IAAI;AACpB,aAAO;AAAA,QACN,mBAAmB;AAAA,MACpB;AAAA,IACD;AAEA,QAAI,CAAC,UAAU,SAAS,GAAG,KAAK,CAAC,UAAU,SAAS,OAAO,CAAC,GAAG,SAAS,EAAG,QAAO,CAAC;AAEnF,WAAO;AAAA,MACN,cAAc,CAAC,KAAK,QAAQ,gBAAgB,MAAM,CAAC;AAAA,IACpD;AAAA,EACD;AACD;", "names": []}