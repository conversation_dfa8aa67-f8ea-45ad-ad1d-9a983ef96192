{"version": 3, "sources": ["../../credentials/WufooApi.credentials.ts"], "sourcesContent": ["import type {\n\tIAuthenticateGeneric,\n\tICredentialTestRequest,\n\tICredentialType,\n\tINodeProperties,\n} from 'n8n-workflow';\n\nexport class WufooApi implements ICredentialType {\n\tname = 'wufooApi';\n\n\tdisplayName = 'Wufoo API';\n\n\tdocumentationUrl = 'wufoo';\n\n\tproperties: INodeProperties[] = [\n\t\t{\n\t\t\tdisplayName: 'API Key',\n\t\t\tname: 'apiKey',\n\t\t\ttype: 'string',\n\t\t\ttypeOptions: { password: true },\n\t\t\tdefault: '',\n\t\t},\n\t\t{\n\t\t\tdisplayName: 'Subdomain',\n\t\t\tname: 'subdomain',\n\t\t\ttype: 'string',\n\t\t\tdefault: '',\n\t\t},\n\t];\n\n\tauthenticate: IAuthenticateGeneric = {\n\t\ttype: 'generic',\n\t\tproperties: {\n\t\t\tauth: {\n\t\t\t\tusername: '={{$credentials.apiKey}}',\n\t\t\t\tpassword: 'not-needed',\n\t\t\t},\n\t\t},\n\t};\n\n\ttest: ICredentialTestRequest = {\n\t\trequest: {\n\t\t\tbaseURL: '=https://{{$credentials.subdomain}}.wufoo.com',\n\t\t\turl: '/api/v3/forms.json',\n\t\t},\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOO,MAAM,SAAoC;AAAA,EAA1C;AACN,gBAAO;AAEP,uBAAc;AAEd,4BAAmB;AAEnB,sBAAgC;AAAA,MAC/B;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa,EAAE,UAAU,KAAK;AAAA,QAC9B,SAAS;AAAA,MACV;AAAA,MACA;AAAA,QACC,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,IACD;AAEA,wBAAqC;AAAA,MACpC,MAAM;AAAA,MACN,YAAY;AAAA,QACX,MAAM;AAAA,UACL,UAAU;AAAA,UACV,UAAU;AAAA,QACX;AAAA,MACD;AAAA,IACD;AAEA,gBAA+B;AAAA,MAC9B,SAAS;AAAA,QACR,SAAS;AAAA,QACT,KAAK;AAAA,MACN;AAAA,IACD;AAAA;AACD;", "names": []}