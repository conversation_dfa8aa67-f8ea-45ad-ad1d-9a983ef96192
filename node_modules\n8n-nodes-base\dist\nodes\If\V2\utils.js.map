{"version": 3, "sources": ["../../../../nodes/If/V2/utils.ts"], "sourcesContent": ["import type { IExecuteFunctions } from 'n8n-workflow';\n\nexport const getTypeValidationStrictness = (version: number) => {\n\treturn `={{ ($nodeVersion < ${version} ? $parameter.options.looseTypeValidation :  $parameter.looseTypeValidation) ? \"loose\" : \"strict\" }}`;\n};\n\nexport const getTypeValidationParameter = (version: number) => {\n\treturn (context: IExecuteFunctions, itemIndex: number, option: boolean | undefined) => {\n\t\tif (context.getNode().typeVersion < version) {\n\t\t\treturn option;\n\t\t} else {\n\t\t\treturn context.getNodeParameter('looseTypeValidation', itemIndex, false) as boolean;\n\t\t}\n\t};\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,8BAA8B,CAAC,YAAoB;AAC/D,SAAO,uBAAuB,OAAO;AACtC;AAEO,MAAM,6BAA6B,CAAC,YAAoB;AAC9D,SAAO,CAAC,SAA4B,WAAmB,WAAgC;AACtF,QAAI,QAAQ,QAAQ,EAAE,cAAc,SAAS;AAC5C,aAAO;AAAA,IACR,OAAO;AACN,aAAO,QAAQ,iBAAiB,uBAAuB,WAAW,KAAK;AAAA,IACxE;AAAA,EACD;AACD;", "names": []}