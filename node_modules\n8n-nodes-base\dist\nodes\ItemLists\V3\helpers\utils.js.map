{"version": 3, "sources": ["../../../../../nodes/ItemLists/V3/helpers/utils.ts"], "sourcesContent": ["import { NodeVM } from '@n8n/vm2';\nimport type {\n\tIExecuteFunctions,\n\tIBinaryData,\n\tINodeExecutionData,\n\tGenericValue,\n} from 'n8n-workflow';\nimport { ApplicationError, NodeOperationError } from 'n8n-workflow';\n\nexport const prepareFieldsArray = (fields: string | string[], fieldName = 'Fields') => {\n\tif (typeof fields === 'string') {\n\t\treturn fields\n\t\t\t.split(',')\n\t\t\t.map((entry) => entry.trim())\n\t\t\t.filter((entry) => entry !== '');\n\t}\n\tif (Array.isArray(fields)) {\n\t\treturn fields;\n\t}\n\tthrow new ApplicationError(\n\t\t`The \\'${fieldName}\\' parameter must be a string of fields separated by commas or an array of strings.`,\n\t\t{ level: 'warning' },\n\t);\n};\n\nconst returnRegExp = /\\breturn\\b/g;\n\nexport function sortByCode(\n\tthis: IExecuteFunctions,\n\titems: INodeExecutionData[],\n): INodeExecutionData[] {\n\tconst code = this.getNodeParameter('code', 0) as string;\n\tif (!returnRegExp.test(code)) {\n\t\tthrow new NodeOperationError(\n\t\t\tthis.getNode(),\n\t\t\t\"Sort code doesn't return. Please add a 'return' statement to your code\",\n\t\t);\n\t}\n\n\tconst mode = this.getMode();\n\tconst vm = new NodeVM({\n\t\tconsole: mode === 'manual' ? 'redirect' : 'inherit',\n\t\tsandbox: { items },\n\t});\n\n\treturn vm.run(`module.exports = items.sort((a, b) => { ${code} })`);\n}\n\ntype PartialBinaryData = Omit<IBinaryData, 'data'>;\nconst isBinaryUniqueSetup = () => {\n\tconst binaries: PartialBinaryData[] = [];\n\treturn (binary: IBinaryData) => {\n\t\tfor (const existingBinary of binaries) {\n\t\t\tif (\n\t\t\t\texistingBinary.mimeType === binary.mimeType &&\n\t\t\t\texistingBinary.fileType === binary.fileType &&\n\t\t\t\texistingBinary.fileSize === binary.fileSize &&\n\t\t\t\texistingBinary.fileExtension === binary.fileExtension\n\t\t\t) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tbinaries.push({\n\t\t\tmimeType: binary.mimeType,\n\t\t\tfileType: binary.fileType,\n\t\t\tfileSize: binary.fileSize,\n\t\t\tfileExtension: binary.fileExtension,\n\t\t});\n\n\t\treturn true;\n\t};\n};\n\nexport function addBinariesToItem(\n\tnewItem: INodeExecutionData,\n\titems: INodeExecutionData[],\n\tuniqueOnly?: boolean,\n) {\n\tconst isBinaryUnique = uniqueOnly ? isBinaryUniqueSetup() : undefined;\n\n\tfor (const item of items) {\n\t\tif (item.binary === undefined) continue;\n\n\t\tfor (const key of Object.keys(item.binary)) {\n\t\t\tif (!newItem.binary) newItem.binary = {};\n\t\t\tlet binaryKey = key;\n\t\t\tconst binary = item.binary[key];\n\n\t\t\tif (isBinaryUnique && !isBinaryUnique(binary)) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\t// If the binary key already exists add a suffix to it\n\t\t\tlet i = 1;\n\t\t\twhile (newItem.binary[binaryKey] !== undefined) {\n\t\t\t\tbinaryKey = `${key}_${i}`;\n\t\t\t\ti++;\n\t\t\t}\n\n\t\t\tnewItem.binary[binaryKey] = binary;\n\t\t}\n\t}\n\n\treturn newItem;\n}\n\nexport function typeToNumber(value: GenericValue): number {\n\tif (typeof value === 'object') {\n\t\tif (Array.isArray(value)) return 9;\n\t\tif (value === null) return 10;\n\t\tif (value instanceof Date) return 11;\n\t}\n\tconst types = {\n\t\t_string: 1,\n\t\t_number: 2,\n\t\t_bigint: 3,\n\t\t_boolean: 4,\n\t\t_symbol: 5,\n\t\t_undefined: 6,\n\t\t_object: 7,\n\t\t_function: 8,\n\t};\n\treturn types[`_${typeof value}`];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAuB;AAOvB,0BAAqD;AAE9C,MAAM,qBAAqB,CAAC,QAA2B,YAAY,aAAa;AACtF,MAAI,OAAO,WAAW,UAAU;AAC/B,WAAO,OACL,MAAM,GAAG,EACT,IAAI,CAAC,UAAU,MAAM,KAAK,CAAC,EAC3B,OAAO,CAAC,UAAU,UAAU,EAAE;AAAA,EACjC;AACA,MAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,WAAO;AAAA,EACR;AACA,QAAM,IAAI;AAAA,IACT,QAAS,SAAS;AAAA,IAClB,EAAE,OAAO,UAAU;AAAA,EACpB;AACD;AAEA,MAAM,eAAe;AAEd,SAAS,WAEf,OACuB;AACvB,QAAM,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAC5C,MAAI,CAAC,aAAa,KAAK,IAAI,GAAG;AAC7B,UAAM,IAAI;AAAA,MACT,KAAK,QAAQ;AAAA,MACb;AAAA,IACD;AAAA,EACD;AAEA,QAAM,OAAO,KAAK,QAAQ;AAC1B,QAAM,KAAK,IAAI,kBAAO;AAAA,IACrB,SAAS,SAAS,WAAW,aAAa;AAAA,IAC1C,SAAS,EAAE,MAAM;AAAA,EAClB,CAAC;AAED,SAAO,GAAG,IAAI,2CAA2C,IAAI,KAAK;AACnE;AAGA,MAAM,sBAAsB,MAAM;AACjC,QAAM,WAAgC,CAAC;AACvC,SAAO,CAAC,WAAwB;AAC/B,eAAW,kBAAkB,UAAU;AACtC,UACC,eAAe,aAAa,OAAO,YACnC,eAAe,aAAa,OAAO,YACnC,eAAe,aAAa,OAAO,YACnC,eAAe,kBAAkB,OAAO,eACvC;AACD,eAAO;AAAA,MACR;AAAA,IACD;AAEA,aAAS,KAAK;AAAA,MACb,UAAU,OAAO;AAAA,MACjB,UAAU,OAAO;AAAA,MACjB,UAAU,OAAO;AAAA,MACjB,eAAe,OAAO;AAAA,IACvB,CAAC;AAED,WAAO;AAAA,EACR;AACD;AAEO,SAAS,kBACf,SACA,OACA,YACC;AACD,QAAM,iBAAiB,aAAa,oBAAoB,IAAI;AAE5D,aAAW,QAAQ,OAAO;AACzB,QAAI,KAAK,WAAW,OAAW;AAE/B,eAAW,OAAO,OAAO,KAAK,KAAK,MAAM,GAAG;AAC3C,UAAI,CAAC,QAAQ,OAAQ,SAAQ,SAAS,CAAC;AACvC,UAAI,YAAY;AAChB,YAAM,SAAS,KAAK,OAAO,GAAG;AAE9B,UAAI,kBAAkB,CAAC,eAAe,MAAM,GAAG;AAC9C;AAAA,MACD;AAGA,UAAI,IAAI;AACR,aAAO,QAAQ,OAAO,SAAS,MAAM,QAAW;AAC/C,oBAAY,GAAG,GAAG,IAAI,CAAC;AACvB;AAAA,MACD;AAEA,cAAQ,OAAO,SAAS,IAAI;AAAA,IAC7B;AAAA,EACD;AAEA,SAAO;AACR;AAEO,SAAS,aAAa,OAA6B;AACzD,MAAI,OAAO,UAAU,UAAU;AAC9B,QAAI,MAAM,QAAQ,KAAK,EAAG,QAAO;AACjC,QAAI,UAAU,KAAM,QAAO;AAC3B,QAAI,iBAAiB,KAAM,QAAO;AAAA,EACnC;AACA,QAAM,QAAQ;AAAA,IACb,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,WAAW;AAAA,EACZ;AACA,SAAO,MAAM,IAAI,OAAO,KAAK,EAAE;AAChC;", "names": []}